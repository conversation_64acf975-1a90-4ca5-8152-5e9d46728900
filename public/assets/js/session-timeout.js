/**
 * Session Timeout Manager
 * Handles automatic session timeout with user-friendly dialogs
 */
class SessionTimeoutManager {
    constructor(options = {}) {
        this.sessionLifetime = options.sessionLifetime || 120; // minutes
        this.warningTime = options.warningTime || 5; // minutes before expiry to show warning
        this.checkInterval = options.checkInterval || 60000; // check every minute
        this.heartbeatInterval = options.heartbeatInterval || 300000; // heartbeat every 5 minutes
        this.logoutUrl = options.logoutUrl || '/logout';
        this.loginUrl = options.loginUrl || '/login';
        this.heartbeatUrl = options.heartbeatUrl || '/session/heartbeat';
        
        this.lastActivity = Date.now();
        this.sessionStart = Date.now();
        this.warningShown = false;
        this.isActive = true;
        this.pauseWarnings = false; // Flag to pause warnings during other modal operations

        this.init();
    }
    
    init() {
        this.bindActivityEvents();
        this.startSessionMonitor();
        this.startHeartbeat();
        this.createWarningModal();
        this.setupGlobalAjaxHandler();
    }
    
    bindActivityEvents() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.updateActivity();
            }, true);
        });
    }
    
    updateActivity() {
        this.lastActivity = Date.now();
        if (this.warningShown) {
            this.hideWarning();
        }
    }
    
    startSessionMonitor() {
        setInterval(() => {
            this.checkSession();
        }, this.checkInterval);
    }
    
    startHeartbeat() {
        setInterval(() => {
            if (this.isUserActive()) {
                this.sendHeartbeat();
            }
        }, this.heartbeatInterval);
    }
    
    checkSession() {
        const now = Date.now();
        const sessionAge = (now - this.sessionStart) / 1000 / 60; // minutes
        const timeSinceActivity = (now - this.lastActivity) / 1000 / 60; // minutes
        
        const timeUntilExpiry = this.sessionLifetime - sessionAge;
        
        if (timeUntilExpiry <= 0) {
            this.forceLogout();
        } else if (timeUntilExpiry <= this.warningTime && !this.warningShown && !this.pauseWarnings) {
            this.showWarning(Math.ceil(timeUntilExpiry));
        }
    }
    
    isUserActive() {
        const timeSinceActivity = (Date.now() - this.lastActivity) / 1000 / 60;
        return timeSinceActivity < 5; // Active if activity within last 5 minutes
    }
    
    sendHeartbeat() {
        fetch(this.heartbeatUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                'X-Requested-With': 'XMLHttpRequest'
            }
        }).then(response => {
            if (response.status === 401) {
                // Session expired, force logout
                this.forceLogout();
            }
            return response.json();
        }).catch(error => {
            console.warn('Heartbeat failed:', error);
        });
    }
    
    createWarningModal() {
        const modalHtml = `
            <div id="sessionTimeoutModal" class="modal fade" tabindex="-1" role="dialog" data-bs-backdrop="static" data-bs-keyboard="false">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header bg-warning-100">
                            <h5 class="modal-title text-warning-600 d-flex align-items-center">
                                <iconify-icon icon="mdi:clock-alert-outline" class="me-2"></iconify-icon>
                                Session Timeout Warning
                            </h5>
                        </div>
                        <div class="modal-body text-center py-4">
                            <div class="mb-3 d-flex justify-content-center">
                                <iconify-icon icon="mdi:timer-sand" class="text-warning-500" style="font-size: 48px;"></iconify-icon>
                            </div>
                            <h6 class="mb-3">Your session will expire soon!</h6>
                            <p class="mb-3">Your session will expire in <strong id="timeoutCountdown" class="text-danger-600">5</strong> minutes due to inactivity.</p>
                            <p class="text-muted small">Click "Stay Logged In" to continue your session or "Logout" to end your session now.</p>
                        </div>
                        <div class="modal-footer justify-content-center">
                            <button type="button" class="btn btn-success d-flex align-items-center" id="stayLoggedInBtn">
                                <iconify-icon icon="mdi:account-check" class="me-1"></iconify-icon>
                                Stay Logged In
                            </button>
                            <button type="button" class="btn btn-outline-danger d-flex align-items-center" id="logoutNowBtn">
                                <iconify-icon icon="mdi:logout" class="me-1"></iconify-icon>
                                Logout Now
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Bind events
        document.getElementById('stayLoggedInBtn').addEventListener('click', () => {
            this.extendSession();
        });
        
        document.getElementById('logoutNowBtn').addEventListener('click', () => {
            this.forceLogout();
        });
    }
    
    showWarning(minutesLeft) {
        this.warningShown = true;
        document.getElementById('timeoutCountdown').textContent = minutesLeft;
        
        const modal = new bootstrap.Modal(document.getElementById('sessionTimeoutModal'));
        modal.show();
        
        // Update countdown every minute
        this.countdownInterval = setInterval(() => {
            minutesLeft--;
            if (minutesLeft <= 0) {
                this.forceLogout();
            } else {
                document.getElementById('timeoutCountdown').textContent = minutesLeft;
            }
        }, 60000);
    }
    
    hideWarning() {
        if (this.warningShown) {
            this.warningShown = false;
            const modalElement = document.getElementById('sessionTimeoutModal');
            const modal = bootstrap.Modal.getInstance(modalElement);

            if (modal) {
                // Add event listener to clean up after modal is hidden
                modalElement.addEventListener('hidden.bs.modal', () => {
                    // Ensure proper cleanup of modal state
                    setTimeout(() => {
                        // Only clean up if this is the only modal
                        const openModals = document.querySelectorAll('.modal.show');
                        if (openModals.length === 0) {
                            document.body.classList.remove('modal-open');
                            document.body.style.overflow = '';
                            document.body.style.paddingRight = '';

                            // Remove any orphaned backdrops
                            document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                                backdrop.remove();
                            });
                        }
                    }, 100);
                }, { once: true });

                modal.hide();
            }

            if (this.countdownInterval) {
                clearInterval(this.countdownInterval);
            }
        }
    }
    
    extendSession() {
        this.sessionStart = Date.now();
        this.lastActivity = Date.now();
        this.hideWarning();
        this.sendHeartbeat();

        // Show success notification
        this.showNotification('Session extended successfully!', 'success');

        // Fix modal backdrop issues by ensuring proper cleanup
        setTimeout(() => {
            // Remove any lingering modal backdrops
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                if (!document.querySelector('.modal.show')) {
                    backdrop.remove();
                }
            });

            // Restore body scroll if no modals are open
            if (!document.querySelector('.modal.show')) {
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            }
        }, 300);
    }
    
    forceLogout() {
        this.isActive = false;

        // Show logout notification
        this.showNotification('Session expired. Logging out...', 'warning');

        // Perform proper logout by calling the logout endpoint
        fetch(this.logoutUrl, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        }).then(() => {
            // Redirect to login page after logout
            window.location.href = this.loginUrl;
        }).catch(() => {
            // If logout fails, force redirect to login
            window.location.href = this.loginUrl;
        });
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} position-fixed top-0 end-0 m-3`;
        notification.style.zIndex = '9999';
        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <iconify-icon icon="mdi:information" class="me-2"></iconify-icon>
                ${message}
            </div>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    setupGlobalAjaxHandler() {
        // Setup global AJAX error handler for jQuery
        if (typeof $ !== 'undefined') {
            $(document).ajaxError((_, xhr) => {
                if (xhr.status === 401 && xhr.responseJSON && xhr.responseJSON.logout) {
                    this.forceLogout();
                }
            });
        }

        // Setup global fetch error handler
        const originalFetch = window.fetch;
        window.fetch = (...args) => {
            return originalFetch(...args).then(response => {
                if (response.status === 401) {
                    response.clone().json().then(data => {
                        if (data.logout) {
                            this.forceLogout();
                        }
                    }).catch(() => {
                        // If not JSON, still check for 401
                        this.forceLogout();
                    });
                }
                return response;
            });
        };
    }
}

// Initialize session timeout manager when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Check if session timeout is enabled
    const enabled = document.querySelector('meta[name="session-timeout-enabled"]')?.getAttribute('content') !== 'false';

    if (!enabled) {
        console.log('Session timeout is disabled');
        return;
    }

    // Get session configuration from meta tags
    const sessionLifetime = parseInt(document.querySelector('meta[name="session-lifetime"]')?.getAttribute('content')) || 120;
    const warningTime = parseInt(document.querySelector('meta[name="session-warning"]')?.getAttribute('content')) || 5;
    const checkInterval = parseInt(document.querySelector('meta[name="session-check-interval"]')?.getAttribute('content')) || 60000;
    const heartbeatInterval = parseInt(document.querySelector('meta[name="session-heartbeat-interval"]')?.getAttribute('content')) || 300000;

    // Initialize session timeout manager
    window.sessionManager = new SessionTimeoutManager({
        sessionLifetime: sessionLifetime,
        warningTime: warningTime,
        checkInterval: checkInterval,
        heartbeatInterval: heartbeatInterval,
        logoutUrl: '/logout',
        loginUrl: '/login',
        heartbeatUrl: '/session/heartbeat'
    });

    console.log('Session timeout manager initialized', {
        sessionLifetime: sessionLifetime + ' minutes',
        warningTime: warningTime + ' minutes',
        checkInterval: checkInterval + 'ms',
        heartbeatInterval: heartbeatInterval + 'ms'
    });
});
