/* Client Dues Report Enhanced Styles */

/* Main Layout */
.client-dues-report {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

/* Summary Cards */
.summary-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

/* Aging Cards */
.aging-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.aging-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Enhanced Filter Section */
.filter-section {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.quick-filter {
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.quick-filter:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.quick-filter.active {
    background-color: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.3);
}

/* Enhanced Form Controls */
.form-control:focus,
.form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
    transform: translateY(-1px);
}

.icon-field {
    position: relative;
}

.icon-field .icon {
    position: absolute;
    top: 50%;
    left: 12px;
    transform: translateY(-50%);
    color: var(--bs-secondary);
    z-index: 2;
}

.icon-field .form-control {
    padding-left: 40px;
}

/* Enhanced Buttons */
.btn {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
}

/* Fix table header text visibility */
.clientDuesTable thead th {
    color: #000000 !important;
    font-weight: 700 !important;
    font-size: 14px !important;
    text-transform: none !important;
}

/* Ensure all table header variations are visible */
.table thead th,
.table > thead > tr > th,
.clientDuesTable > thead > tr > th,
table.clientDuesTable thead th {
    color: #000000 !important;
    font-weight: 700 !important;
}

/* Tooltip styles for rounded values */
[data-bs-toggle="tooltip"] {
    cursor: help !important;
    text-decoration: underline;
    text-decoration-style: dotted;
    text-underline-offset: 3px;
}

[data-bs-toggle="tooltip"]:hover {
    opacity: 0.8;
    transition: opacity 0.2s ease;
}


/* Enhanced Progress Bars */
.progress {
    height: 6px;
    border-radius: 10px;
    background-color: rgba(0,0,0,0.05);
    overflow: hidden;
}

.progress-bar {
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Enhanced Badges */
.badge {
    font-weight: 600;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    transition: all 0.2s ease;
}

.badge:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

/* Loading States */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Notification Styles */
.alert {
    border: none;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

/* Mobile Responsive Enhancements */
@media (max-width: 768px) {
    .summary-card {
        margin-bottom: 1rem;
    }
    
    .aging-card {
        margin-bottom: 0.75rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }
    
    .table-responsive {
        border-radius: 8px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .client-dues-report {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    }
    
    .summary-card,
    .aging-card {
        background-color: #2d2d2d;
        border-color: rgba(255,255,255,0.1);
    }
    
    .table {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .table thead th {
        color: #ffffff;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.shadow-soft {
    box-shadow: 0 4px 25px rgba(0,0,0,0.08);
}

.shadow-medium {
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.shadow-strong {
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}
