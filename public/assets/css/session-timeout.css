/* Session Timeout Modal Styles */
#sessionTimeoutModal .modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

#sessionTimeoutModal .modal-header {
    border-bottom: 1px solid #f1f3f4;
    border-radius: 15px 15px 0 0;
    padding: 1.5rem;
}

#sessionTimeoutModal .modal-body {
    padding: 2rem 1.5rem;
}

#sessionTimeoutModal .modal-footer {
    border-top: 1px solid #f1f3f4;
    border-radius: 0 0 15px 15px;
    padding: 1.5rem;
}

#sessionTimeoutModal .modal-title {
    font-weight: 600;
    font-size: 1.25rem;
}

#sessionTimeoutModal #timeoutCountdown {
    font-size: 1.5rem;
    font-weight: 700;
    color: #dc3545;
}

#sessionTimeoutModal .btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    min-width: 120px;
}

#sessionTimeoutModal .btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

#sessionTimeoutModal .btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

#sessionTimeoutModal .btn-outline-danger {
    border: 2px solid #dc3545;
    color: #dc3545;
    background: transparent;
}

#sessionTimeoutModal .btn-outline-danger:hover {
    background: #dc3545;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

/* Session notification styles */
.session-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Pulse animation for countdown */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

#sessionTimeoutModal #timeoutCountdown {
    animation: pulse 1s infinite;
}

/* Modal backdrop enhancement */
#sessionTimeoutModal.modal .modal-backdrop {
    background-color: rgba(0, 0, 0, 0.6);
}

/* Responsive adjustments */
@media (max-width: 576px) {
    #sessionTimeoutModal .modal-dialog {
        margin: 1rem;
    }
    
    #sessionTimeoutModal .modal-body {
        padding: 1.5rem 1rem;
    }
    
    #sessionTimeoutModal .btn {
        min-width: 100px;
        padding: 0.5rem 1rem;
    }
    
    .session-notification {
        right: 10px;
        left: 10px;
        min-width: auto;
    }
}
