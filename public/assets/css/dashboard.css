/* Dashboard Specific Styles */

/* Dashboard Header */
.dashboard-header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.dashboard-header-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 0%, transparent 50%), 
                      radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
    opacity: 0.6;
}

.dashboard-icon {
    background: linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.1));
    padding: 10px;
    border-radius: 10px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.2);
}

.dashboard-icon iconify-icon {
    font-size: 2.5rem;
}

.dashboard-title {
    font-size: 2rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
    letter-spacing: -1px;
}

.dashboard-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 400;
}

.dashboard-refresh-btn {
    border-radius: 8px;
    padding: 15px 20px;
    font-weight: 700;
    border: none;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    color: #1e3c72;
    transition: all 0.3s ease;
}

.dashboard-refresh-btn iconify-icon {
    font-size: 1.2rem;
}

.dashboard-date-widget {
    background: rgba(255,255,255,0.15);
    padding: 10px 16px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    min-width: 140px;
}

.dashboard-date-widget iconify-icon {
    font-size: 1.3rem;
}

.dashboard-date-day {
    font-size: 1.1rem;
}

.dashboard-date-full {
    opacity: 0.9;
    font-weight: 500;
}

/* Quick Actions Panel */
.quick-actions-panel {
    background: linear-gradient(135deg, #d4e3f3 0%, #b1bef7 100%);
    border-radius: 12px;
    overflow: hidden;
}

.quick-actions-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 12px;
    border-radius: 12px;
}

.quick-actions-icon iconify-icon {
    font-size: 1.5rem;
}

.quick-actions-title {
    color: #2d3748;
    font-size: 1.3rem;
}

/* Action Buttons */
.action-btn {
    border-radius: 8px;
    padding: 10px 16px;
    font-weight: 600;
    border: none;
    transition: all 0.3s ease;
}

.action-btn iconify-icon {
    font-size: 1.1rem;
}

.action-btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    &:hover {
        background: linear-gradient(135deg, #1d4ed8, #3b82f6);
    }
    &:focus {
        background: linear-gradient(135deg, #1d4ed8, #3b82f6);
    }
}

.action-btn-success {
    background: linear-gradient(135deg, #059669, #047857);
    &:hover {
        background: linear-gradient(135deg, #047857, #059669);
    }
    &:focus {
        background: linear-gradient(135deg, #047857, #059669);
    }
}

.action-btn-info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    &:hover {
        background: linear-gradient(135deg, #0891b2, #06b6d4);
    }
    &:focus {
        background: linear-gradient(135deg, #0891b2, #06b6d4);
    }
}

.action-btn-warning {
    background: linear-gradient(135deg, #ea580c, #c2410c);
    &:hover {
        background: linear-gradient(135deg, #c2410c, #ea580c);
    }
    &:focus {
        background: linear-gradient(135deg, #c2410c, #ea580c);
    }
}

.action-btn-secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    &:hover {
        background: linear-gradient(135deg, #4b5563, #6b7280);
    }
    &:focus {
        background: linear-gradient(135deg, #4b5563, #6b7280);
    }
}

/* Statistics Cards */
.stats-card {
    border-radius: 20px;
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.stats-card-label {
    opacity: 0.9;
    font-size: 0.8rem;
}

.stats-card-revenue {
    /* background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%); */
    border:solid 1px #6366f1;
}
.stats-card-revenue .stats-card-icon{
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
}
.stats-card-icon {
    width: 50px;
    height: 50px;
    background: rgba(255,255,255,0.2);
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.3);
    flex-shrink: 0;
}

.stats-card-clients {
    border: solid 1px #047857;
}
.stats-card-clients .stats-card-icon {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}


.stats-card-payments {
    border: solid 1px #c2410c;
}
.stats-card-payments .stats-card-icon{
    background: linear-gradient(135deg, #ea580c 0%, #c2410c 100%);
}

.stats-card-services {
    border: solid 1px #0891b2;
}
.stats-card-services .stats-card-icon{
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

.stats-card-inventory {
    border: solid 1px #7c3aed;
}
.stats-card-inventory .stats-card-icon{
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}
.stats-card-expenses {
    border: solid 1px #dc2626;
}
.stats-card-expenses .stats-card-icon{
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}



.stats-card-content {
    margin-right: 1rem;
}

.stats-card-indicator {
    width: 8px;
    height: 8px;
    background: #ffd700;
    border-radius: 50%;
}



.stats-card-label-lg {
    opacity: 0.9;
    font-size: 0.9rem;
}

.stats-card-value {
    font-size: 1.3rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
    line-height: 1.2;
}

.stats-card-value-lg {
    font-size: 1.5rem !important;
}

.stats-card-badge {
    border-radius: 6px;
    padding: 3px 6px;
    font-weight: 600;
    font-size: 0.65rem;
}

.stats-card-badge-lg {
    border-radius: 8px;
    padding: 4px 8px;
    font-weight: 600;
    font-size: 0.8rem;
}

.stats-card-badge-positive {
    background: rgba(5, 150, 105, 0.2);
    color: #059669;
    border: 1px solid rgba(5, 150, 105, 0.3);
}

.stats-card-badge-negative {
    background: rgba(220, 38, 38, 0.2);
    color: #dc2626;
    border: 1px solid rgba(220, 38, 38, 0.3);
}

.stats-card-badge-neutral {
    background: rgba(255,255,255,0.2);
    color: #333;
    border: 1px solid #333;
}

.stats-card-comparison {
    opacity: 0.8;
    font-size: 0.65rem;
    font-weight: 500;
}

.stats-card-comparison-lg {
    opacity: 0.8;
    font-size: 0.8rem;
    font-weight: 500;
}



.stats-card-icon-lg {
    width: 60px;
    height: 60px;
    border-radius: 18px;
}

.stats-card-icon iconify-icon {
    font-size: 1.5rem;
}

.stats-card-icon-lg iconify-icon {
    font-size: 1.8rem;
}


/* Performance Metrics */
.performance-metrics-panel {
    background: linear-gradient(135deg, #c2d7ec 0%, #ffffff 100%);
    border-radius: 25px;
    overflow: hidden;
}

.performance-metrics-header {
    padding: 2rem 2rem 0 2rem;
}

.performance-metrics-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 12px;
    border-radius: 12px;
}

.performance-metrics-icon iconify-icon {
    font-size: 1.5rem;
}

.performance-metrics-title {
    color: #2d3748;
    font-size: 1.4rem;
}

.performance-metrics-badge {
    border:solid 1px #764ba2;
    color: #333;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 600;
}

.performance-metrics-body {
    padding: 1.5rem 2rem 2rem 2rem;
}

/* Performance Metric Cards */
.metric-card {
    padding: 1.5rem;
    border-radius: 18px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    min-height: 200px;
    text-align: center;
    position: relative;
    display: flex;
    flex-direction: column;
}

.metric-card-success {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
}

.metric-card-danger {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
}

.metric-card-info {
    background: linear-gradient(135deg, #f0f9ff 0%, #bae6fd 100%);
}

.metric-card-warning {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.metric-card-icon {
    width: 50px;
    height: 50px;
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 1rem auto;
}

.metric-card-icon-success {
    background: linear-gradient(135deg, #059669, #047857);
}

.metric-card-icon-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.metric-card-icon-info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.metric-card-icon-warning-positive {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.metric-card-icon-warning-negative {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.metric-card-icon iconify-icon {
    font-size: 1.3rem;
}

.metric-card-value {
    color: #047857;
    font-size: 1.5rem !important;
}

.metric-card-value-danger {
    color: #dc2626;
    font-size: 1.5rem !important;
}

.metric-card-value-info {
    color: #0891b2;
    font-size: 1.5rem !important;
}

.metric-card-value-warning-positive {
    color: #16a34a;
    font-size: 1.5rem !important;
}

.metric-card-value-warning-negative {
    color: #dc2626;
    font-size: 1.5rem !important;
}

.metric-card-label {
    color: #374151;
    font-weight: 600;
    font-size: 0.85rem;
    flex-grow: 1;
}

.metric-card-badge {
    padding: 6px 12px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.75rem;
}

.metric-card-badge-excellent {
    background: linear-gradient(135deg, #059669, #047857);
    color: white;
}

.metric-card-badge-good {
    background: linear-gradient(135deg, #ea580c, #c2410c);
    color: white;
}

.metric-card-badge-attention {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
}

.metric-card-badge-strong {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;
}

.metric-card-badge-positive {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    color: white;
}

.metric-card-badge-stable {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

.metric-card-badge-declining {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

/* Charts Section */
.charts-panel {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    box-shadow: 0 15px 40px rgba(0,0,0,0.1);
    border-radius: 25px;
    overflow: hidden;
}

.charts-header {
    padding: 2rem 2rem 0 2rem;
}

.charts-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 15px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.charts-icon iconify-icon {
    font-size: 1.5rem;
}

.charts-title {
    color: #2d3748;
    font-size: 1.4rem;
}

.charts-subtitle {
    color: #6b7280;
    font-size: 0.9rem;
    font-weight: 500;
}

.charts-select {
    border-radius: 12px;
    padding: 10px 15px;
    font-weight: 600;
    border: 2px solid #e5e7eb;
    background: white;
    color: #374151;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.charts-body {
    padding: 1.5rem 2rem 2rem 2rem;
}

.chart-container {
    min-height: 400px;
    border-radius: 15px;
    background: rgba(255,255,255,0.5);
    padding: 20px;
}

/* Payment Analysis */
.payment-analysis-panel {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    box-shadow: 0 15px 40px rgba(0,0,0,0.1);
    border-radius: 25px;
    overflow: hidden;
}

.payment-analysis-icon {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    padding: 15px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
}

.payment-analysis-icon iconify-icon {
    font-size: 1.5rem;
}

.payment-daterange {
    border-radius: 12px 0 0 12px;
    padding: 10px 15px;
    font-weight: 500;
    border: 2px solid #e5e7eb;
    background: white;
}

.payment-daterange-icon {
    border-radius: 0 12px 12px 0;
    border: 2px solid #e5e7eb;
    border-left: none;
    background: white;
    padding: 10px 15px;
}

.payment-daterange-icon iconify-icon {
    color: #667eea;
    font-size: 1.2rem;
}

.payment-chart-container {
    background: rgba(255,255,255,0.5);
    border-radius: 15px;
    padding: 20px;
    min-height: 300px;
}

/* Loading States */
.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.chart-loading-content {
    text-align: center;
}

/* CSS Chart Fallbacks */
.css-chart-container {
    display: none;
}

.css-chart-header {
    margin-bottom: 1rem;
}

.css-chart-bars {
    height: 280px;
    gap: 8px;
    display: flex;
    align-items: end;
    justify-content: space-between;
}

.css-bar {
    background: linear-gradient(to top, #667eea, #764ba2);
    border-radius: 4px 4px 0 0;
    flex: 1;
    position: relative;
}

.bar-label {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    font-weight: 600;
    color: #6b7280;
}

.bar-value {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.7rem;
    font-weight: 600;
    color: #374151;
    white-space: nowrap;
}

/* CSS Donut Chart */
.css-donut-container {
    display: none;
    text-align: center;
}

.css-donut {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 0 auto 20px;
    border-radius: 50%;
    background: conic-gradient(
        #3b82f6 0deg 120deg,
        #10b981 120deg 200deg,
        #f59e0b 200deg 280deg,
        #ef4444 280deg 360deg
    );
}

.css-donut-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    background: white;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.css-donut-total {
    font-size: 1.2rem;
    font-weight: 700;
    color: #374151;
    margin: 0;
}

.css-donut-label {
    font-size: 0.8rem;
    color: #6b7280;
    margin: 0;
}

.css-legend {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
}

.css-legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
}

.css-legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

/* Additional Dashboard Styles */

/* CSS Chart Bars */
.css-bar-45 { height: 45%; }
.css-bar-55 { height: 55%; }
.css-bar-40 { height: 40%; }
.css-bar-70 { height: 70%; }
.css-bar-80 { height: 80%; }
.css-bar-95 { height: 95%; }
.css-bar-75 { height: 75%; }
.css-bar-85 { height: 85%; }
.css-bar-90 { height: 90%; }
.css-bar-100 { height: 100%; }
.css-bar-88 { height: 88%; }

/* Payment Analysis Styles */
.payment-analysis-header {
    padding: 2rem 2rem 0 2rem;
}

.payment-analysis-body {
    padding: 1.5rem 2rem 2rem 2rem;
}

/* Service Distribution Styles */
.service-distribution-panel {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    box-shadow: 0 15px 40px rgba(0,0,0,0.1);
    border-radius: 25px;
    overflow: hidden;
    min-height: 500px;
}

.service-distribution-header {
    padding: 2rem 2rem 0 2rem;
}

.service-distribution-icon {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    padding: 15px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
}

.service-distribution-icon iconify-icon {
    font-size: 1.5rem;
}

.service-distribution-title {
    color: #2d3748;
    font-size: 1.2rem;
}

.service-distribution-subtitle {
    color: #6b7280;
    font-size: 0.85rem;
    font-weight: 500;
}

.service-distribution-body {
    padding: 1.5rem 2rem 2rem 2rem;
}

/* Service Cards */
.service-card {
    padding: 1.5rem;
    border-radius: 18px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.service-card-bedded {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
}

.service-card-non-bedded {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
}

.service-card-weight {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.service-card-fixed {
    background: linear-gradient(135deg, #f0f9ff 0%, #bae6fd 100%);
}

.service-card-icon {
    width: 50px;
    height: 50px;
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.service-card-icon-bedded {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.service-card-icon-non-bedded {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.service-card-icon-weight {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.service-card-icon-fixed {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.service-card-icon iconify-icon {
    font-size: 1.3rem;
}

.service-card-title {
    font-size: 1.1rem;
}

.service-card-title-bedded {
    color: #1e40af;
}

.service-card-title-non-bedded {
    color: #15803d;
}

.service-card-title-weight {
    color: #b45309;
}

.service-card-title-fixed {
    color: #0e7490;
}

.service-card-subtitle {
    color: #6b7280;
    font-size: 0.85rem;
    font-weight: 500;
}

.service-card-value {
    font-size: 1.6rem;
}

.service-card-value-bedded {
    color: #1e40af;
}

.service-card-value-non-bedded {
    color: #15803d;
}

.service-card-value-weight {
    color: #b45309;
}

.service-card-value-fixed {
    color: #0e7490;
}

.service-card-label {
    color: #6b7280;
    font-size: 0.85rem;
    font-weight: 600;
}

/* Recent Activities Styles */
.activities-panel {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    box-shadow: 0 15px 40px rgba(0,0,0,0.1);
    border-radius: 25px;
    overflow: hidden;
    min-height: 500px;
}

.activities-header {
    padding: 2rem 2rem 0 2rem;
}

.activities-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    padding: 15px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

.activities-icon iconify-icon {
    font-size: 1.5rem;
}

.activities-title {
    color: #2d3748;
    font-size: 1.2rem;
}

.activities-subtitle {
    color: #6b7280;
    font-size: 0.85rem;
    font-weight: 500;
}

.activities-dropdown-btn {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 8px 12px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.activities-dropdown-btn iconify-icon {
    font-size: 1rem;
}

.activities-dropdown-menu {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.15);
}

.activities-dropdown-item {
    border-radius: 8px;
    margin: 4px;
    padding: 8px 12px;
}

.activities-body {
    padding: 1.5rem 2rem 2rem 2rem;
}

.activity-timeline {
    max-height: 350px;
    overflow-y: auto;
    padding-right: 10px;
}

.activity-item {
    background: rgba(255,255,255,0.7);
    border-radius: 12px;
    border: 1px solid rgba(0,0,0,0.05);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.activity-icon-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.activity-icon-success {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.activity-icon-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.activity-icon-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.activity-icon iconify-icon {
    font-size: 1.1rem;
}

.activity-title {
    color: #2d3748;
    font-size: 0.9rem;
}

.activity-description {
    color: #6b7280;
    font-size: 0.8rem;
    line-height: 1.4;
}

.activity-badge {
    color: white;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
}

.activity-badge-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.activity-badge-success {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.activity-badge-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.activity-badge-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.activity-time {
    color: #9ca3af;
    font-size: 0.7rem;
    font-weight: 500;
}

.activity-empty {
    height: 350px;
}

.activity-empty-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    border-radius: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1rem;
}

.activity-empty-icon iconify-icon {
    font-size: 2rem;
    color: #9ca3af;
}

.activity-empty-text {
    color: #6b7280;
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0;
}

/* Analytics Charts Styles */
.analytics-panel {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    box-shadow: 0 15px 40px rgba(0,0,0,0.1);
    border-radius: 25px;
    overflow: hidden;
    min-height: 450px;
}

.analytics-header {
    padding: 2rem 2rem 0 2rem;
}

.analytics-body {
    padding: 1.5rem 2rem 2rem 2rem;
}

.analytics-icon {
    padding: 15px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.analytics-icon-invoice {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.analytics-icon-payment {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.analytics-icon-pending {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.analytics-icon-growth {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.analytics-icon iconify-icon {
    font-size: 1.5rem;
}

.analytics-title {
    color: #2d3748;
    font-size: 1.2rem;
}

.analytics-subtitle {
    color: #6b7280;
    font-size: 0.85rem;
    font-weight: 500;
}

.analytics-select {
    border-radius: 12px;
    padding: 8px 12px;
    font-weight: 600;
    border: 2px solid #e5e7eb;
    background: white;
    color: #374151;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    font-size: 0.85rem;
}

.analytics-chart-container {
    min-height: 320px;
    border-radius: 15px;
    background: rgba(255,255,255,0.5);
    padding: 20px;
}

.analytics-refresh-btn {
    border: none;
    border-radius: 10px;
    padding: 8px 12px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.analytics-refresh-btn-pending {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.analytics-refresh-btn iconify-icon {
    font-size: 0.9rem;
}

/* Chart Container Styles */
.chart-container-300 {
    background: rgba(255,255,255,0.5);
    border-radius: 15px;
    padding: 20px;
    min-height: 300px;
}

.chart-container-full {
    width: 100%;
    height: 300px;
}

.chart-container-full-width {
    width: 100%;
}

/* Legend Styles */
.legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
}

.legend-color-cash {
    background: #3b82f6;
}

.legend-color-cheque {
    background: #10b981;
}

.legend-color-discount {
    background: #f59e0b;
}

.legend-color-netbanking {
    background: #ef4444;
}

.legend-color-transfer {
    background: #8b5cf6;
}

.legend-color-tds {
    background: #f97316;
}

.legend-color-upi {
    background: #06b6d4;
}

.legend-text {
    color: #374151;
    font-weight: 500;
}

/* Input Group Styles */
.input-group-daterange {
    max-width: 100%;
}

.input-daterange {
    border-radius: 12px 0 0 12px;
    padding: 10px 15px;
    font-weight: 500;
    border: 2px solid #e5e7eb;
    background: white;
}

.input-group-text-daterange {
    border-radius: 0 12px 12px 0;
    border: 2px solid #e5e7eb;
    border-left: none;
    background: white;
    padding: 10px 15px;
}

.input-group-text-daterange iconify-icon {
    color: #667eea;
    font-size: 1.2rem;
}

/* Notification Styles */
.notification-toast {
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .dashboard-title {
        font-size: 2rem;
    }

    .dashboard-subtitle {
        font-size: 1rem;
    }

    .stats-card-body {
        padding: 1rem !important;
    }

    .performance-metrics-header,
    .charts-header,
    .analytics-header {
        padding: 1.5rem 1.5rem 0 1.5rem;
    }

    .performance-metrics-body,
    .charts-body,
    .analytics-body {
        padding: 1rem 1.5rem 1.5rem 1.5rem;
    }
}

/* Icon Alignment Fixes */
iconify-icon {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    vertical-align: middle !important;
    width: auto !important;
    height: auto !important;
}

.d-flex iconify-icon {
    flex-shrink: 0;
}

.card-body iconify-icon {
    line-height: 1 !important;
}

iconify-icon:not([hidden]) {
    display: inline-flex !important;
}

/* Consistent Design System */

.dashboard-main-body .card-body {
    padding: 1.5rem !important;
}

.dashboard-main-body .row {
    margin-bottom: 1.5rem;
}

.d-flex.gap-2 {
    gap: 0.5rem !important;
}

.d-flex.gap-3 {
    gap: 0.75rem !important;
}

.d-flex.gap-4 {
    gap: 1rem !important;
}

/* Text Styles */
.text-white {
    color: #ffffff !important;
    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.text-white-50 {
    color: rgba(255,255,255,0.8) !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.text-muted {
    color: #6b7280 !important;
}

.text-secondary-light {
    color: #4b5563 !important;
}

.text-primary-light {
    color: #1f2937 !important;
    font-weight: 600;
}

/* Typography */
.dashboard-main-body h1 {
    font-size: 2.2rem !important;
    font-weight: 700;
    line-height: 1.2;
}

.dashboard-main-body h4 {
    font-size: 1.1rem !important;
    font-weight: 600;
    line-height: 1.4;
}

.dashboard-main-body h5 {
    font-size: 1rem !important;
    font-weight: 600;
    line-height: 1.4;
}

.dashboard-main-body h6 {
    font-size: 0.9rem !important;
    font-weight: 600;
    line-height: 1.4;
}

.dashboard-main-body p {
    font-size: 0.8rem !important;
    line-height: 1.5;
}

.dashboard-main-body small {
    font-size: 0.7rem !important;
}

/* Card Styling */
.dashboard-main-body .card {
    border-radius: 8px !important;
}

/* Button Styling */
.dashboard-main-body .btn {
    border-radius: 8px !important;
    font-weight: 600 !important;
    padding: 10px 16px !important;
    font-size: 0.85rem !important;
}
