<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Process email queue every minute
        $schedule->command('email:process-queue --timeout=60')
                 ->everyMinute()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Send daily reports every day at 8:00 AM
        $schedule->command('report:daily')
                 ->dailyAt('08:00')
                 ->withoutOverlapping()
                 ->runInBackground();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
