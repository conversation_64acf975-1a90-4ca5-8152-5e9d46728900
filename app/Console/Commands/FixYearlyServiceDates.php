<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ClientService;
use App\Models\ServiceChangeLog;
use Illuminate\Support\Facades\DB;

class FixYearlyServiceDates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'services:fix-yearly-dates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix next_invoice_date for yearly services where it has incorrect date calculation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fixing yearly service dates...');

        try {
            DB::beginTransaction();

            // Find yearly services where next_invoice_date needs fixing
            $yearlyServices = ClientService::where('payment_cycle', 'Yearly')
                ->where('status', 1) // Active services only
                ->where(function($query) {
                    // Case 1: next_invoice_date equals start_date (old bug)
                    $query->whereColumn('next_invoice_date', '=', 'start_date')
                    // Case 2: next_invoice_date is exactly one year ahead of start_date (new bug)
                    ->orWhereRaw('DATE_ADD(start_date, INTERVAL 1 YEAR) = next_invoice_date');
                })
                ->get();

            $this->info("Found {$yearlyServices->count()} yearly services with incorrect next_invoice_date");

            $fixedCount = 0;
            $errors = [];

            foreach ($yearlyServices as $service) {
                try {
                    // Determine the type of issue
                    $issueType = '';
                    if ($service->next_invoice_date === $service->start_date) {
                        $issueType = 'next_invoice_date equals start_date';
                    } else {
                        $issueType = 'next_invoice_date is one year ahead of start_date';
                    }

                    $this->line("Fixing Service ID: {$service->id} - Client: {$service->client->name}");
                    $this->line("  Issue Type: {$issueType}");
                    $this->line("  Start Date: {$service->start_date}");
                    $this->line("  Current Next Invoice Date: {$service->next_invoice_date}");

                    // For yearly services, the first invoice should be on the start date
                    // The next_invoice_date will be updated to next year after the first invoice is generated
                    $newNextInvoiceDate = $service->start_date;

                    $this->line("  New Next Invoice Date: {$newNextInvoiceDate}");

                    // Update the service
                    $service->update(['next_invoice_date' => $newNextInvoiceDate]);

                    // Log the change
                    ServiceChangeLog::logChange(
                        $service->id,
                        'next_invoice_date_fixed',
                        'System Auto-Fix Command',
                        ['next_invoice_date' => $service->start_date],
                        ['next_invoice_date' => $newNextInvoiceDate],
                        "Fixed incorrect next_invoice_date for yearly service. Changed from {$service->start_date} to {$newNextInvoiceDate}"
                    );

                    $fixedCount++;
                    $this->info("  ✅ Fixed successfully");

                } catch (\Exception $e) {
                    $error = "Service ID {$service->id}: " . $e->getMessage();
                    $errors[] = $error;
                    $this->error("  ❌ Error: {$error}");
                }
            }

            DB::commit();

            $this->info("\n=== Summary ===");
            $this->info("Total services found: {$yearlyServices->count()}");
            $this->info("Successfully fixed: {$fixedCount}");
            $this->info("Errors: " . count($errors));

            if (count($errors) > 0) {
                $this->error("\nErrors encountered:");
                foreach ($errors as $error) {
                    $this->error("  - {$error}");
                }
            }

            return 0;

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("Error fixing yearly service dates: " . $e->getMessage());
            return 1;
        }
    }


}
