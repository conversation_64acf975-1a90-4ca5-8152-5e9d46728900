<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Models\Invoice;
use App\Models\Client;
use Carbon\Carbon;

class GenerateTestActivity extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:generate-test-activity';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate test activity for queue monitor dashboard';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating test activity for Queue Monitor...');

        // Generate some test log entries
        $activities = [
            'Email queued - Manual: INV/24-25/07/001 for ABC Company (<EMAIL>)',
            'Email sent successfully for invoice INV/24-25/07/002',
            'Email queued - Auto: INV/24-25/07/003 for XYZ Corp (<EMAIL>)',
            'Email queued - Manual: INV/24-25/07/004 for Test Business (<EMAIL>)',
            'Email sent successfully for invoice INV/24-25/07/005',
        ];

        foreach ($activities as $activity) {
            Log::info($activity);
            $this->line("✓ Logged: {$activity}");
        }

        // Get some real invoice data if available
        $recentInvoices = Invoice::with('client')
                               ->orderBy('created_at', 'desc')
                               ->limit(3)
                               ->get();

        if ($recentInvoices->count() > 0) {
            $this->info("\nFound {$recentInvoices->count()} recent invoices:");
            foreach ($recentInvoices as $invoice) {
                $clientName = $invoice->client->name ?? 'Unknown Client';
                $email = $invoice->client->email ?? 'No email';
                $type = $invoice->created_type == 1 ? 'Manual' : 'Auto';
                
                $message = "Email queued - {$type}: {$invoice->invoice_code} for {$clientName} ({$email})";
                Log::info($message);
                $this->line("✓ Logged: {$message}");
            }
        }

        $this->info("\n✅ Test activity generated successfully!");
        $this->info("📊 Check the Queue Monitor dashboard to see the activity.");
        $this->info("🔗 Navigate to: Reports → Queue Monitor");

        return 0;
    }
}
