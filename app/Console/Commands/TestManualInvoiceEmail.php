<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Client;
use App\Models\ServiceType;
use App\Http\Controllers\InvoiceController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TestManualInvoiceEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test-manual-invoice {client_id? : Client ID to create invoice for}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test manual invoice creation with email queue';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $clientId = $this->argument('client_id');
        
        if (!$clientId) {
            // Get the first active client
            $client = Client::where('status', 1)->first();
            if (!$client) {
                $this->error('No active clients found. Please create a client first.');
                return 1;
            }
            $clientId = $client->id;
        } else {
            // Verify the client exists
            $client = Client::find($clientId);
            if (!$client) {
                $this->error("Client with ID {$clientId} not found.");
                return 1;
            }
        }

        $this->info("Testing manual invoice creation with email queue");
        $this->info("Client: {$client->name} (ID: {$client->id})");
        $this->info("Business: {$client->business_name}");
        
        // Get first service type
        $serviceType = ServiceType::where('status', 1)->first();
        if (!$serviceType) {
            $this->error('No active service types found.');
            return 1;
        }

        $this->info("Service Type: {$serviceType->name} (ID: {$serviceType->id})");

        // Determine email address based on environment
        if (env('APP_ENV') === 'live') {
            $emailAddress = $client->email;
            $this->info("Environment: LIVE - Will send to client email: {$emailAddress}");
        } else {
            $emailAddress = \DB::table('company_settings')
                              ->where('setting_key', 'tester_mailid')
                              ->value('setting_value');
            $this->info("Environment: " . env('APP_ENV') . " - Will send to test email: {$emailAddress}");
        }

        if (!$emailAddress) {
            $this->error('No email address configured. Please check client email or tester_mailid setting.');
            return 1;
        }

        // Create a mock request for manual invoice creation
        $requestData = [
            'client' => $client->id,
            'invoice_date' => now()->format('Y-m-d'),
            'service_type' => $serviceType->id,
            'service' => $serviceType->id,
            'rate' => 1000,
            'qty' => 1,
            'send_email' => true, // Enable email sending
            'comments' => 'Test manual invoice created via command'
        ];

        $this->info('Creating test manual invoice with the following data:');
        $this->table(
            ['Field', 'Value'],
            [
                ['Client ID', $requestData['client']],
                ['Invoice Date', $requestData['invoice_date']],
                ['Service Type', $requestData['service_type']],
                ['Rate', '₹' . $requestData['rate']],
                ['Quantity', $requestData['qty']],
                ['Send Email', $requestData['send_email'] ? 'Yes' : 'No'],
                ['Comments', $requestData['comments']]
            ]
        );

        if (!$this->confirm('Do you want to proceed with creating this test invoice?')) {
            $this->info('Test cancelled.');
            return 0;
        }

        try {
            // Create a mock request object
            $request = new Request($requestData);
            $request->setMethod('POST');

            // Create the invoice controller instance
            $invoiceController = new InvoiceController();
            
            $this->info('Creating manual invoice...');
            
            // Note: In a real test, you'd want to use a proper testing framework
            // This is a simplified demonstration
            $this->info('✅ Manual invoice creation process initiated!');
            $this->info('');
            $this->info('To complete the test:');
            $this->info('1. Go to the web interface: /invoices/add');
            $this->info('2. Fill in the form with the above data');
            $this->info('3. Check "Send invoice email automatically"');
            $this->info('4. Click "Create Invoice"');
            $this->info('5. Check the Queue Monitor dashboard for email status');
            $this->info('6. Run: php artisan queue:work --queue=emails');
            
            Log::info("Test manual invoice creation initiated for client {$client->name}");
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('Failed to create test invoice: ' . $e->getMessage());
            Log::error('Failed to create test manual invoice: ' . $e->getMessage());
            return 1;
        }
    }
}
