<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DailyReportService;
use Carbon\Carbon;

class SendDailyReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'report:daily {--date= : The date for which to generate the report (YYYY-MM-DD format)} {--force : Force generation even if disabled}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate and send daily business report via email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting daily report generation...');

        try {
            // Get date from option or use yesterday
            $date = $this->option('date');
            if ($date) {
                try {
                    $reportDate = Carbon::createFromFormat('Y-m-d', $date);
                } catch (\Exception $e) {
                    $this->error('Invalid date format. Please use YYYY-MM-DD format.');
                    return 1;
                }
            } else {
                $reportDate = Carbon::yesterday();
            }

            $this->info("Generating report for: {$reportDate->format('Y-m-d')}");

            // Generate and send report
            $reportService = new DailyReportService();
            $forceGenerate = $this->option('force');
            $result = $reportService->generateAndSendDailyReport($reportDate, $forceGenerate);

            if ($result['success']) {
                $this->info($result['message']);

                // Display summary
                $reportData = $result['report_data'];
                $this->table(
                    ['Metric', 'Value'],
                    [
                        ['Total Revenue', '₹' . number_format($reportData['summary']['total_revenue'], 2)],
                        ['Total Collections', '₹' . number_format($reportData['summary']['total_collections'], 2)],
                        ['Outstanding Amount', '₹' . number_format($reportData['summary']['net_outstanding'], 2)],
                        ['New Invoices', $reportData['invoices']['total_count']],
                        ['New Payments', $reportData['payments']['total_count']],
                        ['New Clients', $reportData['clients']['new_clients_count']],
                        ['New Services', $reportData['services']['new_services_count']],
                        ['Collection Efficiency', number_format($reportData['summary']['collection_efficiency'], 1) . '%'],
                    ]
                );

                return 0;
            } else {
                $this->error($result['message']);
                return 1;
            }

        } catch (\Exception $e) {
            $this->error('Failed to generate daily report: ' . $e->getMessage());
            return 1;
        }
    }
}
