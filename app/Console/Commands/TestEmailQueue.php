<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\SendInvoiceEmailJob;
use App\Models\Invoice;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class TestEmailQueue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test-queue {invoice_id? : Invoice ID to test with} {--type=auto : Type of invoice (auto|manual)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the email queue system with a sample invoice (auto-generated or manual)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $invoiceId = $this->argument('invoice_id');

        if (!$invoiceId) {
            // Get the latest invoice
            $invoice = Invoice::latest()->first();
            if (!$invoice) {
                $this->error('No invoices found in the database. Please create an invoice first.');
                return 1;
            }
            $invoiceId = $invoice->id;
        } else {
            // Verify the invoice exists
            $invoice = Invoice::find($invoiceId);
            if (!$invoice) {
                $this->error("Invoice with ID {$invoiceId} not found.");
                return 1;
            }
        }

        $invoiceType = $invoice->created_type == 1 ? 'Manual' : 'Auto-generated';

        $this->info("Testing email queue with Invoice ID: {$invoiceId}");
        $this->info("Invoice Code: {$invoice->invoice_code}");
        $this->info("Invoice Type: {$invoiceType}");
        $this->info("Client: {$invoice->client->name}");

        // Determine email address based on environment
        $testEmail = null;
        if (env('APP_ENV') === 'live') {
            $testEmail = $invoice->client->email;
            $this->info("Environment: LIVE - Will send to client email: {$testEmail}");
        } else {
            $testEmail = DB::table('company_settings')
                           ->where('setting_key', 'tester_mailid')
                           ->value('setting_value');
            $this->info("Environment: " . env('APP_ENV') . " - Will send to test email: {$testEmail}");
        }

        if (!$testEmail) {
            $this->error('No email address configured. Please check client email or tester_mailid setting.');
            return 1;
        }

        try {
            // Queue the email job
            SendInvoiceEmailJob::dispatch($invoiceId, $testEmail);

            $this->info('✅ Email job queued successfully!');
            $this->info('');
            $this->info('Next steps:');
            $this->info('1. Run: php artisan queue:work --queue=emails');
            $this->info('2. Or run: php artisan email:process-queue');
            $this->info('3. Check the Queue Monitor dashboard for status');

            Log::info("Test email queued for invoice {$invoice->invoice_code} to {$testEmail}");

            return 0;

        } catch (\Exception $e) {
            $this->error('Failed to queue email: ' . $e->getMessage());
            Log::error('Failed to queue test email: ' . $e->getMessage());
            return 1;
        }
    }
}
