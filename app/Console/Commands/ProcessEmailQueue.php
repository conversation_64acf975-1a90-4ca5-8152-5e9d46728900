<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class ProcessEmailQueue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:process-queue {--timeout=60 : Maximum execution time in seconds}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process the email queue for invoice emails';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $timeout = $this->option('timeout');
        
        $this->info("Starting email queue processing (timeout: {$timeout}s)...");
        
        try {
            // Process the queue with timeout
            $exitCode = Artisan::call('queue:work', [
                '--queue' => 'default',
                '--timeout' => $timeout,
                '--tries' => 3,
                '--delay' => 3,
                '--stop-when-empty' => true,
            ]);

            if ($exitCode === 0) {
                $this->info('Email queue processed successfully.');
                Log::info('Email queue processed successfully via command.');
            } else {
                $this->error('Email queue processing failed.');
                Log::error('Email queue processing failed via command.');
            }

            return $exitCode;

        } catch (\Exception $e) {
            $this->error('Error processing email queue: ' . $e->getMessage());
            Log::error('Error processing email queue: ' . $e->getMessage());
            return 1;
        }
    }
}
