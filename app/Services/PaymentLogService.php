<?php

namespace App\Services;

use App\Models\PaymentLog;
use App\Models\Payment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class PaymentLogService
{
    /**
     * Log payment creation
     */
    public static function logCreated(Payment $payment, $reason = null)
    {
        self::createLog($payment, 'created', null, null, $payment->toArray(), $reason);
    }

    /**
     * Log payment update
     */
    public static function logUpdated(Payment $payment, array $originalData, array $changes, $reason = null)
    {
        // Log overall update
        self::createLog($payment, 'updated', null, null, $payment->toArray(), $reason);

        // Log individual field changes
        foreach ($changes as $field => $newValue) {
            $oldValue = $originalData[$field] ?? null;
            if ($oldValue != $newValue) {
                self::createLog(
                    $payment, 
                    'updated', 
                    $field, 
                    $oldValue, 
                    $newValue, 
                    $reason
                );
            }
        }
    }

    /**
     * Log payment deletion
     */
    public static function logDeleted(Payment $payment, $reason = null)
    {
        self::createLog($payment, 'deleted', null, null, $payment->toArray(), $reason);
    }

    /**
     * Log custom action
     */
    public static function logCustomAction(Payment $payment, $action, $description = null, $reason = null)
    {
        self::createLog($payment, $action, 'custom_action', null, $description, $reason);
    }

    /**
     * Create a log entry
     */
    private static function createLog(Payment $payment, $action, $fieldName = null, $oldValue = null, $newValue = null, $reason = null)
    {
        try {
            PaymentLog::create([
                'payment_id' => $payment->id,
                'action' => $action,
                'field_name' => $fieldName,
                'old_value' => is_array($oldValue) ? json_encode($oldValue) : $oldValue,
                'new_value' => is_array($newValue) ? json_encode($newValue) : $newValue,
                'changed_by' => Auth::user()->name ?? 'System',
                'ip_address' => Request::ip(),
                'user_agent' => Request::userAgent(),
                'reason' => $reason,
                'full_data' => $action === 'created' || $action === 'updated' ? $payment->toArray() : null
            ]);
        } catch (\Exception $e) {
            // Log the error but don't break the main operation
            \Log::error('Failed to create payment log: ' . $e->getMessage());
        }
    }

    /**
     * Get payment change history
     */
    public static function getPaymentHistory($paymentId, $limit = 50)
    {
        return PaymentLog::where('payment_id', $paymentId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent payment changes across all payments
     */
    public static function getRecentChanges($limit = 100)
    {
        return PaymentLog::with('payment')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get changes by user
     */
    public static function getChangesByUser($userName, $limit = 50)
    {
        return PaymentLog::where('changed_by', $userName)
            ->with('payment')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get changes by action type
     */
    public static function getChangesByAction($action, $limit = 50)
    {
        return PaymentLog::where('action', $action)
            ->with('payment')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get payment statistics
     */
    public static function getPaymentLogStats()
    {
        return [
            'total_logs' => PaymentLog::count(),
            'created_count' => PaymentLog::where('action', 'created')->count(),
            'updated_count' => PaymentLog::where('action', 'updated')->count(),
            'deleted_count' => PaymentLog::where('action', 'deleted')->count(),
            'recent_activity' => PaymentLog::where('created_at', '>=', now()->subDays(7))->count(),
            'top_users' => PaymentLog::selectRaw('changed_by, COUNT(*) as count')
                ->groupBy('changed_by')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get()
        ];
    }

    /**
     * Clean old logs (optional - for maintenance)
     */
    public static function cleanOldLogs($daysToKeep = 365)
    {
        $cutoffDate = now()->subDays($daysToKeep);
        return PaymentLog::where('created_at', '<', $cutoffDate)->delete();
    }
}
