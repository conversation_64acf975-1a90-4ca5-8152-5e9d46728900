<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class NotificationAlertService
{
    /**
     * Send invoice creation alert
     */
    public function sendInvoiceCreationAlert($invoice, $isManual = true)
    {
        try {
            // Check if invoice alerts are enabled
            $alertEnabled = DB::table('company_settings')
                            ->where('setting_key', 'invoice_alert_enabled')
                            ->value('setting_value') == '1';

            if (!$alertEnabled) {
                return;
            }

            // Check if this type of creation should trigger alert
            $manualEnabled = DB::table('company_settings')
                           ->where('setting_key', 'invoice_alert_manual_creation')
                           ->value('setting_value') == '1';

            $autoEnabled = DB::table('company_settings')
                         ->where('setting_key', 'invoice_alert_auto_creation')
                         ->value('setting_value') == '1';

            if (($isManual && !$manualEnabled) || (!$isManual && !$autoEnabled)) {
                return;
            }

            // Get alert emails
            $alertEmails = DB::table('company_settings')
                          ->where('setting_key', 'invoice_alert_emails')
                          ->value('setting_value');

            if (!$alertEmails) {
                return;
            }

            $emails = $this->parseEmailList($alertEmails);
            if (empty($emails)) {
                return;
            }

            // Get company email for sender
            $companyEmail = DB::table('company_settings')
                            ->where('setting_key', 'email')
                            ->value('setting_value') ?: config('mail.from.address');

            // Send alert email
            $subject = 'Invoice Creation Alert - ' . $invoice->invoice_code;
            $creationType = $isManual ? 'Manual' : 'Auto-Generated';
            
            $emailData = [
                'invoice' => $invoice,
                'creation_type' => $creationType,
                'company_name' => config('app.name')
            ];

            foreach ($emails as $email) {
                Mail::send('email.alerts.invoice-creation', $emailData, function ($message) use ($email, $subject, $companyEmail) {
                    $message->to($email)
                            ->subject($subject)
                            ->from($companyEmail, config('app.name'));
                });
            }

            Log::info("Invoice creation alert sent for invoice {$invoice->invoice_code} to " . count($emails) . " recipients");

        } catch (\Exception $e) {
            Log::error("Failed to send invoice creation alert: " . $e->getMessage());
        }
    }

    /**
     * Send payment creation alert
     */
    public function sendPaymentCreationAlert($payment)
    {
        try {
            // Check if payment alerts are enabled
            $alertEnabled = DB::table('company_settings')
                            ->where('setting_key', 'payment_alert_enabled')
                            ->value('setting_value') == '1';

            if (!$alertEnabled) {
                return;
            }

            // Get alert emails
            $alertEmails = DB::table('company_settings')
                          ->where('setting_key', 'payment_alert_emails')
                          ->value('setting_value');

            if (!$alertEmails) {
                return;
            }

            $emails = $this->parseEmailList($alertEmails);
            if (empty($emails)) {
                return;
            }

            // Get company email for sender
            $companyEmail = DB::table('company_settings')
                            ->where('setting_key', 'email')
                            ->value('setting_value') ?: config('mail.from.address');

            // Send alert email
            $subject = 'Payment Creation Alert - ₹' . number_format($payment->amount, 2);
            
            $emailData = [
                'payment' => $payment,
                'company_name' => config('app.name')
            ];

            foreach ($emails as $email) {
                Mail::send('email.alerts.payment-creation', $emailData, function ($message) use ($email, $subject, $companyEmail) {
                    $message->to($email)
                            ->subject($subject)
                            ->from($companyEmail, config('app.name'));
                });
            }

            Log::info("Payment creation alert sent for payment ID {$payment->id} to " . count($emails) . " recipients");

        } catch (\Exception $e) {
            Log::error("Failed to send payment creation alert: " . $e->getMessage());
        }
    }

    /**
     * Send client creation alert
     */
    public function sendClientCreationAlert($client)
    {
        try {
            // Check if client alerts are enabled
            $alertEnabled = DB::table('company_settings')
                            ->where('setting_key', 'client_alert_enabled')
                            ->value('setting_value') == '1';

            if (!$alertEnabled) {
                return;
            }

            // Get alert emails
            $alertEmails = DB::table('company_settings')
                          ->where('setting_key', 'client_alert_emails')
                          ->value('setting_value');

            if (!$alertEmails) {
                return;
            }

            $emails = $this->parseEmailList($alertEmails);
            if (empty($emails)) {
                return;
            }

            // Get company email for sender
            $companyEmail = DB::table('company_settings')
                            ->where('setting_key', 'email')
                            ->value('setting_value') ?: config('mail.from.address');

            // Send alert email
            $subject = 'New Client Created - ' . $client->business_name;
            
            $emailData = [
                'client' => $client,
                'company_name' => config('app.name')
            ];

            foreach ($emails as $email) {
                Mail::send('email.alerts.client-creation', $emailData, function ($message) use ($email, $subject, $companyEmail) {
                    $message->to($email)
                            ->subject($subject)
                            ->from($companyEmail, config('app.name'));
                });
            }

            Log::info("Client creation alert sent for client {$client->business_name} to " . count($emails) . " recipients");

        } catch (\Exception $e) {
            Log::error("Failed to send client creation alert: " . $e->getMessage());
        }
    }

    /**
     * Send employee creation alert
     */
    public function sendEmployeeCreationAlert($employee)
    {
        try {
            // Check if employee alerts are enabled
            $alertEnabled = DB::table('company_settings')
                            ->where('setting_key', 'employee_alert_enabled')
                            ->value('setting_value') == '1';

            if (!$alertEnabled) {
                return;
            }

            // Get alert emails
            $alertEmails = DB::table('company_settings')
                          ->where('setting_key', 'employee_alert_emails')
                          ->value('setting_value');

            if (!$alertEmails) {
                return;
            }

            $emails = $this->parseEmailList($alertEmails);
            if (empty($emails)) {
                return;
            }

            // Get company email for sender
            $companyEmail = DB::table('company_settings')
                            ->where('setting_key', 'email')
                            ->value('setting_value') ?: config('mail.from.address');

            // Send alert email
            $subject = 'New Employee Added - ' . $employee->name;
            
            $emailData = [
                'employee' => $employee,
                'company_name' => config('app.name')
            ];

            foreach ($emails as $email) {
                Mail::send('email.alerts.employee-creation', $emailData, function ($message) use ($email, $subject, $companyEmail) {
                    $message->to($email)
                            ->subject($subject)
                            ->from($companyEmail, config('app.name'));
                });
            }

            Log::info("Employee creation alert sent for employee {$employee->name} to " . count($emails) . " recipients");

        } catch (\Exception $e) {
            Log::error("Failed to send employee creation alert: " . $e->getMessage());
        }
    }

    /**
     * Send service creation alert
     */
    public function sendServiceCreationAlert($service)
    {
        try {
            // Check if service alerts are enabled
            $alertEnabled = DB::table('company_settings')
                            ->where('setting_key', 'service_alert_enabled')
                            ->value('setting_value') == '1';

            if (!$alertEnabled) {
                return;
            }

            // Get alert emails
            $alertEmails = DB::table('company_settings')
                          ->where('setting_key', 'service_alert_emails')
                          ->value('setting_value');

            if (!$alertEmails) {
                return;
            }

            $emails = $this->parseEmailList($alertEmails);
            if (empty($emails)) {
                return;
            }

            // Get company email for sender
            $companyEmail = DB::table('company_settings')
                            ->where('setting_key', 'email')
                            ->value('setting_value') ?: config('mail.from.address');

            // Send alert email
            $subject = 'New Service Created - ' . $service->client->business_name;
            
            $emailData = [
                'service' => $service,
                'company_name' => config('app.name')
            ];

            foreach ($emails as $email) {
                Mail::send('email.alerts.service-creation', $emailData, function ($message) use ($email, $subject, $companyEmail) {
                    $message->to($email)
                            ->subject($subject)
                            ->from($companyEmail, config('app.name'));
                });
            }

            Log::info("Service creation alert sent for service ID {$service->id} to " . count($emails) . " recipients");

        } catch (\Exception $e) {
            Log::error("Failed to send service creation alert: " . $e->getMessage());
        }
    }

    /**
     * Parse comma-separated email list and validate emails
     */
    private function parseEmailList($emailString)
    {
        if (!$emailString) {
            return [];
        }

        $emails = array_map('trim', explode(',', $emailString));
        $validEmails = [];

        foreach ($emails as $email) {
            if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $validEmails[] = $email;
            }
        }

        return $validEmails;
    }
}
