<?php

namespace App\Services;

use App\Models\ServiceLog;
use App\Models\ClientService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

class ServiceLogService
{
    /**
     * Log service creation
     */
    public static function logCreated(ClientService $service, string $reason = null)
    {
        self::createLog($service, 'created', null, null, null, $reason, $service->toArray());
    }

    /**
     * Log service update
     */
    public static function logUpdated(ClientService $service, array $originalData, array $newData, string $reason = null)
    {
        // Log the overall update
        self::createLog($service, 'updated', null, null, null, $reason, [
            'original' => $originalData,
            'updated' => $newData
        ]);

        // Log individual field changes
        foreach ($newData as $field => $newValue) {
            $oldValue = $originalData[$field] ?? null;
            
            // Skip if values are the same
            if ($oldValue == $newValue) {
                continue;
            }

            // Skip timestamps and system fields
            if (in_array($field, ['updated_at', 'created_at', 'id'])) {
                continue;
            }

            self::createLog($service, 'field_updated', $field, $oldValue, $newValue, $reason);
        }
    }

    /**
     * Log service deletion
     */
    public static function logDeleted(ClientService $service, string $reason = null)
    {
        self::createLog($service, 'deleted', null, null, null, $reason, $service->toArray());
    }

    /**
     * Log service status change
     */
    public static function logStatusChanged(ClientService $service, $oldStatus, $newStatus, string $reason = null)
    {
        self::createLog($service, 'status_changed', 'status', $oldStatus, $newStatus, $reason);
    }

    /**
     * Create a log entry
     */
    private static function createLog(
        ClientService $service, 
        string $action, 
        string $fieldName = null, 
        $oldValue = null, 
        $newValue = null, 
        string $reason = null, 
        array $fullData = null
    ) {
        $request = request();
        
        ServiceLog::create([
            'service_id' => $service->id,
            'action' => $action,
            'field_name' => $fieldName,
            'old_value' => $oldValue ? (is_array($oldValue) ? json_encode($oldValue) : (string)$oldValue) : null,
            'new_value' => $newValue ? (is_array($newValue) ? json_encode($newValue) : (string)$newValue) : null,
            'changed_by' => Auth::user() ? Auth::user()->name : 'System',
            'ip_address' => $request ? $request->ip() : null,
            'user_agent' => $request ? $request->userAgent() : null,
            'reason' => $reason,
            'full_data' => $fullData
        ]);
    }

    /**
     * Get service logs with pagination
     */
    public static function getServiceLogs(int $serviceId, int $perPage = 15)
    {
        return ServiceLog::where('service_id', $serviceId)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get recent service logs
     */
    public static function getRecentLogs(int $limit = 10)
    {
        return ServiceLog::with('service.client')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get logs by action type
     */
    public static function getLogsByAction(string $action, int $limit = 50)
    {
        return ServiceLog::where('action', $action)
            ->with('service.client')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get logs by user
     */
    public static function getLogsByUser(string $userName, int $limit = 50)
    {
        return ServiceLog::where('changed_by', $userName)
            ->with('service.client')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get field change history for a specific field
     */
    public static function getFieldHistory(int $serviceId, string $fieldName)
    {
        return ServiceLog::where('service_id', $serviceId)
            ->where('field_name', $fieldName)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Format log entry for display
     */
    public static function formatLogEntry(ServiceLog $log)
    {
        $message = '';
        
        switch ($log->action) {
            case 'created':
                $message = "Service created";
                break;
            case 'updated':
                $message = "Service updated";
                break;
            case 'deleted':
                $message = "Service deleted";
                break;
            case 'field_updated':
                $message = "Field '{$log->field_name}' changed from '{$log->old_value}' to '{$log->new_value}'";
                break;
            case 'status_changed':
                $message = "Status changed from '{$log->old_value}' to '{$log->new_value}'";
                break;
            default:
                $message = ucfirst($log->action);
        }

        if ($log->reason) {
            $message .= " - Reason: {$log->reason}";
        }

        return $message;
    }
}
