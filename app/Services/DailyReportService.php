<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Client;
use App\Models\Employee;
use App\Models\ClientService;
use App\Models\Expense;
use App\Models\ExpenseCategory;

class DailyReportService
{
    /**
     * Generate and send daily report
     */
    public function generateAndSendDailyReport($date = null, $forceGenerate = false)
    {
        try {
            // Check if daily report alerts are enabled (unless forced)
            $alertEnabled = DB::table('company_settings')
                            ->where('setting_key', 'daily_report_alert_enabled')
                            ->value('setting_value') == '1';

            if (!$alertEnabled && !$forceGenerate) {
                Log::info('Daily report alerts are disabled');
                return [
                    'success' => false,
                    'message' => 'Daily report alerts are disabled. Enable them in Settings → Notification Alerts.'
                ];
            }

            // Get alert emails
            $alertEmails = DB::table('company_settings')
                          ->where('setting_key', 'daily_report_alert_emails')
                          ->value('setting_value');

            if (!$alertEmails && !$forceGenerate) {
                Log::info('No email addresses configured for daily reports');
                return [
                    'success' => false,
                    'message' => 'No email addresses configured for daily reports. Add them in Settings → Notification Alerts.'
                ];
            }

            // For forced generation (testing), use a default email if none configured
            if ($forceGenerate && !$alertEmails) {
                $companyEmail = DB::table('company_settings')
                              ->where('setting_key', 'email')
                              ->value('setting_value') ?: '<EMAIL>';
                $alertEmails = $companyEmail;
            }

            $emails = $this->parseEmailList($alertEmails);
            if (empty($emails)) {
                Log::info('No valid email addresses found for daily reports');
                return [
                    'success' => false,
                    'message' => 'No valid email addresses found for daily reports.'
                ];
            }

            // Use provided date or yesterday's date
            $reportDate = $date ? Carbon::parse($date) : Carbon::yesterday();

            // Generate report data
            $reportData = $this->generateReportData($reportDate);

            // Get company email for sender
            $companyEmail = DB::table('company_settings')
                            ->where('setting_key', 'email')
                            ->value('setting_value') ?: config('mail.from.address');

            // Send report email
            $subject = 'Daily Report - ' . $reportDate->format('d M Y') . ' - ' . config('app.name');

            $emailData = [
                'report_data' => $reportData,
                'report_date' => $reportDate,
                'company_name' => config('app.name')
            ];

            foreach ($emails as $email) {
                Mail::send('email.reports.daily-report', $emailData, function ($message) use ($email, $subject, $companyEmail) {
                    $message->to($email)
                            ->subject($subject)
                            ->from($companyEmail, config('app.name'));
                });
            }

            Log::info("Daily report sent for {$reportDate->format('Y-m-d')} to " . count($emails) . " recipients");

            return [
                'success' => true,
                'message' => "Daily report sent to " . count($emails) . " recipients",
                'report_data' => $reportData
            ];

        } catch (\Exception $e) {
            Log::error("Failed to generate/send daily report: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to generate daily report: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate comprehensive report data for the given date
     */
    public function generateReportData($date)
    {
        $startDate = $date->copy()->startOfDay();
        $endDate = $date->copy()->endOfDay();

        // Invoice Statistics
        $invoiceStats = $this->getInvoiceStats($startDate, $endDate);

        // Payment Statistics
        $paymentStats = $this->getPaymentStats($startDate, $endDate);

        // Expense Statistics
        $expenseStats = $this->getExpenseStats($startDate, $endDate);

        // Client Statistics
        $clientStats = $this->getClientStats($startDate, $endDate);

        // Employee Statistics
        $employeeStats = $this->getEmployeeStats($startDate, $endDate);

        // Service Statistics
        $serviceStats = $this->getServiceStats($startDate, $endDate);

        // Outstanding Amounts
        $outstandingStats = $this->getOutstandingStats();

        // Financial Summary
        $financialStats = $this->getFinancialStats($startDate, $endDate);

        // Top Clients by Revenue
        $topClients = $this->getTopClientsByRevenue($startDate, $endDate);

        // Recent Activities
        $recentActivities = $this->getRecentActivities($startDate, $endDate);

        return [
            'invoices' => $invoiceStats,
            'payments' => $paymentStats,
            'expenses' => $expenseStats,
            'clients' => $clientStats,
            'employees' => $employeeStats,
            'services' => $serviceStats,
            'outstanding' => $outstandingStats,
            'financial' => $financialStats,
            'top_clients' => $topClients,
            'recent_activities' => $recentActivities,
            'summary' => $this->generateSummary($invoiceStats, $paymentStats, $expenseStats, $clientStats, $employeeStats, $serviceStats)
        ];
    }

    /**
     * Get invoice statistics for the date range
     */
    private function getInvoiceStats($startDate, $endDate)
    {
        // Use whereDate for better date filtering
        $invoices = Invoice::with('client')
                          ->whereDate('created_at', '>=', $startDate->format('Y-m-d'))
                          ->whereDate('created_at', '<=', $endDate->format('Y-m-d'))
                          ->get();

        return [
            'total_count' => $invoices->count(),
            'total_amount' => $invoices->sum('total_amount_due'),
            'paid_count' => $invoices->where('invoice_status', 'paid')->count(),
            'paid_amount' => $invoices->where('invoice_status', 'paid')->sum('total_amount_due'),
            'pending_count' => $invoices->where('invoice_status', 'pending')->count(),
            'pending_amount' => $invoices->where('invoice_status', 'pending')->sum('total_amount_due'),
            'overdue_count' => $invoices->where('invoice_status', 'overdue')->count(),
            'overdue_amount' => $invoices->where('invoice_status', 'overdue')->sum('total_amount_due'),
            'partial_count' => $invoices->where('invoice_status', 'partial')->count(),
            'partial_amount' => $invoices->where('invoice_status', 'partial')->sum('total_amount_due'),
            'cancelled_count' => $invoices->where('invoice_status', 'cancelled')->count(),
            'cancelled_amount' => $invoices->where('invoice_status', 'cancelled')->sum('total_amount_due'),
            'average_amount' => $invoices->count() > 0 ? $invoices->avg('total_amount_due') : 0,
            'highest_amount' => $invoices->count() > 0 ? $invoices->max('total_amount_due') : 0,
            'lowest_amount' => $invoices->count() > 0 ? $invoices->min('total_amount_due') : 0,
            'invoices' => $invoices->sortByDesc('created_at')->take(10) // Latest 10 invoices for details
        ];
    }

    /**
     * Get payment statistics for the date range
     */
    private function getPaymentStats($startDate, $endDate)
    {
        $payments = Payment::with('client')
                          ->whereDate('created_at', '>=', $startDate->format('Y-m-d'))
                          ->whereDate('created_at', '<=', $endDate->format('Y-m-d'))
                          ->get();

        return [
            'total_count' => $payments->count(),
            'total_amount' => $payments->sum('amount'),
            'cash_count' => $payments->where('payment_mode', 'cash')->count(),
            'cash_amount' => $payments->where('payment_mode', 'cash')->sum('amount'),
            'online_count' => $payments->where('payment_mode', 'online')->count(),
            'online_amount' => $payments->where('payment_mode', 'online')->sum('amount'),
            'cheque_count' => $payments->where('payment_mode', 'cheque')->count(),
            'cheque_amount' => $payments->where('payment_mode', 'cheque')->sum('amount'),
            'bank_transfer_count' => $payments->where('payment_mode', 'bank_transfer')->count(),
            'bank_transfer_amount' => $payments->where('payment_mode', 'bank_transfer')->sum('amount'),
            'card_count' => $payments->where('payment_mode', 'card')->count(),
            'card_amount' => $payments->where('payment_mode', 'card')->sum('amount'),
            'average_amount' => $payments->count() > 0 ? $payments->avg('amount') : 0,
            'highest_amount' => $payments->count() > 0 ? $payments->max('amount') : 0,
            'lowest_amount' => $payments->count() > 0 ? $payments->min('amount') : 0,
            'payments' => $payments->sortByDesc('created_at')->take(10) // Latest 10 payments for details
        ];
    }

    /**
     * Get expense statistics for the date range
     */
    private function getExpenseStats($startDate, $endDate)
    {
        $expenses = Expense::with(['category', 'createdBy'])
                          ->whereDate('expense_date', '>=', $startDate->format('Y-m-d'))
                          ->whereDate('expense_date', '<=', $endDate->format('Y-m-d'))
                          ->get();

        // Group by category
        $categoryStats = $expenses->groupBy('category.name')->map(function ($categoryExpenses) {
            return [
                'count' => $categoryExpenses->count(),
                'amount' => $categoryExpenses->sum('amount')
            ];
        });

        // Group by status
        $statusStats = $expenses->groupBy('status')->map(function ($statusExpenses) {
            return [
                'count' => $statusExpenses->count(),
                'amount' => $statusExpenses->sum('amount')
            ];
        });

        // Group by payment method
        $paymentMethodStats = $expenses->groupBy('payment_method')->map(function ($methodExpenses) {
            return [
                'count' => $methodExpenses->count(),
                'amount' => $methodExpenses->sum('amount')
            ];
        });

        return [
            'total_count' => $expenses->count(),
            'total_amount' => $expenses->sum('amount'),
            'pending_count' => $expenses->where('status', 'pending')->count(),
            'pending_amount' => $expenses->where('status', 'pending')->sum('amount'),
            'approved_count' => $expenses->where('status', 'approved')->count(),
            'approved_amount' => $expenses->where('status', 'approved')->sum('amount'),
            'paid_count' => $expenses->where('status', 'paid')->count(),
            'paid_amount' => $expenses->where('status', 'paid')->sum('amount'),
            'rejected_count' => $expenses->where('status', 'rejected')->count(),
            'rejected_amount' => $expenses->where('status', 'rejected')->sum('amount'),
            'reimbursable_count' => $expenses->where('is_reimbursable', true)->count(),
            'reimbursable_amount' => $expenses->where('is_reimbursable', true)->sum('amount'),
            'average_amount' => $expenses->count() > 0 ? $expenses->avg('amount') : 0,
            'highest_amount' => $expenses->count() > 0 ? $expenses->max('amount') : 0,
            'lowest_amount' => $expenses->count() > 0 ? $expenses->min('amount') : 0,
            'category_breakdown' => $categoryStats,
            'status_breakdown' => $statusStats,
            'payment_method_breakdown' => $paymentMethodStats,
            'expenses' => $expenses->sortByDesc('expense_date')->take(10) // Latest 10 expenses for details
        ];
    }

    /**
     * Get client statistics for the date range
     */
    private function getClientStats($startDate, $endDate)
    {
        $newClients = Client::whereBetween('created_at', [$startDate, $endDate])->get();
        $totalClients = Client::count();
        $activeClients = Client::where('status', 1)->count();

        return [
            'new_clients_count' => $newClients->count(),
            'total_clients' => $totalClients,
            'active_clients' => $activeClients,
            'inactive_clients' => $totalClients - $activeClients,
            'new_clients' => $newClients->take(10) // Latest 10 new clients
        ];
    }

    /**
     * Get employee statistics for the date range
     */
    private function getEmployeeStats($startDate, $endDate)
    {
        $newEmployees = Employee::whereBetween('created_at', [$startDate, $endDate])->get();
        $totalEmployees = Employee::count();
        $activeEmployees = Employee::where('status', 1)->count();

        return [
            'new_employees_count' => $newEmployees->count(),
            'total_employees' => $totalEmployees,
            'active_employees' => $activeEmployees,
            'inactive_employees' => $totalEmployees - $activeEmployees,
            'new_employees' => $newEmployees->take(10) // Latest 10 new employees
        ];
    }

    /**
     * Get service statistics for the date range
     */
    private function getServiceStats($startDate, $endDate)
    {
        $newServices = ClientService::whereBetween('created_at', [$startDate, $endDate])->get();
        $totalServices = ClientService::count();
        $activeServices = ClientService::where('status', 1)->count();

        return [
            'new_services_count' => $newServices->count(),
            'total_services' => $totalServices,
            'active_services' => $activeServices,
            'inactive_services' => $totalServices - $activeServices,
            'new_services' => $newServices->take(10) // Latest 10 new services
        ];
    }

    /**
     * Get financial statistics for the date range
     */
    private function getFinancialStats($startDate, $endDate)
    {
        // Get invoice and payment data for the date range
        $invoices = Invoice::whereDate('created_at', '>=', $startDate->format('Y-m-d'))
                          ->whereDate('created_at', '<=', $endDate->format('Y-m-d'))
                          ->get();

        $payments = Payment::whereDate('created_at', '>=', $startDate->format('Y-m-d'))
                          ->whereDate('created_at', '<=', $endDate->format('Y-m-d'))
                          ->get();

        $expenses = Expense::whereDate('expense_date', '>=', $startDate->format('Y-m-d'))
                          ->whereDate('expense_date', '<=', $endDate->format('Y-m-d'))
                          ->get();

        $totalRevenue = $invoices->sum('total_amount_due');
        $totalCollections = $payments->sum('amount');
        $totalExpenses = $expenses->sum('amount');
        $netProfit = $totalCollections - $totalExpenses;
        $profitMargin = $totalCollections > 0 ? ($netProfit / $totalCollections) * 100 : 0;

        return [
            'total_revenue' => $totalRevenue,
            'total_collections' => $totalCollections,
            'total_expenses' => $totalExpenses,
            'net_profit' => $netProfit,
            'profit_margin' => $profitMargin,
            'collection_efficiency' => $totalRevenue > 0 ? ($totalCollections / $totalRevenue) * 100 : 0,
            'expense_ratio' => $totalCollections > 0 ? ($totalExpenses / $totalCollections) * 100 : 0,
            'revenue_vs_expenses' => $totalExpenses > 0 ? ($totalRevenue / $totalExpenses) : 0,
            'cash_flow' => $totalCollections - $totalExpenses,
            'break_even_point' => $totalExpenses > 0 ? $totalExpenses : 0
        ];
    }

    /**
     * Get outstanding amounts statistics
     */
    private function getOutstandingStats()
    {
        $pendingInvoices = Invoice::where('invoice_status', 'pending')->get();
        $overdueInvoices = Invoice::where('invoice_status', 'overdue')->get();

        return [
            'pending_invoices_count' => $pendingInvoices->count(),
            'pending_amount' => $pendingInvoices->sum('unpaid_amount'),
            'overdue_invoices_count' => $overdueInvoices->count(),
            'overdue_amount' => $overdueInvoices->sum('unpaid_amount'),
            'total_outstanding' => $pendingInvoices->sum('unpaid_amount') + $overdueInvoices->sum('unpaid_amount')
        ];
    }

    /**
     * Get top clients by revenue for the date range
     */
    private function getTopClientsByRevenue($startDate, $endDate)
    {
        return DB::table('invoices')
            ->join('clients', 'invoices.client_id', '=', 'clients.id')
            ->whereBetween('invoices.created_at', [$startDate, $endDate])
            ->select('clients.business_name', 'clients.id', DB::raw('SUM(invoices.total_amount_due) as total_revenue'), DB::raw('COUNT(invoices.id) as invoice_count'))
            ->groupBy('clients.id', 'clients.business_name')
            ->orderBy('total_revenue', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Get recent activities for the date range
     */
    private function getRecentActivities($startDate, $endDate)
    {
        $activities = [];

        // Recent invoices
        $recentInvoices = Invoice::with('client')
            ->whereDate('created_at', '>=', $startDate->format('Y-m-d'))
            ->whereDate('created_at', '<=', $endDate->format('Y-m-d'))
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        foreach ($recentInvoices as $invoice) {
            $activities[] = [
                'type' => 'invoice',
                'icon' => '📄',
                'description' => "Invoice {$invoice->invoice_code} created for {$invoice->client->business_name}",
                'amount' => $invoice->total_amount_due,
                'time' => $invoice->created_at
            ];
        }

        // Recent payments
        $recentPayments = Payment::with('client')
            ->whereDate('created_at', '>=', $startDate->format('Y-m-d'))
            ->whereDate('created_at', '<=', $endDate->format('Y-m-d'))
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        foreach ($recentPayments as $payment) {
            $activities[] = [
                'type' => 'payment',
                'icon' => '💰',
                'description' => "Payment of ₹" . number_format($payment->amount, 2) . " received from {$payment->client->business_name}",
                'amount' => $payment->amount,
                'time' => $payment->created_at
            ];
        }

        // Recent expenses
        $recentExpenses = Expense::with(['category', 'createdBy'])
            ->whereDate('expense_date', '>=', $startDate->format('Y-m-d'))
            ->whereDate('expense_date', '<=', $endDate->format('Y-m-d'))
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        foreach ($recentExpenses as $expense) {
            $activities[] = [
                'type' => 'expense',
                'icon' => '💸',
                'description' => "Expense: {$expense->title} - " . ($expense->category->name ?? 'No Category') . " (₹" . number_format($expense->amount, 2) . ")",
                'amount' => -$expense->amount, // Negative for expenses
                'time' => $expense->created_at
            ];
        }

        // Recent clients
        $recentClients = Client::whereDate('created_at', '>=', $startDate->format('Y-m-d'))
            ->whereDate('created_at', '<=', $endDate->format('Y-m-d'))
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get();

        foreach ($recentClients as $client) {
            $activities[] = [
                'type' => 'client',
                'icon' => '👥',
                'description' => "New client registered: {$client->business_name}",
                'amount' => 0,
                'time' => $client->created_at
            ];
        }

        // Recent services
        $recentServices = ClientService::with('client')
            ->whereDate('created_at', '>=', $startDate->format('Y-m-d'))
            ->whereDate('created_at', '<=', $endDate->format('Y-m-d'))
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get();

        foreach ($recentServices as $service) {
            $activities[] = [
                'type' => 'service',
                'icon' => '🔧',
                'description' => "New service activated for {$service->client->business_name}",
                'amount' => 0,
                'time' => $service->created_at
            ];
        }

        // Sort by time
        usort($activities, function($a, $b) {
            return $b['time'] <=> $a['time'];
        });

        return array_slice($activities, 0, 15);
    }

    /**
     * Generate summary statistics
     */
    private function generateSummary($invoiceStats, $paymentStats, $expenseStats, $clientStats, $employeeStats, $serviceStats)
    {
        $totalRevenue = $invoiceStats['total_amount'];
        $totalCollections = $paymentStats['total_amount'];
        $totalExpenses = $expenseStats['total_amount'];
        $netProfit = $totalCollections - $totalExpenses;

        return [
            'total_revenue' => $totalRevenue,
            'total_collections' => $totalCollections,
            'total_expenses' => $totalExpenses,
            'net_profit' => $netProfit,
            'net_outstanding' => $invoiceStats['pending_amount'] + $invoiceStats['overdue_amount'],
            'new_entities' => $clientStats['new_clients_count'] + $employeeStats['new_employees_count'] + $serviceStats['new_services_count'],
            'collection_efficiency' => $totalRevenue > 0 ? ($totalCollections / $totalRevenue) * 100 : 0,
            'profit_margin' => $totalCollections > 0 ? ($netProfit / $totalCollections) * 100 : 0,
            'expense_ratio' => $totalCollections > 0 ? ($totalExpenses / $totalCollections) * 100 : 0,
            'business_growth' => $clientStats['new_clients_count'] + $serviceStats['new_services_count'],
            'team_growth' => $employeeStats['new_employees_count']
        ];
    }

    /**
     * Parse comma-separated email list and validate emails
     */
    private function parseEmailList($emailString)
    {
        if (!$emailString) {
            return [];
        }

        $emails = array_map('trim', explode(',', $emailString));
        $validEmails = [];

        foreach ($emails as $email) {
            if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $validEmails[] = $email;
            }
        }

        return $validEmails;
    }
}
