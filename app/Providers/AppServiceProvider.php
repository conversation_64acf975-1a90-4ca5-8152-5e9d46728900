<?php

namespace App\Providers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $company_details=DB::table('company_settings')->pluck('setting_value', 'setting_key')
        ->toArray();

        View::share('company_details',$company_details);
        Config::set('company.details', $company_details);

        // Update Laravel session configuration from database settings
        if (isset($company_details['session_lifetime_minutes'])) {
            Config::set('session.lifetime', (int)$company_details['session_lifetime_minutes']);
        }

        if (isset($company_details['session_expire_on_close'])) {
            Config::set('session.expire_on_close', $company_details['session_expire_on_close'] == '1');
        }
    }
}
