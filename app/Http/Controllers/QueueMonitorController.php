<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Invoice;
use App\Models\Client;

class QueueMonitorController extends Controller
{
    /**
     * Display the queue monitoring dashboard
     */
    public function index()
    {
        // Get queue statistics
        $stats = $this->getQueueStats();

        return view('reports.queue-monitor', compact('stats'));
    }

    /**
     * Get queue statistics
     */
    public function getQueueStats()
    {
        try {
            // Pending jobs
            $pendingJobs = DB::table('jobs')->count();

            // Failed jobs
            $failedJobs = DB::table('failed_jobs')->count();

            // Recent failed jobs (last 24 hours)
            $recentFailedJobs = DB::table('failed_jobs')
                              ->where('failed_at', '>=', Carbon::now()->subDay())
                              ->count();

            // Jobs by queue
            $jobsByQueue = DB::table('jobs')
                           ->select('queue', DB::raw('count(*) as count'))
                           ->groupBy('queue')
                           ->get();

            // Recent job activity (last hour)
            $recentActivity = $this->getRecentJobActivity();

            // Success jobs (last 24 hours) - estimate based on email logs
            $successJobs = $this->getSuccessJobsCount();

            // Queue configuration
            $queueConfig = [
                'connection' => config('queue.default'),
                'email_queue' => config('email-queue.queue', 'emails'),
                'retry_attempts' => config('email-queue.retry.attempts', 3),
                'delay_range' => config('email-queue.delay.min_seconds', 5) . '-' . config('email-queue.delay.max_seconds', 30) . 's'
            ];

            return [
                'pending_jobs' => $pendingJobs,
                'failed_jobs' => $failedJobs,
                'recent_failed_jobs' => $recentFailedJobs,
                'success_jobs' => $successJobs,
                'jobs_by_queue' => $jobsByQueue,
                'recent_activity' => $recentActivity,
                'queue_config' => $queueConfig,
                'last_updated' => Carbon::now()->format('Y-m-d H:i:s')
            ];
        } catch (\Exception $e) {
            Log::error('Error getting queue stats: ' . $e->getMessage());
            return [
                'pending_jobs' => 0,
                'failed_jobs' => 0,
                'recent_failed_jobs' => 0,
                'success_jobs' => 0,
                'jobs_by_queue' => collect(),
                'recent_activity' => [],
                'queue_config' => [],
                'last_updated' => Carbon::now()->format('Y-m-d H:i:s'),
                'error' => 'Unable to fetch queue statistics'
            ];
        }
    }

    /**
     * Get success jobs count (last 24 hours)
     */
    private function getSuccessJobsCount()
    {
        try {
            // Count recent invoices with valid emails as proxy for successful email sends
            $successCount = DB::table('invoices')
                ->join('clients', 'invoices.client_id', '=', 'clients.id')
                ->where('invoices.created_at', '>=', Carbon::now()->subDay())
                ->whereNotNull('clients.email')
                ->where('clients.email', '!=', '')
                ->where('invoices.invoice_status', '!=', 'Cancelled')
                ->count();

            return $successCount;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get recent job activity from logs and database
     */
    private function getRecentJobActivity()
    {
        try {
            $activity = [];

            // Get recent invoices with email activity (last 24 hours)
            $recentInvoices = DB::table('invoices')
                ->join('clients', 'invoices.client_id', '=', 'clients.id')
                ->where('invoices.created_at', '>=', Carbon::now()->subDay())
                ->select([
                    'invoices.invoice_code',
                    'clients.name as client_name',
                    'clients.email',
                    'invoices.created_at',
                    'invoices.created_type'
                ])
                ->orderBy('invoices.created_at', 'desc')
                ->limit(10)
                ->get();

            foreach ($recentInvoices as $invoice) {
                $type = $invoice->created_type == 1 ? 'Manual' : 'Auto';
                $email = $invoice->email ?: 'No email';
                $activity[] = [
                    'timestamp' => Carbon::parse($invoice->created_at)->format('Y-m-d H:i:s'),
                    'message' => "Email queued - {$type}: {$invoice->invoice_code} for {$invoice->client_name} ({$email})"
                ];
            }

            // Also try to get from log file
            $logActivity = $this->getLogActivity();
            $activity = array_merge($activity, $logActivity);

            // Sort by timestamp and return last 10
            usort($activity, function($a, $b) {
                return strtotime($b['timestamp']) - strtotime($a['timestamp']);
            });

            return array_slice($activity, 0, 10);
        } catch (\Exception $e) {
            return $this->getLogActivity(); // Fallback to log activity
        }
    }

    /**
     * Get activity from log file
     */
    private function getLogActivity()
    {
        try {
            $logFile = storage_path('logs/laravel.log');

            if (!file_exists($logFile)) {
                return [];
            }

            $lines = file($logFile);
            $recentLines = array_slice($lines, -50); // Get last 50 lines

            $activity = [];
            foreach ($recentLines as $line) {
                if ((strpos($line, 'Email') !== false && strpos($line, 'queued') !== false) ||
                    (strpos($line, 'invoice') !== false && strpos($line, 'sent') !== false)) {
                    $activity[] = [
                        'timestamp' => $this->extractTimestamp($line),
                        'message' => $this->cleanLogMessage($line)
                    ];
                }
            }

            return array_slice(array_reverse($activity), 0, 5); // Return last 5 from logs
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Clean log message for display
     */
    private function cleanLogMessage($line)
    {
        // Remove log level and timestamp prefix
        $message = preg_replace('/^\[.*?\]\s*\w+\.\w+:\s*/', '', $line);
        return trim($message);
    }

    /**
     * Extract timestamp from log line
     */
    private function extractTimestamp($line)
    {
        preg_match('/\[(.*?)\]/', $line, $matches);
        return $matches[1] ?? 'Unknown';
    }

    /**
     * Get failed jobs data for DataTable
     */
    public function getFailedJobs(Request $request)
    {
        if ($request->ajax()) {
            $failedJobs = DB::table('failed_jobs')
                          ->select(['id', 'uuid', 'connection', 'queue', 'payload', 'exception', 'failed_at'])
                          ->orderBy('failed_at', 'desc');

            return DataTables::of($failedJobs)
                ->addColumn('job_class', function ($job) {
                    $payload = json_decode($job->payload, true);
                    return $payload['displayName'] ?? 'Unknown';
                })
                ->addColumn('error_message', function ($job) {
                    $lines = explode("\n", $job->exception);
                    return $lines[0] ?? 'Unknown error';
                })
                ->addColumn('failed_time', function ($job) {
                    return Carbon::parse($job->failed_at)->diffForHumans();
                })
                ->addColumn('action', function ($job) {
                    return '
                        <button class="btn btn-sm btn-primary retry-job" data-id="' . $job->id . '">
                            <i class="fas fa-redo"></i> Retry
                        </button>
                        <button class="btn btn-sm btn-danger delete-job" data-id="' . $job->id . '">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    ';
                })
                ->rawColumns(['action'])
                ->make(true);
        }
    }

    /**
     * Retry a failed job
     */
    public function retryJob(Request $request)
    {
        try {
            $jobId = $request->input('job_id');

            // Retry the specific job
            Artisan::call('queue:retry', ['id' => $jobId]);

            return response()->json([
                'success' => true,
                'message' => 'Job retried successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retry job: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Delete a failed job
     */
    public function deleteJob(Request $request)
    {
        try {
            $jobId = $request->input('job_id');

            DB::table('failed_jobs')->where('id', $jobId)->delete();

            return response()->json([
                'success' => true,
                'message' => 'Job deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete job: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Clear all failed jobs
     */
    public function clearFailedJobs()
    {
        try {
            Artisan::call('queue:flush');

            return response()->json([
                'success' => true,
                'message' => 'All failed jobs cleared successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear jobs: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get real-time queue stats via AJAX
     */
    public function getStats()
    {
        return response()->json($this->getQueueStats());
    }

    /**
     * Process queue manually
     */
    public function processQueue()
    {
        try {
            // Process emails queue with timeout
            Artisan::call('email:process-queue', ['--timeout' => 60]);

            return response()->json([
                'success' => true,
                'message' => 'Queue processing started successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process queue: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get success jobs for the last 24 hours
     */
    public function getSuccessJobs()
    {
        try {
            // Get recent invoices that likely had emails sent
            $successJobs = DB::table('invoices')
                ->join('clients', 'invoices.client_id', '=', 'clients.id')
                ->where('invoices.created_at', '>=', Carbon::now()->subHours(48)) // Extended to 48 hours for more data
                ->whereNotNull('clients.email')
                ->where('clients.email', '!=', '')
                ->where('invoices.invoice_status', '!=', 'Cancelled')
                ->select([
                    'invoices.invoice_code',
                    'clients.name as client_name',
                    'clients.email',
                    'invoices.created_at as completed_at',
                    'invoices.created_type'
                ])
                ->orderBy('invoices.created_at', 'desc')
                ->limit(15)
                ->get();

            $formattedJobs = $successJobs->map(function ($job) {
                $type = $job->created_type == 1 ? 'Manual' : 'Auto';
                return [
                    'invoice_code' => $job->invoice_code,
                    'client_name' => $job->client_name,
                    'email' => $job->email,
                    'completed_at' => Carbon::parse($job->completed_at)->format('M d, Y H:i'),
                    'type' => $type,
                    'time_ago' => Carbon::parse($job->completed_at)->diffForHumans()
                ];
            });

            return response()->json([
                'success' => true,
                'jobs' => $formattedJobs,
                'count' => $formattedJobs->count()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to fetch success jobs: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch success jobs: ' . $e->getMessage(),
                'jobs' => [],
                'count' => 0
            ]);
        }
    }
}
