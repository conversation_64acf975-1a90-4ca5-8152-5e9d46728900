<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\InventoryItem;
use App\Models\InventoryCategory;
use App\Models\InventoryTransaction;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class InventoryController extends Controller
{
    public function index()
    {
        // Dynamic summary data
        $totalItems = InventoryItem::active()->count();
        $inStockItems = InventoryItem::active()->inStock()->count();
        $lowStockItems = InventoryItem::active()->lowStock()->count();
        $outOfStockItems = InventoryItem::active()->outOfStock()->count();

        // Get categories for dropdown
        $categories = InventoryCategory::active()->get();

        return view('inventory.Inventory', [
            'totalItems' => $totalItems,
            'inStockItems' => $inStockItems,
            'lowStockItems' => $lowStockItems,
            'outOfStockItems' => $outOfStockItems,
            'categories' => $categories
        ]);
    }

    public function getInventoryData(Request $request)
    {
        $query = InventoryItem::with('category')->active();

        return DataTables::of($query)
            ->addColumn('category_name', function ($item) {
                return $item->category->name ?? 'N/A';
            })
            ->addColumn('stock_status', function ($item) {
                $badgeClass = match($item->status) {
                    'in_stock' => 'bg-success',
                    'low_stock' => 'bg-warning',
                    'out_of_stock' => 'bg-danger',
                    default => 'bg-secondary'
                };
                $statusText = ucfirst(str_replace('_', ' ', $item->status));
                return '<span class="badge ' . $badgeClass . '">' . $statusText . '</span>';
            })
            ->addColumn('actions', function ($item) {
                $user = auth()->user();
                $actions = '';

                if ($user->can('inventory-edit')) {
                    $actions .= '<a href="#" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center edit-item" data-id="' . $item->id . '" data-bs-toggle="modal" data-bs-target="#editItemModal" title="Edit Item">
                        <iconify-icon icon="lucide:edit"></iconify-icon>
                    </a>';

                    $actions .= '<a href="#" class="w-32-px h-32-px bg-warning-focus text-warning-main rounded-circle d-inline-flex align-items-center justify-content-center update-stock" data-id="' . $item->id . '" data-bs-toggle="modal" data-bs-target="#updateStockModal" title="Update Stock">
                        <iconify-icon icon="solar:box-bold"></iconify-icon>
                    </a>';
                }

                $actions .= '<a href="#" class="w-32-px h-32-px bg-info-focus text-info-main rounded-circle d-inline-flex align-items-center justify-content-center view-history" data-id="' . $item->id . '" data-bs-toggle="modal" data-bs-target="#stockHistoryModal" title="View History">
                    <iconify-icon icon="solar:history-bold"></iconify-icon>
                </a>';

                if ($user->can('inventory-delete')) {
                    $actions .= '<a href="#" class="w-32-px h-32-px bg-danger-focus text-danger-main rounded-circle d-inline-flex align-items-center justify-content-center delete-item" data-id="' . $item->id . '" title="Delete Item">
                        <iconify-icon icon="lucide:trash-2"></iconify-icon>
                    </a>';
                }

                return $actions;
            })
            ->editColumn('unit_price', function ($item) {
                return '₹' . number_format($item->unit_price, 2);
            })
            ->editColumn('updated_at', function ($item) {
                return $item->updated_at->format('M d, Y');
            })
            ->addColumn('gst_info', function ($item) {
                if ($item->is_gst_applicable) {
                    return '<div class="text-center">' .
                           '<span class="badge bg-success-100 text-success-600 px-2 py-1 rounded-pill fw-semibold text-xs">' .
                           'GST: ' . ($item->cgst_rate + $item->sgst_rate) . '%' .
                           '</span>' .
                           '<br><small class="text-muted mt-1">C:' . $item->cgst_rate . '% S:' . $item->sgst_rate . '%</small>' .
                           '</div>';
                } else {
                    return '<span class="badge bg-neutral-200 text-neutral-600 px-2 py-1 rounded-pill fw-semibold text-xs">GST Exempt</span>';
                }
            })
            ->rawColumns(['stock_status', 'actions', 'gst_info'])
            ->make(true);
    }

    public function store(Request $request)
    {
        try {
            // Handle GST checkbox before validation
            $isGstApplicable = $request->has('is_gst_applicable') && $request->is_gst_applicable;

            $request->merge([
                'is_gst_applicable' => $isGstApplicable
            ]);

            $request->validate([
                'category_id' => 'required|exists:inventory_categories,id',
                'name' => 'required|string|max:255',
                'sku_code' => 'required|string|max:255|unique:inventory_items,sku_code',
                'description' => 'nullable|string',
                'quantity_available' => 'required|integer|min:0',
                'minimum_required' => 'required|integer|min:0',
                'unit_of_measure' => 'required|string|max:50',
                'unit_price' => 'required|numeric|min:0',
                'cgst_rate' => 'nullable|numeric|min:0|max:50',
                'sgst_rate' => 'nullable|numeric|min:0|max:50',
                'igst_rate' => 'nullable|numeric|min:0|max:50',
                'is_gst_applicable' => 'boolean',
                'image_url' => 'nullable|url'
            ]);

            $data = $request->all();

            // Handle GST rates based on applicability
            $data['cgst_rate'] = $isGstApplicable ? ($request->cgst_rate ?? 9.00) : 0.00;
            $data['sgst_rate'] = $isGstApplicable ? ($request->sgst_rate ?? 9.00) : 0.00;
            $data['igst_rate'] = $isGstApplicable ? ($request->igst_rate ?? 18.00) : 0.00;

            $item = InventoryItem::create($data);
            $item->updateStatus();

            // Create initial transaction
            InventoryTransaction::create([
                'inventory_item_id' => $item->id,
                'transaction_type' => 'in',
                'quantity' => $item->quantity_available,
                'quantity_before' => 0,
                'quantity_after' => $item->quantity_available,
                'reason' => 'Initial stock',
                'created_by' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Inventory item created successfully!'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error creating inventory item: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while saving the item: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show($id)
    {
        $item = InventoryItem::with('category')->findOrFail($id);
        return response()->json($item);
    }

    public function update(Request $request, $id)
    {
        try {
            $item = InventoryItem::findOrFail($id);

            // Handle GST checkbox before validation
            $isGstApplicable = $request->has('is_gst_applicable') && $request->is_gst_applicable;

            $request->merge([
                'is_gst_applicable' => $isGstApplicable
            ]);

            $request->validate([
                'category_id' => 'required|exists:inventory_categories,id',
                'name' => 'required|string|max:255',
                'sku_code' => 'required|string|max:255|unique:inventory_items,sku_code,' . $id,
                'description' => 'nullable|string',
                'minimum_required' => 'required|integer|min:0',
                'unit_of_measure' => 'required|string|max:50',
                'unit_price' => 'required|numeric|min:0',
                'cgst_rate' => 'nullable|numeric|min:0|max:50',
                'sgst_rate' => 'nullable|numeric|min:0|max:50',
                'igst_rate' => 'nullable|numeric|min:0|max:50',
                'is_gst_applicable' => 'boolean',
                'image_url' => 'nullable|url'
            ]);

            $data = $request->all();

            // Handle GST rates based on applicability
            $data['cgst_rate'] = $isGstApplicable ? ($request->cgst_rate ?? 9.00) : 0.00;
            $data['sgst_rate'] = $isGstApplicable ? ($request->sgst_rate ?? 9.00) : 0.00;
            $data['igst_rate'] = $isGstApplicable ? ($request->igst_rate ?? 18.00) : 0.00;

            $item->update($data);
            $item->updateStatus();

            return response()->json([
                'success' => true,
                'message' => 'Inventory item updated successfully!'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error updating inventory item: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the item: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        $item = InventoryItem::findOrFail($id);
        $item->delete();

        return response()->json([
            'success' => true,
            'message' => 'Inventory item deleted successfully!'
        ]);
    }

    public function updateStock(Request $request, $id)
    {
        $item = InventoryItem::findOrFail($id);

        $request->validate([
            'transaction_type' => 'required|in:in,out,adjustment',
            'quantity' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string'
        ]);

        $quantityBefore = $item->quantity_available;

        switch ($request->transaction_type) {
            case 'in':
                $quantityAfter = $quantityBefore + $request->quantity;
                break;
            case 'out':
                $quantityAfter = max(0, $quantityBefore - $request->quantity);
                break;
            case 'adjustment':
                $quantityAfter = $request->quantity;
                break;
        }

        // Update item quantity
        $item->quantity_available = $quantityAfter;
        $item->save();
        $item->updateStatus();

        // Create transaction record
        InventoryTransaction::create([
            'inventory_item_id' => $item->id,
            'transaction_type' => $request->transaction_type,
            'quantity' => $request->quantity,
            'quantity_before' => $quantityBefore,
            'quantity_after' => $quantityAfter,
            'reason' => $request->reason,
            'notes' => $request->notes,
            'created_by' => Auth::id()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Stock updated successfully!'
        ]);
    }

    public function getStockHistory($id)
    {
        $item = InventoryItem::findOrFail($id);
        $transactions = InventoryTransaction::with('createdBy')
            ->where('inventory_item_id', $id)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'item' => $item,
            'transactions' => $transactions
        ]);
    }
}