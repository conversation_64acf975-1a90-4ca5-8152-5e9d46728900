<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Expense;
use App\Models\ExpenseCategory;
use App\Models\ExpenseChangeLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\DB;

class ExpenseController extends Controller
{
    /**
     * Display a listing of expenses
     */
    public function index()
    {
        $categories = ExpenseCategory::active()->get();
        return view('expenses.list', compact('categories'));
    }

    /**
     * Get expenses data for DataTables
     */
    public function getExpenses(Request $request)
    {
        $query = Expense::with(['category', 'createdBy', 'approvedBy'])
            ->select(['id', 'title', 'amount', 'category_id', 'expense_date', 'status', 'created_by', 'vendor_name']);

        // Apply filters
        if ($request->searchkey) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->searchkey . '%')
                  ->orWhere('vendor_name', 'like', '%' . $request->searchkey . '%')
                  ->orWhere('reference_number', 'like', '%' . $request->searchkey . '%');
            });
        }

        if ($request->searchStatus && $request->searchStatus !== 'all') {
            $query->where('status', $request->searchStatus);
        }

        if ($request->searchCategory && $request->searchCategory !== 'all') {
            $query->where('category_id', $request->searchCategory);
        }

        if ($request->daterange) {
            $dateRange = explode(' - ', $request->daterange);
            if (count($dateRange) == 2) {
                $startDate = Carbon::createFromFormat('d/m/Y', trim($dateRange[0]))->format('Y-m-d');
                $endDate = Carbon::createFromFormat('d/m/Y', trim($dateRange[1]))->format('Y-m-d');
                $query->whereBetween('expense_date', [$startDate, $endDate]);
            }
        }

        return DataTables::of($query)
            ->addColumn('category_name', function ($row) {
                return $row->category ? $row->category->name : 'N/A';
            })
            ->addColumn('formatted_amount', function ($row) {
                return '₹' . number_format($row->amount, 2);
            })
            ->addColumn('formatted_date', function ($row) {
                return $row->expense_date->format('d/m/Y');
            })
            ->addColumn('status_badge', function ($row) {
                $badges = [
                    'pending' => 'warning',
                    'approved' => 'success',
                    'rejected' => 'danger',
                    'paid' => 'info'
                ];
                $class = $badges[$row->status] ?? 'secondary';
                return '<span class="badge bg-' . $class . '">' . ucfirst($row->status) . '</span>';
            })
            ->addColumn('created_by_name', function ($row) {
                return $row->createdBy ? $row->createdBy->name : 'N/A';
            })
            ->addColumn('action', function ($row) {
                $actions = '<div class="d-flex align-items-center gap-10 justify-content-end">';

                if (auth()->user()->can('expense-view')) {
                    $actions .= '<a href="' . route('expenses.show', $row->id) . '" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                        <iconify-icon icon="majesticons:eye-line" class="icon text-xl"></iconify-icon>
                    </a>';
                }

                if (auth()->user()->can('expense-edit')) {
                    $actions .= '<a href="' . route('expenses.edit', $row->id) . '" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                        <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                    </a>';
                }

                if (auth()->user()->can('expense-delete')) {
                    $actions .= '<button type="button" onclick="deleteExpense(' . $row->id . ')" class="bg-danger-focus bg-hover-danger-200 text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                        <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                    </button>';
                }

                $actions .= '</div>';
                return $actions;
            })
            ->rawColumns(['status_badge', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new expense
     */
    public function create()
    {
        $categories = ExpenseCategory::active()->get();
        return view('expenses.add', compact('categories'));
    }

    /**
     * Store a newly created expense
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'category_id' => 'required|exists:expense_categories,id',
            'expense_date' => 'required|date_format:d/m/Y',
            'vendor_name' => 'nullable|string|max:255',
            'payment_method' => 'nullable|string|max:255',
            'reference_number' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'notes' => 'nullable|string',
            'is_reimbursable' => 'boolean',
            'receipt_file' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
        ]);

        $data = $request->all();
        $data['expense_date'] = Carbon::createFromFormat('d/m/Y', $request->expense_date)->format('Y-m-d');
        $data['created_by'] = Auth::id();
        $data['is_reimbursable'] = $request->has('is_reimbursable');

        // Handle file upload
        if ($request->hasFile('receipt_file')) {
            $file = $request->file('receipt_file');
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
            $filePath = $file->storeAs('expenses/receipts', $filename, 'public');
            $data['receipt_file'] = $filePath;
        }

        $expense = Expense::create($data);

        // Log the creation
        $this->logExpenseChange($expense, 'created', null, $expense->toArray(), 'Expense created');

        return redirect()->route('expenses.index')->with('success', 'Expense created successfully!');
    }

    /**
     * Display the specified expense
     */
    public function show($id)
    {
        $expense = Expense::with(['category', 'createdBy', 'approvedBy', 'changeLogs'])->findOrFail($id);
        return view('expenses.show', compact('expense'));
    }

    /**
     * Show the form for editing the specified expense
     */
    public function edit($id)
    {
        $expense = Expense::findOrFail($id);
        $categories = ExpenseCategory::active()->get();
        return view('expenses.edit', compact('expense', 'categories'));
    }

    /**
     * Update the specified expense
     */
    public function update(Request $request, $id)
    {
        $expense = Expense::findOrFail($id);
        $oldData = $expense->toArray();

        $request->validate([
            'title' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'category_id' => 'required|exists:expense_categories,id',
            'expense_date' => 'required|date_format:d/m/Y',
            'vendor_name' => 'nullable|string|max:255',
            'payment_method' => 'nullable|string|max:255',
            'reference_number' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'notes' => 'nullable|string',
            'is_reimbursable' => 'boolean',
            'receipt_file' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:5120',
        ]);

        $data = $request->all();
        $data['expense_date'] = Carbon::createFromFormat('d/m/Y', $request->expense_date)->format('Y-m-d');
        $data['is_reimbursable'] = $request->has('is_reimbursable');

        // Handle file upload
        if ($request->hasFile('receipt_file')) {
            // Delete old file if exists
            if ($expense->receipt_file) {
                Storage::disk('public')->delete($expense->receipt_file);
            }

            $file = $request->file('receipt_file');
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
            $filePath = $file->storeAs('expenses/receipts', $filename, 'public');
            $data['receipt_file'] = $filePath;
        }

        $expense->update($data);

        // Log the update
        $this->logExpenseChange($expense, 'updated', $oldData, $expense->fresh()->toArray(), 'Expense updated');

        return redirect()->route('expenses.index')->with('success', 'Expense updated successfully!');
    }

    /**
     * Remove the specified expense
     */
    public function destroy($id)
    {
        $expense = Expense::findOrFail($id);
        $oldData = $expense->toArray();

        // Delete receipt file if exists
        if ($expense->receipt_file) {
            Storage::disk('public')->delete($expense->receipt_file);
        }

        // Log the deletion
        $this->logExpenseChange($expense, 'deleted', $oldData, null, 'Expense deleted');

        $expense->delete();

        return response()->json(['success' => true, 'message' => 'Expense deleted successfully!']);
    }

    /**
     * Approve or reject an expense
     */
    public function updateStatus(Request $request)
    {
        $request->validate([
            'expense_id' => 'required|exists:expenses,id',
            'status' => 'required|in:approved,rejected,paid',
            'notes' => 'nullable|string'
        ]);

        $expense = Expense::findOrFail($request->expense_id);
        $oldStatus = $expense->status;

        $expense->update([
            'status' => $request->status,
            'approved_by' => Auth::id(),
            'approved_at' => now(),
            'notes' => $request->notes
        ]);

        // Log the status change
        $this->logExpenseChange(
            $expense,
            'status_changed',
            ['status' => $oldStatus],
            ['status' => $request->status],
            "Status changed from {$oldStatus} to {$request->status}"
        );

        return response()->json(['success' => true, 'message' => 'Expense status updated successfully!']);
    }

    /**
     * Get expense change logs
     */
    public function changeLogs($id)
    {
        $expense = Expense::findOrFail($id);
        $changeLogs = ExpenseChangeLog::where('expense_id', $id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('expenses.change-logs', compact('expense', 'changeLogs'));
    }

    /**
     * Export expenses to Excel
     */
    public function exportExpenses(Request $request)
    {
        $query = Expense::with(['category', 'createdBy', 'approvedBy']);

        // Apply same filters as the list
        if ($request->searchkey) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->searchkey . '%')
                  ->orWhere('vendor_name', 'like', '%' . $request->searchkey . '%')
                  ->orWhere('reference_number', 'like', '%' . $request->searchkey . '%');
            });
        }

        if ($request->searchStatus && $request->searchStatus !== 'all') {
            $query->where('status', $request->searchStatus);
        }

        if ($request->searchCategory && $request->searchCategory !== 'all') {
            $query->where('category_id', $request->searchCategory);
        }

        if ($request->daterange) {
            $dateRange = explode(' - ', $request->daterange);
            if (count($dateRange) == 2) {
                $startDate = Carbon::createFromFormat('d/m/Y', trim($dateRange[0]))->format('Y-m-d');
                $endDate = Carbon::createFromFormat('d/m/Y', trim($dateRange[1]))->format('Y-m-d');
                $query->whereBetween('expense_date', [$startDate, $endDate]);
            }
        }

        $expenses = $query->orderBy('expense_date', 'desc')->get();

        $filename = 'expenses_export_' . date('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($expenses) {
            $file = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($file, [
                'Title', 'Description', 'Amount', 'Category', 'Expense Date',
                'Vendor', 'Payment Method', 'Reference Number', 'Status',
                'Reimbursable', 'Created By', 'Approved By', 'Created At'
            ]);

            foreach ($expenses as $expense) {
                fputcsv($file, [
                    $expense->title,
                    $expense->description,
                    '₹' . number_format($expense->amount, 2),
                    $expense->category ? $expense->category->name : 'N/A',
                    $expense->expense_date->format('d/m/Y'),
                    $expense->vendor_name,
                    $expense->payment_method,
                    $expense->reference_number,
                    ucfirst($expense->status),
                    $expense->is_reimbursable ? 'Yes' : 'No',
                    $expense->createdBy ? $expense->createdBy->name : 'N/A',
                    $expense->approvedBy ? $expense->approvedBy->name : 'N/A',
                    $expense->created_at->format('d/m/Y H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Log expense changes
     */
    private function logExpenseChange($expense, $action, $oldValues = null, $newValues = null, $description = null)
    {
        $changedFields = [];

        if ($oldValues && $newValues) {
            foreach ($newValues as $key => $value) {
                if (isset($oldValues[$key]) && $oldValues[$key] != $value) {
                    $changedFields[] = $key;
                }
            }
        }

        ExpenseChangeLog::create([
            'expense_id' => $expense->id,
            'action' => $action,
            'changed_by' => Auth::user()->name,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'changed_fields' => $changedFields,
            'description' => $description,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }
}
