<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;

class ApiController extends Controller
{
    //
    public function fetch_data(Request $request){
        if($request->get_type==4){ // all select 2 data genration
            $search_keyword = $request->q;
            if($request->datafrom=="states"){
              $data = DB::table('states')->where('status',1)->where('name', 'LIKE', "%".$search_keyword."%")->orderBy('name', 'ASC')->get();
              $json = [];
              foreach($data as $row){
                 $json[] = ['id'=>$row->id, 'text'=>strtoupper($row->name),'data'=>$row];
              }
            }

            if($request->datafrom=="districts"){
                $data = DB::table('districts')->where('status',1)->where('state_id',$request->state_id)->where('name', 'LIKE', "%".$search_keyword."%")->orderBy('name', 'ASC')->get();
                $json = [];
                foreach($data as $row){
                   $json[] = ['id'=>$row->id, 'text'=>strtoupper($row->name),'data'=>$row];
                }
              }
            return Response::json($json);
        }
    }
}
