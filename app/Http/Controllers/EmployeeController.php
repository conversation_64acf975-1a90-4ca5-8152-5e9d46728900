<?php

namespace App\Http\Controllers;

use Mail;
use Carbon\Carbon;
use App\Models\Role;
use App\Models\User;
use App\Models\State;
use App\Models\Client;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\District;
use App\Models\Employee;
use App\Models\EmployeeChangeLog;
use Illuminate\Http\Request;
use App\Models\EmployeeClient;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;
use Spatie\Permission\Models\Role as SpatieRole;
use App\Services\NotificationAlertService;

class EmployeeController extends Controller
{
    //
    public function index()
    {
        $client_areas = '';//Employee::distinct()->pluck('area');


        return view('employees.list', compact('client_areas'));
    }
    public function add()
    {
        $roles=Role::where('status',1)->get();
        return view('employees.add',compact('roles'));
    }
    //
    // Store method to save client data
    public function store(Request $request)
    {
        $request->validate([
            'emp_name' => 'required|string|min:3',
            'email' => [
                'required',
                'email',
                'unique:employees,email',
                'unique:users,email', // Ensure email is unique in both tables
            ],
            'phone' => 'nullable|digits_between:10,15',
            'dob' => 'required|date_format:d/m/Y',
            'doj' => 'required|date_format:d/m/Y',
            'role_id' => 'required',
            'department' => 'required|string',
            'job_title' => 'required|string',
            'address' => 'required|string',
            'gender' => 'required|string',
            'zipcode' => 'required|digits:6',
            'city' => 'required|string',
            'state_id' => 'required|integer',
            'district_id' => 'required|integer',
            'photo' => 'nullable|image|mimes:jpg,jpeg,png|max:2048',
        ]);

        // Handle file upload
        $filePath = null;
        if ($request->hasFile('photo')) {
            $file = $request->file('photo');
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
            $filePath = $file->storeAs('employees', $filename, 'public');
        }

        // Create User for Employee
        $randomPassword = mt_rand(100000, 999999);
        $user = User::create([
            'name' => $request->emp_name,
            'email' => $request->email,
            'password' => Hash::make($randomPassword), // Default password
            'role_id' => $request->role_id,
        ]);
         // Assign Role to User using Spatie's Role model
        $role = SpatieRole::findOrFail($request->role_id);
        $user->assignRole($role);
        // Store employee data
        $employee = Employee::create([
            'emp_name' => $request->emp_name,
            'email' => $request->email,
            'phone' => $request->phone,
           'dob' => Carbon::createFromFormat('d/m/Y', $request->dob)->format('Y-m-d'),
            'doj' => Carbon::createFromFormat('d/m/Y', $request->doj)->format('Y-m-d'),
            'role_id' => $request->role_id,
            'department' => $request->department,
            'job_title' => $request->job_title,
            'gender' => $request->gender,
            'address' => $request->address,
            'zipcode' => $request->zipcode,
            'city' => $request->city,
            'state_id' => $request->state_id,
            'district_id' => $request->district_id,
            'photo' => $filePath,
            'user_id' => $user->id, // Store newly created user ID
        ]);

        // Log the employee creation
        EmployeeChangeLog::logChange(
            $employee->id,
            'created',
            Auth::user()->name,
            null,
            $employee->toArray(),
            'Employee created via registration form'
        );

        // Send notification alert for employee creation
        $notificationService = new NotificationAlertService();
        $notificationService->sendEmployeeCreationAlert($employee);

        //Mail sending
        $data = ['email'=>$request->email];
        Mail::send('email.emp-creation',['employee'=>$employee,'randomPassword'=>$randomPassword], function($message) use ($data) {
            $message->to($data['email'], env('APP_NAME'))
            ->subject(ucfirst(env('APP_NAME')." - Account Creation"));
            $message->from(env('MAIL_USERNAME'),env('APP_NAME'));
            // $message->cc(env('ADMIN_EMAIL'));
            $message->replyTo('<EMAIL>', env('APP_NAME'));
         });

        return redirect()->route('employees.index')->with('success', 'Employee and User created successfully');
    }

    public function edit($id)
    {
        $employee = Employee::findOrFail($id);
        $roles = Role::all();
        $states=State::where('status',1)->get();
        $districts=District::where('status',1)->get();
        return view('employees.edit', compact('employee', 'roles','states','districts'));
    }
    public function update(Request $request, $id)
    {
        $employee = Employee::findOrFail($id);
        $user = User::where('email', $employee->email)->first(); // Get associated user

        $request->validate([
            'emp_name' => 'required|string|min:3',
              'email',
                'unique:employees,email,' . $id,
                'unique:users,email,' . ($user ? $user->id : 'NULL'),
            'phone' => 'nullable|digits_between:10,15',
            'dob' => 'required|date_format:d/m/Y',
            'doj' => 'required|date_format:d/m/Y',
            'role_id' => 'required',
            'department' => 'required|string',
            'job_title' => 'required|string',
            'address' => 'required|string',
            'gender' => 'required|string',
            'zipcode' => 'required|digits:6',
            'city' => 'required|string',
            'state_id' => 'required|integer',
            'district_id' => 'required|integer',
            'photo' => 'nullable|image|mimes:jpg,jpeg,png|max:2048',
        ]);

        // Store old values before update
        $oldValues = $employee->getOriginal();

        // Handle file upload
        if ($request->hasFile('photo')) {
            $file = $request->file('photo');
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
            $filePath = $file->storeAs('employees', $filename, 'public');

            // Delete old photo if exists
            if ($employee->photo) {
                Storage::disk('public')->delete($employee->photo);
            }

            $employee->photo = $filePath;
        }

        // Update employee data
        $employee->update([
            'emp_name' => $request->emp_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'dob' => Carbon::createFromFormat('d/m/Y', $request->dob)->format('Y-m-d'),
            'doj' => Carbon::createFromFormat('d/m/Y', $request->doj)->format('Y-m-d'),
            'role_id' => $request->role_id,
            'department' => $request->department,
            'job_title' => $request->job_title,
            'gender' => $request->gender,
            'address' => $request->address,
            'zipcode' => $request->zipcode,
            'city' => $request->city,
            'state_id' => $request->state_id,
            'district_id' => $request->district_id,
            'photo' => $employee->photo,
        ]);

        // If user exists, update it, otherwise create a new user
        if ($user) {
            $user->update([
                'name' => $request->emp_name,
                'email' => $request->email,
                'role_id' => $request->role_id,
            ]);
        } else {
            $user = User::create([
                'name' => $request->emp_name,
                'email' => $request->email,
                'password' => Hash::make('123456'), // Default password
                'role_id' => $request->role_id,
            ]);
        }

        // Assign role to user
        $role = SpatieRole::findOrFail($request->role_id);
        $user->syncRoles([$role]); // Syncing roles ensures the correct role is assigned

        // Update `user_id` in employee table if not already set
        if (!$employee->user_id) {
            $employee->update(['user_id' => $user->id]);
        }

        // Log the employee update
        EmployeeChangeLog::logChange(
            $employee->id,
            'updated',
            Auth::user()->name,
            $oldValues,
            $employee->fresh()->toArray(),
            'Employee information updated via edit form'
        );

        return redirect()->route('employees.index')->with('success', 'Employee and User updated successfully with Role assigned.');
    }


    public function show($id)
    {
        $employee = Employee::with('user', 'role', 'state', 'district')->findOrFail($id);
        $clientIds = EmployeeClient::where('employee_id', $id)->pluck('client_id');

        // Fetch invoice data for these clients in the current month
        $dashboard_data = Invoice::whereIn('client_id', $clientIds)
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->selectRaw("
                    COALESCE(COUNT(*), 0) as total_invoices,
                    COALESCE(SUM(total_amount_due), 0) as total_revenue,
                    COALESCE(SUM(paid_amount), 0) as total_paid_amount,
                    COALESCE(COUNT(CASE WHEN paid_amount > 0 THEN 1 END), 0) as paid_invoices_count,
                    COALESCE(SUM(unpaid_amount), 0) as total_pending_amount,
                    COALESCE(COUNT(CASE WHEN unpaid_amount > 0 THEN 1 END), 0) as pending_invoices_count
                ")->first();

        // Get total clients assigned to this employee
        $totalClients = EmployeeClient::where('employee_id', $id)->pluck('client_id');

        // Get active and inactive clients based on the `status` column
        $activeClients = Client::whereIn('id', $totalClients)->where('status', 1)->count();
        $inactiveClients = Client::whereIn('id', $totalClients)->where('status', 0)->count();
        $dashboard_data['clients']=EmployeeClient::where('employee_id',$id)->get();
        $dashboard_data['active_clients'] =$activeClients;
        $dashboard_data['inactive_clients'] =$inactiveClients;
        // Get client IDs assigned to other employees
        $assignedClientIds = EmployeeClient::where('employee_id', '!=', $id)
        ->pluck('client_id');

        // Get all clients excluding those assigned to other employees
        $availableClients = Client::whereNotIn('id', $assignedClientIds)
        ->get();

        // Get clients already assigned to the current employee
        $assignedClients = EmployeeClient::where('employee_id', $id)
        ->pluck('client_id')->toArray();
        $enable['dashboard']=false;
        $enable['clients']=false;
        $enable['invoices']=false;
        $enable['payments']=false;
        $enable['password_change']=false;
        if(Auth::user()->employee->id==$id){
            $enable['password_change']=true;
        }
        if(count($totalClients)){
            $enable['dashboard']=true;
            $enable['clients']=true;
            $enable['invoices']=true;
            $enable['payments']=true;
        }
        // Get districts for filtering
        $districts = District::where('status', 1)
                    ->whereHas('clients') // Only districts that have clients
                    ->orderBy('name')
                    ->get();

        return view('employees.view', compact('employee','dashboard_data','availableClients', 'assignedClients','enable', 'districts'));
    }
    public function getClients(Request $request)
    {
        if ($request->ajax()) {
            $clients = Employee::select(['id', 'emp_name', 'email', 'phone', 'role_id', 'status', 'address', 'created_at']);

            return DataTables::of($clients)
                ->addColumn('status', function ($row) {
                    if ($row->status == 1) {
                        return '<span class="badge badge-sm text-sm fw-normal text-success-600 bg-success-100 px-20 py-9 radius-4 text-white">Active</span>';
                    } else {
                        return '<span class="badge badge-sm text-sm fw-normal text-danger-600 bg-danger-100 px-20 py-9 radius-4 text-white">Inactive</span>';
                    }
                })
                ->addColumn('role', function ($row) {
                    return $row->role_id ? $row->role->name : '-';
                })
                ->addColumn('action', function ($row) {
                    $user = auth()->user(); // Get authenticated user
                    $output = '';
                    // Check 'employee-view' permission
                    if ($user->can('employee-view')) {
                        $output.=  '
                        <a href="' . route('employees.show', $row->id) . '" class="w-32-px h-32-px bg-primary-light text-primary-600 rounded-circle d-inline-flex align-items-center justify-content-center">
                                <iconify-icon icon="iconamoon:eye-light"></iconify-icon>
                            </a>';
                    }
                    // Check 'employee-edit' permission
                    if ($user->can('employee-edit')) {
                        $output.=  '<a href="' . route('employees.edit', $row->id) . '" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center">
                            <iconify-icon icon="lucide:edit"></iconify-icon>
                        </a>

                    ';
                    }
                    return $output;
                })
                ->filter(function ($query) use ($request) {

                    if ($request->has('searchkey') && $request->searchkey != '') {
                        $query->where(function ($q) use ($request) {
                            $q->where('emp_name', 'like', "%{$request->searchkey}%")
                                ->orWhere('email', 'like', "%{$request->searchkey}%")
                                ->orWhere('email', 'like', "%{$request->searchkey}%")
                                ->orWhere('phone', 'like', "%{$request->searchkey}%")
                                ->orWhere('address', 'like', "%{$request->searchkey}%");
                        });
                    }
                })

                ->rawColumns(['status', 'action']) // Ensure HTML rendering
                ->make(true);
        }
    }
    public function assignClients(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'client_ids' => 'required|array',
            'client_ids.*' => 'exists:clients,id',
        ]);

        $employeeId = $request->employee_id;
        $clientIds = $request->client_ids;

        DB::beginTransaction();
        try {
            // Delete existing assigned clients for this employee
            DB::table('employee_client')->where('employee_id', $employeeId)->delete();

            // Remove clients from any other employee
            DB::table('employee_client')->whereIn('client_id', $clientIds)->delete();

            // Assign new clients (only if there are clients to assign)
            if (!empty($clientIds)) {
                $insertData = [];
                foreach ($clientIds as $clientId) {
                    $insertData[] = [
                        'employee_id' => $employeeId,
                        'client_id' => $clientId,
                    ];
                }
                DB::table('employee_client')->insert($insertData);
            }

            DB::commit();

            // Log the client assignment
            $clientNames = Client::whereIn('id', $clientIds)->pluck('name')->toArray();
            EmployeeChangeLog::logChange(
                $employeeId,
                'clients_assigned',
                Auth::user()->name,
                null,
                ['assigned_clients' => $clientNames],
                'Clients assigned to employee: ' . implode(', ', $clientNames)
            );

            return response()->json(['message' => 'Clients assigned successfully.'], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => 'Error assigning clients.'], 500);
        }
    }

    public function getClientsForAssignment(Request $request)
    {
        $employeeId = $request->employee_id;

        // Get all clients with district information and assigned employee details
        $clients = DB::table('clients')
            ->leftJoin('districts', 'clients.district_id', '=', 'districts.id')
            ->leftJoin('employee_client', 'clients.id', '=', 'employee_client.client_id')
            ->leftJoin('employees', 'employee_client.employee_id', '=', 'employees.id')
            ->select(
                'clients.id',
                'clients.client_code',
                'clients.name',
                'clients.phone',
                'clients.area',
                'clients.status',
                'clients.district_id',
                'districts.name as district_name',
                'employees.emp_name as assigned_employee_name',
                'employees.id as assigned_employee_id'
            )
            ->where('clients.status', '>=', 0) // Include both active and inactive
            ->orderBy('clients.name')
            ->get();

        // Get currently assigned client IDs for this employee
        $assignedClientIds = DB::table('employee_client')
            ->where('employee_id', $employeeId)
            ->pluck('client_id')
            ->toArray();

        return response()->json([
            'clients' => $clients,
            'assigned_client_ids' => $assignedClientIds
        ]);
    }
    public function mail_test()  {
        // $data='';
        // Mail::send('email.emp-creation',[], function($message) use ($data) {
        //     $message->to('<EMAIL>', env('APP_NAME'))
        //     ->subject(ucfirst(env('APP_NAME')." - Account Creation"));
        //     $message->from(env('MAIL_USERNAME'),env('APP_NAME'));
        //     // $message->cc(env('ADMIN_EMAIL'));
        //     $message->replyTo('<EMAIL>', env('APP_NAME'));
        //  });
    }
    public function getAssignedClients(Request $request)
    {
        if ($request->ajax()) {
            $employee_id = $request->input('employee_id');

            $clients = DB::table('employee_client')
                ->join('clients', 'employee_client.client_id', '=', 'clients.id')
                ->where('employee_client.employee_id', $employee_id)
                ->select('clients.id', 'clients.client_code', 'clients.name', 'clients.status');

            // Apply filters
            if ($request->has('searchkey') && $request->searchkey != '') {
                $clients->where(function ($q) use ($request) {
                    $q->where('clients.name', 'like', "%{$request->searchkey}%")
                      ->orWhere('clients.client_code', 'like', "%{$request->searchkey}%");
                });
            }

            if ($request->has('searchStatus') && $request->searchStatus != '') {
                $clients->where('clients.status', $request->searchStatus);
            }

            return DataTables::of($clients)
                ->addColumn('status', function ($row) {
                    if ($row->status == 1) {
                        return '<span class="badge bg-success">Active</span>';
                    } else {
                        return '<span class="badge bg-danger">Inactive</span>';
                    }
                })
                ->addColumn('action', function ($row) {
                    $viewBtn = '<a href="/clients/view/'.$row->id.'" class="w-32-px h-32-px bg-primary-light text-primary-600 rounded-circle d-inline-flex align-items-center justify-content-center"><iconify-icon icon="iconamoon:eye-light"></iconify-icon></a>';
                    return $viewBtn;
                })
                ->rawColumns(['status', 'action'])
                ->make(true);
        }
    }
    public function updateStatus(Request $request)
    {
        $employee = Employee::find($request->employee_id);

        if ($employee) {
            $oldStatus = $employee->status;

            // Update employee status
            $employee->status = $request->status;
            $employee->save();

            // Log the status change
            EmployeeChangeLog::logChange(
                $employee->id,
                'status_changed',
                Auth::user()->name,
                ['status' => $oldStatus],
                ['status' => $request->status],
                'Employee status changed via toggle switch'
            );

            // Update user status
            $user = User::find($employee->user_id);
            if ($user) {
                $user->status = $request->status;
                $user->save();

                // If deactivating, remove all assigned clients
                if ($request->status == 0) {
                    EmployeeClient::where('employee_id', $employee->id)->delete();
                }

                return response()->json(['success' => true, 'message' => 'Status updated successfully!']);
            }
        }

        return response()->json(['success' => false, 'message' => 'Employee not found!'], 404);
    }

    /**
     * Export employees with all fields and filters
     */
    public function exportEmployees(Request $request)
    {
        try {
            // Build the query with all relationships
            $query = Employee::with(['state', 'district', 'role', 'user'])
                ->orderBy('id', 'desc');

            // Apply filters (same as in getClients method)
            if ($request->has('searchkey') && $request->searchkey != '') {
                $query->where(function ($q) use ($request) {
                    $q->where('emp_name', 'like', "%{$request->searchkey}%")
                        ->orWhere('email', 'like', "%{$request->searchkey}%")
                        ->orWhere('phone', 'like', "%{$request->searchkey}%")
                        ->orWhere('address', 'like', "%{$request->searchkey}%");
                });
            }

            // Get all employees
            $employees = $query->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers - All available fields
            $headers = [
                'Employee ID',
                'Employee Name',
                'Email',
                'Phone',
                'Date of Birth',
                'Date of Joining',
                'Role',
                'Department',
                'Job Title',
                'Gender',
                'Address',
                'City',
                'State',
                'District',
                'Zipcode',
                'Status',
                'Photo',
                'User Account',
                'Assigned Clients Count',
                'Created Date',
                'Last Updated'
            ];

            $csvData[] = $headers;

            // Process each employee
            foreach ($employees as $employee) {
                // Get assigned clients count
                $assignedClientsCount = EmployeeClient::where('employee_id', $employee->id)->count();

                $row = [
                    $employee->id,
                    $employee->emp_name,
                    $employee->email,
                    $employee->phone ?: 'N/A',
                    $employee->dob ? date('d/m/Y', strtotime($employee->dob)) : 'N/A',
                    $employee->doj ? date('d/m/Y', strtotime($employee->doj)) : 'N/A',
                    $employee->role ? $employee->role->name : 'N/A',
                    $employee->department ?: 'N/A',
                    $employee->job_title ?: 'N/A',
                    $employee->gender ?: 'N/A',
                    $employee->address ?: 'N/A',
                    $employee->city ?: 'N/A',
                    $employee->state ? $employee->state->name : 'N/A',
                    $employee->district ? $employee->district->name : 'N/A',
                    $employee->zipcode ?: 'N/A',
                    $employee->status == 1 ? 'Active' : 'Inactive',
                    $employee->photo ? 'Yes' : 'No',
                    $employee->user ? 'Yes' : 'No',
                    $assignedClientsCount,
                    $employee->created_at ? $employee->created_at->format('d/m/Y H:i:s') : 'N/A',
                    $employee->updated_at ? $employee->updated_at->format('d/m/Y H:i:s') : 'N/A'
                ];

                $csvData[] = $row;
            }

            // Generate simple filename with timestamp
            $filename = 'Employees_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export employee assigned clients
     */
    public function exportEmployeeClients(Request $request, $employeeId)
    {
        try {
            $employee = Employee::findOrFail($employeeId);

            // Get assigned clients
            $clientIds = EmployeeClient::where('employee_id', $employeeId)->pluck('client_id');
            $clients = Client::whereIn('id', $clientIds)->orderBy('name', 'asc')->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers
            $headers = [
                'Client Name',
                'Client Code',
                'Client Type',
                'Area',
                'Phone',
                'Email',
                'Address',
                'Status',
                'Pending Amount',
                'Advance Amount',
                'Created Date'
            ];

            $csvData[] = $headers;

            // Process each client
            foreach ($clients as $client) {
                $row = [
                    $client->name,
                    $client->client_code,
                    $client->client_type ?: 'N/A',
                    $client->area ?: 'N/A',
                    $client->phone ?: 'N/A',
                    $client->email ?: 'N/A',
                    $client->address ?: 'N/A',
                    $client->status == 1 ? 'Active' : 'Inactive',
                    '₹' . number_format($client->pending_amount ?? 0, 2),
                    '₹' . number_format($client->advance_amount ?? 0, 2),
                    $client->created_at ? $client->created_at->format('d/m/Y H:i:s') : 'N/A'
                ];

                $csvData[] = $row;
            }

            // Generate filename
            $filename = 'EMP' . $employee->id . '_Assigned_Clients_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export employee invoices
     */
    public function exportEmployeeInvoices(Request $request, $employeeId)
    {
        try {
            $employee = Employee::findOrFail($employeeId);

            // Get assigned client IDs
            $clientIds = EmployeeClient::where('employee_id', $employeeId)->pluck('client_id');

            // Build query for invoices
            $query = Invoice::with(['client', 'service', 'service_type_data'])
                ->whereIn('client_id', $clientIds);

            // Apply date range filter if provided
            if ($request->has('daterange') && !empty($request->daterange)) {
                $dates = explode(' - ', $request->daterange);
                if (count($dates) === 2) {
                    $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                    $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                    $query->whereBetween('invoice_date', [$start_date, $end_date]);
                }
            }

            $invoices = $query->orderBy('invoice_date', 'desc')->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers
            $headers = [
                'Invoice Code',
                'Invoice Date',
                'Client Name',
                'Client Code',
                'Service Name',
                'Service Type',
                'Invoice Status',
                'Total Amount Due',
                'Paid Amount',
                'Unpaid Amount',
                'Grand Total',
                'Created By',
                'Created Date'
            ];

            $csvData[] = $headers;

            // Process each invoice
            foreach ($invoices as $invoice) {
                $row = [
                    $invoice->invoice_code,
                    $invoice->invoice_date ? date('d/m/Y', strtotime($invoice->invoice_date)) : 'N/A',
                    $invoice->client ? $invoice->client->name : 'N/A',
                    $invoice->client ? $invoice->client->client_code : 'N/A',
                    $invoice->service ? $invoice->service->user_label : 'N/A',
                    $invoice->service_type_data ? $invoice->service_type_data->name : 'N/A',
                    $invoice->invoice_status,
                    '₹' . number_format($invoice->total_amount_due, 2),
                    '₹' . number_format($invoice->paid_amount, 2),
                    '₹' . number_format($invoice->unpaid_amount, 2),
                    $invoice->grand_total ? '₹' . number_format($invoice->grand_total, 2) : '₹' . number_format($invoice->total_amount_due, 2),
                    $invoice->created_by ?: 'N/A',
                    $invoice->created_at ? $invoice->created_at->format('d/m/Y H:i:s') : 'N/A'
                ];

                $csvData[] = $row;
            }

            // Generate filename
            $filename = 'EMP' . $employee->id . '_Invoices_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export employee payments
     */
    public function exportEmployeePayments(Request $request, $employeeId)
    {
        try {
            $employee = Employee::findOrFail($employeeId);

            // Get assigned client IDs
            $clientIds = EmployeeClient::where('employee_id', $employeeId)->pluck('client_id');

            // Build query for payments
            $query = Payment::with(['client', 'invoice'])
                ->whereIn('client_id', $clientIds);

            // Apply date range filter if provided
            if ($request->has('daterange') && !empty($request->daterange)) {
                $dates = explode(' - ', $request->daterange);
                if (count($dates) === 2) {
                    $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                    $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                    $query->whereBetween('paid_on', [$start_date, $end_date]);
                }
            }

            $payments = $query->orderBy('paid_on', 'desc')->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers
            $headers = [
                'Payment Date',
                'Client Name',
                'Client Code',
                'Invoice Code',
                'Payment Amount',
                'Payment Mode',
                'Reference Number',
                'Payment Status',
                'Comments',
                'Created By',
                'Created Date'
            ];

            $csvData[] = $headers;

            // Process each payment
            foreach ($payments as $payment) {
                $row = [
                    $payment->paid_on ? date('d/m/Y', strtotime($payment->paid_on)) : 'N/A',
                    $payment->client ? $payment->client->name : 'N/A',
                    $payment->client ? $payment->client->client_code : 'N/A',
                    $payment->invoice ? $payment->invoice->invoice_code : 'No Invoice',
                    '₹' . number_format($payment->amount, 2),
                    $payment->payment_mode,
                    $payment->ref_number ?: 'N/A',
                    $payment->payment_status ?: 'N/A',
                    $payment->comments ?: 'N/A',
                    $payment->created_by ?: 'N/A',
                    $payment->created_at ? $payment->created_at->format('d/m/Y H:i:s') : 'N/A'
                ];

                $csvData[] = $row;
            }

            // Generate filename
            $filename = 'EMP' . $employee->id . '_Payments_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show change logs for an employee
     */
    public function changeLogs($id)
    {
        $employee = Employee::findOrFail($id);
        return view('employees.change-logs', compact('employee'));
    }
}
