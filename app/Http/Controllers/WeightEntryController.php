<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Client;
use App\Models\ClientService;
use App\Models\ClientServiceWeight;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class WeightEntryController extends Controller
{
    public function index()
    {
        $clients = Client::whereHas('clientServices', function($query) {
            $query->where('service_id', 3)
                  ->where('status', 1);
        })->where('status', 1)->get();

        return view('weight-entries.list', compact('clients'));
    }

    public function getWeightEntries(Request $request)
    {
        // Handle different date formats from the month picker
        if ($request->month) {
            try {
                // Try parsing "MMMM YYYY" format (e.g., "January 2024")
                $date = Carbon::createFromFormat('F Y', $request->month);
                $currentMonth = $date->month;
                $currentYear = $date->year;
            } catch (\Exception $e) {
                try {
                    // Fallback to "m/Y" format (e.g., "01/2024")
                    $date = Carbon::createFromFormat('m/Y', $request->month);
                    $currentMonth = $date->month;
                    $currentYear = $date->year;
                } catch (\Exception $e2) {
                    // Default to current month if parsing fails
                    $currentMonth = Carbon::now()->month;
                    $currentYear = Carbon::now()->year;
                }
            }
        } else {
            $currentMonth = Carbon::now()->month;
            $currentYear = Carbon::now()->year;
        }

        // Create the selected month date for comparison
        $selectedMonthDate = Carbon::createFromDate($currentYear, $currentMonth, 1);

        $query = Client::with([
            'clientServiceWeights' => function($query) use ($currentMonth, $currentYear) {
                $query->where('month', $currentMonth)
                      ->where('year', $currentYear);
            },
            'clientServices' => function($query) {
                $query->where('service_id', 3)
                      ->where('status', 1)
                      ->orderBy('start_date', 'desc');
            }
        ])
        ->whereHas('clientServices', function($query) {
            $query->where('service_id', 3)
                  ->where('status', 1);
        });

        if ($request->client) {
            $query->where('id', $request->client);
        }

        if ($request->searchkey) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->searchkey . '%')
                  ->orWhere('client_code', 'like', '%' . $request->searchkey . '%');
            });
        }

        // Filter by start date range
        if ($request->start_date_range) {
            $dateRange = explode(' - ', $request->start_date_range);
            if (count($dateRange) == 2) {
                $startDate = Carbon::createFromFormat('d/m/Y', $dateRange[0])->startOfDay();
                $endDate = Carbon::createFromFormat('d/m/Y', $dateRange[1])->endOfDay();

                $query->whereHas('clientServices', function($q) use ($startDate, $endDate) {
                    $q->where('service_id', 3)
                      ->where('status', 1)
                      ->whereBetween('start_date', [$startDate, $endDate]);
                });
            }
        }

        // Filter by service status for the selected month
        if ($request->service_status) {
            $query->whereHas('clientServices', function($q) use ($request, $selectedMonthDate) {
                $q->where('service_id', 3);

                if ($request->service_status === 'active') {
                    // Service is active if start_date <= selected month and (end_date is null or end_date >= selected month)
                    $q->where('start_date', '<=', $selectedMonthDate->endOfMonth())
                      ->where(function($subQ) use ($selectedMonthDate) {
                          $subQ->whereNull('end_date')
                               ->orWhere('end_date', '>=', $selectedMonthDate->startOfMonth());
                      })
                      ->where('status', 1);
                } elseif ($request->service_status === 'inactive') {
                    // Service is inactive if start_date > selected month or end_date < selected month or status = 0
                    $q->where(function($subQ) use ($selectedMonthDate) {
                        $subQ->where('start_date', '>', $selectedMonthDate->endOfMonth())
                             ->orWhere('end_date', '<', $selectedMonthDate->startOfMonth())
                             ->orWhere('status', 0);
                    });
                }
            });
        }

        // Apply sorting
        $sortBy = $request->sort_by;
        if ($sortBy) {
            switch ($sortBy) {
                case 'start_date_asc':
                    $query->orderByRaw('(SELECT MIN(start_date) FROM client_services WHERE client_services.client_id = clients.id AND client_services.service_id = 3 AND client_services.status = 1) ASC');
                    break;
                case 'start_date_desc':
                    $query->orderByRaw('(SELECT MIN(start_date) FROM client_services WHERE client_services.client_id = clients.id AND client_services.service_id = 3 AND client_services.status = 1) DESC');
                    break;
                case 'client_name':
                    $query->orderBy('name', 'asc');
                    break;
            }
        } else {
            $query->orderBy('name', 'asc');
        }

        return DataTables::of($query)
            ->addColumn('client_name', function ($row) {
                return $row->name . ' - ' . $row->client_code;
            })
            ->addColumn('start_date', function ($row) {
                $clientService = $row->clientServices->first();
                if ($clientService && $clientService->start_date) {
                    return '<span class="badge bg-info-subtle text-info d-inline-flex align-items-center gap-1">' .
                           '<iconify-icon icon="mdi:calendar-start" width="14"></iconify-icon>' .
                           Carbon::parse($clientService->start_date)->format('d M Y') .
                           '</span>';
                }
                return '<span class="text-muted d-inline-flex align-items-center gap-1">' .
                       '<iconify-icon icon="mdi:calendar-remove" width="14"></iconify-icon>N/A</span>';
            })
            ->addColumn('service_status', function ($row) use ($currentMonth, $currentYear) {
                $clientService = $row->clientServices->first();
                if (!$clientService) {
                    return '<span class="badge bg-secondary">No Service</span>';
                }

                $selectedMonthDate = Carbon::createFromDate($currentYear, $currentMonth, 1);
                $startDate = Carbon::parse($clientService->start_date);
                $endDate = $clientService->end_date ? Carbon::parse($clientService->end_date) : null;

                // Check if service is active for the selected month
                $isActiveForMonth = $clientService->status == 1 &&
                                   $startDate->lte($selectedMonthDate->copy()->endOfMonth()) &&
                                   ($endDate === null || $endDate->gte($selectedMonthDate->copy()->startOfMonth()));

                if ($isActiveForMonth) {
                    return '<span class="badge bg-success-subtle text-success d-inline-flex align-items-center gap-1">' .
                           '<iconify-icon icon="mdi:check-circle" width="14"></iconify-icon>Active</span>';
                } else {
                    $reason = '';
                    if ($clientService->status == 0) {
                        $reason = 'Service Ended';
                    } elseif ($startDate->gt($selectedMonthDate->copy()->endOfMonth())) {
                        $reason = 'Not Started';
                    } elseif ($endDate && $endDate->lt($selectedMonthDate->copy()->startOfMonth())) {
                        $reason = 'Service Ended';
                    }

                    return '<span class="badge bg-danger-subtle text-danger d-inline-flex align-items-center gap-1" title="' . $reason . '">' .
                           '<iconify-icon icon="mdi:close-circle" width="14"></iconify-icon>Inactive</span>';
                }
            })
            ->addColumn('month_year', function ($row) use ($currentMonth, $currentYear) {
                return Carbon::createFromDate($currentYear, $currentMonth, 1)->format('F Y');
            })
            ->addColumn('weight', function ($row) {
                $weight = $row->clientServiceWeights->first();
                return $weight ? $weight->weight . ' kg' : '<span class="text-danger">Not entered</span>';
            })
            ->addColumn('action', function ($row) use ($currentMonth, $currentYear) {
                $clientService = $row->clientServices->first();
                $weight = $row->clientServiceWeights->first();

                // Check if service is active for the selected month
                $isActiveForMonth = false;
                if ($clientService) {
                    $selectedMonthDate = Carbon::createFromDate($currentYear, $currentMonth, 1);
                    $startDate = Carbon::parse($clientService->start_date);
                    $endDate = $clientService->end_date ? Carbon::parse($clientService->end_date) : null;

                    $isActiveForMonth = $clientService->status == 1 &&
                                       $startDate->lte($selectedMonthDate->copy()->endOfMonth()) &&
                                       ($endDate === null || $endDate->gte($selectedMonthDate->copy()->startOfMonth()));
                }

                if (!$isActiveForMonth) {
                    return '<span class="badge bg-secondary d-inline-flex align-items-center gap-1" title="Service not active for this month">' .
                           '<iconify-icon icon="mdi:lock" width="14"></iconify-icon>Locked</span>';
                }

                if ($weight) {
                    return '
                    <a data-id="'.$row->id.'" data-weight="'.$weight->weight.'" data-month="'.$currentMonth.'" data-year="'.$currentYear.'" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center cursor-pointer edit-weight" data-bs-toggle="tooltip" title="Edit Weight Entry">
                                <iconify-icon icon="mdi:pencil" width="18"></iconify-icon>
                            </a>';
                } else {
                    return '
                    <a data-id="'.$row->id.'" data-month="'.$currentMonth.'" data-year="'.$currentYear.'" class="w-32-px h-32-px bg-primary-100 text-primary-600 rounded-circle d-inline-flex align-items-center justify-content-center cursor-pointer add-weight" data-bs-toggle="tooltip" title="Add Weight Entry">
                                <iconify-icon icon="mdi:plus" width="18"></iconify-icon>
                            </a>';
                }
            })
            ->rawColumns(['start_date', 'service_status', 'weight', 'action'])
            ->make(true);
    }

    public function store(Request $request)
    {
        $request->validate([
            'client_id' => 'required|exists:clients,id',
            'month' => 'required|numeric|min:1|max:12',
            'year' => 'required|numeric|min:2000',
            'weight' => 'required|numeric|min:0.01',
        ]);

        // Check if the service is active for the given month/year
        $selectedMonthDate = Carbon::createFromDate($request->year, $request->month, 1);
        $clientService = ClientService::where('client_id', $request->client_id)
            ->where('service_id', 3)
            ->where('status', 1)
            ->first();

        if (!$clientService) {
            return response()->json([
                'success' => false,
                'message' => 'No active weight-based service found for this client.'
            ], 400);
        }

        $startDate = Carbon::parse($clientService->start_date);
        $endDate = $clientService->end_date ? Carbon::parse($clientService->end_date) : null;

        // Check if service is active for the selected month
        $isActiveForMonth = $startDate->lte($selectedMonthDate->copy()->endOfMonth()) &&
                           ($endDate === null || $endDate->gte($selectedMonthDate->copy()->startOfMonth()));

        if (!$isActiveForMonth) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot enter weight. Service is not active for the selected month.'
            ], 400);
        }

        $weight = ClientServiceWeight::updateOrCreate(
            [
                'client_id' => $request->client_id,
                'month' => $request->month,
                'year' => $request->year,
            ],
            [
                'weight' => $request->weight,
            ]
        );

        return response()->json(['success' => true, 'message' => 'Weight saved successfully']);
    }

    public function downloadTemplate(Request $request)
    {
        // Get month and year from request or use current month/year
        if ($request->month) {
            try {
                // Try parsing "MMMM YYYY" format (e.g., "January 2024")
                $date = Carbon::createFromFormat('F Y', $request->month);
                $month = $date->month;
                $year = $date->year;
            } catch (\Exception $e) {
                try {
                    // Fallback to "m/Y" format (e.g., "01/2024")
                    $date = Carbon::createFromFormat('m/Y', $request->month);
                    $month = $date->month;
                    $year = $date->year;
                } catch (\Exception $e2) {
                    // Default to current month if parsing fails
                    $month = Carbon::now()->month;
                    $year = Carbon::now()->year;
                }
            }
        } else {
            $month = Carbon::now()->month;
            $year = Carbon::now()->year;
        }
        $monthYearText = Carbon::createFromDate($year, $month, 1)->format('F_Y');

        // Get clients with service_id = 3
        $clients = Client::whereHas('clientServices', function($query) {
            $query->where('service_id', 3)
                  ->where('status', 1);
        })
        ->where('status', 1)
        ->with([
            'clientServiceWeights' => function($query) use ($month, $year) {
                $query->where('month', $month)
                      ->where('year', $year);
            },
            'clientServices' => function($query) {
                $query->where('service_id', 3)
                      ->where('status', 1)
                      ->orderBy('start_date', 'desc');
            }
        ])
        ->get();

        // Create CSV headers
        $headers = [
            'Client ID',
            'Client Name',
            'Client Code',
            'Service Start Date',
            'Month',
            'Year',
            'Weight (kg)'
        ];

        // Create CSV data - only include clients with active services for the selected month
        $selectedMonthDate = Carbon::createFromDate($year, $month, 1);
        $data = [];

        foreach ($clients as $client) {
            $weight = $client->clientServiceWeights->first();
            $clientService = $client->clientServices->first();

            if (!$clientService) {
                continue; // Skip clients without services
            }

            // Check if service is active for the selected month
            $startDate = Carbon::parse($clientService->start_date);
            $endDate = $clientService->end_date ? Carbon::parse($clientService->end_date) : null;

            $isActiveForMonth = $clientService->status == 1 &&
                               $startDate->lte($selectedMonthDate->copy()->endOfMonth()) &&
                               ($endDate === null || $endDate->gte($selectedMonthDate->copy()->startOfMonth()));

            // Only include active services in the CSV
            if ($isActiveForMonth) {
                $data[] = [
                    'client_id' => $client->id,
                    'client_name' => $client->name,
                    'client_code' => $client->client_code,
                    'service_start_date' => $startDate->format('Y-m-d'),
                    'month' => $month,
                    'year' => $year,
                    'weight' => $weight ? $weight->weight : ''
                ];
            }
        }

        // Generate CSV file
        $filename = 'weight_entries_template_' . $monthYearText . '.csv';
        $handle = fopen('php://temp', 'r+');

        // Add BOM for UTF-8 encoding
        fputs($handle, "\xEF\xBB\xBF");

        // Add headers
        fputcsv($handle, $headers);

        // Add data rows
        foreach ($data as $row) {
            fputcsv($handle, $row);
        }

        rewind($handle);
        $csv = stream_get_contents($handle);
        fclose($handle);

        // Return CSV as download
        return response($csv, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    public function bulkUpload(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:2048',
        ]);

        $file = $request->file('csv_file');
        $path = $file->getRealPath();

        // Open the file
        $handle = fopen($path, 'r');

        // Skip the header row
        $headers = fgetcsv($handle);

        $successCount = 0;
        $errorCount = 0;
        $errors = [];
        $row = 1;

        // Process each row
        while (($data = fgetcsv($handle)) !== false) {
            $row++;

            // Map CSV columns to variables
            $clientId = $data[0] ?? null;
            $clientName = $data[1] ?? null; // Not used but included for reference
            $clientCode = $data[2] ?? null; // Not used but included for reference
            $month = $data[3] ?? null;
            $year = $data[4] ?? null;
            $weight = $data[5] ?? null;

            // Validate data
            if (!$clientId || !is_numeric($clientId) || !$month || !is_numeric($month) ||
                !$year || !is_numeric($year) || !$weight || !is_numeric($weight)) {
                $errorCount++;
                $errors[] = "Row {$row}: Invalid data format";
                continue;
            }

            // Check if client exists and has service_id = 3
            $client = Client::whereHas('clientServices', function($query) {
                $query->where('service_id', 3);
            })->find($clientId);

            if (!$client) {
                $errorCount++;
                $errors[] = "Row {$row}: Client not found or doesn't have the required service";
                continue;
            }

            try {
                // Update or create weight entry
                ClientServiceWeight::updateOrCreate(
                    [
                        'client_id' => $clientId,
                        'month' => $month,
                        'year' => $year,
                    ],
                    [
                        'weight' => $weight,
                    ]
                );

                $successCount++;
            } catch (\Exception $e) {
                $errorCount++;
                $errors[] = "Row {$row}: " . $e->getMessage();
            }
        }

        fclose($handle);

        return response()->json([
            'success' => true,
            'message' => "Processed {$successCount} entries successfully. {$errorCount} entries failed.",
            'errors' => $errors
        ]);
    }
}
