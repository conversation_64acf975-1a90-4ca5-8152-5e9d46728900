<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Service;
use App\Models\Employee;
use Illuminate\Http\Request;
use App\Models\ClientService;
use Illuminate\Http\Response;
use App\Models\EmployeeClient;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;
    public function dashboard()
    {
        $user = Auth::user();

        // If the user is an employee (role_id = 3), fetch their assigned clients
        $assignedClientIds = [];
        if ($user->role_id == 3) {
            $employee = Employee::where('user_id', $user->id)->first();

            if ($employee) {
                $assignedClientIds = EmployeeClient::where('employee_id', $employee->id)
                    ->pluck('client_id')
                    ->toArray();
            } else {
                abort(Response::HTTP_FORBIDDEN, 'You do not have permission to access this data.');
            }
        }

        // Filter invoices based on assigned clients (if employee)
        $invoiceQuery = Invoice::selectRaw("
                SUM(CASE WHEN MONTH(invoice_date) = MONTH(CURRENT_DATE()) AND YEAR(invoice_date) = YEAR(CURRENT_DATE())
                        THEN total_amount_due ELSE 0 END) as total_revenue,
                SUM(CASE WHEN MONTH(invoice_date) = MONTH(CURRENT_DATE()) AND YEAR(invoice_date) = YEAR(CURRENT_DATE())
                        THEN paid_amount ELSE 0 END) as paid_amount,
                SUM(CASE WHEN MONTH(invoice_date) = MONTH(CURRENT_DATE()) AND YEAR(invoice_date) = YEAR(CURRENT_DATE())
                        THEN unpaid_amount ELSE 0 END) as unpaid_amount,
                SUM(CASE WHEN MONTH(invoice_date) = MONTH(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH))
                        AND YEAR(invoice_date) = YEAR(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH))
                        THEN total_amount_due ELSE 0 END) as last_month_revenue,
                SUM(CASE WHEN MONTH(invoice_date) = MONTH(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH))
                        AND YEAR(invoice_date) = YEAR(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH))
                        THEN paid_amount ELSE 0 END) as last_month_paid,
                SUM(CASE WHEN MONTH(invoice_date) = MONTH(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH))
                        AND YEAR(invoice_date) = YEAR(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH))
                        THEN unpaid_amount ELSE 0 END) as last_month_unpaid
            ");

        if ($user->role_id == 3) {
            $invoiceQuery->whereIn('client_id', $assignedClientIds);
        }

        $data = $invoiceQuery->first()->toArray();

        // Filter clients based on assigned clients (if employee)
        $clientQuery = Client::selectRaw("
                COUNT(id) as total_clients,
                SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as active_clients,
                SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as inactive_clients
            ");

        if ($user->role_id == 3) {
            $clientQuery->whereIn('id', $assignedClientIds);
        }

        $clients = $clientQuery->first()->toArray();

        // Merge client data into $data array
        $data = array_merge($data, $clients);

        // Calculate differences
        $data['revenue_difference'] = $data['total_revenue'] - $data['last_month_revenue'];
        $data['paid_difference'] = $data['paid_amount'] - $data['last_month_paid'];
        $data['unpaid_difference'] = $data['unpaid_amount'] - $data['last_month_unpaid'];

        // Fetch top 10 clients with highest pending amount (filtered for employees)
        $top_due_clients = Client::leftJoin('employee_client', 'clients.id', '=', 'employee_client.client_id')
                        ->leftJoin('employees', 'employee_client.employee_id', '=', 'employees.id')
                        ->join('invoices', 'clients.id', '=', 'invoices.client_id')
                        ->selectRaw('
                            clients.id,
                            clients.name as client_name,
                            clients.client_type as client_type,
                            employees.emp_name as employee_name,
                            employees.id as employee_id,
                            SUM(invoices.total_amount_due) as total_invoice_amount,
                            SUM(invoices.paid_amount) as total_paid_amount,
                            SUM(invoices.unpaid_amount) as total_pending_amount
                        ')
                        ->groupBy('clients.id', 'clients.name', 'clients.client_type', 'employees.emp_name', 'employees.id')
                        ->orderByDesc('total_pending_amount')
                        ->limit(10);

                    if ($user->role_id == 3) {
                        $top_due_clients->whereIn('clients.id', $assignedClientIds);
                    }

                    $top_due_clients = $top_due_clients->get();


        // Fetch additional counts (filtered for employees)
        $activeServicesQuery = ClientService::where('status', 1);
        $activeEmployeesQuery = Employee::where('status', 1);
        $invoiceCountQuery = Invoice::query();
        $paymentCountQuery = Payment::query();

        if ($user->role_id == 3) {
            $activeServicesQuery->whereIn('client_id', $assignedClientIds);
            $invoiceCountQuery->whereIn('client_id', $assignedClientIds);
            $paymentCountQuery->whereIn('client_id', $assignedClientIds);
        }
        // Bedded Service (service_id = 1)
        $bededData = DB::table('clients')
            ->join('client_services', 'clients.id', '=', 'client_services.client_id')
            ->where('client_services.status', 1) // Only active services
            ->where('client_services.service_id', 1) // Bedded clients
            ->where('clients.status', 1) // Only active clients
            ->selectRaw('COUNT(clients.id) as client_count, SUM(client_services.beds_count) as total_beds')
            ->first();

        $bededClients = $bededData->client_count ?? 0;
        $totalBeds = $bededData->total_beds ?? 0;

        // Non-Bedded Service (service_id = 2)
        $nonBededClients = DB::table('clients')
            ->join('client_services', 'clients.id', '=', 'client_services.client_id')
            ->where('client_services.status', 1) // Only active services
            ->where('client_services.service_id', 2) // Non-bedded clients
            ->where('clients.status', 1)
            ->count();
        $nonBeddedData = DB::table('clients')
            ->join('client_services', 'clients.id', '=', 'client_services.client_id')
            ->where('client_services.status', 1) // Only active services
            ->where('client_services.service_id', 2) // Non-bedded clients
            ->where('clients.status', 1) // Only active clients
            ->select('client_services.non_bedded_type', DB::raw('COUNT(clients.id) as client_count'))
            ->groupBy('client_services.non_bedded_type')
            ->get();

        // Weight-Based Service (service_id = 3)
        $weightBasedClients = DB::table('clients')
            ->join('client_services', 'clients.id', '=', 'client_services.client_id')
            ->where('client_services.status', 1) // Only active services
            ->where('client_services.service_id', 3) // Weight-based clients
            ->where('clients.status', 1)
            ->count();

        // Bedded with Fixed Price (service_id = 4)
        $beddedFixedData = DB::table('clients')
            ->join('client_services', 'clients.id', '=', 'client_services.client_id')
            ->where('client_services.status', 1) // Only active services
            ->where('client_services.service_id', 4) // Bedded with fixed price
            ->where('clients.status', 1) // Only active clients
            ->selectRaw('COUNT(clients.id) as client_count, SUM(client_services.beds_count) as total_beds_fixed')
            ->first();

        $beddedFixedClients = $beddedFixedData->client_count ?? 0;
        $totalBedsFixed = $beddedFixedData->total_beds_fixed ?? 0;

        $data['active_services'] = $activeServicesQuery->count();
        $data['active_employees'] = $activeEmployeesQuery->count();
        $data['no_of_invoices'] = $invoiceCountQuery->count();
        $data['no_of_payments'] = $paymentCountQuery->count();
        $data['beded_clients'] = $bededClients;
        $data['non_beded_clients'] = $nonBededClients;
        $data['weight_based_clients'] = $weightBasedClients;
        $data['bedded_fixed_clients'] = $beddedFixedClients;
        $data['total_beds'] = $totalBeds;
        $data['total_beds_fixed'] = $totalBedsFixed;
        $data['non_beded_data'] = $nonBeddedData;

        // Get additional dashboard components for enhanced dashboard with caching
        $cacheKey = "dashboard_user_{$user->id}_role_{$user->role_id}";

        $recentActivities = Cache::remember($cacheKey . '_activities', 120, function() use ($user, $assignedClientIds) {
            return $this->getRecentActivities($user, $assignedClientIds);
        });

        $performanceMetrics = Cache::remember($cacheKey . '_performance', 300, function() use ($user, $assignedClientIds) {
            return $this->getPerformanceMetrics($user, $assignedClientIds);
        });

        $quickStats = Cache::remember($cacheKey . '_quick_stats', 120, function() use ($user, $assignedClientIds) {
            return $this->getQuickStats($user, $assignedClientIds);
        });

        $chartData = Cache::remember($cacheKey . '_charts', 300, function() use ($user, $assignedClientIds) {
            return $this->getChartData($user, $assignedClientIds);
        });

        $inventoryMetrics = Cache::remember($cacheKey . '_inventory', 300, function() use ($user, $assignedClientIds) {
            return $this->getInventoryMetrics($user, $assignedClientIds);
        });

        $expenseMetrics = Cache::remember($cacheKey . '_expenses', 300, function() use ($user, $assignedClientIds) {
            return $this->getExpenseMetrics($user, $assignedClientIds);
        });

        return view('dashboard', compact('data', 'top_due_clients', 'recentActivities', 'performanceMetrics', 'quickStats', 'chartData', 'inventoryMetrics', 'expenseMetrics'));
    }

    /**
     * Get recent activities for dashboard
     */
    private function getRecentActivities($user, $assignedClientIds)
    {
        $activities = [];

        // Recent invoices
        $recentInvoices = Invoice::with('client')
            ->when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function($invoice) {
                return [
                    'type' => 'invoice',
                    'title' => 'New Invoice Created',
                    'description' => "Invoice #{$invoice->invoice_number} for {$invoice->client->name}",
                    'amount' => $invoice->total_amount_due,
                    'date' => $invoice->created_at,
                    'icon' => 'hugeicons:invoice-03',
                    'color' => 'primary'
                ];
            });

        // Recent payments
        $recentPayments = Payment::with('client')
            ->when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function($payment) {
                return [
                    'type' => 'payment',
                    'title' => 'Payment Received',
                    'description' => "Payment from {$payment->client->name}",
                    'amount' => $payment->amount,
                    'date' => $payment->created_at,
                    'icon' => 'hugeicons:money-receive-circle',
                    'color' => 'success'
                ];
            });

        // Recent clients
        $recentClients = Client::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('id', $assignedClientIds);
            })
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get()
            ->map(function($client) {
                return [
                    'type' => 'client',
                    'title' => 'New Client Added',
                    'description' => "Client: {$client->name}",
                    'amount' => null,
                    'date' => $client->created_at,
                    'icon' => 'hugeicons:user-add-01',
                    'color' => 'info'
                ];
            });

        // Merge and sort all activities
        $activities = collect()
            ->merge($recentInvoices)
            ->merge($recentPayments)
            ->merge($recentClients)
            ->sortByDesc('date')
            ->take(10)
            ->values();

        return $activities;
    }

    /**
     * Get performance metrics for dashboard
     */
    private function getPerformanceMetrics($user, $assignedClientIds)
    {
        $currentMonth = now()->format('Y-m');
        $lastMonth = now()->subMonth()->format('Y-m');

        // Collection efficiency
        $totalInvoiced = Invoice::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->whereMonth('invoice_date', now()->month)
            ->whereYear('invoice_date', now()->year)
            ->sum('total_amount_due');

        $totalCollected = Payment::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->whereMonth('paid_on', now()->month)
            ->whereYear('paid_on', now()->year)
            ->sum('amount');

        $collectionEfficiency = $totalInvoiced > 0 ? ($totalCollected / $totalInvoiced) * 100 : 0;

        // Overdue amount
        $overdueAmount = Invoice::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->where('invoice_date', '<', now())
            ->sum('unpaid_amount');

        // Average invoice value
        $avgInvoiceValue = Invoice::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->whereMonth('invoice_date', now()->month)
            ->whereYear('invoice_date', now()->year)
            ->avg('total_amount_due') ?? 0;

        // Client growth
        $currentMonthClients = Client::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('id', $assignedClientIds);
            })
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        $lastMonthClients = Client::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('id', $assignedClientIds);
            })
            ->whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->count();

        $clientGrowth = $lastMonthClients > 0 ? (($currentMonthClients - $lastMonthClients) / $lastMonthClients) * 100 : 0;

        return [
            'collection_efficiency' => round($collectionEfficiency, 2),
            'overdue_amount' => $overdueAmount,
            'avg_invoice_value' => round($avgInvoiceValue, 2),
            'client_growth' => round($clientGrowth, 2),
            'total_invoiced' => $totalInvoiced,
            'total_collected' => $totalCollected
        ];
    }

    /**
     * Get quick stats for dashboard
     */
    private function getQuickStats($user, $assignedClientIds)
    {
        // Today's stats
        $todayInvoices = Invoice::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->whereDate('created_at', today())
            ->count();

        $todayPayments = Payment::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->whereDate('created_at', today())
            ->count();

        $todayRevenue = Payment::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->whereDate('paid_on', today())
            ->sum('amount');

        // This week's stats
        $weekStart = now()->startOfWeek();
        $weekInvoices = Invoice::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->whereBetween('created_at', [$weekStart, now()])
            ->count();

        $weekPayments = Payment::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->whereBetween('created_at', [$weekStart, now()])
            ->count();

        // Pending invoices
        $pendingInvoices = Invoice::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->where('unpaid_amount', '>', 0)
            ->count();

        return [
            'today_invoices' => $todayInvoices,
            'today_payments' => $todayPayments,
            'today_revenue' => $todayRevenue,
            'week_invoices' => $weekInvoices,
            'week_payments' => $weekPayments,
            'pending_invoices' => $pendingInvoices
        ];
    }

    /**
     * Get chart data for dashboard
     */
    private function getChartData($user, $assignedClientIds)
    {
        // Monthly revenue trend (last 6 months)
        $monthlyRevenue = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $revenue = Payment::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                    return $q->whereIn('client_id', $assignedClientIds);
                })
                ->whereMonth('paid_on', $month->month)
                ->whereYear('paid_on', $month->year)
                ->sum('amount');

            $monthlyRevenue[] = [
                'month' => $month->format('M Y'),
                'revenue' => $revenue
            ];
        }

        // Service distribution
        $serviceDistribution = [
            'bedded' => ClientService::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                    return $q->whereIn('client_id', $assignedClientIds);
                })
                ->where('service_id', 1)
                ->where('status', 1)
                ->count(),
            'non_bedded' => ClientService::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                    return $q->whereIn('client_id', $assignedClientIds);
                })
                ->where('service_id', 2)
                ->where('status', 1)
                ->count(),
            'weight_based' => ClientService::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                    return $q->whereIn('client_id', $assignedClientIds);
                })
                ->where('service_id', 3)
                ->where('status', 1)
                ->count(),
            'bedded_fixed' => ClientService::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                    return $q->whereIn('client_id', $assignedClientIds);
                })
                ->where('service_id', 4)
                ->where('status', 1)
                ->count()
        ];

        return [
            'monthly_revenue' => $monthlyRevenue,
            'service_distribution' => $serviceDistribution
        ];
    }

    /**
     * Get financial metrics for dashboard
     */
    private function getFinancialMetrics($user, $assignedClientIds)
    {

        // Current month revenue
        $currentRevenue = Payment::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->whereMonth('payment_date', now()->month)
            ->whereYear('payment_date', now()->year)
            ->sum('amount');

        // Last month revenue
        $lastRevenue = Payment::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->whereMonth('payment_date', now()->subMonth()->month)
            ->whereYear('payment_date', now()->subMonth()->year)
            ->sum('amount');

        // Current month unpaid amount
        $currentUnpaid = Invoice::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->whereMonth('invoice_date', now()->month)
            ->whereYear('invoice_date', now()->year)
            ->sum('unpaid_amount');

        // Last month unpaid amount
        $lastUnpaid = Invoice::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->whereMonth('invoice_date', now()->subMonth()->month)
            ->whereYear('invoice_date', now()->subMonth()->year)
            ->sum('unpaid_amount');

        // Total paid amount
        $paidAmount = Payment::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->sum('amount');

        // Total clients count
        $totalClients = Client::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('id', $assignedClientIds);
            })
            ->count();

        return [
            'revenue_difference' => $currentRevenue - $lastRevenue,
            'unpaid_difference' => $currentUnpaid - $lastUnpaid,
            'paid_amount' => $paidAmount,
            'unpaid_amount' => $currentUnpaid,
            'total_clients' => $totalClients
        ];
    }

    /**
     * Get expense metrics for dashboard
     */
    private function getExpenseMetrics($user, $assignedClientIds)
    {
        // Get current month expenses
        $currentMonth = now()->format('Y-m');

        // For now, return sample data since expense module structure is not defined
        // This should be updated based on your actual expense table structure
        $monthlyTotal = 50000; // Sample data
        $monthlyCount = 15; // Sample data

        return [
            'monthly_total' => $monthlyTotal,
            'monthly_count' => $monthlyCount,
            'yearly_total' => $monthlyTotal * 12, // Estimated
        ];
    }

    /**
     * Get inventory metrics for dashboard
     */
    private function getInventoryMetrics($user, $assignedClientIds)
    {
        // Check if user has inventory access
        if (!$user->can('inventory-list')) {
            return [
                'total_items' => 0,
                'total_value' => 0,
                'low_stock_items' => 0
            ];
        }

        // For now, return sample data since inventory module structure needs to be checked
        // This should be updated based on your actual inventory table structure
        $totalItems = 150; // Sample data
        $totalValue = 250000; // Sample data
        $lowStockItems = 5; // Sample data

        return [
            'total_items' => $totalItems,
            'low_stock_items' => $totalValue,
            'low_stock_items' => $lowStockItems
        ];
    }

    /**
     * Clear dashboard cache for a specific user
     */
    public static function clearDashboardCache($userId, $roleId = null)
    {
        $cacheKey = "dashboard_user_{$userId}";
        if ($roleId) {
            $cacheKey .= "_role_{$roleId}";
        }

        Cache::forget($cacheKey . '_activities');
        Cache::forget($cacheKey . '_performance');
        Cache::forget($cacheKey . '_quick_stats');
        Cache::forget($cacheKey . '_charts');

        // Also clear main dashboard data cache
        Cache::forget($cacheKey . '_main');
    }

    /**
     * Clear all dashboard caches (useful when system-wide data changes)
     */
    public static function clearAllDashboardCaches()
    {
        // Get all cache keys that start with 'dashboard_'
        $cacheKeys = Cache::getRedis()->keys('*dashboard_*');
        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * Get real-time dashboard updates via AJAX
     */
    public function getDashboardUpdates(Request $request)
    {
        $user = Auth::user();
        $assignedClientIds = [];

        if ($user->role_id == 3) {
            $employee = Employee::where('user_id', $user->id)->first();
            if ($employee) {
                $assignedClientIds = EmployeeClient::where('employee_id', $employee->id)
                    ->pluck('client_id')
                    ->toArray();
            }
        }

        // Get fresh data without cache for real-time updates
        $quickStats = $this->getQuickStats($user, $assignedClientIds);
        $recentActivities = $this->getRecentActivities($user, $assignedClientIds);

        return response()->json([
            'success' => true,
            'data' => [
                'quick_stats' => $quickStats,
                'recent_activities' => $recentActivities,
                'timestamp' => now()->toISOString()
            ]
        ]);
    }

    public function getInvoiceData(Request $request)
    {
        $year = $request->year ?? now()->year; // Default to current year if not provided

        $user = Auth::user();

        // Check if role_id == 3 (Employee)
        if ($user->role_id == 3) {
            // Get employee_id from employees table
            $employee = DB::table('employees')->where('user_id', $user->id)->first();

            if ($employee) {
                // Get all assigned client_ids from employee_client table
                $clientIds = DB::table('employee_client')
                    ->where('employee_id', $employee->id)
                    ->pluck('client_id')
                    ->toArray();
            } else {
                $clientIds = []; // No assigned clients
            }
        } else {
            $clientIds = null; // No restriction for other roles
        }

        // Query to fetch total invoices count & amount per month
        $totalInvoices = DB::table('invoices')
            ->selectRaw("
                MONTH(invoice_date) as month,
                COUNT(CASE WHEN total_amount_due > 0 THEN id END) as count,
                SUM(total_amount_due) as total_amount
            ")
            ->whereYear('invoice_date', $year)
            ->when(!is_null($clientIds), fn($q) => $q->whereIn('client_id', $clientIds))
            ->groupBy('month')
            ->orderBy('month')
            ->get();



        // Format data for the chart - simple format expected by frontend
        $monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        $totalAmounts = $this->formatMonthlyData($totalInvoices, 'total_amount');

        $chartData = [
            'months' => $monthNames,
            'amounts' => $totalAmounts
        ];

        return response()->json([
            'success' => true,
            'data' => $chartData
        ]);
    }



    private function formatMonthlyData($data, $field)
    {
        $formattedData = array_fill(0, 12, 0); // Default 12 months with 0

        foreach ($data as $row) {
            $formattedData[$row->month - 1] = (float) $row->$field;
        }

        return $formattedData;
    }
    public function getPaymentData(Request $request)
    {
        try {
            $startDate = $request->start_date;
            $endDate = $request->end_date;

            // Add logging for debugging
            Log::info('Payment Data Request:', [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'user_id' => Auth::id()
            ]);

            $user = Auth::user();

            // Initialize client restriction
            $clientIds = null;

            // Apply restriction only if role_id == 3 (Employee)
            if ($user->role_id == 3) {
                // Get employee_id from employees table
                $employee = DB::table('employees')->where('user_id', $user->id)->first();

                if ($employee) {
                    // Get all assigned client_ids from employee_client table
                    $clientIds = DB::table('employee_client')
                        ->where('employee_id', $employee->id)
                        ->pluck('client_id')
                        ->toArray();
                } else {
                    $clientIds = [];
                }
            }

            // Fetch payment data grouped by payment_mode
            $payments = DB::table('payments')
                ->select('payment_mode', DB::raw('SUM(amount) as total_amount'))
                ->whereBetween('paid_on', [$startDate, $endDate])
                ->when(!is_null($clientIds), fn($q) => $q->whereIn('client_id', $clientIds))
                ->groupBy('payment_mode')
                ->get();

            Log::info('Payment Query Result:', [
                'count' => $payments->count(),
                'data' => $payments->toArray()
            ]);

            // Format the data for the frontend
            $formattedData = [
                'labels' => [],
                'amounts' => []
            ];

            foreach ($payments as $payment) {
                $formattedData['labels'][] = $payment->payment_mode;
                $formattedData['amounts'][] = (float) $payment->total_amount;
            }

            // If no data found, return sample data for testing
            if (empty($formattedData['labels'])) {
                $formattedData = [
                    'labels' => ['Cash', 'Online Transfer', 'Bank Transfer'],
                    'amounts' => [25000, 35000, 15000]
                ];
            }

            return response()->json([
                'success' => true,
                'data' => $formattedData
            ]);

        } catch (\Exception $e) {
            Log::error('Payment Data Error:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'data' => [
                    'labels' => ['Cash', 'Online Transfer', 'Bank Transfer'],
                    'amounts' => [25000, 35000, 15000]
                ]
            ], 500);
        }
    }

    public static function IND_money_format($num, $decimals = 2)
{
    $explrestunits = "";
    $num = preg_replace('/,+/', '', $num);
    $words = explode(".", $num);
    $des = "00";

    if (count($words) <= 2) {
        $num = $words[0];
        if (count($words) == 2) {
            $des = $words[1];
        }
    }

    // Format main number
    if (strlen($num) > 3) {
        $lastthree = substr($num, strlen($num) - 3);
        $restunits = substr($num, 0, strlen($num) - 3);
        $restunits = (strlen($restunits) % 2 == 1) ? "0" . $restunits : $restunits;
        $expunit = str_split($restunits, 2);
        foreach ($expunit as $i => $unit) {
            $explrestunits .= ($i == 0) ? (int)$unit . "," : $unit . ",";
        }
        $thecash = $explrestunits . $lastthree;
    } else {
        $thecash = $num;
    }

    // Handle decimals dynamically
    if ($decimals > 0 && intval($des) > 0) {
        $des = substr($des . "00", 0, $decimals); // Ensure proper decimal places
        return "$thecash.$des";
    }

    return $thecash; // Return without decimals if they are zero
}

public function getTopDueClients(Request $request)
{
    try {
        $user = Auth::user();

        // First, let's build a more robust query
        $query = Client::leftJoin('employee_client', 'clients.id', '=', 'employee_client.client_id')
            ->leftJoin('employees', 'employee_client.employee_id', '=', 'employees.id')
            ->join('invoices', 'clients.id', '=', 'invoices.client_id')
            ->selectRaw('
                clients.id,
                clients.name as client_name,
                clients.client_type as client_type,
                employees.emp_name as employee_name,
                employees.id as employee_id,
                SUM(invoices.total_amount_due) as total_invoice_amount,
                SUM(invoices.paid_amount) as total_paid_amount,
                SUM(invoices.unpaid_amount) as total_pending_amount
            ')
            ->havingRaw('SUM(invoices.unpaid_amount) > 0') // Use HAVING instead of WHERE for aggregated columns
            ->groupBy('clients.id', 'clients.name', 'clients.client_type', 'employees.emp_name', 'employees.id')
            ->orderByDesc('total_pending_amount');

        // Filter by client type if specified
        if ($request->client_type && $request->client_type != 'all') {
            $query->where('clients.client_type', (int)$request->client_type);
        }

        // Apply employee filter for role_id 3
        if ($user->role_id == 3) {
            $employee = Employee::where('user_id', $user->id)->first();
            if ($employee) {
                $assignedClientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');
                $query->whereIn('clients.id', $assignedClientIds);
            } else {
                // If no employee found, return empty result
                $query->whereRaw('1 = 0');
            }
        }

        // Get top 10 results
        $top_due_clients = $query->limit(10)->get();

        // Debug: Let's also get some additional info
        $debugInfo = [
            'client_type_filter' => $request->client_type,
            'user_role' => $user->role_id,
            'total_found' => $top_due_clients->count(),
            'sql_query' => $query->toSql(),
            'query_bindings' => $query->getBindings()
        ];

        // If no results for specific client type, let's check if there are any clients of that type at all
        if ($top_due_clients->count() === 0 && $request->client_type && $request->client_type != 'all') {
            $clientTypeCount = Client::where('client_type', (int)$request->client_type)->count();
            $clientsWithInvoices = Client::where('client_type', (int)$request->client_type)
                ->join('invoices', 'clients.id', '=', 'invoices.client_id')
                ->where('invoices.unpaid_amount', '>', 0)
                ->distinct('clients.id')
                ->count();

            $debugInfo['clients_of_type'] = $clientTypeCount;
            $debugInfo['clients_with_pending'] = $clientsWithInvoices;
        }

        return response()->json([
            'clients' => $top_due_clients,
            'can_view_client' => $user->can('client-view'),
            'can_view_employee' => $user->can('employee-view'),
            'debug' => $debugInfo
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Failed to fetch data',
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
}

    /**
     * Get monthly invoice analytics data
     */
    public function getMonthlyInvoiceAnalytics(Request $request)
    {
        $user = Auth::user();
        $year = $request->get('year', date('Y'));

        // Get assigned client IDs for employees
        $assignedClientIds = [];
        if ($user->role_id == 3) {
            $employee = Employee::where('user_id', $user->id)->first();
            if ($employee) {
                $assignedClientIds = EmployeeClient::where('employee_id', $employee->id)
                    ->pluck('client_id')
                    ->toArray();
            }
        }

        // Query to fetch invoice count and amount per month
        $monthlyData = DB::table('invoices')
            ->selectRaw("
                MONTH(invoice_date) as month,
                COUNT(*) as count,
                SUM(total_amount_due) as total_amount
            ")
            ->whereYear('invoice_date', $year)
            ->when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Format data for the chart
        $monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        $counts = $this->formatMonthlyData($monthlyData, 'count');
        $amounts = $this->formatMonthlyData($monthlyData, 'total_amount');

        return response()->json([
            'success' => true,
            'data' => [
                'months' => $monthNames,
                'counts' => $counts,
                'amounts' => $amounts
            ]
        ]);
    }

    /**
     * Get payment trends analytics data
     */
    public function getPaymentTrendsAnalytics(Request $request)
    {
        $user = Auth::user();
        $year = $request->get('year', date('Y'));

        // Get assigned client IDs for employees
        $assignedClientIds = [];
        if ($user->role_id == 3) {
            $employee = Employee::where('user_id', $user->id)->first();
            if ($employee) {
                $assignedClientIds = EmployeeClient::where('employee_id', $employee->id)
                    ->pluck('client_id')
                    ->toArray();
            }
        }

        // Query to fetch payment amounts per month
        $monthlyPayments = DB::table('payments')
            ->selectRaw("
                MONTH(paid_on) as month,
                SUM(amount) as total_amount
            ")
            ->whereYear('paid_on', $year)
            ->when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Format data for the chart
        $monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        $amounts = $this->formatMonthlyData($monthlyPayments, 'total_amount');

        return response()->json([
            'success' => true,
            'data' => [
                'months' => $monthNames,
                'amounts' => $amounts
            ]
        ]);
    }

    /**
     * Get pending amount analytics by aging
     */
    public function getPendingAmountAnalytics(Request $request)
    {
        $user = Auth::user();

        // Get assigned client IDs for employees
        $assignedClientIds = [];
        if ($user->role_id == 3) {
            $employee = Employee::where('user_id', $user->id)->first();
            if ($employee) {
                $assignedClientIds = EmployeeClient::where('employee_id', $employee->id)
                    ->pluck('client_id')
                    ->toArray();
            }
        }

        // Get pending amounts by aging periods
        $currentDate = now();

        $agingData = DB::table('invoices')
            ->selectRaw("
                SUM(CASE
                    WHEN DATEDIFF(?, invoice_date) <= 30 THEN unpaid_amount
                    ELSE 0
                END) as current_month,
                SUM(CASE
                    WHEN DATEDIFF(?, invoice_date) BETWEEN 31 AND 60 THEN unpaid_amount
                    ELSE 0
                END) as one_month_old,
                SUM(CASE
                    WHEN DATEDIFF(?, invoice_date) BETWEEN 61 AND 90 THEN unpaid_amount
                    ELSE 0
                END) as two_months_old,
                SUM(CASE
                    WHEN DATEDIFF(?, invoice_date) > 90 THEN unpaid_amount
                    ELSE 0
                END) as three_months_plus
            ", [$currentDate, $currentDate, $currentDate, $currentDate])
            ->where('unpaid_amount', '>', 0)
            ->when($user->role_id == 3, function($q) use ($assignedClientIds) {
                return $q->whereIn('client_id', $assignedClientIds);
            })
            ->first();

        $labels = ['0-30 Days', '31-60 Days', '61-90 Days', '90+ Days'];
        $amounts = [
            (float) $agingData->current_month,
            (float) $agingData->one_month_old,
            (float) $agingData->two_months_old,
            (float) $agingData->three_months_plus
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'labels' => $labels,
                'amounts' => $amounts
            ]
        ]);
    }

    /**
     * Get client growth analytics data
     */
    public function getClientGrowthAnalytics(Request $request)
    {
        $user = Auth::user();

        // Get assigned client IDs for employees
        $assignedClientIds = [];
        if ($user->role_id == 3) {
            $employee = Employee::where('user_id', $user->id)->first();
            if ($employee) {
                $assignedClientIds = EmployeeClient::where('employee_id', $employee->id)
                    ->pluck('client_id')
                    ->toArray();
            }
        }

        // Get client growth data for the last 12 months
        $monthlyGrowth = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $count = Client::when($user->role_id == 3, function($q) use ($assignedClientIds) {
                    return $q->whereIn('id', $assignedClientIds);
                })
                ->whereMonth('created_at', $month->month)
                ->whereYear('created_at', $month->year)
                ->count();

            $monthlyGrowth[] = [
                'month' => $month->format('M'),
                'count' => $count
            ];
        }

        $months = array_column($monthlyGrowth, 'month');
        $counts = array_column($monthlyGrowth, 'count');

        return response()->json([
            'success' => true,
            'data' => [
                'months' => $months,
                'counts' => $counts
            ]
        ]);
    }

}
