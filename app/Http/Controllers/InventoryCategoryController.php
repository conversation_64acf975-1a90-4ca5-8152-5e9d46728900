<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\InventoryCategory;
use App\Models\InventoryItem;
use Yajra\DataTables\Facades\DataTables;

class InventoryCategoryController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:inventory-list')->only(['index', 'getCategoriesData']);
        $this->middleware('permission:inventory-create')->only(['store']);
        $this->middleware('permission:inventory-edit')->only(['show', 'update']);
        $this->middleware('permission:inventory-delete')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $totalCategories = InventoryCategory::count();
        $activeCategories = InventoryCategory::where('is_active', true)->count();
        $inactiveCategories = InventoryCategory::where('is_active', false)->count();

        return view('inventory.categories', [
            'totalCategories' => $totalCategories,
            'activeCategories' => $activeCategories,
            'inactiveCategories' => $inactiveCategories
        ]);
    }

    /**
     * Get categories data for DataTable
     */
    public function getCategoriesData(Request $request)
    {
        $query = InventoryCategory::withCount('inventoryItems');

        return DataTables::of($query)
            ->addColumn('items_count', function ($category) {
                return $category->inventory_items_count;
            })
            ->addColumn('status', function ($category) {
                $badgeClass = $category->is_active ? 'bg-success' : 'bg-secondary';
                $statusText = $category->is_active ? 'Active' : 'Inactive';
                return '<span class="badge ' . $badgeClass . '">' . $statusText . '</span>';
            })
            ->addColumn('actions', function ($category) {
                $user = auth()->user();
                $actions = '';

                if ($user->can('inventory-edit')) {
                    $actions .= '<a href="#" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center edit-category" data-id="' . $category->id . '" data-bs-toggle="modal" data-bs-target="#editCategoryModal" title="Edit Category">
                        <iconify-icon icon="lucide:edit"></iconify-icon>
                    </a>';
                }

                if ($user->can('inventory-delete')) {
                    $deleteDisabled = $category->inventory_items_count > 0;
                    $deleteTitle = $deleteDisabled ? 'Cannot delete category with items' : 'Delete category';
                    $deleteClass = $deleteDisabled ? 'w-32-px h-32-px bg-secondary bg-opacity-50 text-secondary rounded-circle d-inline-flex align-items-center justify-content-center' : 'w-32-px h-32-px bg-danger-focus text-danger-main rounded-circle d-inline-flex align-items-center justify-content-center delete-category';
                    $deleteData = $deleteDisabled ? 'style="cursor: not-allowed;"' : 'data-id="' . $category->id . '"';

                    $actions .= '<a href="#" class="' . $deleteClass . '" ' . $deleteData . ' title="' . $deleteTitle . '">
                        <iconify-icon icon="lucide:trash-2"></iconify-icon>
                    </a>';
                }

                return $actions;
            })
            ->editColumn('created_at', function ($category) {
                return $category->created_at->format('M d, Y');
            })
            ->rawColumns(['status', 'actions'])
            ->make(true);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Handle checkbox before validation
            $isActive = $request->has('is_active') && $request->is_active;

            $request->merge([
                'is_active' => $isActive
            ]);

            $request->validate([
                'name' => 'required|string|max:255|unique:inventory_categories,name',
                'description' => 'nullable|string|max:1000',
                'is_active' => 'boolean'
            ]);

            $category = InventoryCategory::create([
                'name' => $request->name,
                'description' => $request->description,
                'is_active' => $isActive
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Category created successfully!',
                'category' => $category
            ]);
        } catch (\Exception $e) {
            \Log::error('Error creating category: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while saving the category: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $category = InventoryCategory::with('inventoryItems')->findOrFail($id);
        return response()->json($category);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        try {
            $category = InventoryCategory::findOrFail($id);

            // Handle checkbox before validation
            $isActive = $request->has('is_active') && $request->is_active;

            $request->merge([
                'is_active' => $isActive
            ]);

            $request->validate([
                'name' => 'required|string|max:255|unique:inventory_categories,name,' . $id,
                'description' => 'nullable|string|max:1000',
                'is_active' => 'boolean'
            ]);

            $category->update([
                'name' => $request->name,
                'description' => $request->description,
                'is_active' => $isActive
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Category updated successfully!',
                'category' => $category
            ]);
        } catch (\Exception $e) {
            \Log::error('Error updating category: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the category: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $category = InventoryCategory::findOrFail($id);

            // Check if category has items
            $itemsCount = $category->inventoryItems()->count();
            if ($itemsCount > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete category that contains inventory items. Please move or delete the items first.'
                ], 422);
            }

            $categoryName = $category->name;
            $category->delete();

            \Log::info('Category deleted successfully', [
                'category_id' => $id,
                'category_name' => $categoryName,
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Category "' . $categoryName . '" deleted successfully!'
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            \Log::error('Category not found for deletion', [
                'category_id' => $id,
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Category not found.'
            ], 404);

        } catch (\Exception $e) {
            \Log::error('Error deleting category', [
                'category_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while deleting the category. Please try again.'
            ], 500);
        }
    }
}
