<?php

namespace App\Http\Controllers;

use Mail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Models\User;

class ForgotPasswordController extends Controller
{

    public function showForgotForm()
    {
        return view('forgot-password');
    }

    public function sendToken(Request $request)
    {
        $request->validate(['email' => 'required|email|exists:users,email']);
        $user = User::where('email', $request->email)->first();
        $token = Str::random(35); // Generate OTP
        $email = $request->email;
        // Store OTP in password_resets table
        DB::table('password_resets')->updateOrInsert(
            ['user_id' => $user->id],
            ['token' => $token, 'created_at' => Carbon::now()]
        );
        //Mail sending
        $data = ['email' => $request->email];
        Mail::send('email.password-reset', ['token' => $token, 'user' => $user], function ($message) use ($email) {
            $message->to($email)->subject('Password Reset OTP');
            $message->from(env('MAIL_USERNAME'), env('APP_NAME'));
            $message->replyTo('<EMAIL>', env('APP_NAME'));
        });


        return redirect()->route('login')->with('success', 'Reset password link sent to your email.');
    }

    public function showOtpForm(Request $request)
    {
        return view('auth.verify-otp', ['email' => $request->email]);
    }

    public function token_verify($token)
    {

        $tokenrecord = DB::table('password_resets')->where('token', $token)->first();

        if (!$tokenrecord) {
            return redirect()->route('login')->with('error', 'Invalid token or token expired');
        }

        return redirect()->route('reset.password.form', ['user_id' => $tokenrecord->user_id, 'token' => $token])->with('success', 'Token Verified. Enter new password.');
    }

    public function showResetForm(Request $request)
    {
        return view('auth.reset-password', ['email' => $request->email]);
    }

    public function resetPassword(Request $request)
    {
        // dd ($request);
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'user_token' => 'required|exists:password_resets,token',
            'password' => 'required|min:6|confirmed'
        ]);

        $user = User::where('id', $request->user_id)->first();

        // dd ($user);
        $user->password = Hash::make($request->password);
        $user->save();

        // Delete OTP record
        DB::table('password_resets')->where('user_id', $request->user_id)->delete();

        return redirect()->route('login')->with('success', 'Password reset successfully. Please login.');
    }
}
