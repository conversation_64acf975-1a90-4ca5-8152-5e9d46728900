<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Client;
use App\Models\Invoice;
use App\Models\Employee;
use Illuminate\Http\Request;
use App\Models\EmployeeClient;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;

class ReportController extends Controller
{

    public function client_report(){
        $client_areas = Client::distinct()->pluck('area');
        $employees = Employee::distinct()->where('status',1)->get();
        return view('reports.client-report', compact('client_areas','employees'));
    }
    public function gst_report(){
        $clients = Client::where('status',1)->get();
        $employees = Employee::distinct()->where('status',1)->get();
        return view('reports.gst-report', compact('clients','employees'));
    }

    public function getClientReport(Request $request)
    {
        $query = DB::table('clients')
            ->leftJoin('employee_client', 'clients.id', '=', 'employee_client.client_id')
            ->leftJoin('employees', 'employee_client.employee_id', '=', 'employees.id')
            ->select(
                'clients.id',
                'clients.name',
                'clients.area',
                'employees.emp_name as employee_name', // Fetch Employee Name
                DB::raw('COALESCE(ROUND(SUM(invoices.total_amount_due), 0), 0) as total_invoice_amount'),
                DB::raw('COALESCE(ROUND(SUM(invoices.paid_amount), 0), 0) as total_paid_amount'),
                DB::raw('COALESCE(ROUND(SUM(invoices.total_amount_due) - SUM(invoices.paid_amount), 0), 0) as pending_amount')
            )
            ->leftJoin('invoices', 'clients.id', '=', 'invoices.client_id')
            ->groupBy('clients.id', 'clients.name','clients.area', 'employees.emp_name'); // Group by employee and client
            if (Auth::check() && Auth::user()->role_id==3) {
                $query->where('employees.id',Auth::user()->employee->id);
            }
            if ($request->employee) {
                $query->where('employees.id',$request->employee);
            }
        if ($request->searchkey) {
            $query->where('invoice_code', 'like', '%' . $request->searchkey . '%');
        }
        if ($request->has('client') && $request->client != '') {
            $query->where('client_id', $request->client);
        }
        if ($request->has('searchArea') && $request->searchArea != '') {
            $query->where('clients.area', $request->searchArea);
        }
        if ($request->has('daterange') && !empty($request->daterange)) {
            $dates = explode(' - ', $request->daterange);
            if (count($dates) === 2) {
                $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                $query->whereBetween('invoice_date', [$start_date, $end_date]);
            }
        }

        return datatables()->of($query)
                    ->editColumn('total_invoice_amount', function ($row) {
                        return [
                            'display' => '₹ ' . Controller::IND_money_format($row->total_invoice_amount, 0),
                            'raw' => $row->total_invoice_amount
                        ];
                    })
                    ->editColumn('total_paid_amount', function ($row) {
                        return [
                            'display' => '₹ ' . Controller::IND_money_format($row->total_paid_amount, 0),
                            'raw' => $row->total_paid_amount
                        ];
                    })
                    ->editColumn('pending_amount', function ($row) {
                        return [
                            'display' => '₹ ' . Controller::IND_money_format($row->pending_amount, 0),
                            'raw' => $row->pending_amount
                        ];
                    })
                    ->addColumn('action', function ($row) {
                        return '<a href="'.route('clients.show', $row->id).'" class="w-32-px h-32-px bg-primary-light text-primary-600 rounded-circle d-inline-flex align-items-center justify-content-center"><iconify-icon icon="iconamoon:eye-light"></iconify-icon></a>';
                    })
                    ->rawColumns(['action']) // Allow HTML content in the action column
                    ->make(true);
    }
    public function employee_report(){
        $client_areas = Client::distinct()->pluck('area');
        $employees = Employee::distinct()->where('status',1)->get();
        return view('reports.employee-report', compact('client_areas','employees'));
    }

    public function getEmployeeReport(Request $request)
    {
        $query = DB::table('employees')
            ->leftJoin('employee_client', 'employees.id', '=', 'employee_client.employee_id')
            ->leftJoin('clients', 'employee_client.client_id', '=', 'clients.id')
            ->leftJoin('invoices', 'clients.id', '=', 'invoices.client_id')
            ->select(
                'employees.id',
                'employees.emp_name as employee_name',
                DB::raw('COUNT(DISTINCT clients.id) as total_clients'),
                DB::raw('COALESCE(ROUND(SUM(invoices.total_amount_due), 0), 0) as total_invoice_amount'),
                DB::raw('COALESCE(ROUND(SUM(invoices.paid_amount), 0), 0) as total_paid_amount'),
                DB::raw('COALESCE(ROUND(SUM(invoices.total_amount_due) - SUM(invoices.paid_amount), 0), 0) as pending_amount'),
                DB::raw('
                    CASE
                        WHEN SUM(invoices.total_amount_due) > 0
                        THEN ROUND((SUM(invoices.paid_amount) / SUM(invoices.total_amount_due)) * 100, 2)
                        ELSE 0
                    END as percentage_paid
                ') // Calculate percentage
            )
            ->groupBy('employees.id', 'employees.emp_name')
            ->havingRaw('SUM(invoices.total_amount_due) > 0');

        // Filtering conditions
        if (Auth::check() && Auth::user()->role_id == 3) {
            $query->where('employees.id', Auth::user()->employee->id);
        }
        if ($request->employee) {
            $query->where('employees.id', $request->employee);
        }
        if ($request->searchkey) {
            $query->where('employees.emp_name', 'like', '%' . $request->searchkey . '%');
        }
        if ($request->has('searchArea') && $request->searchArea != '') {
            $query->where('clients.area', $request->searchArea);
        }
        if ($request->has('daterange') && !empty($request->daterange)) {
            $dates = explode(' - ', $request->daterange);
            if (count($dates) === 2) {
                $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                $query->whereBetween('invoices.invoice_date', [$start_date, $end_date]);
            }
        }

        return datatables()->of($query)
            ->editColumn('total_invoice_amount', function ($row) {
                return [
                    'display' => '₹ ' . Controller::IND_money_format($row->total_invoice_amount, 0),
                    'raw' => $row->total_invoice_amount
                ];
            })
            ->editColumn('total_paid_amount', function ($row) {
                return [
                    'display' => '₹ ' . Controller::IND_money_format($row->total_paid_amount, 0),
                    'raw' => $row->total_paid_amount
                ];
            })
            ->editColumn('pending_amount', function ($row) {
                return [
                    'display' => '₹ ' . Controller::IND_money_format($row->pending_amount, 0),
                    'raw' => $row->pending_amount
                ];
            })
            ->addColumn('total_clients', function ($row) {
                return $row->total_clients;
            })
            ->addColumn('percentage_paid', function ($row) {
                return [
                    'display' =>  Controller::IND_money_format($row->percentage_paid, 0). ' %',
                    'raw' => $row->percentage_paid
                ];
            })
            ->addColumn('action', function ($row) {
                return '<a href="' . route('employees.show', $row->id) . '" class="w-32-px h-32-px bg-primary-light text-primary-600 rounded-circle d-inline-flex align-items-center justify-content-center"><iconify-icon icon="iconamoon:eye-light"></iconify-icon></a>';
            })
            ->rawColumns(['action'])
            ->make(true);
    }
    public function employee_payment_report()
    {
        $payment_modes = DB::table('payments')
            ->select('payment_mode')
            ->distinct()
            ->whereNotNull('payment_mode')
            ->pluck('payment_mode')
            ->toArray();
        // Fetch distinct payment modes from DB
        $rawModes = DB::table('payments')->distinct()->pluck('payment_mode')->filter();

        // Convert to safe SQL aliases (e.g., "Net Banking" → "net_banking")
        $paymentModes = [];
        foreach ($rawModes as $mode) {
            $key = strtolower(str_replace(' ', '_', $mode)); // sanitize alias
            $paymentModes[$key] = $mode;
        }

        return view('reports.employee-payment-report', compact('paymentModes','payment_modes'));
    }

    public function getEmployeePaymentReport(Request $request)
    {
        // Fetch distinct payment modes from DB
        $rawModes = DB::table('payments')->distinct()->pluck('payment_mode')->filter();

        // Convert to safe SQL aliases (e.g., "Net Banking" → "net_banking")
        $paymentModes = [];
        foreach ($rawModes as $mode) {
            $key = strtolower(str_replace(' ', '_', $mode)); // sanitize alias
            $paymentModes[$key] = $mode;
        }

        $query = DB::table('employees')
            ->leftJoin('employee_client', 'employee_client.employee_id', '=', 'employees.id')
            ->leftJoin('payments', 'employee_client.client_id', '=', 'payments.client_id')
            ->select('employees.id', 'employees.emp_name as employee_name');

        foreach ($paymentModes as $key => $label) {
            $query->selectRaw(
                "COALESCE(SUM(CASE WHEN payments.payment_mode = ? THEN payments.amount ELSE 0 END), 0) as `$key`",
                [$label]
            );
        }

        $query->selectRaw("COALESCE(SUM(payments.amount), 0) as total");
        $query->groupBy('employees.id', 'employees.emp_name');

        if ($request->has('daterange') && !empty($request->daterange)) {
            $dates = explode(' - ', $request->daterange);
            if (count($dates) === 2) {
                $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                $query->whereBetween('payments.paid_on', [$start_date, $end_date]);
            }
        }
        if ($request->has('searchkey') && !empty($request->searchkey)) {

            $query->where('employees.emp_name', 'like', '%' . $request->searchkey . '%');

        }

        $dataTable = datatables()->of($query)
            ->editColumn('employee_name', fn($row) => $row->employee_name)
            ->addColumn('total', function ($row) {
                return [
                    'display' => '₹ ' . Controller::IND_money_format($row->total),
                    'raw' => $row->total
                ];
            });

        foreach ($paymentModes as $key => $label) {
            $dataTable->addColumn($key, function ($row) use ($key) {
                return [
                    'display' => '₹ ' . Controller::IND_money_format($row->$key),
                    'raw' => $row->$key
                ];
            });
        }

        // 👉 Return both DataTable and modes (optional)
        session(['payment_modes' => $paymentModes]); // Pass it to blade if needed

        return $dataTable->make(true);
    }

    public function getGstReport(Request $request)
    {
        if ($request->ajax()) {
            $user = Auth::user();

            $invoices = Invoice::select(['id', 'client_id', 'invoice_date', 'invoice_code', 'gross_amount', 'cgst_amount','sgst_amount', 'total_amount_due','paid_amount','unpaid_amount'])
                ->where('invoice_status','Paid')
                ->orderBy('id', 'desc');

            // If user has role_id == 3, fetch only invoices related to their assigned clients
            if ($user->role_id == 3) {
                $employee = Employee::where('user_id', $user->id)->first();

                if ($employee) {
                    $assignedClientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');

                    // Filter invoices to only those belonging to assigned clients
                    $invoices->whereIn('client_id', $assignedClientIds);
                } else {
                    // If the employee record is missing, return an empty list
                    $invoices->whereRaw('0 = 1');
                }
            }
            // Apply Employee Filter
            if ($request->has('employee') && $request->employee != '') {
                $clientIds = DB::table('employee_client')
                    ->where('employee_id', $request->employee)
                    ->pluck('client_id')
                    ->toArray();

                $invoices->whereIn('client_id', $clientIds);
            }
            return DataTables::of($invoices)
                ->addColumn('invoice_status', function ($row) {
                    if ($row->invoice_status == 'Paid') {
                        return '<span class="badge badge-sm text-sm fw-normal text-success-600 bg-success-100 px-20 py-9 radius-4 text-white">Paid</span>';
                    } else if ($row->invoice_status == 'Pending') {
                        return '<span class="badge badge-sm text-sm fw-normal text-danger-600 bg-danger-100 px-20 py-9 radius-4 text-white">Pending</span>';
                    } else if ($row->invoice_status == 'Partially Paid') {
                        return '<span class="badge badge-sm text-sm fw-normal text-warning-600 bg-warning-100 px-20 py-9 radius-4 text-white">Partially Paid</span>';
                    }
                })
                ->addColumn('client_name', function ($row) {
                    return $row->client_id ? $row->client->name : '-';
                })
                ->addColumn('gst', function ($row) {
                    return $row->client_id ? $row->client->gst : '-';
                })

                ->editColumn('gross_amount', function ($row) {
                    return [
                        'display' => '₹ ' . Controller::IND_money_format($row->gross_amount, 0),
                        'raw' => $row->gross_amount
                    ];
                })
                ->editColumn('cgst_amount', function ($row) {
                    return [
                        'display' => '₹ ' . Controller::IND_money_format($row->cgst_amount, 0),
                        'raw' => $row->cgst_amount
                    ];
                })
                ->editColumn('sgst_amount', function ($row) {
                    return [
                        'display' => '₹ ' . Controller::IND_money_format($row->sgst_amount, 0),
                        'raw' => $row->sgst_amount
                    ];
                })
                ->editColumn('total_amount_due', function ($row) {
                    return [
                        'display' => '₹ ' . Controller::IND_money_format($row->total_amount_due, 0),
                        'raw' => $row->total_amount_due
                    ];
                })
                ->editColumn('paid_amount', function ($row) {
                    return [
                        'display' => '₹ ' . Controller::IND_money_format($row->paid_amount, 0),
                        'raw' => $row->paid_amount
                    ];
                })
                ->addColumn('action', function ($row) {
                    $user = auth()->user(); // Get authenticated user
                    $output = '';
                    // Check 'invoice-view' permission
                    if ($user->can('invoice-view')) {
                        $output .=  '
                        <a href="' . route('invoices.show', $row->id) . '" class="w-32-px h-32-px bg-primary-light text-primary-600 rounded-circle d-inline-flex align-items-center justify-content-center">
                            <iconify-icon icon="iconamoon:eye-light"></iconify-icon>
                        </a>';
                    }
                    $output .=  ' <a href="' . route('invoice.download', $row->id) . '" class="w-32-px h-32-px bg-warning-focus text-warning-main rounded-circle d-inline-flex align-items-center justify-content-center">
                            <iconify-icon icon="lucide:download"></iconify-icon>
                        </a>

                    ';

                    return $output;
                })
                ->filter(function ($query) use ($request) {
                    if ($request->has('searchkey') && $request->searchkey != '') {
                        $query->where('invoice_code', 'like', "%{$request->searchkey}%");
                    }
                    if ($request->has('client') && $request->client != '') {
                        $query->where('client_id', $request->client);
                    }
                    if ($request->has('invoice_status') && $request->invoice_status != '') {
                        $query->where('invoice_status', $request->invoice_status);
                    }
                    if ($request->has('daterange') && !empty($request->daterange)) {
                        $dates = explode(' - ', $request->daterange);
                        if (count($dates) === 2) {
                            $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                            $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                            $query->whereBetween('invoice_date', [$start_date, $end_date]);
                        }
                    }
                })

                ->editColumn('invoice_date', function ($row) {
                    return $row->invoice_date ? date('d-m-Y', strtotime($row->invoice_date)) : 'NA';
                })



                ->rawColumns(['action', 'invoice_status']) // Ensure HTML rendering
                ->make(true);
        }
    }






    public function getGstSummary(Request $request)
    {
        try {
            $user = Auth::user();

            // Base query for invoices
            $baseQuery = Invoice::where('invoice_status', 'Paid');

            // Apply user role restrictions
            if ($user->role_id == 3) {
                $employee = Employee::where('user_id', $user->id)->first();
                if ($employee) {
                    $assignedClientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');
                    $baseQuery->whereIn('client_id', $assignedClientIds);
                } else {
                    $baseQuery->whereRaw('1 = 0');
                }
            }

            // Apply filters
            if ($request->has('searchkey') && !empty($request->searchkey)) {
                $baseQuery->where('invoice_code', 'like', '%' . $request->searchkey . '%');
            }

            if ($request->has('client') && $request->client != '') {
                $baseQuery->where('client_id', $request->client);
            }

            if ($request->has('daterange') && !empty($request->daterange)) {
                $dates = explode(' - ', $request->daterange);
                if (count($dates) === 2) {
                    $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                    $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                    $baseQuery->whereBetween('invoice_date', [$start_date, $end_date]);
                }
            }

            // Get invoices with client data
            $invoices = $baseQuery->with('client')->get();

            // Initialize summary data
            $summary = [
                'with_gst' => [
                    'count' => 0,
                    'taxable_amount' => 0,
                    'tax_amount' => 0,
                    'total_value' => 0
                ],
                'without_gst' => [
                    'count' => 0,
                    'taxable_amount' => 0,
                    'tax_amount' => 0,
                    'total_value' => 0
                ],
                'total' => [
                    'count' => 0,
                    'taxable_amount' => 0,
                    'tax_amount' => 0,
                    'total_value' => 0
                ]
            ];

            // Process each invoice
            foreach ($invoices as $invoice) {
                $hasGst = !empty($invoice->client->gst);
                $taxableAmount = $invoice->gross_amount;
                $taxAmount = $invoice->cgst_amount + $invoice->sgst_amount;
                $totalValue = $invoice->total_amount_due;

                if ($hasGst) {
                    $summary['with_gst']['count']++;
                    $summary['with_gst']['taxable_amount'] += $taxableAmount;
                    $summary['with_gst']['tax_amount'] += $taxAmount;
                    $summary['with_gst']['total_value'] += $totalValue;
                } else {
                    $summary['without_gst']['count']++;
                    $summary['without_gst']['taxable_amount'] += $taxableAmount;
                    $summary['without_gst']['tax_amount'] += $taxAmount;
                    $summary['without_gst']['total_value'] += $totalValue;
                }

                // Add to total
                $summary['total']['count']++;
                $summary['total']['taxable_amount'] += $taxableAmount;
                $summary['total']['tax_amount'] += $taxAmount;
                $summary['total']['total_value'] += $totalValue;
            }

            return response()->json($summary);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch GST summary',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export Client Report
     */
    public function exportClientReport(Request $request)
    {
        try {
            $query = DB::table('clients')
                ->leftJoin('employee_client', 'clients.id', '=', 'employee_client.client_id')
                ->leftJoin('employees', 'employee_client.employee_id', '=', 'employees.id')
                ->select(
                    'clients.id',
                    'clients.name',
                    'clients.area',
                    'employees.emp_name as employee_name',
                    DB::raw('COALESCE(ROUND(SUM(invoices.total_amount_due), 0), 0) as total_invoice_amount'),
                    DB::raw('COALESCE(ROUND(SUM(invoices.paid_amount), 0), 0) as total_paid_amount'),
                    DB::raw('COALESCE(ROUND(SUM(invoices.total_amount_due) - SUM(invoices.paid_amount), 0), 0) as pending_amount')
                )
                ->leftJoin('invoices', 'clients.id', '=', 'invoices.client_id')
                ->groupBy('clients.id', 'clients.name','clients.area', 'employees.emp_name');

            // Apply filters (same as getClientReport)
            if (Auth::check() && Auth::user()->role_id == 3) {
                $query->where('employees.id', Auth::user()->employee->id);
            }
            if ($request->employee) {
                $query->where('employees.id', $request->employee);
            }
            if ($request->searchkey) {
                $query->where('invoice_code', 'like', '%' . $request->searchkey . '%');
            }
            if ($request->has('client') && $request->client != '') {
                $query->where('client_id', $request->client);
            }
            if ($request->has('searchArea') && $request->searchArea != '') {
                $query->where('clients.area', $request->searchArea);
            }

            $clients = $query->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers
            $headers = [
                'Client Name',
                'Area',
                'Employee Name',
                'Total Invoice Amount',
                'Total Paid Amount',
                'Pending Amount'
            ];

            $csvData[] = $headers;

            // Process each client
            foreach ($clients as $client) {
                $row = [
                    $client->name,
                    $client->area ?: 'N/A',
                    $client->employee_name ?: 'N/A',
                    '₹' . number_format($client->total_invoice_amount, 2),
                    '₹' . number_format($client->total_paid_amount, 2),
                    '₹' . number_format($client->pending_amount, 2)
                ];

                $csvData[] = $row;
            }

            // Generate filename
            $filename = 'Client_Report_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export Employee Report
     */
    public function exportEmployeeReport(Request $request)
    {
        try {
            $query = DB::table('employees')
                ->leftJoin('employee_client', 'employees.id', '=', 'employee_client.employee_id')
                ->leftJoin('clients', 'employee_client.client_id', '=', 'clients.id')
                ->leftJoin('invoices', 'clients.id', '=', 'invoices.client_id')
                ->select(
                    'employees.id',
                    'employees.emp_name as employee_name',
                    DB::raw('COUNT(DISTINCT clients.id) as total_clients'),
                    DB::raw('COALESCE(ROUND(SUM(invoices.total_amount_due), 0), 0) as total_invoice_amount'),
                    DB::raw('COALESCE(ROUND(SUM(invoices.paid_amount), 0), 0) as total_paid_amount'),
                    DB::raw('COALESCE(ROUND(SUM(invoices.total_amount_due) - SUM(invoices.paid_amount), 0), 0) as pending_amount'),
                    DB::raw('
                        CASE
                            WHEN SUM(invoices.total_amount_due) > 0
                            THEN ROUND((SUM(invoices.paid_amount) / SUM(invoices.total_amount_due)) * 100, 2)
                            ELSE 0
                        END as paid_percentage
                    ')
                )
                ->where('employees.status', 1)
                ->groupBy('employees.id', 'employees.emp_name');

            // Apply filters (same as getEmployeeReport)
            if ($request->has('searchkey') && !empty($request->searchkey)) {
                $query->where('employees.emp_name', 'like', '%' . $request->searchkey . '%');
            }

            if ($request->has('daterange') && !empty($request->daterange)) {
                $dates = explode(' - ', $request->daterange);
                if (count($dates) === 2) {
                    $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                    $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                    $query->whereBetween('invoices.invoice_date', [$start_date, $end_date]);
                }
            }

            $employees = $query->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers
            $headers = [
                'Employee Name',
                'Total Clients',
                'Total Invoice Amount',
                'Total Paid Amount',
                'Paid Percentage',
                'Pending Amount'
            ];

            $csvData[] = $headers;

            // Process each employee
            foreach ($employees as $employee) {
                $row = [
                    $employee->employee_name,
                    $employee->total_clients,
                    '₹' . number_format($employee->total_invoice_amount, 2),
                    '₹' . number_format($employee->total_paid_amount, 2),
                    number_format($employee->paid_percentage, 2) . '%',
                    '₹' . number_format($employee->pending_amount, 2)
                ];

                $csvData[] = $row;
            }

            // Generate filename
            $filename = 'Employee_Report_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export Employee Payments Report
     */
    public function exportEmployeePaymentsReport(Request $request)
    {
        try {
            // Get payment modes (same as in employee_payment_report method)
            $rawModes = DB::table('payments')->distinct()->pluck('payment_mode')->filter();
            $paymentModes = [];
            foreach ($rawModes as $mode) {
                $key = strtolower(str_replace(' ', '_', $mode));
                $paymentModes[$key] = $mode;
            }

            // Build dynamic select for payment modes
            $selectColumns = ['employees.emp_name as employee_name'];
            foreach ($paymentModes as $key => $label) {
                $selectColumns[] = DB::raw("COALESCE(SUM(CASE WHEN payments.payment_mode = '$label' THEN payments.amount ELSE 0 END), 0) as $key");
            }
            $selectColumns[] = DB::raw('COALESCE(SUM(payments.amount), 0) as total');

            $query = DB::table('employees')
                ->leftJoin('employee_client', 'employees.id', '=', 'employee_client.employee_id')
                ->leftJoin('clients', 'employee_client.client_id', '=', 'clients.id')
                ->leftJoin('payments', 'clients.id', '=', 'payments.client_id')
                ->select($selectColumns)
                ->where('employees.status', 1)
                ->groupBy('employees.id', 'employees.emp_name');

            // Apply filters (same as getEmployeePaymentReport)
            if ($request->has('daterange') && !empty($request->daterange)) {
                $dates = explode(' - ', $request->daterange);
                if (count($dates) === 2) {
                    $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                    $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                    $query->whereBetween('payments.paid_on', [$start_date, $end_date]);
                }
            }

            if ($request->has('searchkey') && !empty($request->searchkey)) {
                $query->where('employees.emp_name', 'like', '%' . $request->searchkey . '%');
            }

            $employees = $query->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers
            $headers = ['Employee Name'];
            foreach ($paymentModes as $mode) {
                $headers[] = ucfirst($mode);
            }
            $headers[] = 'Total';

            $csvData[] = $headers;

            // Process each employee
            foreach ($employees as $employee) {
                $row = [$employee->employee_name];

                // Add payment mode amounts
                foreach ($paymentModes as $key => $label) {
                    $row[] = '₹' . number_format($employee->$key, 2);
                }

                // Add total
                $row[] = '₹' . number_format($employee->total, 2);

                $csvData[] = $row;
            }

            // Generate filename
            $filename = 'Employee_Payments_Report_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export GST Report
     */
    public function exportGstReport(Request $request)
    {
        try {
            $user = Auth::user();

            // Base query for invoices (same as getGstReport)
            $query = Invoice::with(['client', 'service', 'service_type_data'])
                ->where('invoice_status', 'Paid')
                ->orderBy('invoice_date', 'desc');

            // Apply user role restrictions
            if ($user->role_id == 3) {
                $employee = Employee::where('user_id', $user->id)->first();
                if ($employee) {
                    $assignedClientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');
                    $query->whereIn('client_id', $assignedClientIds);
                } else {
                    $query->whereRaw('1 = 0');
                }
            }

            // Apply filters
            if ($request->has('searchkey') && !empty($request->searchkey)) {
                $query->where('invoice_code', 'like', '%' . $request->searchkey . '%');
            }

            if ($request->has('client') && $request->client != '') {
                $query->where('client_id', $request->client);
            }

            if ($request->has('daterange') && !empty($request->daterange)) {
                $dates = explode(' - ', $request->daterange);
                if (count($dates) === 2) {
                    $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                    $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                    $query->whereBetween('invoice_date', [$start_date, $end_date]);
                }
            }

            $invoices = $query->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers
            $headers = [
                'Invoice Code',
                'Invoice Date',
                'Client Name',
                'Client GST',
                'Service Name',
                'Service Type',
                'Gross Amount',
                'CGST Amount',
                'SGST Amount',
                'Total Tax Amount',
                'Total Invoice Value',
                'GST Status'
            ];

            $csvData[] = $headers;

            // Process each invoice
            foreach ($invoices as $invoice) {
                $hasGst = !empty($invoice->client->gst);
                $taxAmount = $invoice->cgst_amount + $invoice->sgst_amount;

                $row = [
                    $invoice->invoice_code,
                    $invoice->invoice_date ? date('d/m/Y', strtotime($invoice->invoice_date)) : 'N/A',
                    $invoice->client ? $invoice->client->name : 'N/A',
                    $invoice->client && $invoice->client->gst ? $invoice->client->gst : 'No GST',
                    $invoice->service ? $invoice->service->user_label : 'N/A',
                    $invoice->service_type_data ? $invoice->service_type_data->name : 'N/A',
                    '₹' . number_format($invoice->gross_amount ?? 0, 2),
                    '₹' . number_format($invoice->cgst_amount ?? 0, 2),
                    '₹' . number_format($invoice->sgst_amount ?? 0, 2),
                    '₹' . number_format($taxAmount, 2),
                    '₹' . number_format($invoice->total_amount_due, 2),
                    $hasGst ? 'With GST' : 'Without GST'
                ];

                $csvData[] = $row;
            }

            // Generate filename
            $filename = 'GST_Report_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Client Dues Report
     */
    public function clientDuesReport()
    {
        $employees = Employee::where('status', 1)->get();
        $client_areas = Client::distinct()->pluck('area');
        $amount_ranges = $this->generateDynamicAmountRanges();
        return view('reports.client-dues-report', compact('employees', 'client_areas', 'amount_ranges'));
    }

    /**
     * Generate dynamic amount ranges based on actual data
     */
    private function generateDynamicAmountRanges()
    {
        try {
            // Get all pending amounts to analyze the data distribution
            $pendingAmounts = DB::table('clients')
                ->leftJoin('invoices', 'clients.id', '=', 'invoices.client_id')
                ->select(DB::raw('COALESCE(SUM(invoices.total_amount_due) - SUM(invoices.paid_amount), 0) as pending_amount'))
                ->where('clients.status', 1)
                ->groupBy('clients.id')
                ->havingRaw('pending_amount > 0')
                ->pluck('pending_amount')
                ->filter(function($amount) {
                    return $amount > 0;
                })
                ->sort()
                ->values();

            if ($pendingAmounts->isEmpty()) {
                // Return default ranges if no data
                return [
                    ['value' => 'below_1000', 'label' => 'Below ₹1,000'],
                    ['value' => '1000_5000', 'label' => '₹1,000 - ₹5,000'],
                    ['value' => '5000_10000', 'label' => '₹5,000 - ₹10,000'],
                    ['value' => '10000_25000', 'label' => '₹10,000 - ₹25,000'],
                    ['value' => '25000_50000', 'label' => '₹25,000 - ₹50,000'],
                    ['value' => 'above_50000', 'label' => 'Above ₹50,000']
                ];
            }

            $min = $pendingAmounts->min();
            $max = $pendingAmounts->max();
            $count = $pendingAmounts->count();

            // Calculate percentiles for better distribution
            $p25 = $pendingAmounts->slice(intval($count * 0.25), 1)->first();
            $p50 = $pendingAmounts->slice(intval($count * 0.50), 1)->first();
            $p75 = $pendingAmounts->slice(intval($count * 0.75), 1)->first();
            $p90 = $pendingAmounts->slice(intval($count * 0.90), 1)->first();

            // Round to meaningful numbers
            $ranges = [];

            // First range: Below 25th percentile
            $firstThreshold = $this->roundToMeaningfulNumber($p25);
            if ($firstThreshold > 0) {
                $ranges[] = [
                    'value' => 'below_' . $firstThreshold,
                    'label' => 'Below ₹' . number_format($firstThreshold)
                ];
            }

            // Second range: 25th to 50th percentile
            $secondThreshold = $this->roundToMeaningfulNumber($p50);
            if ($secondThreshold > $firstThreshold) {
                $ranges[] = [
                    'value' => $firstThreshold . '_' . $secondThreshold,
                    'label' => '₹' . number_format($firstThreshold) . ' - ₹' . number_format($secondThreshold)
                ];
            }

            // Third range: 50th to 75th percentile
            $thirdThreshold = $this->roundToMeaningfulNumber($p75);
            if ($thirdThreshold > $secondThreshold) {
                $ranges[] = [
                    'value' => $secondThreshold . '_' . $thirdThreshold,
                    'label' => '₹' . number_format($secondThreshold) . ' - ₹' . number_format($thirdThreshold)
                ];
            }

            // Fourth range: 75th to 90th percentile
            $fourthThreshold = $this->roundToMeaningfulNumber($p90);
            if ($fourthThreshold > $thirdThreshold) {
                $ranges[] = [
                    'value' => $thirdThreshold . '_' . $fourthThreshold,
                    'label' => '₹' . number_format($thirdThreshold) . ' - ₹' . number_format($fourthThreshold)
                ];
            }

            // Fifth range: Above 90th percentile
            if ($fourthThreshold > 0) {
                $ranges[] = [
                    'value' => 'above_' . $fourthThreshold,
                    'label' => 'Above ₹' . number_format($fourthThreshold)
                ];
            }

            return $ranges;

        } catch (\Exception $e) {
            // Return default ranges on error
            return [
                ['value' => 'below_1000', 'label' => 'Below ₹1,000'],
                ['value' => '1000_5000', 'label' => '₹1,000 - ₹5,000'],
                ['value' => '5000_10000', 'label' => '₹5,000 - ₹10,000'],
                ['value' => '10000_25000', 'label' => '₹10,000 - ₹25,000'],
                ['value' => '25000_50000', 'label' => '₹25,000 - ₹50,000'],
                ['value' => 'above_50000', 'label' => 'Above ₹50,000']
            ];
        }
    }

    /**
     * Round numbers to meaningful thresholds
     */
    private function roundToMeaningfulNumber($number)
    {
        if ($number < 1000) {
            return ceil($number / 100) * 100; // Round to nearest 100
        } elseif ($number < 10000) {
            return ceil($number / 500) * 500; // Round to nearest 500
        } elseif ($number < 50000) {
            return ceil($number / 1000) * 1000; // Round to nearest 1000
        } elseif ($number < 100000) {
            return ceil($number / 5000) * 5000; // Round to nearest 5000
        } else {
            return ceil($number / 10000) * 10000; // Round to nearest 10000
        }
    }

    /**
     * Apply amount range filter to query
     */
    private function applyAmountRangeFilter($query, $amountRange)
    {
        // Handle both static and dynamic ranges
        if (strpos($amountRange, 'below_') === 0) {
            // Below threshold: below_1000, below_5000, etc.
            $threshold = str_replace('below_', '', $amountRange);
            if (is_numeric($threshold)) {
                $query->havingRaw('pending_amount < ?', [$threshold]);
            }
        } elseif (strpos($amountRange, 'above_') === 0) {
            // Above threshold: above_50000, above_100000, etc.
            $threshold = str_replace('above_', '', $amountRange);
            if (is_numeric($threshold)) {
                $query->havingRaw('pending_amount > ?', [$threshold]);
            }
        } elseif (strpos($amountRange, '_') !== false) {
            // Range: 1000_5000, 5000_10000, etc.
            $parts = explode('_', $amountRange);
            if (count($parts) === 2 && is_numeric($parts[0]) && is_numeric($parts[1])) {
                $min = $parts[0];
                $max = $parts[1];
                $query->havingRaw('pending_amount BETWEEN ? AND ?', [$min, $max]);
            }
        } else {
            // Fallback for legacy static ranges
            switch ($amountRange) {
                case 'below_1000':
                    $query->havingRaw('pending_amount < 1000');
                    break;
                case '1000_5000':
                    $query->havingRaw('pending_amount BETWEEN 1000 AND 5000');
                    break;
                case '5000_10000':
                    $query->havingRaw('pending_amount BETWEEN 5000 AND 10000');
                    break;
                case '10000_25000':
                    $query->havingRaw('pending_amount BETWEEN 10000 AND 25000');
                    break;
                case '25000_50000':
                    $query->havingRaw('pending_amount BETWEEN 25000 AND 50000');
                    break;
                case 'above_50000':
                    $query->havingRaw('pending_amount > 50000');
                    break;
            }
        }
    }

    /**
     * Get Client Dues Report Data
     */
    public function getClientDuesReport(Request $request)
    {
        $query = DB::table('clients')
            ->leftJoin('employee_client', 'clients.id', '=', 'employee_client.client_id')
            ->leftJoin('employees', 'employee_client.employee_id', '=', 'employees.id')
            ->leftJoin('invoices', 'clients.id', '=', 'invoices.client_id')
            ->select(
                'clients.id',
                'clients.name as client_name',
                'clients.client_code',
                'clients.area',
                'clients.phone',
                'clients.email',
                'employees.emp_name as employee_name',
                DB::raw('COALESCE(SUM(invoices.total_amount_due), 0) as total_invoice_amount'),
                DB::raw('COALESCE(SUM(invoices.paid_amount), 0) as total_paid_amount'),
                DB::raw('COALESCE(SUM(invoices.total_amount_due) - SUM(invoices.paid_amount), 0) as pending_amount'),
                DB::raw('MAX(invoices.invoice_date) as last_invoice_date'),
                DB::raw('COUNT(invoices.id) as total_invoices'),
                DB::raw('SUM(CASE WHEN invoices.invoice_status = "Pending" THEN 1 ELSE 0 END) as pending_invoices'),
                DB::raw('DATEDIFF(CURDATE(), MAX(invoices.invoice_date)) as days_since_last_invoice')
            )
            ->where('clients.status', 1)
            ->groupBy('clients.id', 'clients.name', 'clients.client_code', 'clients.area', 'clients.phone', 'clients.email', 'employees.emp_name')
            ->havingRaw('pending_amount > 0');

        // Apply user role restrictions
        if (Auth::check() && Auth::user()->role_id == 3) {
            $query->where('employees.id', Auth::user()->employee->id);
        }

        // Apply filters
        if ($request->has('employee') && $request->employee != '') {
            $query->where('employees.id', $request->employee);
        }

        if ($request->has('searchkey') && $request->searchkey != '') {
            $query->where(function($q) use ($request) {
                $q->where('clients.name', 'like', '%' . $request->searchkey . '%')
                  ->orWhere('clients.client_code', 'like', '%' . $request->searchkey . '%');
            });
        }

        if ($request->has('searchArea') && $request->searchArea != '') {
            $query->where('clients.area', $request->searchArea);
        }

        // Duration-based filters
        if ($request->has('duration_filter') && $request->duration_filter != '') {
            switch ($request->duration_filter) {
                case '30_days':
                    $query->havingRaw('days_since_last_invoice >= 30');
                    break;
                case '60_days':
                    $query->havingRaw('days_since_last_invoice >= 60');
                    break;
                case '90_days':
                    $query->havingRaw('days_since_last_invoice >= 90');
                    break;
                case '6_months':
                    $query->havingRaw('days_since_last_invoice >= 180');
                    break;
                case '1_year':
                    $query->havingRaw('days_since_last_invoice >= 365');
                    break;
                case 'above_1_year':
                    $query->havingRaw('days_since_last_invoice > 365');
                    break;
            }
        }

        // Amount range filters - dynamic handling
        if ($request->has('amount_range') && $request->amount_range != '') {
            $this->applyAmountRangeFilter($query, $request->amount_range);
        }

        return datatables()->of($query)
            ->editColumn('pending_amount', function ($row) {
                $color = 'text-danger';
                if ($row->pending_amount < 5000) $color = 'text-warning';
                if ($row->pending_amount < 1000) $color = 'text-info';

                return [
                    'display' => '₹ ' . Controller::IND_money_format($row->pending_amount, 0),
                    'raw' => $row->pending_amount,
                    'color' => $color
                ];
            })
            ->editColumn('total_invoice_amount', function ($row) {
                return [
                    'display' => '₹ ' . Controller::IND_money_format($row->total_invoice_amount, 0),
                    'raw' => $row->total_invoice_amount
                ];
            })
            ->editColumn('total_paid_amount', function ($row) {
                return [
                    'display' => '₹ ' . Controller::IND_money_format($row->total_paid_amount, 0),
                    'raw' => $row->total_paid_amount
                ];
            })
            ->addColumn('days_overdue', function ($row) {
                $days = $row->days_since_last_invoice;
                $color = 'text-success';
                $label = 'Recent';

                if ($days > 365) {
                    $color = 'text-danger';
                    $label = 'Critical';
                } elseif ($days > 180) {
                    $color = 'text-warning';
                    $label = 'High';
                } elseif ($days > 90) {
                    $color = 'text-info';
                    $label = 'Medium';
                } elseif ($days > 30) {
                    $color = 'text-primary';
                    $label = 'Low';
                }

                return '<span class="badge bg-' . str_replace('text-', '', $color) . '-subtle ' . $color . '">' . $days . ' days (' . $label . ')</span>';
            })
            ->editColumn('last_invoice_date', function ($row) {
                return $row->last_invoice_date ? date('d-m-Y', strtotime($row->last_invoice_date)) : 'N/A';
            })
            ->addColumn('action', function ($row) {
                return '<a href="'.route('clients.show', $row->id).'" class="w-32-px h-32-px bg-primary-light text-primary-600 rounded-circle d-inline-flex align-items-center justify-content-center"><iconify-icon icon="iconamoon:eye-light"></iconify-icon></a>';
            })
            ->rawColumns(['action', 'pending_amount', 'days_overdue'])
            ->make(true);
    }

    /**
     * Get Client Dues Summary
     */
    public function getClientDuesSummary(Request $request)
    {
        try {
            // Build the same base query as the main report
            $query = DB::table('clients')
                ->leftJoin('employee_client', 'clients.id', '=', 'employee_client.client_id')
                ->leftJoin('employees', 'employee_client.employee_id', '=', 'employees.id')
                ->leftJoin('invoices', 'clients.id', '=', 'invoices.client_id')
                ->select(
                    'clients.id',
                    'clients.name as client_name',
                    'clients.client_code',
                    'clients.area',
                    'employees.emp_name as employee_name',
                    DB::raw('COALESCE(SUM(invoices.total_amount_due), 0) as total_invoice_amount'),
                    DB::raw('COALESCE(SUM(invoices.paid_amount), 0) as total_paid_amount'),
                    DB::raw('COALESCE(SUM(invoices.total_amount_due) - SUM(invoices.paid_amount), 0) as pending_amount'),
                    DB::raw('MAX(invoices.invoice_date) as last_invoice_date'),
                    DB::raw('COUNT(invoices.id) as total_invoices'),
                    DB::raw('SUM(CASE WHEN invoices.invoice_status = "Pending" THEN 1 ELSE 0 END) as pending_invoices'),
                    DB::raw('DATEDIFF(CURDATE(), MAX(invoices.invoice_date)) as days_since_last_invoice')
                )
                ->where('clients.status', 1)
                ->groupBy('clients.id', 'clients.name', 'clients.client_code', 'clients.area', 'employees.emp_name')
                ->havingRaw('pending_amount > 0');

            // Apply same filters as main report
            if (Auth::check() && Auth::user()->role_id == 3) {
                $query->where('employees.id', Auth::user()->employee->id);
            }

            if ($request->has('employee') && $request->employee != '') {
                $query->where('employees.id', $request->employee);
            }

            if ($request->has('searchkey') && $request->searchkey != '') {
                $query->where(function($q) use ($request) {
                    $q->where('clients.name', 'like', '%' . $request->searchkey . '%')
                      ->orWhere('clients.client_code', 'like', '%' . $request->searchkey . '%');
                });
            }

            if ($request->has('searchArea') && $request->searchArea != '') {
                $query->where('clients.area', $request->searchArea);
            }

            // Apply duration and amount filters to summary as well
            if ($request->has('duration_filter') && $request->duration_filter != '') {
                switch ($request->duration_filter) {
                    case '30_days':
                        $query->havingRaw('DATEDIFF(CURDATE(), MAX(invoices.invoice_date)) >= 30');
                        break;
                    case '60_days':
                        $query->havingRaw('DATEDIFF(CURDATE(), MAX(invoices.invoice_date)) >= 60');
                        break;
                    case '90_days':
                        $query->havingRaw('DATEDIFF(CURDATE(), MAX(invoices.invoice_date)) >= 90');
                        break;
                    case '6_months':
                        $query->havingRaw('DATEDIFF(CURDATE(), MAX(invoices.invoice_date)) >= 180');
                        break;
                    case '1_year':
                        $query->havingRaw('DATEDIFF(CURDATE(), MAX(invoices.invoice_date)) >= 365');
                        break;
                    case 'above_1_year':
                        $query->havingRaw('DATEDIFF(CURDATE(), MAX(invoices.invoice_date)) > 365');
                        break;
                }
            }

            if ($request->has('amount_range') && $request->amount_range != '') {
                $this->applyAmountRangeFilter($query, $request->amount_range);
            }

            // Get the filtered results and calculate summary
            $filteredResults = $query->get();

            $summary = (object) [
                'total_clients' => $filteredResults->count(),
                'total_invoice_amount' => $filteredResults->sum('total_invoice_amount'),
                'total_paid_amount' => $filteredResults->sum('total_paid_amount'),
                'total_pending_amount' => $filteredResults->sum('pending_amount'),
                'total_invoices' => $filteredResults->sum('total_invoices'),
                'pending_invoices' => $filteredResults->sum('pending_invoices')
            ];

            // Calculate aging buckets from filtered results
            $aging = (object) [
                'current_30' => 0,
                'days_31_60' => 0,
                'days_61_90' => 0,
                'days_91_180' => 0,
                'above_180' => 0
            ];

            // Calculate aging from the filtered results
            foreach ($filteredResults as $client) {
                $days = $client->days_since_last_invoice ?? 0;
                $pendingAmount = $client->pending_amount ?? 0;

                if ($days <= 30) {
                    $aging->current_30 += $pendingAmount;
                } elseif ($days <= 60) {
                    $aging->days_31_60 += $pendingAmount;
                } elseif ($days <= 90) {
                    $aging->days_61_90 += $pendingAmount;
                } elseif ($days <= 180) {
                    $aging->days_91_180 += $pendingAmount;
                } else {
                    $aging->above_180 += $pendingAmount;
                }
            }

            return response()->json([
                'summary' => [
                    'total_clients' => $summary->total_clients ?? 0,
                    'total_invoice_amount' => $summary->total_invoice_amount ?? 0,
                    'total_paid_amount' => $summary->total_paid_amount ?? 0,
                    'total_pending_amount' => $summary->total_pending_amount ?? 0,
                    'total_invoices' => $summary->total_invoices ?? 0,
                    'pending_invoices' => $summary->pending_invoices ?? 0,
                ],
                'aging' => [
                    'current_30' => $aging->current_30 ?? 0,
                    'days_31_60' => $aging->days_31_60 ?? 0,
                    'days_61_90' => $aging->days_61_90 ?? 0,
                    'days_91_180' => $aging->days_91_180 ?? 0,
                    'above_180' => $aging->above_180 ?? 0,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch client dues summary',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get dynamic amount ranges based on current filters
     */
    public function getDynamicAmountRanges(Request $request)
    {
        try {
            // Build filtered query to get relevant pending amounts
            $query = DB::table('clients')
                ->leftJoin('employee_client', 'clients.id', '=', 'employee_client.client_id')
                ->leftJoin('employees', 'employee_client.employee_id', '=', 'employees.id')
                ->leftJoin('invoices', 'clients.id', '=', 'invoices.client_id')
                ->select(DB::raw('COALESCE(SUM(invoices.total_amount_due) - SUM(invoices.paid_amount), 0) as pending_amount'))
                ->where('clients.status', 1)
                ->groupBy('clients.id')
                ->havingRaw('pending_amount > 0');

            // Apply user role restrictions
            if (Auth::check() && Auth::user()->role_id == 3) {
                $query->where('employees.id', Auth::user()->employee->id);
            }

            // Apply filters (excluding amount_range to avoid circular dependency)
            if ($request->has('searchkey') && $request->searchkey != '') {
                $query->where(function($q) use ($request) {
                    $q->where('clients.name', 'like', '%' . $request->searchkey . '%')
                      ->orWhere('clients.client_code', 'like', '%' . $request->searchkey . '%');
                });
            }

            if ($request->has('employee') && $request->employee != '') {
                $query->where('employees.id', $request->employee);
            }

            if ($request->has('searchArea') && $request->searchArea != '') {
                $query->where('clients.area', $request->searchArea);
            }

            // Apply duration filters
            if ($request->has('duration_filter') && $request->duration_filter != '') {
                switch ($request->duration_filter) {
                    case '30_days':
                        $query->havingRaw('DATEDIFF(CURDATE(), MAX(invoices.invoice_date)) > 30');
                        break;
                    case '60_days':
                        $query->havingRaw('DATEDIFF(CURDATE(), MAX(invoices.invoice_date)) > 60');
                        break;
                    case '90_days':
                        $query->havingRaw('DATEDIFF(CURDATE(), MAX(invoices.invoice_date)) > 90');
                        break;
                    case '6_months':
                        $query->havingRaw('DATEDIFF(CURDATE(), MAX(invoices.invoice_date)) > 180');
                        break;
                    case '1_year':
                        $query->havingRaw('DATEDIFF(CURDATE(), MAX(invoices.invoice_date)) > 365');
                        break;
                    case 'above_1_year':
                        $query->havingRaw('DATEDIFF(CURDATE(), MAX(invoices.invoice_date)) > 365');
                        break;
                }
            }

            $pendingAmounts = $query->pluck('pending_amount')
                ->filter(function($amount) {
                    return $amount > 0;
                })
                ->sort()
                ->values();

            // Generate ranges based on filtered data
            $ranges = $this->generateRangesFromData($pendingAmounts);

            return response()->json([
                'success' => true,
                'ranges' => $ranges
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch dynamic amount ranges',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate ranges from data collection
     */
    private function generateRangesFromData($pendingAmounts)
    {
        if ($pendingAmounts->isEmpty()) {
            return [
                ['value' => 'below_1000', 'label' => 'Below ₹1,000'],
                ['value' => '1000_5000', 'label' => '₹1,000 - ₹5,000'],
                ['value' => '5000_10000', 'label' => '₹5,000 - ₹10,000'],
                ['value' => '10000_25000', 'label' => '₹10,000 - ₹25,000'],
                ['value' => '25000_50000', 'label' => '₹25,000 - ₹50,000'],
                ['value' => 'above_50000', 'label' => 'Above ₹50,000']
            ];
        }

        $count = $pendingAmounts->count();
        $p25 = $pendingAmounts->slice(intval($count * 0.25), 1)->first();
        $p50 = $pendingAmounts->slice(intval($count * 0.50), 1)->first();
        $p75 = $pendingAmounts->slice(intval($count * 0.75), 1)->first();
        $p90 = $pendingAmounts->slice(intval($count * 0.90), 1)->first();

        $ranges = [];

        $firstThreshold = $this->roundToMeaningfulNumber($p25);
        if ($firstThreshold > 0) {
            $ranges[] = [
                'value' => 'below_' . $firstThreshold,
                'label' => 'Below ₹' . number_format($firstThreshold)
            ];
        }

        $secondThreshold = $this->roundToMeaningfulNumber($p50);
        if ($secondThreshold > $firstThreshold) {
            $ranges[] = [
                'value' => $firstThreshold . '_' . $secondThreshold,
                'label' => '₹' . number_format($firstThreshold) . ' - ₹' . number_format($secondThreshold)
            ];
        }

        $thirdThreshold = $this->roundToMeaningfulNumber($p75);
        if ($thirdThreshold > $secondThreshold) {
            $ranges[] = [
                'value' => $secondThreshold . '_' . $thirdThreshold,
                'label' => '₹' . number_format($secondThreshold) . ' - ₹' . number_format($thirdThreshold)
            ];
        }

        $fourthThreshold = $this->roundToMeaningfulNumber($p90);
        if ($fourthThreshold > $thirdThreshold) {
            $ranges[] = [
                'value' => $thirdThreshold . '_' . $fourthThreshold,
                'label' => '₹' . number_format($thirdThreshold) . ' - ₹' . number_format($fourthThreshold)
            ];
        }

        if ($fourthThreshold > 0) {
            $ranges[] = [
                'value' => 'above_' . $fourthThreshold,
                'label' => 'Above ₹' . number_format($fourthThreshold)
            ];
        }

        return $ranges;
    }

    /**
     * Export Client Dues Report
     */
    public function exportClientDuesReport(Request $request)
    {
        try {
            $query = DB::table('clients')
                ->leftJoin('employee_client', 'clients.id', '=', 'employee_client.client_id')
                ->leftJoin('employees', 'employee_client.employee_id', '=', 'employees.id')
                ->leftJoin('invoices', 'clients.id', '=', 'invoices.client_id')
                ->select(
                    'clients.id',
                    'clients.name as client_name',
                    'clients.client_code',
                    'clients.area',
                    'clients.phone',
                    'clients.email',
                    'employees.emp_name as employee_name',
                    DB::raw('COALESCE(SUM(invoices.total_amount_due), 0) as total_invoice_amount'),
                    DB::raw('COALESCE(SUM(invoices.paid_amount), 0) as total_paid_amount'),
                    DB::raw('COALESCE(SUM(invoices.total_amount_due) - SUM(invoices.paid_amount), 0) as pending_amount'),
                    DB::raw('MAX(invoices.invoice_date) as last_invoice_date'),
                    DB::raw('COUNT(invoices.id) as total_invoices'),
                    DB::raw('SUM(CASE WHEN invoices.invoice_status = "Pending" THEN 1 ELSE 0 END) as pending_invoices'),
                    DB::raw('DATEDIFF(CURDATE(), MAX(invoices.invoice_date)) as days_since_last_invoice')
                )
                ->where('clients.status', 1)
                ->groupBy('clients.id', 'clients.name', 'clients.client_code', 'clients.area', 'clients.phone', 'clients.email', 'employees.emp_name')
                ->havingRaw('pending_amount > 0');

            // Apply same filters as main report
            if (Auth::check() && Auth::user()->role_id == 3) {
                $query->where('employees.id', Auth::user()->employee->id);
            }

            if ($request->has('employee') && $request->employee != '') {
                $query->where('employees.id', $request->employee);
            }

            if ($request->has('searchkey') && $request->searchkey != '') {
                $query->where(function($q) use ($request) {
                    $q->where('clients.name', 'like', '%' . $request->searchkey . '%')
                      ->orWhere('clients.client_code', 'like', '%' . $request->searchkey . '%');
                });
            }

            if ($request->has('searchArea') && $request->searchArea != '') {
                $query->where('clients.area', $request->searchArea);
            }

            // Apply duration and amount filters
            if ($request->has('duration_filter') && $request->duration_filter != '') {
                switch ($request->duration_filter) {
                    case '30_days':
                        $query->havingRaw('days_since_last_invoice >= 30');
                        break;
                    case '60_days':
                        $query->havingRaw('days_since_last_invoice >= 60');
                        break;
                    case '90_days':
                        $query->havingRaw('days_since_last_invoice >= 90');
                        break;
                    case '6_months':
                        $query->havingRaw('days_since_last_invoice >= 180');
                        break;
                    case '1_year':
                        $query->havingRaw('days_since_last_invoice >= 365');
                        break;
                    case 'above_1_year':
                        $query->havingRaw('days_since_last_invoice > 365');
                        break;
                }
            }

            if ($request->has('amount_range') && $request->amount_range != '') {
                $this->applyAmountRangeFilter($query, $request->amount_range);
            }

            $clients = $query->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers
            $headers = [
                'Client Code',
                'Client Name',
                'Area',
                'Phone',
                'Email',
                'Employee Name',
                'Total Invoice Amount',
                'Total Paid Amount',
                'Pending Amount',
                'Total Invoices',
                'Pending Invoices',
                'Last Invoice Date',
                'Days Since Last Invoice',
                'Risk Level'
            ];

            $csvData[] = $headers;

            // Process each client
            foreach ($clients as $client) {
                $days = $client->days_since_last_invoice;
                $riskLevel = 'Low';
                if ($days > 365) $riskLevel = 'Critical';
                elseif ($days > 180) $riskLevel = 'High';
                elseif ($days > 90) $riskLevel = 'Medium';
                elseif ($days > 30) $riskLevel = 'Low';

                $row = [
                    $client->client_code ?: 'N/A',
                    $client->client_name,
                    $client->area ?: 'N/A',
                    $client->phone ?: 'N/A',
                    $client->email ?: 'N/A',
                    $client->employee_name ?: 'N/A',
                    '₹' . number_format($client->total_invoice_amount, 2),
                    '₹' . number_format($client->total_paid_amount, 2),
                    '₹' . number_format($client->pending_amount, 2),
                    $client->total_invoices,
                    $client->pending_invoices,
                    $client->last_invoice_date ? date('d/m/Y', strtotime($client->last_invoice_date)) : 'N/A',
                    $days . ' days',
                    $riskLevel
                ];

                $csvData[] = $row;
            }

            // Generate filename
            $filename = 'Client_Dues_Report_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Client Services Report
     */
    public function clientServicesReport()
    {
        // Get filter options
        $employees = Employee::where('status', 1)->get();
        $serviceTypes = DB::table('services')->where('status', 1)->get();
        $areas = Client::distinct()->whereNotNull('area')->pluck('area');
        $districts = DB::table('districts')->get();

        // Get summary statistics
        $stats = $this->getClientServicesStats();

        return view('reports.client-services-report', compact('employees', 'serviceTypes', 'areas', 'districts', 'stats'));
    }

    /**
     * Get Client Services Report Data
     */
    public function getClientServicesReport(Request $request)
    {
        try {
            $user = Auth::user();

            // Base query with subquery to get the most relevant service per client
            $query = DB::table('clients')
                ->leftJoin(DB::raw('(
                    SELECT
                        client_id,
                        service_id,
                        service_type,
                        start_date,
                        end_date,
                        next_invoice_date,
                        payment_cycle,
                        total_price,
                        beds_count,
                        non_bedded_type,
                        ROW_NUMBER() OVER (
                            PARTITION BY client_id
                            ORDER BY
                                start_date DESC,
                                COALESCE(end_date, DATE("9999-12-31")) DESC
                        ) as rn
                    FROM client_services
                    WHERE status = 1
                ) as current_service'), function($join) {
                    $join->on('clients.id', '=', 'current_service.client_id')
                         ->where('current_service.rn', '=', 1);
                })
                ->leftJoin('services', 'current_service.service_id', '=', 'services.id')
                ->leftJoin('service_types', 'current_service.service_type', '=', 'service_types.id')
                ->leftJoin('employee_client', 'clients.id', '=', 'employee_client.client_id')
                ->leftJoin('employees', 'employee_client.employee_id', '=', 'employees.id')
                ->leftJoin('districts', 'clients.district_id', '=', 'districts.id')
                ->select([
                    'clients.id',
                    'clients.client_code',
                    'clients.name as client_name',
                    'clients.phone',
                    'clients.secondary_phone',
                    'clients.email',
                    'clients.area',
                    'clients.status as client_status',
                    'districts.name as district_name',
                    'services.user_label as service_name',
                    'service_types.name as service_type_name',
                    'current_service.start_date',
                    'current_service.end_date',
                    'current_service.next_invoice_date',
                    'current_service.payment_cycle',
                    'current_service.total_price',
                    'current_service.beds_count',
                    'current_service.non_bedded_type',
                    'employees.emp_name as employee_name',
                    DB::raw('CASE
                        WHEN current_service.service_id IS NULL THEN "No Service"
                        WHEN current_service.end_date IS NOT NULL AND current_service.end_date < CURDATE() THEN "Expired"
                        WHEN current_service.end_date IS NOT NULL AND current_service.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN "Expiring Soon"
                        WHEN current_service.next_invoice_date IS NOT NULL AND current_service.next_invoice_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN "Invoice Due Soon"
                        ELSE "Active"
                    END as service_status'),
                    DB::raw('CASE
                        WHEN current_service.end_date IS NOT NULL THEN DATEDIFF(current_service.end_date, CURDATE())
                        ELSE NULL
                    END as days_remaining')
                ])
                ->where('clients.status', 1);

            // Apply role-based filtering
            if ($user->role_id == 3) {
                $employee = Employee::where('user_id', $user->id)->first();
                if ($employee) {
                    $query->where('employees.id', $employee->id);
                } else {
                    $query->whereRaw('1 = 0');
                }
            }

            // Apply filters
            if ($request->filled('service_status')) {
                switch ($request->service_status) {
                    case 'no_service':
                        $query->whereNull('current_service.service_id');
                        break;
                    case 'expired':
                        $query->whereNotNull('current_service.end_date')
                              ->where('current_service.end_date', '<', now()->toDateString());
                        break;
                    case 'expiring_soon':
                        $query->whereNotNull('current_service.end_date')
                              ->whereBetween('current_service.end_date', [
                                  now()->toDateString(),
                                  now()->addDays(30)->toDateString()
                              ]);
                        break;
                    case 'invoice_due_soon':
                        $query->whereNotNull('current_service.next_invoice_date')
                              ->whereBetween('current_service.next_invoice_date', [
                                  now()->toDateString(),
                                  now()->addDays(7)->toDateString()
                              ]);
                        break;
                    case 'active':
                        $query->whereNotNull('current_service.service_id')
                              ->where(function($q) {
                                  $q->whereNull('current_service.end_date')
                                    ->orWhere('current_service.end_date', '>=', now()->toDateString());
                              });
                        break;
                }
            }

            if ($request->filled('service_type')) {
                $query->where('current_service.service_id', $request->service_type);
            }

            if ($request->filled('employee_id')) {
                $query->where('employees.id', $request->employee_id);
            }

            if ($request->filled('area')) {
                $query->where('clients.area', $request->area);
            }

            if ($request->filled('district_id')) {
                $query->where('clients.district_id', $request->district_id);
            }

            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('clients.name', 'like', "%{$search}%")
                      ->orWhere('clients.client_code', 'like', "%{$search}%")
                      ->orWhere('clients.phone', 'like', "%{$search}%")
                      ->orWhere('clients.email', 'like', "%{$search}%");
                });
            }

            return DataTables::of($query)
                ->addColumn('client_info', function ($row) {
                    return '<div class="d-flex flex-column">
                        <strong>' . $row->client_name . '</strong>
                        <small class="text-muted">' . $row->client_code . '</small>
                    </div>';
                })
                ->addColumn('contact_info', function ($row) {
                    $contact = '<div class="d-flex flex-column">';
                    $contact .= '<span><i class="fas fa-phone text-primary me-1"></i>' . $row->phone . '</span>';
                    if ($row->secondary_phone) {
                        $contact .= '<span><i class="fas fa-phone text-muted me-1"></i>' . $row->secondary_phone . '</span>';
                    }
                    $contact .= '<span><i class="fas fa-envelope text-info me-1"></i>' . $row->email . '</span>';
                    $contact .= '</div>';
                    return $contact;
                })
                ->addColumn('service_info', function ($row) {
                    if (!$row->service_name) {
                        return '<span class="badge bg-secondary">No Service</span>';
                    }

                    $info = '<div class="d-flex flex-column">';
                    $info .= '<strong>' . $row->service_name . '</strong>';
                    if ($row->service_type_name) {
                        $info .= '<small class="text-muted">' . $row->service_type_name . '</small>';
                    }
                    if ($row->beds_count) {
                        $info .= '<small class="text-info">Beds: ' . $row->beds_count . '</small>';
                    }
                    if ($row->non_bedded_type) {
                        $info .= '<small class="text-info">' . $row->non_bedded_type . '</small>';
                    }
                    $info .= '</div>';
                    return $info;
                })
                ->addColumn('status_badge', function ($row) {
                    $badgeClass = match($row->service_status) {
                        'No Service' => 'bg-secondary',
                        'Expired' => 'bg-danger',
                        'Expiring Soon' => 'bg-warning',
                        'Invoice Due Soon' => 'bg-info',
                        'Active' => 'bg-success',
                        default => 'bg-primary'
                    };

                    $badge = '<span class="badge ' . $badgeClass . '">' . $row->service_status . '</span>';

                    if ($row->days_remaining !== null) {
                        if ($row->days_remaining > 0) {
                            $badge .= '<br><small class="text-muted">' . $row->days_remaining . ' days remaining</small>';
                        } elseif ($row->days_remaining < 0) {
                            $badge .= '<br><small class="text-danger">Overdue by ' . abs($row->days_remaining) . ' days</small>';
                        }
                    }

                    return $badge;
                })
                ->addColumn('dates_info', function ($row) {
                    if (!$row->start_date) {
                        return '<span class="text-muted">N/A</span>';
                    }

                    $info = '<div class="d-flex flex-column">';
                    $info .= '<span><strong>Start:</strong> ' . date('d/m/Y', strtotime($row->start_date)) . '</span>';
                    if ($row->end_date) {
                        $info .= '<span><strong>End:</strong> ' . date('d/m/Y', strtotime($row->end_date)) . '</span>';
                    }
                    if ($row->next_invoice_date) {
                        $info .= '<span><strong>Next Invoice:</strong> ' . date('d/m/Y', strtotime($row->next_invoice_date)) . '</span>';
                    }
                    $info .= '</div>';
                    return $info;
                })
                ->addColumn('location_info', function ($row) {
                    $location = '<div class="d-flex flex-column">';
                    if ($row->area) {
                        $location .= '<span>' . $row->area . '</span>';
                    }
                    if ($row->district_name) {
                        $location .= '<small class="text-muted">' . $row->district_name . '</small>';
                    }
                    $location .= '</div>';
                    return $location;
                })
                ->addColumn('employee_info', function ($row) {
                    return $row->employee_name ?: '<span class="text-muted">Not Assigned</span>';
                })
                ->addColumn('service_value', function ($row) {
                    return $row->total_price ? '₹' . number_format($row->total_price, 2) : '<span class="text-muted">N/A</span>';
                })
                ->rawColumns(['client_info', 'contact_info', 'service_info', 'status_badge', 'dates_info', 'location_info', 'employee_info', 'service_value'])
                ->make(true);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get Client Services Statistics
     */
    private function getClientServicesStats()
    {
        $user = Auth::user();

        $baseQuery = DB::table('clients')
            ->leftJoin(DB::raw('(
                SELECT
                    client_id,
                    service_id,
                    end_date,
                    next_invoice_date,
                    total_price,
                    ROW_NUMBER() OVER (
                        PARTITION BY client_id
                        ORDER BY
                            start_date DESC,
                            COALESCE(end_date, DATE("9999-12-31")) DESC
                    ) as rn
                FROM client_services
                WHERE status = 1
            ) as current_service'), function($join) {
                $join->on('clients.id', '=', 'current_service.client_id')
                     ->where('current_service.rn', '=', 1);
            })
            ->leftJoin('employee_client', 'clients.id', '=', 'employee_client.client_id')
            ->leftJoin('employees', 'employee_client.employee_id', '=', 'employees.id')
            ->where('clients.status', 1);

        // Apply role-based filtering
        if ($user->role_id == 3) {
            $employee = Employee::where('user_id', $user->id)->first();
            if ($employee) {
                $baseQuery->where('employees.id', $employee->id);
            } else {
                $baseQuery->whereRaw('1 = 0');
            }
        }

        return [
            'total_clients' => (clone $baseQuery)->count(),
            'clients_with_service' => (clone $baseQuery)->whereNotNull('current_service.service_id')->count(),
            'clients_no_service' => (clone $baseQuery)->whereNull('current_service.service_id')->count(),
            'expired_services' => (clone $baseQuery)
                ->whereNotNull('current_service.end_date')
                ->where('current_service.end_date', '<', now()->toDateString())
                ->count(),
            'expiring_soon' => (clone $baseQuery)
                ->whereNotNull('current_service.end_date')
                ->whereBetween('current_service.end_date', [
                    now()->toDateString(),
                    now()->addDays(30)->toDateString()
                ])
                ->count(),
            'invoice_due_soon' => (clone $baseQuery)
                ->whereNotNull('current_service.next_invoice_date')
                ->whereBetween('current_service.next_invoice_date', [
                    now()->toDateString(),
                    now()->addDays(7)->toDateString()
                ])
                ->count(),
            'active_services' => (clone $baseQuery)
                ->whereNotNull('current_service.service_id')
                ->where(function($q) {
                    $q->whereNull('current_service.end_date')
                      ->orWhere('current_service.end_date', '>=', now()->toDateString());
                })
                ->count(),
            'total_service_value' => (clone $baseQuery)
                ->whereNotNull('current_service.service_id')
                ->sum('current_service.total_price') ?: 0
        ];
    }

    /**
     * Export Client Services Report
     */
    public function exportClientServicesReport(Request $request)
    {
        try {
            $user = Auth::user();

            // Build the same query as getClientServicesReport but for export
            $query = DB::table('clients')
                ->leftJoin(DB::raw('(
                    SELECT
                        client_id,
                        service_id,
                        service_type,
                        start_date,
                        end_date,
                        next_invoice_date,
                        payment_cycle,
                        total_price,
                        beds_count,
                        non_bedded_type,
                        ROW_NUMBER() OVER (
                            PARTITION BY client_id
                            ORDER BY
                                start_date DESC,
                                COALESCE(end_date, DATE("9999-12-31")) DESC
                        ) as rn
                    FROM client_services
                    WHERE status = 1
                ) as current_service'), function($join) {
                    $join->on('clients.id', '=', 'current_service.client_id')
                         ->where('current_service.rn', '=', 1);
                })
                ->leftJoin('services', 'current_service.service_id', '=', 'services.id')
                ->leftJoin('service_types', 'current_service.service_type', '=', 'service_types.id')
                ->leftJoin('employee_client', 'clients.id', '=', 'employee_client.client_id')
                ->leftJoin('employees', 'employee_client.employee_id', '=', 'employees.id')
                ->leftJoin('districts', 'clients.district_id', '=', 'districts.id')
                ->select([
                    'clients.client_code',
                    'clients.name as client_name',
                    'clients.phone',
                    'clients.secondary_phone',
                    'clients.email',
                    'clients.secondary_email',
                    'clients.area',
                    'districts.name as district_name',
                    'services.user_label as service_name',
                    'service_types.name as service_type_name',
                    'current_service.start_date',
                    'current_service.end_date',
                    'current_service.next_invoice_date',
                    'current_service.payment_cycle',
                    'current_service.total_price',
                    'current_service.beds_count',
                    'current_service.non_bedded_type',
                    'employees.emp_name as employee_name',
                    DB::raw('CASE
                        WHEN current_service.service_id IS NULL THEN "No Service"
                        WHEN current_service.end_date IS NOT NULL AND current_service.end_date < CURDATE() THEN "Expired"
                        WHEN current_service.end_date IS NOT NULL AND current_service.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN "Expiring Soon"
                        WHEN current_service.next_invoice_date IS NOT NULL AND current_service.next_invoice_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN "Invoice Due Soon"
                        ELSE "Active"
                    END as service_status'),
                    DB::raw('CASE
                        WHEN current_service.end_date IS NOT NULL THEN DATEDIFF(current_service.end_date, CURDATE())
                        ELSE NULL
                    END as days_remaining')
                ])
                ->where('clients.status', 1);

            // Apply same filters as the main report
            if ($user->role_id == 3) {
                $employee = Employee::where('user_id', $user->id)->first();
                if ($employee) {
                    $query->where('employees.id', $employee->id);
                } else {
                    $query->whereRaw('1 = 0');
                }
            }

            // Apply filters (same logic as getClientServicesReport)
            if ($request->filled('service_status')) {
                switch ($request->service_status) {
                    case 'no_service':
                        $query->whereNull('current_service.service_id');
                        break;
                    case 'expired':
                        $query->whereNotNull('current_service.end_date')
                              ->where('current_service.end_date', '<', now()->toDateString());
                        break;
                    case 'expiring_soon':
                        $query->whereNotNull('current_service.end_date')
                              ->whereBetween('current_service.end_date', [
                                  now()->toDateString(),
                                  now()->addDays(30)->toDateString()
                              ]);
                        break;
                    case 'invoice_due_soon':
                        $query->whereNotNull('current_service.next_invoice_date')
                              ->whereBetween('current_service.next_invoice_date', [
                                  now()->toDateString(),
                                  now()->addDays(7)->toDateString()
                              ]);
                        break;
                    case 'active':
                        $query->whereNotNull('current_service.service_id')
                              ->where(function($q) {
                                  $q->whereNull('current_service.end_date')
                                    ->orWhere('current_service.end_date', '>=', now()->toDateString());
                              });
                        break;
                }
            }

            if ($request->filled('service_type')) {
                $query->where('current_service.service_id', $request->service_type);
            }

            if ($request->filled('employee_id')) {
                $query->where('employees.id', $request->employee_id);
            }

            if ($request->filled('area')) {
                $query->where('clients.area', $request->area);
            }

            if ($request->filled('district_id')) {
                $query->where('clients.district_id', $request->district_id);
            }

            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('clients.name', 'like', "%{$search}%")
                      ->orWhere('clients.client_code', 'like', "%{$search}%")
                      ->orWhere('clients.phone', 'like', "%{$search}%")
                      ->orWhere('clients.email', 'like', "%{$search}%");
                });
            }

            $data = $query->get();

            // CSV Headers
            $headers = [
                'Client Code',
                'Client Name',
                'Primary Phone',
                'Secondary Phone',
                'Primary Email',
                'Secondary Email',
                'Area',
                'District',
                'Service Name',
                'Service Type',
                'Start Date',
                'End Date',
                'Next Invoice Date',
                'Payment Cycle',
                'Service Value',
                'Beds Count',
                'Non-Bedded Type',
                'Employee',
                'Service Status',
                'Days Remaining'
            ];

            $filename = 'client_services_report_' . date('Y-m-d_H-i-s') . '.csv';

            $callback = function() use ($data, $headers) {
                $file = fopen('php://output', 'w');
                fputcsv($file, $headers);

                foreach ($data as $row) {
                    $csvRow = [
                        $row->client_code,
                        $row->client_name,
                        $row->phone,
                        $row->secondary_phone ?: '',
                        $row->email,
                        $row->secondary_email ?: '',
                        $row->area ?: '',
                        $row->district_name ?: '',
                        $row->service_name ?: 'No Service',
                        $row->service_type_name ?: '',
                        $row->start_date ? date('d/m/Y', strtotime($row->start_date)) : '',
                        $row->end_date ? date('d/m/Y', strtotime($row->end_date)) : '',
                        $row->next_invoice_date ? date('d/m/Y', strtotime($row->next_invoice_date)) : '',
                        $row->payment_cycle ?: '',
                        $row->total_price ? '₹' . number_format($row->total_price, 2) : '',
                        $row->beds_count ?: '',
                        $row->non_bedded_type ?: '',
                        $row->employee_name ?: 'Not Assigned',
                        $row->service_status,
                        $row->days_remaining !== null ? $row->days_remaining . ' days' : ''
                    ];
                    fputcsv($file, $csvRow);
                }

                fclose($file);
            };

            return response()->stream($callback, 200, [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
