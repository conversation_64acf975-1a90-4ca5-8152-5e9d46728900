<?php

namespace App\Http\Controllers;

use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Traits\PermissionHelperTrait;

class AdminController extends Controller
{
    use PermissionHelperTrait;
    public function index()
    {
        $roles = Role::all();
        $permissions=Permission::all();
        return view('admin.roles.index', compact('roles','permissions'));
    }
    public function rolePermissions($id)
    {
        $role = Role::findOrFail($id);
        $permissions = Permission::all();
        $rolePermissions = $role->permissions->pluck('name')->toArray(); // Get assigned permissions

        return view('admin.roles.permissions', compact('role', 'permissions', 'rolePermissions'));
    }


    public function createRole(Request $request)
    {
        $request->validate(['name' => 'required|unique:roles,name']);
        Role::create(['name' => $request->name]);
        return back()->with('success', 'Role created successfully.');
    }

    public function assignPermissions(Request $request)
    {
        $role = Role::findOrFail($request->role_id);
        $role->syncPermissions($request->permissions ?? []);

        // Reset permission cache to reflect changes immediately
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        return back()->with('success', 'Permissions updated successfully.');
    }

    /**
     * Clone a role with all its permissions
     */
    public function cloneRole(Request $request)
    {
        $request->validate([
            'source_role_id' => 'required|exists:roles,id',
            'new_role_name' => 'required|unique:roles,name|max:255'
        ]);

        try {
            DB::beginTransaction();

            $sourceRole = Role::findOrFail($request->source_role_id);
            $newRole = Role::create(['name' => $request->new_role_name]);

            // Copy all permissions from source role to new role
            $permissions = $sourceRole->permissions->pluck('name')->toArray();
            $newRole->syncPermissions($permissions);

            // Reset permission cache
            app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

            DB::commit();

            return back()->with('success', "Role '{$request->new_role_name}' created successfully with permissions from '{$sourceRole->name}'.");
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to clone role: ' . $e->getMessage());
        }
    }

    /**
     * Apply permission template to a role
     */
    public function applyPermissionTemplate(Request $request)
    {
        $request->validate([
            'role_id' => 'required|exists:roles,id',
            'template' => 'required|in:admin,manager,employee,viewer'
        ]);

        try {
            $role = Role::findOrFail($request->role_id);
            $permissions = $this->getPermissionTemplate($request->template);

            $role->syncPermissions($permissions);

            // Reset permission cache
            app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

            return back()->with('success', "Permission template '{$request->template}' applied to role '{$role->name}' successfully.");
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to apply permission template: ' . $e->getMessage());
        }
    }

    /**
     * Bulk update permissions for multiple roles
     */
    public function bulkUpdatePermissions(Request $request)
    {
        $request->validate([
            'role_ids' => 'required|array',
            'role_ids.*' => 'exists:roles,id',
            'permissions' => 'array',
            'action' => 'required|in:add,remove,replace'
        ]);

        try {
            DB::beginTransaction();

            $roles = Role::whereIn('id', $request->role_ids)->get();
            $permissions = $request->permissions ?? [];

            foreach ($roles as $role) {
                switch ($request->action) {
                    case 'add':
                        $role->givePermissionTo($permissions);
                        break;
                    case 'remove':
                        $role->revokePermissionTo($permissions);
                        break;
                    case 'replace':
                        $role->syncPermissions($permissions);
                        break;
                }
            }

            // Reset permission cache
            app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

            DB::commit();

            $roleNames = $roles->pluck('name')->implode(', ');
            return back()->with('success', "Permissions {$request->action}ed for roles: {$roleNames}");
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to update permissions: ' . $e->getMessage());
        }
    }

    /**
     * Get permission templates
     */
    private function getPermissionTemplate($template)
    {
        $templates = [
            'admin' => Permission::all()->pluck('name')->toArray(),
            'manager' => [
                'dashboard',
                'client-list', 'client-view', 'client-create', 'client-edit', 'client-export', 'client-change-logs',
                'service-list', 'service-view', 'service-create', 'service-edit', 'service-change-logs',
                'invoice-list', 'invoice-view', 'invoice-create', 'invoice-edit', 'invoice-send', 'invoice-change-logs', 'invoice-cancelled-list',
                'payment-list', 'payment-view', 'payment-create', 'payment-edit', 'payment-history', 'payment-deleted-list',
                'employee-list', 'employee-view', 'employee-change-logs',
                'report-client-services', 'report-payments', 'report-invoices',
                'weight-entry-list', 'weight-entry-create', 'weight-entry-edit',
                'inventory-list', 'inventory-view', 'inventory-history',
                'expense-list', 'expense-view', 'expense-create', 'expense-edit', 'expense-change-logs',
            ],
            'employee' => [
                'dashboard',
                'client-list', 'client-view', 'client-change-logs',
                'service-list', 'service-view', 'service-change-logs',
                'invoice-list', 'invoice-view', 'invoice-change-logs',
                'payment-list', 'payment-view', 'payment-history',
                'weight-entry-list', 'weight-entry-create',
            ],
            'viewer' => [
                'dashboard',
                'client-list', 'client-view',
                'service-list', 'service-view',
                'invoice-list', 'invoice-view',
                'payment-list', 'payment-view',
                'report-client-services', 'report-payments', 'report-invoices',
            ]
        ];

        return $templates[$template] ?? [];
    }

    /**
     * Get role statistics
     */
    public function getRoleStats()
    {
        $stats = [
            'total_roles' => Role::count(),
            'total_permissions' => Permission::count(),
            'roles_with_users' => Role::has('users')->count(),
            'unused_permissions' => Permission::doesntHave('roles')->count(),
        ];

        return response()->json($stats);
    }

}
