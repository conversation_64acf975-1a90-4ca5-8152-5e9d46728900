protected function attemptLogin(Request $request)
{
    $credentials = $this->credentials($request);
    
    // First check if the user exists
    $user = User::where('email', $credentials['email'])->first();
    
    if ($user && $user->status == 0) {
        // User exists but is inactive
        return false;
    }
    
    return $this->guard()->attempt(
        $credentials, $request->filled('remember')
    );
}

protected function sendFailedLoginResponse(Request $request)
{
    $user = User::where('email', $request->email)->first();
    
    if ($user && $user->status == 0) {
        throw ValidationException::withMessages([
            $this->username() => [trans('auth.inactive')],
        ]);
    }
    
    throw ValidationException::withMessages([
        $this->username() => [trans('auth.failed')],
    ]);
}