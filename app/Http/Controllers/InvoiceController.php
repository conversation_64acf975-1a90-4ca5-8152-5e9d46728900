<?php

namespace App\Http\Controllers;

use ZipArchive;
use Carbon\Carbon;
use App\Models\Client;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\ClientService;
use setasign\Fpdi\Fpdi;
use App\Models\Employee;
use Endroid\QrCode\QrCode;
use App\Models\ServiceType;
use Illuminate\Http\Request;
use setasign\Fpdi\PdfReader;
use App\Models\AccountLedger;
use App\Models\InvoiceChangeLog;
use Illuminate\Http\Response;
use App\Models\CancelledInvoice;
use App\Models\EmployeeClient;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\DB;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use App\Jobs\SendInvoiceEmailJob;
use App\Traits\EmailHelperTrait;
use App\Models\InventoryItem;
use App\Models\InventoryCategory;
use App\Models\InvoiceItem;
use App\Models\InventoryTransaction;
use App\Services\NotificationAlertService;

class InvoiceController extends Controller
{
    use EmailHelperTrait;
    //
    public function index()
    {
        $user = Auth::user();
        if ($user->role_id == 3) {
            // Fetch the employee record for the logged-in user
            $employee = Employee::where('user_id', $user->id)->first();

            if ($employee) {
                // Get only the clients assigned to this employee
                $clientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');
                $clients = Client::whereIn('id', $clientIds)->where('status', 1)->get();
            } else {
                // If no employee record found, return an empty collection
                $clients = collect([]);
            }
        } else {
            // For other roles, return all active clients
            $clients = Client::where('status', 1)->get();
        }
        return view('invoices.list', compact('clients'));
    }
    public function getInvoices(Request $request)
    {
        if ($request->ajax()) {
            $user = Auth::user();

            $invoices = Invoice::select(['id', 'client_id', 'invoice_code', 'invoice_date', 'invoice_status', 'total_amount_due','paid_amount','unpaid_amount'])
                ->orderBy('id', 'desc');

            // If user has role_id == 3, fetch only invoices related to their assigned clients
            if ($user->role_id == 3) {
                $employee = Employee::where('user_id', $user->id)->first();

                if ($employee) {
                    $assignedClientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');

                    // Filter invoices to only those belonging to assigned clients
                    $invoices->whereIn('client_id', $assignedClientIds);
                } else {
                    // If the employee record is missing, return an empty list
                    $invoices->whereRaw('0 = 1');
                }
            }
            // Apply Employee Filter
            if ($request->has('employee') && $request->employee != '') {
                $clientIds = DB::table('employee_client')
                    ->where('employee_id', $request->employee)
                    ->pluck('client_id')
                    ->toArray();

                $invoices->whereIn('client_id', $clientIds);
            }
            return DataTables::of($invoices)
                ->addColumn('invoice_status', function ($row) {
                    if ($row->invoice_status == 'Paid') {
                        return '<span class="badge badge-sm text-sm fw-normal text-success-600 bg-success-100 px-20 py-9 radius-4 text-white">Paid</span>';
                    } else if ($row->invoice_status == 'Pending') {
                        return '<span class="badge badge-sm text-sm fw-normal text-danger-600 bg-danger-100 px-20 py-9 radius-4 text-white">Pending</span>';
                    } else if ($row->invoice_status == 'Partially Paid') {
                        return '<span class="badge badge-sm text-sm fw-normal text-warning-600 bg-warning-100 px-20 py-9 radius-4 text-white">Partially Paid</span>';
                    }
                })
                ->addColumn('invoice_type', function ($row) {
                    if ($row->created_type == 2) {
                        return '<span class="badge badge-sm text-sm fw-normal text-info-600 bg-info-100 px-20 py-9 radius-4 text-white">📦 Inventory</span>';
                    } else if ($row->created_type == 1) {
                        return '<span class="badge badge-sm text-sm fw-normal text-secondary-600 bg-secondary-100 px-20 py-9 radius-4 text-white">Manual</span>';
                    } else {
                        return '<span class="badge badge-sm text-sm fw-normal text-primary-600 bg-primary-100 px-20 py-9 radius-4 text-white">Auto</span>';
                    }
                })
                ->addColumn('client_name', function ($row) {
                    return $row->client_id ? $row->client->name : '-';
                })
                ->addColumn('mobile', function ($row) {
                    return $row->client_id ? $row->client->phone : '-';
                })

                ->addColumn('action', function ($row) {
                    $user = auth()->user(); // Get authenticated user
                    $output = '';
                    // Check 'invoice-view' permission
                    if ($user->can('invoice-view')) {
                        $output .=  '
                        <a href="' . route('invoices.show', $row->id) . '" class="w-32-px h-32-px bg-primary-light text-primary-600 rounded-circle d-inline-flex align-items-center justify-content-center">
                            <iconify-icon icon="iconamoon:eye-light"></iconify-icon>
                        </a>';
                    }
                    //delete invoice
                    if ($user->can('invoice-delete')) {
                        $output .=  '
                        <a href="javascript:void(0);" onclick="confirmDelete(\'' . route('invoices.delete', $row->id) . '\',\'' . $row->id . '\')"
                           class="w-32-px h-32-px bg-danger-100 text-danger-600 rounded-circle d-inline-flex align-items-center justify-content-center" title="Delete Invoice">
                            <iconify-icon icon="material-symbols:delete-outline"></iconify-icon>
                        </a>';
                    }
                    $output .=  ' <a href="' . route('invoice.download', $row->id) . '" class="w-32-px h-32-px bg-warning-focus text-warning-main rounded-circle d-inline-flex align-items-center justify-content-center">
                            <iconify-icon icon="lucide:download"></iconify-icon>
                        </a>

                    ';

                    return $output;
                })
                ->filter(function ($query) use ($request) {
                    if ($request->has('searchkey') && $request->searchkey != '') {
                        $query->where('invoice_code', 'like', "%{$request->searchkey}%");
                    }
                    if ($request->has('client') && $request->client != '') {
                        $query->where('client_id', $request->client);
                    }
                    if ($request->has('invoice_status') && $request->invoice_status != '') {
                        $query->where('invoice_status', $request->invoice_status);
                    }
                    if ($request->has('daterange') && !empty($request->daterange)) {
                        $dates = explode(' - ', $request->daterange);
                        if (count($dates) === 2) {
                            $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                            $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                            $query->whereBetween('invoice_date', [$start_date, $end_date]);
                        }
                    }
                })

                ->editColumn('invoice_date', function ($row) {
                    return $row->invoice_date ? date('d-m-Y', strtotime($row->invoice_date)) : 'NA';
                })



                ->rawColumns(['action', 'invoice_status', 'invoice_type']) // Ensure HTML rendering
                ->make(true);
        }
    }

    public function show($id)
    {
        $user = Auth::user();
        $invoice = Invoice::where('id', $id)->first();

        if (!$invoice) {
            abort(404, 'Invoice not found');
        }

        // Restrict access if the user has role_id == 3
        if ($user->role_id == 3) {
            $employee = Employee::where('user_id', $user->id)->first();

            if ($employee) {
                $assignedClientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');

                // If the invoice client_id is not in the assigned client list, deny access
                if (!$assignedClientIds->contains($invoice->client_id)) {
                    abort(Response::HTTP_FORBIDDEN, 'You do not have permission to view this Invoice.');
                }
            } else {
                abort(Response::HTTP_FORBIDDEN, 'You do not have permission to view this Invoice.');
            }
        }

        $qr_url=$this->payment_qr($invoice->unpaid_amount);
        $invoice_payments=Payment::where('invoice_id',$invoice->id)->get();
        return view('invoices.view', compact('invoice','invoice_payments','qr_url'));
    }


    public function downloadPDF($id)
    {
        $invoice = Invoice::findOrFail($id);
        $qr_url=$this->payment_qr($invoice->unpaid_amount);
        $verificationUrl=$this->verify_invoice($invoice->id);
        $pdf = Pdf::loadView('invoices.invoice_template_v2', compact('invoice','qr_url','verificationUrl'))->setPaper('a4', 'portrait');
        $invoice_code = str_replace(['/', '\\'], '-', $invoice->invoice_code);
        return $pdf->download("invoice_{$invoice_code}.pdf");
    }
    public function mail_sent($id)
    {
        try {
            $invoice = Invoice::findOrFail($id);

            // Get the appropriate email address based on environment
            $recipientEmail = $this->getValidatedEmailAddress($invoice->client);

            if (!$recipientEmail) {
                return back()->with('error', 'No valid email address found for this client.');
            }

            // Queue the email job for immediate processing
            SendInvoiceEmailJob::dispatch($id, $recipientEmail);

            // Log the queuing attempt
            $this->logEmailAttempt('invoice', $invoice->invoice_code, $recipientEmail, 'queued', 'Manual send');

            // Log the email sending in change logs
            InvoiceChangeLog::logChange(
                $invoice->id,
                'email_sent',
                Auth::user()->name,
                null,
                ['email_sent_to' => $recipientEmail],
                'Invoice email sent manually to: ' . $recipientEmail
            );

            return back()->with('success', 'Invoice email has been queued for sending to: ' . $recipientEmail);

        } catch (\Exception $e) {
            Log::error("Failed to queue invoice email for invoice ID {$id}: " . $e->getMessage());
            return back()->with('error', 'Failed to queue invoice email. Please try again.');
        }
    }

    /**
     * Get email configuration for the modal
     */
    public function getEmailConfig($id)
    {
        try {
            $invoice = Invoice::findOrFail($id);

            // Get email environment from company settings
            $emailEnvironment = $this->getEmailEnvironment();

            // Get company email from settings
            $companyEmail = DB::table('company_settings')
                            ->where('setting_key', 'email')
                            ->value('setting_value') ?: config('mail.from.address');

            $config = [
                'app_env' => $emailEnvironment,
                'client_email' => $invoice->client->email,
                'tester_email' => $this->getTesterEmail(),
                'sender_name' => config('app.name'),
                'sender_email' => $companyEmail,
                'invoice_code' => $invoice->invoice_code,
                'client_name' => $invoice->client->business_name
            ];

            return response()->json($config);

        } catch (\Exception $e) {
            Log::error("Failed to get email config for invoice ID {$id}: " . $e->getMessage());
            return response()->json(['error' => 'Failed to load email configuration'], 500);
        }
    }

    /**
     * Send email with custom recipient
     */
    public function sendEmailWithCustomRecipient(Request $request, $id)
    {
        try {
            $request->validate([
                'recipient_email' => 'required|email'
            ]);

            $invoice = Invoice::findOrFail($id);
            $recipientEmail = $request->recipient_email;

            // Validate email format
            if (!$this->isValidEmail($recipientEmail)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid email address format.'
                ], 400);
            }

            // For test environments, override with tester email if configured
            $emailEnvironment = $this->getEmailEnvironment();
            if ($emailEnvironment !== 'live') {
                $testerEmail = $this->getTesterEmail();
                if ($testerEmail) {
                    $recipientEmail = $testerEmail;
                    Log::info("Email environment is test, using tester email: {$testerEmail}");
                }
            } else {
                Log::info("Email environment is live, using provided email: {$recipientEmail}");
            }

            // Queue the email job for immediate processing
            SendInvoiceEmailJob::dispatch($id, $recipientEmail);

            // Log the queuing attempt
            $this->logEmailAttempt('invoice', $invoice->invoice_code, $recipientEmail, 'queued', 'Manual send via modal');

            // Log the email sending in change logs
            InvoiceChangeLog::logChange(
                $invoice->id,
                'email_sent',
                Auth::user()->name,
                null,
                ['email_sent_to' => $recipientEmail, 'requested_email' => $request->recipient_email],
                'Invoice email sent manually via modal to: ' . $recipientEmail
            );

            return response()->json([
                'success' => true,
                'message' => 'Invoice email has been queued for sending to: ' . $recipientEmail
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to queue invoice email for invoice ID {$id}: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to queue invoice email. Please try again.'
            ], 500);
        }
    }

    // Print Invoice
    public function print($id)
    {
        $invoice = Invoice::findOrFail($id);
        $qr_url=$this->payment_qr($invoice->unpaid_amount);
        $verificationUrl=$this->verify_invoice($invoice->id);
        $print=true;
        return view('invoices.invoice_template_v2', compact('invoice','qr_url','print','verificationUrl'));
    }
    public function verify($encrypted)
    {
        try {
            $invoiceId = Crypt::decryptString($encrypted);
            $invoice = Invoice::findOrFail($invoiceId);
            $qr_url=$this->payment_qr($invoice->unpaid_amount);
            $verificationUrl=$this->verify_invoice($invoice->id);
        $print=true;
           return view('invoices.invoice_template_v2', compact('invoice','qr_url','print','verificationUrl'));

        } catch (\Exception $e) {
            abort(404, 'Invalid or expired verification link.');
        }
    }
    public function add($client_id = null)
    {
        $clients = Client::where('status', 1)->get();
        $service_types = ServiceType::where('status', 1)->get();

        // If client_id is provided, find the specific client for pre-selection
        $selectedClient = null;
        if ($client_id) {
            $selectedClient = Client::where('id', $client_id)->where('status', 1)->first();
            // If client not found or inactive, redirect to add invoice without pre-selection
            if (!$selectedClient) {
                return redirect()->route('invoices.add')->with('warning', 'Client not found or inactive. Please select a client.');
            }
        }

        return view('invoices.add', compact('clients', 'service_types', 'selectedClient'));
    }
    public function manul_invoice_store(Request $request)
    {
        $request->validate([
            'client' => 'required|exists:clients,id',
            'invoice_date' => 'required|date',
            'service_type' => 'required',
            'service' => 'required',
            'rate' => 'required',
            'qty' => 'required|numeric|min:1',
        ]);

        $client_pending_amount = Client::where('id', $request->client)->first()->pending_amount;

        try {
            DB::beginTransaction();

            // Calculate amounts
            $rate = $request->rate;
            $qty = $request->qty;
            $service_type = $request->service;
            $without_gst = $request->has('without_gst') && $request->without_gst;

            // For different service types, handle rate and qty differently
            if ($service_type == 3) { // Weight-based service
                // For weight-based: rate = rate per kg, qty = weight in kg
                $item_amount = $rate * $qty;
            } else {
                // For other services: standard calculation
                $item_amount = $rate * $qty;
            }

            // Calculate GST based on checkbox
            if ($without_gst) {
                $cgst = 0;  // 0% CGST
                $sgst = 0;  // 0% SGST
            } else {
                $cgst = $item_amount * 0.06;  // 6% CGST
                $sgst = $item_amount * 0.06;  // 6% SGST
            }

            $total_amount = $item_amount + $cgst + $sgst;


            // Get current date
            $currentDate = Carbon::now();

            // Determine financial year
            $year = $currentDate->format('y'); // Last two digits of the current year
            $month = $currentDate->format('m'); // Current month

            if ($month >= 4) {
                $financialYearStart = $year;
                $financialYearEnd = $year + 1;
            } else {
                $financialYearStart = $year - 1;
                $financialYearEnd = $year;
            }

            // Generate fiscal year format (e.g., 24-25)
            $fiscal_year = "$financialYearStart-$financialYearEnd";

            // Get last invoice ID and increment
            $last_invoice = Invoice::latest()->first();
            $next_id = $last_invoice ? $last_invoice->id + 1 : 1;

            // Format invoice_code
            $invoice_code = config('company.details.company_suffix')."/{$fiscal_year}/{$month}/{$next_id}";

            // Save invoice
            $invoice = new Invoice();
            $invoice->client_id = $request->client;
            $invoice->service_type = $request->service_type;
            $invoice->service_id = $request->service;
            $invoice->invoice_date = $request->invoice_date;
            $invoice->invoice_status = 'Pending';

            // Handle different service types for unit_price and quantity fields
            if ($service_type == 3) { // Weight-based service
                $invoice->unit_price = $rate; // Rate per kg
                $invoice->weight_qty = $qty; // Weight in kg
                $invoice->beds_count = null; // Not applicable for weight-based
            } elseif ($service_type == 1 || $service_type == 4) { // Bedded or Bedded with Fixed Price
                $invoice->unit_price = $rate;
                $invoice->beds_count = $qty; // Number of units/beds
                $invoice->weight_qty = null; // Not applicable
            } else { // Non-bedded service (service_type == 2)
                $invoice->unit_price = $rate; // Total rate
                $invoice->beds_count = $qty; // Usually 1 for non-bedded
                $invoice->weight_qty = null; // Not applicable
            }

            $invoice->gross_amount = $item_amount;
            $invoice->cgst_amount = $cgst;
            $invoice->sgst_amount = $sgst;
            $invoice->total_amount_due = $total_amount;
            $invoice->unpaid_amount = $total_amount;
            $invoice->created_by = Auth::user()->name;
            $invoice->client_pending_amount_before_invoice = $client_pending_amount;
            $invoice->grand_total = $client_pending_amount + $total_amount;
            $invoice->invoice_code = $invoice_code; // Store generated invoice code
            $invoice->created_type = 1;

            // Add GST status to comments
            $comments = $request->comments;
            if ($without_gst) {
                $comments .= ($comments ? ' | ' : '') . '[WITHOUT GST]';
            }
            $invoice->comments = $comments;

            $invoice->save();

            // Log the manual invoice creation
            InvoiceChangeLog::logChange(
                $invoice->id,
                'created',
                Auth::user()->name,
                null,
                $invoice->toArray(),
                'Invoice created manually via add invoice form'
            );

            // Queue email for the manually created invoice if requested
            if ($request->has('send_email') && $request->send_email) {
                $this->queueInvoiceEmail($invoice->id, $request->client, true); // true = manual invoice
            }

            // Send notification alert for manual invoice creation
            $notificationService = new NotificationAlertService();
            $notificationService->sendInvoiceCreationAlert($invoice, true); // true = manual creation

            DB::table('clients')->where('id',$request->client)->increment('pending_amount', $total_amount);
            AccountLedger::create([
                'client_id' => $request->client,
                'transaction' => 'Inovice',
                'transaction_date' => $request->invoice_date,
                'detials' => 'Manual Invoice ' . $invoice_code,
                'amount' => $total_amount,
                'payments' => NULL,
                'balance' => $client_pending_amount + $total_amount
            ]);
            DB::commit();

            // Prepare success message based on email option
            $message = 'Invoice created successfully.';
            if ($request->has('send_email') && $request->send_email) {
                $message .= ' Email has been queued for sending.';
            }

            return redirect()->route('invoices.index')->with('success', $message);
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Error saving invoice: ' . $e->getMessage());
        }
    }
    public function getPendingInvoices(Request $request)
    {
        $invoices = Invoice::where('client_id', $request->client_id)
            ->where('unpaid_amount', '>', 0)
            ->get(['id', 'invoice_code', 'unpaid_amount']);

        return response()->json($invoices);
    }

    /**
     * Calculate weight range breakdown for floating range based pricing
     */
    private function calculateWeightRangeBreakdown($totalWeight, $weightRanges)
    {
        $breakdown = [];
        $processedWeight = 0;

        // Sort ranges by from_kg to ensure proper order
        $sortedRanges = $weightRanges->sortBy('from_kg');

        foreach ($sortedRanges as $range) {
            // Check if we still have weight to process
            if ($processedWeight >= $totalWeight) {
                break;
            }

            $rangeStart = $range->from_kg;
            $rangeEnd = $range->to_kg;

            // Skip if total weight doesn't reach this range
            if ($totalWeight <= $rangeStart) {
                continue;
            }

            // Simple and clear logic:
            // Range 0-100: first 100 KG (weight 1-100)
            // Range 101-200: next 100 KG (weight 101-200)
            // etc.

            $remainingWeight = $totalWeight - $processedWeight;

            // Calculate range capacity (how much weight this range can handle)
            $rangeCapacity = $rangeEnd - $rangeStart + 1;

            // For the first range starting at 0, treat it as having capacity equal to rangeEnd
            if ($rangeStart == 0) {
                $rangeCapacity = $rangeEnd;
            }

            // Calculate how much weight goes into this range
            $weightInThisRange = min($remainingWeight, $rangeCapacity);

            if ($weightInThisRange > 0) {
                $amount = $weightInThisRange * $range->cost;

                $breakdown[] = [
                    'from_kg' => $rangeStart,
                    'to_kg' => $rangeEnd,
                    'weight' => $weightInThisRange,
                    'rate' => $range->cost,
                    'amount' => $amount,
                    'description' => "Weight Range: {$rangeStart}-{$rangeEnd} KG ({$weightInThisRange} KG)"
                ];

                $processedWeight += $weightInThisRange;
            }
        }

        return $breakdown;
    }

    /**
     * Calculate next invoice date for existing service after invoice generation
     */
    private function calculateNextInvoiceDateForExistingService($currentNextInvoiceDate, $paymentCycle, $invoiceGenerationType, $customInvoiceDay = null, $endDate = null)
    {
        $currentDate = Carbon::parse($currentNextInvoiceDate);

        if ($paymentCycle == 'Monthly') {
            switch ($invoiceGenerationType) {
                case 1: // End of month
                    $newDate = $currentDate->copy()->addMonthsNoOverflow(1)->endOfMonth();
                    break;

                case 2: // Based on start date (monthly recurring)
                    $newDate = $currentDate->copy()->addMonthsNoOverflow(1);
                    break;

                case 3: // Custom day
                    if (!$customInvoiceDay || $customInvoiceDay < 1 || $customInvoiceDay > 31) {
                        // Fallback to end of month if invalid custom day
                        $newDate = $currentDate->copy()->addMonthsNoOverflow(1)->endOfMonth();
                    } else {
                        // Move to next month and set custom day
                        $newDate = $currentDate->copy()->addMonthsNoOverflow(1);
                        $year = $newDate->year;
                        $month = $newDate->month;

                        // Check if custom day exists in the target month
                        if (checkdate($month, $customInvoiceDay, $year)) {
                            $newDate->day($customInvoiceDay);
                        } else {
                            // If custom day doesn't exist (e.g., Feb 30), use last day of month
                            $newDate = $newDate->endOfMonth();
                        }
                    }
                    break;

                default:
                    $newDate = $currentDate->copy()->addMonthsNoOverflow(1)->endOfMonth();
            }
        } else { // Yearly
            switch ($invoiceGenerationType) {
                case 1: // End of month (yearly) - next invoice at end of same month next year
                    $newDate = $currentDate->copy()->addYearsNoOverflow(1)->endOfMonth();
                    break;

                case 2: // Based on start date (yearly) - next invoice on same date next year
                    $newDate = $currentDate->copy()->addYearsNoOverflow(1);
                    break;

                case 3: // Custom day (yearly) - next invoice on custom day of same month next year
                    $newDate = $currentDate->copy()->addYearsNoOverflow(1);
                    if ($customInvoiceDay && $customInvoiceDay >= 1 && $customInvoiceDay <= 31) {
                        $year = $newDate->year;
                        $month = $newDate->month;

                        // Check if custom day exists in the target month next year
                        if (checkdate($month, $customInvoiceDay, $year)) {
                            $newDate->day($customInvoiceDay);
                        } else {
                            // If custom day doesn't exist (e.g., Feb 29 in non-leap year), use end of month
                            $newDate = $newDate->endOfMonth();
                        }
                    }
                    break;

                default:
                    $newDate = $currentDate->copy()->addYearsNoOverflow(1);
            }
        }

        // Check if the new date exceeds the service end date
        if ($endDate && $newDate->greaterThan(Carbon::parse($endDate))) {
            return Carbon::parse($endDate);
        }

        return $newDate;
    }

    /**
     * Calculate the start date for the first invoice based on invoice generation type
     */
    private function calculateFirstInvoiceStartDate($service, $nextInvoiceDate)
    {
        $serviceStartDate = Carbon::parse($service->start_date);
        $invoiceDate = Carbon::parse($nextInvoiceDate);

        Log::info('=== FIRST INVOICE START DATE CALCULATION ===', [
            'service_start_date' => $serviceStartDate->toDateString(),
            'invoice_date' => $invoiceDate->toDateString(),
            'invoice_generation_type' => $service->invoice_generation_type,
            'custom_invoice_day' => $service->custom_invoice_day
        ]);

        switch ($service->invoice_generation_type) {
            case 1: // End of month
                // Start date is the beginning of the month for the invoice date
                $startDate = $invoiceDate->copy()->startOfMonth();

                // Ensure start date is not before service start date
                if ($startDate->lt($serviceStartDate)) {
                    $startDate = $serviceStartDate;
                }
                break;

            case 2: // Based on start date
                // For first invoice, start from service start date
                // For subsequent invoices, calculate based on service start date pattern
                $monthsDiff = $serviceStartDate->diffInMonths($invoiceDate);

                if ($monthsDiff == 0) {
                    // First month - start from service start date
                    $startDate = $serviceStartDate;
                } else {
                    // Subsequent months - start from service start date + (months-1)
                    $startDate = $serviceStartDate->copy()->addMonths($monthsDiff - 1);

                    // Ensure start date is not after invoice date
                    if ($startDate->gt($invoiceDate)) {
                        $startDate = $serviceStartDate;
                    }
                }
                break;

            case 3: // Custom day
                // Start date should be from the custom day of previous month
                $customDay = $service->custom_invoice_day;

                // Get the previous month and set the custom day
                $startDate = $invoiceDate->copy()->subMonth()->day($customDay);

                // Ensure start date is not before service start date
                if ($startDate->lt($serviceStartDate)) {
                    $startDate = $serviceStartDate;
                }
                break;

            default:
                // Fallback to service start date
                $startDate = $serviceStartDate;
                break;
        }



        return $startDate;
    }

    /**
     * Calculate invoice period for manually created invoices
     */
    private function calculateManualInvoicePeriod($request)
    {
        $invoiceDate = Carbon::parse($request->invoice_date);

        // Get the service details
        $service = ClientService::where('client_id', $request->client)
            ->where('service_id', $request->service)
            ->where('service_type', $request->service_type)
            ->where('status', 1)
            ->first();

        if (!$service) {
            // Fallback: use invoice date as both start and end
            return [
                'period' => $invoiceDate->format('d-m-Y') . ' To ' . $invoiceDate->format('d-m-Y'),
                'days' => 1
            ];
        }

        $serviceStartDate = Carbon::parse($service->start_date);
        $serviceEndDate = $service->end_date ? Carbon::parse($service->end_date) : null;

        // Check for existing invoices for this service instance
        $query = DB::table('invoices')
            ->where('client_id', $request->client)
            ->where('service_id', $request->service)
            ->where('service_type', $request->service_type)
            ->where('invoice_date', '>=', $serviceStartDate->toDateString())
            ->where('invoice_date', '<', $invoiceDate->toDateString());

        if ($serviceEndDate) {
            $query->where('invoice_date', '<=', $serviceEndDate->toDateString());
        }

        $existingInvoices = $query->count();

        if ($existingInvoices == 0) {
            // First invoice: use first invoice logic
            $startDate = $this->calculateFirstInvoiceStartDate($service, $invoiceDate->toDateString());
        } else {
            // Subsequent invoice: use subsequent invoice logic
            $startDate = $this->calculateSubsequentInvoiceStartDate(
                $service,
                $invoiceDate->toDateString(),
                $invoiceDate,
                $serviceStartDate,
                $serviceEndDate
            );
        }

        $days = $startDate->diffInDays($invoiceDate) + 1;
        $period = $startDate->format('d-m-Y') . ' To ' . $invoiceDate->format('d-m-Y');

        return [
            'period' => $period,
            'days' => $days
        ];
    }

    /**
     * Calculate the start date for subsequent invoices based on invoice generation type
     */
    private function calculateSubsequentInvoiceStartDate($service, $nextInvoiceDate, $currentInvoiceDate, $serviceStartDate, $serviceEndDate)
    {
        $invoiceDate = Carbon::parse($nextInvoiceDate);

        Log::info('=== SUBSEQUENT INVOICE START DATE CALCULATION ===', [
            'service_start_date' => $serviceStartDate->toDateString(),
            'invoice_date' => $invoiceDate->toDateString(),
            'current_invoice_date' => $currentInvoiceDate->toDateString(),
            'invoice_generation_type' => $service->invoice_generation_type,
            'custom_invoice_day' => $service->custom_invoice_day
        ]);

        switch ($service->invoice_generation_type) {
            case 1: // End of month
                // For end of month type, subsequent invoices start from beginning of the month
                $startDate = $invoiceDate->copy()->startOfMonth();

                // Ensure start date is not before service start date
                if ($startDate->lt($serviceStartDate)) {
                    $startDate = $serviceStartDate;
                }
                break;

            case 2: // Based on start date
                // For subsequent invoices, continue from the day after the last invoice
                // to ensure continuous coverage without gaps or overlaps
                $lastInvoiceQuery = DB::table('invoices')
                    ->where('client_id', $service->client_id)
                    ->where('service_id', $service->service_id)
                    ->where('service_type', $service->service_type)
                    ->where('invoice_date', '>=', $serviceStartDate->toDateString())
                    ->where('invoice_date', '<', $currentInvoiceDate->toDateString());

                if ($serviceEndDate) {
                    $lastInvoiceQuery->where('invoice_date', '<=', $serviceEndDate->toDateString());
                }

                $lastInvoice = $lastInvoiceQuery->orderBy('invoice_date', 'desc')->first();

                Log::info('=== TYPE 2 BASED ON START DATE SUBSEQUENT LOGIC ===', [
                    'last_invoice_found' => $lastInvoice ? true : false,
                    'last_invoice_id' => $lastInvoice ? $lastInvoice->id : null,
                    'last_invoice_period' => $lastInvoice ? $lastInvoice->from_invoice_to : null
                ]);

                if ($lastInvoice && $lastInvoice->from_invoice_to) {
                    if (preg_match('/To\s+(\d{2}-\d{2}-\d{4})/', $lastInvoice->from_invoice_to, $matches)) {
                        try {
                            $lastEndDateCarbon = Carbon::createFromFormat('d-m-Y', $matches[1]);
                            $startDate = $lastEndDateCarbon->addDay();

                            Log::info('=== TYPE 2 USING PREVIOUS INVOICE END DATE ===', [
                                'last_invoice_end_date' => $matches[1],
                                'calculated_start_date' => $startDate->toDateString()
                            ]);
                        } catch (\Exception $e) {
                            // Fallback to service start date
                            $startDate = $serviceStartDate;

                            Log::info('=== TYPE 2 FALLBACK TO SERVICE START DATE (PARSE ERROR) ===', [
                                'calculated_start_date' => $startDate->toDateString(),
                                'error' => $e->getMessage()
                            ]);
                        }
                    } else {
                        // Fallback to service start date
                        $startDate = $serviceStartDate;

                        Log::info('=== TYPE 2 FALLBACK TO SERVICE START DATE (NO MATCH) ===', [
                            'calculated_start_date' => $startDate->toDateString()
                        ]);
                    }
                } else {
                    // No previous invoice found, use service start date
                    $startDate = $serviceStartDate;

                    Log::info('=== TYPE 2 FALLBACK TO SERVICE START DATE (NO PREVIOUS INVOICE) ===', [
                        'calculated_start_date' => $startDate->toDateString()
                    ]);
                }
                break;

            case 3: // Custom day
                // For custom day type, subsequent invoices should continue from the day after the last invoice
                // to avoid overlapping billing periods
                $lastInvoiceQuery = DB::table('invoices')
                    ->where('client_id', $service->client_id)
                    ->where('service_id', $service->service_id)
                    ->where('service_type', $service->service_type)
                    ->where('invoice_date', '>=', $serviceStartDate->toDateString())
                    ->where('invoice_date', '<', $currentInvoiceDate->toDateString());

                if ($serviceEndDate) {
                    $lastInvoiceQuery->where('invoice_date', '<=', $serviceEndDate->toDateString());
                }

                $lastInvoice = $lastInvoiceQuery->orderBy('invoice_date', 'desc')->first();

                Log::info('=== TYPE 3 CUSTOM DAY SUBSEQUENT LOGIC ===', [
                    'last_invoice_query_conditions' => [
                        'client_id' => $service->client_id,
                        'service_id' => $service->service_id,
                        'service_type' => $service->service_type,
                        'invoice_date_gte' => $serviceStartDate->toDateString(),
                        'invoice_date_lt' => $currentInvoiceDate->toDateString()
                    ],
                    'last_invoice_found' => $lastInvoice ? true : false,
                    'last_invoice_id' => $lastInvoice ? $lastInvoice->id : null,
                    'last_invoice_period' => $lastInvoice ? $lastInvoice->from_invoice_to : null
                ]);

                if ($lastInvoice && $lastInvoice->from_invoice_to) {
                    if (preg_match('/To\s+(\d{2}-\d{2}-\d{4})/', $lastInvoice->from_invoice_to, $matches)) {
                        try {
                            $lastEndDateCarbon = Carbon::createFromFormat('d-m-Y', $matches[1]);
                            $startDate = $lastEndDateCarbon->addDay();

                            Log::info('=== TYPE 3 USING PREVIOUS INVOICE END DATE ===', [
                                'last_invoice_end_date' => $matches[1],
                                'calculated_start_date' => $startDate->toDateString()
                            ]);
                        } catch (\Exception $e) {
                            // Fallback to custom day logic
                            $customDay = $service->custom_invoice_day;
                            $startDate = $invoiceDate->copy()->subMonth()->day($customDay);
                            if ($startDate->lt($serviceStartDate)) {
                                $startDate = $serviceStartDate;
                            }

                            Log::info('=== TYPE 3 FALLBACK TO CUSTOM DAY (PARSE ERROR) ===', [
                                'custom_day' => $customDay,
                                'calculated_start_date' => $startDate->toDateString(),
                                'error' => $e->getMessage()
                            ]);
                        }
                    } else {
                        // Fallback to custom day logic
                        $customDay = $service->custom_invoice_day;
                        $startDate = $invoiceDate->copy()->subMonth()->day($customDay);
                        if ($startDate->lt($serviceStartDate)) {
                            $startDate = $serviceStartDate;
                        }

                        Log::info('=== TYPE 3 FALLBACK TO CUSTOM DAY (NO MATCH) ===', [
                            'custom_day' => $customDay,
                            'calculated_start_date' => $startDate->toDateString()
                        ]);
                    }
                } else {
                    // No previous invoice found, use custom day logic
                    $customDay = $service->custom_invoice_day;
                    $startDate = $invoiceDate->copy()->subMonth()->day($customDay);
                    if ($startDate->lt($serviceStartDate)) {
                        $startDate = $serviceStartDate;
                    }

                    Log::info('=== TYPE 3 FALLBACK TO CUSTOM DAY (NO PREVIOUS INVOICE) ===', [
                        'custom_day' => $customDay,
                        'calculated_start_date' => $startDate->toDateString()
                    ]);
                }
                break;

            default:
                // Fallback: find the last invoice and continue from there
                $lastInvoiceQuery = DB::table('invoices')
                    ->where('client_id', $service->client_id)
                    ->where('service_id', $service->service_id)
                    ->where('service_type', $service->service_type)
                    ->where('invoice_date', '>=', $serviceStartDate->toDateString())
                    ->where('invoice_date', '<', $currentInvoiceDate->toDateString());

                if ($serviceEndDate) {
                    $lastInvoiceQuery->where('invoice_date', '<=', $serviceEndDate->toDateString());
                }

                $lastInvoice = $lastInvoiceQuery->orderBy('invoice_date', 'desc')->first();

                if ($lastInvoice && $lastInvoice->from_invoice_to) {
                    if (preg_match('/To\s+(\d{2}-\d{2}-\d{4})/', $lastInvoice->from_invoice_to, $matches)) {
                        try {
                            $lastEndDateCarbon = Carbon::createFromFormat('d-m-Y', $matches[1]);
                            $startDate = $lastEndDateCarbon->addDay();
                        } catch (\Exception $e) {
                            $startDate = $serviceStartDate;
                        }
                    } else {
                        $startDate = $serviceStartDate;
                    }
                } else {
                    $startDate = $serviceStartDate;
                }
                break;
        }

        return $startDate;
    }

    /**
     * Calculate the proper end date for an invoice based on invoice generation type
     */
    private function calculateInvoiceEndDate($service, $nextInvoiceDate)
    {
        $invoiceDate = Carbon::parse($nextInvoiceDate);

        switch ($service->invoice_generation_type) {
            case 1: // End of month
                // For end of month, end date is always the last day of the month
                return $invoiceDate->copy()->endOfMonth();

            case 2: // Based on start date
                // For start date based, end date is the invoice date itself
                return $invoiceDate->copy();

            case 3: // Custom day
                // For custom day, end date should be the custom day of the current month
                // But if we're already on the custom day, it should be the custom day of next month
                $customDay = $service->custom_invoice_day;

                // If current invoice date is the custom day, use it
                if ($invoiceDate->day == $customDay) {
                    return $invoiceDate->copy();
                } else {
                    // Otherwise, find the next occurrence of the custom day
                    $endDate = $invoiceDate->copy()->day($customDay);

                    // If the custom day is before the invoice date in the same month,
                    // move to next month
                    if ($endDate->lt($invoiceDate)) {
                        $endDate = $invoiceDate->copy()->addMonth()->day($customDay);
                    }

                    return $endDate;
                }

            default:
                // Fallback to invoice date
                return $invoiceDate->copy();
        }
    }

    public function generateInvoices($client_service_id = null)
    {
        // Log immediately when method is called
        Log::info('=== GENERATE INVOICES METHOD CALLED ===', [
            'client_service_id' => $client_service_id,
            'timestamp' => now(),
            'request_method' => request()->method(),
            'request_url' => request()->fullUrl()
        ]);

        try {
            DB::beginTransaction();

            // Log the start of invoice generation
            Log::info('Invoice generation started', [
                'client_service_id' => $client_service_id,
                'timestamp' => now()
            ]);

            // Fetch eligible client services using Eloquent to get relationships
            $query = ClientService::with(['weightCostRanges', 'client'])
                ->where('status', 1)
                ->whereNotNull('next_invoice_date');

            if ($client_service_id) {
                $query->where('id', $client_service_id); // Fetch only the given client service (allow future dates for manual generation)
            } else {
                $query->whereDate('next_invoice_date', '<=', Carbon::today()); // Auto-generate invoices for due services only
            }

            $clientServices = $query->where(function ($query) {
                $query->whereColumn('end_date', '>=', 'next_invoice_date')
                    ->orWhereNull('end_date');
            })->get();

            // If specific client service ID was provided but no services found, return error
            if ($client_service_id && $clientServices->isEmpty()) {
                $service = ClientService::find($client_service_id);

                Log::info('Service validation for specific ID', [
                    'client_service_id' => $client_service_id,
                    'service_found' => $service ? true : false,
                    'service_status' => $service ? $service->status : null,
                    'next_invoice_date' => $service ? $service->next_invoice_date : null,
                    'end_date' => $service ? $service->end_date : null
                ]);

                if (!$service) {
                    return response()->json(['error' => 'Service not found.'], 404);
                }
                if ($service->status != 1) {
                    return response()->json(['error' => 'Service is not active.'], 400);
                }
                if (!$service->next_invoice_date) {
                    return response()->json(['error' => 'Service does not have a next invoice date set.'], 400);
                }
                if ($service->end_date && Carbon::parse($service->end_date)->lessThan(Carbon::parse($service->next_invoice_date))) {
                    return response()->json(['error' => 'Service has ended before the next invoice date.'], 400);
                }

                // Log that service passed basic validation but wasn't included in query
                Log::warning('Service passed validation but not included in query', [
                    'client_service_id' => $client_service_id,
                    'service_status' => $service->status,
                    'next_invoice_date' => $service->next_invoice_date,
                    'today' => Carbon::today()->toDateString(),
                    'next_invoice_date_comparison' => Carbon::parse($service->next_invoice_date)->format('Y-m-d') . ' vs ' . Carbon::today()->format('Y-m-d')
                ]);
            }

            $invoicesGenerated = 0;
            $skippedServices = [];

            foreach ($clientServices as $service) {
                Log::info('Processing service for invoice generation', [
                    'service_id' => $service->id,
                    'client_name' => $service->client->name,
                    'service_type' => $service->service_id,
                    'next_invoice_date' => $service->next_invoice_date
                ]);

                //check if any service comes under weight based
                if($service->service_id == 3){
                    //check if weight entry exists for the client for the month of next_invoice_date
                    $weightEntry = DB::table('client_service_weights')
                    ->where('client_id', $service->client_id)
                    ->where('month', Carbon::parse($service->next_invoice_date)->month)
                    ->where('year', Carbon::parse($service->next_invoice_date)->year)
                    ->first();

                    Log::info('Weight-based service check', [
                        'service_id' => $service->id,
                        'client_id' => $service->client_id,
                        'month' => Carbon::parse($service->next_invoice_date)->month,
                        'year' => Carbon::parse($service->next_invoice_date)->year,
                        'weight_entry_found' => $weightEntry ? true : false
                    ]);

                    if(!$weightEntry){
                        $skippedServices[] = [
                            'client_name' => $service->client->business_name ?? $service->client->name,
                            'reason' => 'No weight entry found for ' . Carbon::parse($service->next_invoice_date)->format('F Y')
                        ];
                        Log::info('Service skipped due to missing weight entry', [
                            'service_id' => $service->id,
                            'reason' => 'No weight entry found for ' . Carbon::parse($service->next_invoice_date)->format('F Y')
                        ]);
                        continue;
                    }
                }
                // Get the invoice date from service
                $invoiceDate = Carbon::parse($service->next_invoice_date);

                // Determine financial year
                $year = $invoiceDate->format('y'); // Last two digits of the year
                $month = $invoiceDate->format('m'); // Extract month from given date

                // Financial year logic: If the month is April (04) or later, it belongs to the current financial year
                if ($month >= 4) {
                    $financialYearStart = $year;
                    $financialYearEnd = $year + 1;
                } else {
                    $financialYearStart = $year - 1;
                    $financialYearEnd = $year;
                }

                // Generate invoice code
                $invoiceId = DB::table('invoices')->max('id') + 1;
                $invoice_code = config('company.details.company_suffix')."/$financialYearStart-$financialYearEnd/$month/$invoiceId";

                // Set invoice_start_date and invoice_end_date
                $nextInvoiceDate = Carbon::parse($service->next_invoice_date);
                if ($service->payment_cycle == 'Monthly') {
                    // Calculate proper end date based on invoice generation type
                    $endDate = $this->calculateInvoiceEndDate($service, $nextInvoiceDate);

                    Log::info('=== END DATE CALCULATION ===', [
                        'next_invoice_date' => $nextInvoiceDate->toDateString(),
                        'invoice_generation_type' => $service->invoice_generation_type,
                        'custom_invoice_day' => $service->custom_invoice_day,
                        'calculated_end_date' => $endDate->toDateString()
                    ]);

                    // Check if this is the first invoice for this specific service INSTANCE
                    // Use service start date and end date to identify invoices for this specific service instance
                    $serviceStartDate = Carbon::parse($service->start_date);
                    $serviceEndDate = $service->end_date ? Carbon::parse($service->end_date) : null;

                    // Only consider invoices that come BEFORE the current invoice date
                    $currentInvoiceDate = Carbon::parse($service->next_invoice_date);

                    $query = DB::table('invoices')
                        ->where('client_id', $service->client_id)
                        ->where('service_id', $service->service_id)
                        ->where('service_type', $service->service_type)
                        ->where('invoice_date', '>=', $serviceStartDate->toDateString())
                        ->where('invoice_date', '<', $currentInvoiceDate->toDateString()); // Only invoices BEFORE current invoice date

                    // If service has end date, only consider invoices before or on end date
                    if ($serviceEndDate) {
                        $query->where('invoice_date', '<=', $serviceEndDate->toDateString());
                    }

                    $existingInvoicesForThisServiceInstance = $query->count();

                    if ($existingInvoicesForThisServiceInstance == 0) {
                        // First invoice for this service instance: calculate start date based on invoice generation type
                        $startDate = $this->calculateFirstInvoiceStartDate($service, $nextInvoiceDate);
                    } else {
                        // Subsequent invoices: calculate start date based on invoice generation type
                        $startDate = $this->calculateSubsequentInvoiceStartDate($service, $nextInvoiceDate, $currentInvoiceDate, $serviceStartDate, $serviceEndDate);
                    }
                } else { // Yearly
                    $startDate = $nextInvoiceDate;
                    $endDate = $nextInvoiceDate->copy()->addYearsNoOverflow(1)->subDay();
                }

                // DEBUG: Check if we reach this point
                Log::info('=== REACHED DAYS CALCULATION SECTION ===', [
                    'start_date' => $startDate->toDateString(),
                    'end_date' => $endDate->toDateString()
                ]);

                // Calculate number of days using a more robust method
                // Create clean date objects to avoid any timezone/format issues
                $cleanStartDate = Carbon::createFromFormat('Y-m-d', $startDate->format('Y-m-d'));
                $cleanEndDate = Carbon::createFromFormat('Y-m-d', $endDate->format('Y-m-d'));

                // Use day-by-day counting for accuracy
                $dayCount = 0;
                $current = $cleanStartDate->copy();
                while ($current->lte($cleanEndDate)) {
                    $dayCount++;
                    $current->addDay();
                }

                $daysForInvoice = $dayCount;

                // Debug logging
                Log::info('=== ROBUST DAYS CALCULATION ===', [
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                    'old_diff_days' => $startDate->diffInDays($endDate),
                    'old_calculation' => $startDate->diffInDays($endDate) + 1,
                    'new_day_count' => $dayCount,
                    'period' => $startDate->format('d-m-Y') . ' To ' . $endDate->format('d-m-Y')
                ]);

                // Calculate gross amount
                if ($service->service_id == 1) {
                    $grossAmount = $service->beds_count * $service->unit_price * $daysForInvoice;

                    if ($service->payment_cycle == 'Monthly') {
                    //     if ($grossAmount < 2000) {
                    //         $grossAmount = 2000;
                    //     }
                    // } else {
                    //     if ($grossAmount < 24000) {
                    //         $grossAmount = 24000;
                    //     }
                    }
                } elseif ($service->service_id == 4) { // Bedded with Fixed Price
                    // For bedded with fixed price, use the total_price directly (like non-bedded)
                    // but store beds_count for reference
                    $grossAmount = $service->total_price;
                } elseif($service->service_id == 3){
                    if($service->weight_cost_type == 1){
                        $grossAmount = $service->weight_cost * $weightEntry->weight;
                    }elseif($service->weight_cost_type == 2){
                        // Fixed range based - calculate breakdown for invoice items (same as floating range)
                        $weightBreakdown = $this->calculateWeightRangeBreakdown($weightEntry->weight, $service->weightCostRanges);
                        $grossAmount = array_sum(array_column($weightBreakdown, 'amount'));
                    }elseif($service->weight_cost_type == 3){
                        // Floating range based - calculate breakdown for invoice items
                        $weightBreakdown = $this->calculateWeightRangeBreakdown($weightEntry->weight, $service->weightCostRanges);
                        $grossAmount = array_sum(array_column($weightBreakdown, 'amount'));
                    }elseif($service->weight_cost_type == 4){
                        // Fixed with Minimum Qty calculation
                        if($weightEntry->weight < $service->minimum_kgs){
                            // If weight is less than minimum, charge minimum cost only
                            $grossAmount = $service->minimum_cost;
                        }else{
                            // If weight is equal or more than minimum, charge based on actual weight
                            $grossAmount = $weightEntry->weight * $service->weight_cost;
                        }
                    }
                 }
                 else {
                    $grossAmount = $service->total_price;
                }



                // Calculate taxes and totals
                $cgstAmount = $grossAmount * 0.06;
                $sgstAmount = $grossAmount * 0.06;
                $totalAmountDue = $grossAmount + $cgstAmount + $sgstAmount;

                // Fetch client pending amount
                $clientPendingAmount = DB::table('clients')->where('id', $service->client_id)->value('pending_amount');
                $grandTotal = $totalAmountDue + $clientPendingAmount;
                // dd( $service,$grossAmount);
                // Insert into invoices table
                $invoiceData = [
                    'client_id' => $service->client_id,
                    'service_id' => $service->service_id,
                    'service_type' => $service->service_type,
                    'invoice_code' => $invoice_code,
                    'invoice_date' => $service->next_invoice_date,
                    'invoice_status' => 'Pending',
                    'gross_amount' => $grossAmount,
                    'cgst_amount' => $cgstAmount,
                    'sgst_amount' => $sgstAmount,
                    'total_amount_due' => $totalAmountDue,
                    'unpaid_amount' => $totalAmountDue,
                    'created_by' => 'SYSTEM',
                    'from_invoice_to' => $startDate->format('d-m-Y') . " To " . $endDate->format('d-m-Y'),
                    'days_for_invoice' => $daysForInvoice,
                    'client_pending_amount_before_invoice' => $clientPendingAmount,
                    'grand_total' => $grandTotal ? $grandTotal : 0,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                // Handle service-specific fields
                if ($service->service_id == 3) { // Weight-based service
                    $invoiceData['weight_qty'] = $weightEntry->weight ?? 0;

                    // For range-based services, calculate average rate
                    if ($service->weight_cost_type == 2 || $service->weight_cost_type == 3) {
                        // Calculate average rate from total amount and weight
                        $invoiceData['unit_price'] = $weightEntry->weight > 0 ? round($grossAmount / $weightEntry->weight, 2) : 0;
                    } elseif ($service->weight_cost_type == 4) {
                        // For fixed with minimum qty, calculate effective rate
                        if ($weightEntry->weight < $service->minimum_kgs) {
                            // When minimum cost is applied, show effective rate
                            $invoiceData['unit_price'] = $weightEntry->weight > 0 ? round($grossAmount / $weightEntry->weight, 2) : 0;
                        } else {
                            // When weight-based calculation is used, show actual rate
                            $invoiceData['unit_price'] = $service->weight_cost ?? 0;
                        }
                    } else {
                        // For fixed cost type, use the weight_cost directly
                        $invoiceData['unit_price'] = $service->weight_cost ?? 0;
                    }

                    $invoiceData['beds_count'] = null;
                } elseif ($service->service_id == 1 || $service->service_id == 4) { // Bedded or Bedded with Fixed Price
                    $invoiceData['beds_count'] = $service->beds_count;
                    $invoiceData['unit_price'] = $service->unit_price;
                    $invoiceData['weight_qty'] = null;
                } else { // Non-bedded service
                    $invoiceData['beds_count'] = null;
                    $invoiceData['unit_price'] = $grossAmount; // Total amount for non-bedded
                    $invoiceData['weight_qty'] = null;
                }

                $invoiceId = DB::table('invoices')->insertGetId($invoiceData);

                // Create invoice items for weight-based services with range pricing (both fixed and floating)
                if ($service->service_id == 3 && ($service->weight_cost_type == 2 || $service->weight_cost_type == 3)) {
                    $weightBreakdown = $this->calculateWeightRangeBreakdown($weightEntry->weight, $service->weightCostRanges);

                    foreach ($weightBreakdown as $index => $breakdown) {
                        $invoiceItem = new InvoiceItem();
                        $invoiceItem->invoice_id = $invoiceId;
                        $invoiceItem->inventory_item_id = 0; // Not an inventory item
                        $invoiceItem->item_name = $breakdown['description'];
                        $invoiceItem->item_sku = 'WEIGHT-RANGE-' . ($index + 1);
                        $invoiceItem->item_description = "Weight: {$breakdown['weight']} KG at ₹{$breakdown['rate']} per KG";
                        $invoiceItem->quantity = $breakdown['weight'];
                        $invoiceItem->unit_price = $breakdown['rate'];
                        $invoiceItem->total_price = $breakdown['amount'];
                        $invoiceItem->unit_of_measure = 'KG';

                        // Calculate GST for each item
                        if ($invoiceData['cgst_amount'] > 0 || $invoiceData['sgst_amount'] > 0) {
                            $invoiceItem->is_gst_applicable = true;
                            $invoiceItem->cgst_rate = 6.00;
                            $invoiceItem->sgst_rate = 6.00;
                            $invoiceItem->igst_rate = 0.00;
                            $invoiceItem->cgst_amount = $breakdown['amount'] * 0.06;
                            $invoiceItem->sgst_amount = $breakdown['amount'] * 0.06;
                            $invoiceItem->igst_amount = 0.00;
                            $invoiceItem->total_with_gst = $breakdown['amount'] + $invoiceItem->cgst_amount + $invoiceItem->sgst_amount;
                        } else {
                            $invoiceItem->is_gst_applicable = false;
                            $invoiceItem->cgst_rate = 0.00;
                            $invoiceItem->sgst_rate = 0.00;
                            $invoiceItem->igst_rate = 0.00;
                            $invoiceItem->cgst_amount = 0.00;
                            $invoiceItem->sgst_amount = 0.00;
                            $invoiceItem->igst_amount = 0.00;
                            $invoiceItem->total_with_gst = $breakdown['amount'];
                        }

                        $invoiceItem->save();
                    }
                }

                // Log the auto-generated invoice creation
                InvoiceChangeLog::logChange(
                    $invoiceId,
                    'created',
                    'SYSTEM',
                    null,
                    $invoiceData,
                    'Invoice auto-generated by system for service cycle'
                );

                // Queue email for the generated invoice
                $this->queueInvoiceEmail($invoiceId, $service->client_id);

                // Send notification alert for auto-generated invoice
                $invoice = Invoice::find($invoiceId);
                if ($invoice) {
                    $notificationService = new NotificationAlertService();
                    $notificationService->sendInvoiceCreationAlert($invoice, false); // false = auto-generated
                }

                // Update client_services table
                if (!empty($service->end_date) && Carbon::parse($service->end_date)->lessThanOrEqualTo(Carbon::today())) {
                    DB::table('client_services')->where('id', $service->id)->update(['status' => 0]);
                } else {
                    // Use the new logic to calculate next invoice date
                    $newNextInvoiceDate = $this->calculateNextInvoiceDateForExistingService(
                        $service->next_invoice_date,
                        $service->payment_cycle,
                        $service->invoice_generation_type ?? 1, // Default to end of month if not set
                        $service->custom_invoice_day,
                        $service->end_date
                    );

                    DB::table('client_services')->where('id', $service->id)->update(['next_invoice_date' => $newNextInvoiceDate->toDateString()]);
                }

                // Update clients table
                DB::table('clients')->where('id', $service->client_id)->increment('pending_amount', $totalAmountDue);
                AccountLedger::create([
                    'client_id' => $service->client_id,
                    'transaction' => 'Inovice',
                    'transaction_date' => Carbon::today()->toDateString(),
                    'detials' => 'Invoice ' . $invoice_code,
                    'amount' => $totalAmountDue,
                    'payments' => NULL,
                    'balance' => $grandTotal
                ]);
                $invoicesGenerated++;
            }

            DB::commit();

            // Prepare response message
            if ($client_service_id) {
                // For specific service generation
                if ($invoicesGenerated > 0) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Invoice generated successfully!'
                    ]);
                } else {
                    $message = 'No invoice was generated.';
                    if (!empty($skippedServices)) {
                        $message .= ' Reason: ' . $skippedServices[0]['reason'];
                    }
                    return response()->json([
                        'success' => false,
                        'message' => $message
                    ]);
                }
            } else {
                // For bulk generation
                $message = "Generated {$invoicesGenerated} invoice(s) successfully.";
                if (!empty($skippedServices)) {
                    $message .= " Skipped " . count($skippedServices) . " service(s).";
                }
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'invoices_generated' => $invoicesGenerated,
                    'skipped_services' => $skippedServices
                ]);
            }

        } catch (\Exception $e) {
            DB::rollBack();

            // Log the detailed error for debugging
            Log::error('Invoice generation failed', [
                'client_service_id' => $client_service_id,
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Something went wrong. Please try again.',
                'debug_message' => $e->getMessage(), // Include for debugging
                'debug_line' => $e->getLine()
            ], 500);
        }
    }

    /**
     * Queue invoice email based on environment settings
     */
    private function queueInvoiceEmail($invoiceId, $clientId, $isManual = false)
    {
        try {
            // Get client information
            $client = Client::find($clientId);

            if (!$client) {
                Log::warning("Client not found for invoice ID: {$invoiceId}");
                return;
            }

            // Get the appropriate email address
            $recipientEmail = $this->getValidatedEmailAddress($client);

            if (!$recipientEmail) {
                Log::warning("No valid email address found for client {$client->id} (Invoice ID: {$invoiceId})");
                return;
            }

            // For manual invoices, send immediately. For bulk generation, add delay
            if ($isManual) {
                SendInvoiceEmailJob::dispatch($invoiceId, $recipientEmail);
                $logType = 'Manual creation';
            } else {
                // Queue the email job with configurable delay for bulk generation
                $minDelay = config('email-queue.delay.min_seconds', 5);
                $maxDelay = config('email-queue.delay.max_seconds', 30);
                $randomDelay = rand($minDelay, $maxDelay);

                SendInvoiceEmailJob::dispatch($invoiceId, $recipientEmail)
                                  ->delay(now()->addSeconds($randomDelay));
                $logType = 'Bulk generation';
            }

            // Log the queuing attempt
            $this->logEmailAttempt('invoice', $invoiceId, $recipientEmail, 'queued', $logType);

        } catch (\Exception $e) {
            Log::error("Failed to queue invoice email for invoice ID {$invoiceId}: " . $e->getMessage());
        }
    }



    function updateAccountLedger() {
        // Fetch invoices ordered by invoice_date
        $invoices = DB::table('invoices')
            ->select('client_id', 'invoice_date as transaction_date', 'invoice_code', 'total_amount_due as amount',
            'grand_total as balance', 'created_type', DB::raw("'Invoice' as transaction"))
            ->orderBy('invoice_date')
            ->get();

        // Fetch payments ordered by paid_on
        $payments = DB::table('payments')
            ->join('invoices', 'payments.invoice_id', '=', 'invoices.id')
            ->select('payments.client_id', 'payments.paid_on as transaction_date', 'invoices.invoice_code', 'payments.amount',
             DB::raw("NULL as balance"), DB::raw("'Payment' as transaction"))
            ->orderBy('payments.paid_on')
            ->get();

        // Merge transactions and sort by date
        $transactions = $invoices->merge($payments)->sortBy('transaction_date');

        $clientBalances = []; // Track balance per client

        foreach ($transactions as $transaction) {
            $clientId = $transaction->client_id;

            // Get previous balance, default to 0
            $previousBalance = $clientBalances[$clientId] ?? 0;

            // Invoice increases balance
            if ($transaction->transaction === 'Invoice') {
                $currentBalance = $previousBalance + $transaction->amount;
            }
            // Payment reduces balance
            else {
                $currentBalance = $previousBalance - $transaction->amount;
            }

            // Determine details column
            if ($transaction->transaction === 'Invoice') {
                $details = ($transaction->created_type == 0)
                    ? "Invoice " . $transaction->invoice_code
                    : "Manual Invoice " . $transaction->invoice_code;
            } else {
                $details = "Payment Received for " . $transaction->invoice_code;
            }

            // Insert into account_ledger table
            DB::table('account_ledgers')->insert([
                'client_id' => $transaction->client_id,
                'transaction' => $transaction->transaction,
                'transaction_date' => $transaction->transaction_date,
                'detials' => $details,
                'amount' => $transaction->transaction === 'Invoice' ? $transaction->amount : null,
                'payments' => $transaction->transaction === 'Payment' ? $transaction->amount : null,
                'balance' => $currentBalance,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Update the balance for this client
            $clientBalances[$clientId] = $currentBalance;
        }
    }

    public function bulkInvoiceDownload(Request $request)
{
    ini_set('memory_limit', '1024M'); // Increase memory limit
    ini_set('max_execution_time', 6000); // Increase execution time

    $invoices = Invoice::query();

    // Apply filters
    if ($request->date) {
        $dates = explode(' - ', $request->date);

        // Convert `dd/mm/yyyy` to `yyyy-mm-dd`
        $startDate = \Carbon\Carbon::createFromFormat('d/m/Y', trim($dates[0]))->format('Y-m-d');
        $endDate = \Carbon\Carbon::createFromFormat('d/m/Y', trim($dates[1]))->format('Y-m-d');

        $invoices->whereBetween('invoice_date', [$startDate, $endDate]);
    }

    if ($request->client_id) {
        $invoices->where('client_id', $request->client_id);
    }

    $invoices = $invoices->limit(1000)->get();

    if ($invoices->isEmpty()) {
        return response()->json(['error' => 'No invoices found'], 404);
    }

    // Generate a single PDF containing all invoices
    $pdf =  $pdf = Pdf::loadView('invoices.bulk_invoice_template', compact('invoices'))
    ->setPaper('a4', 'portrait');

    $pdfFileName = 'invoices_merged_' . time() . '.pdf';
    $pdfPath = storage_path('app/public/' . $pdfFileName);

    // Save the PDF to storage
    Storage::put('public/' . $pdfFileName, $pdf->output());

    return response()->download($pdfPath)->deleteFileAfterSend(true);
}

    /**
     * Generate PDF and save to storage instead of keeping in memory
     */
    function generateInvoicePDF($invoice)
    {
        $storagePath = storage_path('app/public/invoices');

        // Ensure the directory exists
        if (!file_exists($storagePath)) {
            mkdir($storagePath, 0777, true);
        }

        $pdfPath = $storagePath . '/Invoice_' . $invoice->id . '.pdf';

        $pdf = Pdf::loadView('invoices.invoice_template', compact('invoice'))
            ->setPaper('a4', 'portrait')
            ->save($pdfPath); // Save to file instead of keeping it in memory

        return $pdfPath;
    }
    public function payment_qr($amount){
        $upi_url="upi://pay?pa=".config('company.details.upi')."&pn=".config('company.details.upi_name')."&am=".$amount;
        $qr = new QrCode($upi_url);
        $writer = new PngWriter();
        $result = $writer->write($qr);
        // Save to file or return base64 for inline display
        $qr_url = $result->getDataUri();
        return $qr_url;
    }
     public function verify_invoice($id){
        $encrypted = Crypt::encryptString($id); // You can also encrypt JSON if needed
        $verificationUrl = route('invoice.verify', ['encrypted' => $encrypted]);
        $invoice_url = new QrCode($verificationUrl);
        $writer = new PngWriter();
        $result = $writer->write($invoice_url);
        // Save to file or return base64 for inline display
        $url = $result->getDataUri();
        return $url;
    }
    //implementing invoice delete option
    public function invociceDelete(Request $request, $id){
        try {
            DB::beginTransaction();
        $invoice = Invoice::findOrFail($id);
        //copy record from invoice table to cancelled invoice table
        CancelledInvoice::create([
            'client_id' => $invoice->client_id,
            'service_id' => $invoice->service_id,
            'service_type' => $invoice->service_type,
            'invoice_code' => $invoice->invoice_code,
            'invoice_date' => $invoice->invoice_date,
            'invoice_status' => $invoice->invoice_status,
            'beds_count' => $invoice->beds_count,
            'unit_price' => $invoice->unit_price,
            'weight_qty' => $invoice->weight_qty,
            'gross_amount' => $invoice->gross_amount,
            'cgst_amount' => $invoice->cgst_amount,
            'sgst_amount' => $invoice->sgst_amount,
            'total_amount_due' => $invoice->total_amount_due,
            'paid_amount' => $invoice->paid_amount,
            'unpaid_amount' => $invoice->unpaid_amount,
            'created_by' => $invoice->created_by,
            'created_type' => $invoice->created_type,
            'from_invoice_to' => $invoice->from_invoice_to,
            'days_for_invoice' => $invoice->days_for_invoice,
            'client_pending_amount_before_invoice' => $invoice->client_pending_amount_before_invoice,
            'grand_total' => $invoice->grand_total,
            'comments' => $invoice->comments,
            'invoice_created_at' => $invoice->created_at,
            'invoice_updated_at' => $invoice->updated_at,
            'deleted_by' => Auth::user()->name,
            'deleted_at' => Carbon::now(),
            'deleted_reason' => $request->reason,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Log the invoice deletion
        InvoiceChangeLog::logChange(
            $invoice->id,
            'deleted',
            Auth::user()->name,
            $invoice->toArray(),
            null,
            'Invoice deleted. Reason: ' . $request->reason
        );

        // Restore inventory stock if this is an inventory invoice
        if ($invoice->isInventoryInvoice()) {
            foreach ($invoice->invoiceItems as $invoiceItem) {
                $inventoryItem = InventoryItem::find($invoiceItem->inventory_item_id);
                if ($inventoryItem) {
                    // Restore the stock
                    $inventoryItem->quantity_available += $invoiceItem->quantity;
                    $inventoryItem->save();
                    $inventoryItem->updateStatus();

                    // Create inventory transaction for stock restoration
                    InventoryTransaction::create([
                        'inventory_item_id' => $inventoryItem->id,
                        'transaction_type' => 'in',
                        'quantity' => $invoiceItem->quantity,
                        'quantity_before' => $inventoryItem->quantity_available - $invoiceItem->quantity,
                        'quantity_after' => $inventoryItem->quantity_available,
                        'reason' => 'Invoice deletion - stock restoration',
                        'notes' => "Invoice deleted: {$invoice->invoice_code}",
                        'created_by' => Auth::id()
                    ]);
                }
            }

            // Delete invoice items
            InvoiceItem::where('invoice_id', $id)->delete();
        }

        // Delete invoice
        $invoice->delete();
        //client pending amount update
        DB::table('clients')->where('id', $invoice->client_id)->decrement('pending_amount', $invoice->total_amount_due);
        //delete payments
        Payment::where('invoice_id', $id)->delete();
        //delete account ledger invoice record
        AccountLedger::where('client_id', $invoice->client_id)->where('detials', 'like', '%Invoice ' . $invoice->invoice_code . '%')->delete();
        //delete account ledger payment record
        AccountLedger::where('client_id', $invoice->client_id)->where('detials', 'like', '%Payment Received for ' . $invoice->invoice_code . '%')->delete();
        //delete account ledger tds record
        AccountLedger::where('client_id', $invoice->client_id)->where('detials', 'like', '%TDS Deducted for ' . $invoice->invoice_code . '%')->delete();
        $response=[
            'success'=>true,
            'message'=>'Invoice deleted successfully!'
        ];
        DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $response=[
                'success'=>false,
                'message'=>$e->getMessage()
            ];
        }

        return response()->json($response);
    }
    public function getInvoicePayments($id)
    {
        $invoice = Invoice::findOrFail($id);
        $payments = Payment::where('invoice_id', $id)->select('id', 'amount', 'payment_mode', 'paid_on')->get();

        return response()->json([
            'invoice' => $invoice->only(['id', 'invoice_code']),
            'payments' => $payments
        ]);
    }
    // Cancelled invoices list
    public function cancelledInvoices()
    {
        return view('invoices.cancelled_invoices');
    }
    public function getCancelledInvoices(Request $request)
    {
        if ($request->ajax()) {
            $user = Auth::user();

            $invoices = CancelledInvoice::select(['id', 'client_id', 'invoice_code', 'invoice_date', 'invoice_status', 'total_amount_due','paid_amount','unpaid_amount','deleted_at','deleted_by','deleted_reason'])
                ->orderBy('id', 'desc');

            return DataTables::of($invoices)
                ->addColumn('invoice_status', function ($row) {
                    if ($row->invoice_status == 'Paid') {
                        return '<span class="badge badge-sm text-sm fw-normal text-success-600 bg-success-100 px-20 py-9 radius-4 text-white">Paid</span>';
                    } else if ($row->invoice_status == 'Pending') {
                        return '<span class="badge badge-sm text-sm fw-normal text-danger-600 bg-danger-100 px-20 py-9 radius-4 text-white">Pending</span>';
                    } else if ($row->invoice_status == 'Partially Paid') {
                        return '<span class="badge badge-sm text-sm fw-normal text-warning-600 bg-warning-100 px-20 py-9 radius-4 text-white">Partially Paid</span>';
                    }
                })
                ->addColumn('client_name', function ($row) {
                    return $row->client_id ? $row->client->name : '-';
                })
                ->addColumn('action', function ($row) {
                    $user = auth()->user(); // Get authenticated user
                    $output = '';
                    // Check 'invoice-view' permission
                    if ($user->can('invoice-view')) {
                        $output .=  '
                        <a href="' . route('invoices.show', $row->id) . '" class="w-32-px h-32-px bg-primary-light text-primary-600 rounded-circle d-inline-flex align-items-center justify-content-center">
                            <iconify-icon icon="iconamoon:eye-light"></iconify-icon>
                        </a>';
                    }
                    return $output;
                })
                ->rawColumns(['invoice_status', 'action'])
                ->make(true);
        }
    }

    /**
     * Export invoices with all fields and filters
     */
    public function exportInvoices(Request $request)
    {
        try {
            $user = Auth::user();

            // Build the query with all relationships
            $query = Invoice::with(['client', 'service', 'service_type_data'])
                ->orderBy('id', 'desc');

            // Apply role-based filtering (same as in getInvoices method)
            if ($user->role_id == 3) {
                $employee = Employee::where('user_id', $user->id)->first();
                if ($employee) {
                    $clientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');
                    $query->whereIn('client_id', $clientIds);
                } else {
                    $query->whereRaw('1 = 0');
                }
            }

            // Apply filters (same as in getInvoices method)
            if ($request->has('searchkey') && $request->searchkey != '') {
                $query->where(function ($q) use ($request) {
                    $q->where('invoice_code', 'like', "%{$request->searchkey}%");
                });
            }

            if ($request->has('invoice_status') && $request->invoice_status != '') {
                $query->where('invoice_status', $request->invoice_status);
            }

            if ($request->has('client') && $request->client != '') {
                $query->where('client_id', $request->client);
            }

            if ($request->has('daterange') && !empty($request->daterange)) {
                $dates = explode(' - ', $request->daterange);
                if (count($dates) === 2) {
                    $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                    $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                    $query->whereBetween('invoice_date', [$start_date, $end_date]);
                }
            }

            // Get all invoices
            $invoices = $query->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers - All available fields
            $headers = [
                'Invoice ID',
                'Invoice Code',
                'Client Name',
                'Client Code',
                'Service Name',
                'Service Type',
                'Invoice Date',
                'Invoice Status',
                'Total Amount Due',
                'Paid Amount',
                'Unpaid Amount',
                'Grand Total',
                'Taxable Amount',
                'CGST Amount',
                'SGST Amount',
                'Weight/Qty',
                'Unit Price',
                'From Date',
                'To Date',
                'Days for Invoice',
                'Client Pending Before Invoice',
                'Created By',
                'Created Type',
                'Comments',
                'Created Date',
                'Last Updated'
            ];

            $csvData[] = $headers;

            // Process each invoice
            foreach ($invoices as $invoice) {
                $row = [
                    $invoice->id,
                    $invoice->invoice_code,
                    $invoice->client ? $invoice->client->name : 'N/A',
                    $invoice->client ? $invoice->client->client_code : 'N/A',
                    $invoice->service ? $invoice->service->user_label : 'N/A',
                    $invoice->service_type_data ? $invoice->service_type_data->name : 'N/A',
                    $invoice->invoice_date ? date('d/m/Y', strtotime($invoice->invoice_date)) : 'N/A',
                    $invoice->invoice_status,
                    '₹' . number_format($invoice->total_amount_due, 2),
                    '₹' . number_format($invoice->paid_amount, 2),
                    '₹' . number_format($invoice->unpaid_amount, 2),
                    $invoice->grand_total ? '₹' . number_format($invoice->grand_total, 2) : '₹' . number_format($invoice->total_amount_due, 2),
                    $invoice->taxable_amount ? '₹' . number_format($invoice->taxable_amount, 2) : 'N/A',
                    $invoice->cgst_amount ? '₹' . number_format($invoice->cgst_amount, 2) : 'N/A',
                    $invoice->sgst_amount ? '₹' . number_format($invoice->sgst_amount, 2) : 'N/A',
                    $invoice->weight_qty ?: 'N/A',
                    $invoice->unit_price ? '₹' . number_format($invoice->unit_price, 2) : 'N/A',
                    $invoice->from_invoice_to ? date('d/m/Y', strtotime($invoice->from_invoice_to)) : 'N/A',
                    $invoice->from_invoice_to && $invoice->days_for_invoice ?
                        date('d/m/Y', strtotime($invoice->from_invoice_to . ' + ' . ($invoice->days_for_invoice - 1) . ' days')) : 'N/A',
                    $invoice->days_for_invoice ?: 'N/A',
                    $invoice->client_pending_amount_before_invoice ? '₹' . number_format($invoice->client_pending_amount_before_invoice, 2) : 'N/A',
                    $invoice->created_by ?: 'N/A',
                    $invoice->created_type ?: 'N/A',
                    $invoice->comments ?: 'N/A',
                    $invoice->created_at ? $invoice->created_at->format('d/m/Y H:i:s') : 'N/A',
                    $invoice->updated_at ? $invoice->updated_at->format('d/m/Y H:i:s') : 'N/A'
                ];

                $csvData[] = $row;
            }

            // Generate simple filename with timestamp
            $filename = 'Invoices_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show change logs for an invoice
     */
    public function changeLogs($id)
    {
        $invoice = Invoice::findOrFail($id);
        return view('invoices.change-logs', compact('invoice'));
    }

    /**
     * Show inventory invoice creation form
     */
    public function addInventoryInvoice()
    {
        $user = Auth::user();
        if ($user->role_id == 3) {
            // Fetch the employee record for the logged-in user
            $employee = Employee::where('user_id', $user->id)->first();

            if ($employee) {
                // Get only the clients assigned to this employee
                $clientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');
                $clients = Client::whereIn('id', $clientIds)->where('status', 1)->get();
            } else {
                // If no employee record found, return an empty collection
                $clients = collect([]);
            }
        } else {
            // For other roles, return all active clients
            $clients = Client::where('status', 1)->get();
        }

        $categories = InventoryCategory::active()->with('activeInventoryItems')->get();

        return view('invoices.add-inventory', compact('clients', 'categories'));
    }

    /**
     * Get available inventory items by category
     */
    public function getInventoryItems(Request $request)
    {
        $categoryId = $request->get('category_id');

        $query = InventoryItem::active()->inStock();

        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }

        $items = $query->with('category')->get();

        // Include GST information in the response
        $items = $items->map(function ($item) {
            return [
                'id' => $item->id,
                'name' => $item->name,
                'sku_code' => $item->sku_code,
                'description' => $item->description,
                'unit_price' => $item->unit_price,
                'quantity_available' => $item->quantity_available,
                'unit_of_measure' => $item->unit_of_measure,
                'category_name' => $item->category->name ?? 'N/A',
                'cgst_rate' => $item->cgst_rate,
                'sgst_rate' => $item->sgst_rate,
                'igst_rate' => $item->igst_rate,
                'is_gst_applicable' => $item->is_gst_applicable
            ];
        });

        return response()->json($items);
    }

    /**
     * Get inventory item details
     */
    public function getInventoryItemDetails($id)
    {
        $item = InventoryItem::with('category')->findOrFail($id);

        return response()->json([
            'id' => $item->id,
            'name' => $item->name,
            'sku_code' => $item->sku_code,
            'description' => $item->description,
            'unit_price' => $item->unit_price,
            'quantity_available' => $item->quantity_available,
            'unit_of_measure' => $item->unit_of_measure,
            'category_name' => $item->category->name ?? 'N/A',
            'cgst_rate' => $item->cgst_rate,
            'sgst_rate' => $item->sgst_rate,
            'igst_rate' => $item->igst_rate,
            'is_gst_applicable' => $item->is_gst_applicable
        ]);
    }

    /**
     * Store inventory-based invoice
     */
    public function storeInventoryInvoice(Request $request)
    {
        // Log incoming request for debugging
        Log::info('Inventory Invoice Creation Request', [
            'client' => $request->client,
            'invoice_date' => $request->invoice_date,
            'items_count' => is_array($request->items) ? count($request->items) : 0,
            'items' => $request->items
        ]);

        $request->validate([
            'client' => 'required|exists:clients,id',
            'invoice_date' => 'required|date',
            'items' => 'required|array|min:1',
            'items.*.inventory_item_id' => 'required|exists:inventory_items,id',
            'items.*.quantity' => 'required|integer|min:1',
        ]);

        $client_pending_amount = Client::where('id', $request->client)->first()->pending_amount;

        try {
            DB::beginTransaction();

            // Get current date for invoice code generation
            $currentDate = Carbon::now();
            $year = $currentDate->format('y');
            $month = $currentDate->format('m');

            if ($month >= 4) {
                $financialYearStart = $year;
                $financialYearEnd = $year + 1;
            } else {
                $financialYearStart = $year - 1;
                $financialYearEnd = $year;
            }

            $fiscal_year = "$financialYearStart-$financialYearEnd";
            $last_invoice = Invoice::latest()->first();
            $next_id = $last_invoice ? $last_invoice->id + 1 : 1;
            $invoice_code = config('company.details.company_suffix')."/{$fiscal_year}/{$month}/{$next_id}";

            // Create the invoice with default values for required fields
            $invoice = new Invoice();
            $invoice->client_id = $request->client;
            $invoice->service_id = 1; // Use first service as default for inventory invoices
            $invoice->service_type = 1; // Use first service type as default for inventory invoices
            $invoice->invoice_date = $request->invoice_date;
            $invoice->invoice_status = 'Pending';
            $invoice->invoice_code = $invoice_code;
            $invoice->created_by = Auth::user()->name;
            $invoice->client_pending_amount_before_invoice = $client_pending_amount;
            $invoice->created_type = 2; // 2 for inventory invoice
            $invoice->comments = $request->comments;

            // Set default values for required amount fields (will be updated after adding items)
            $invoice->gross_amount = 0;
            $invoice->cgst_amount = 0;
            $invoice->sgst_amount = 0;
            $invoice->total_amount_due = 0;
            $invoice->unpaid_amount = 0;
            $invoice->grand_total = $client_pending_amount; // Start with client pending amount

            // Set other potentially required fields with defaults
            $invoice->days_for_invoice = 0;
            $invoice->beds_count = 0;
            $invoice->unit_price = 0;
            $invoice->weight_qty = 0;

            // Save the invoice first so we can add items to it
            $invoice->save();

            $totalAmount = 0;
            $withoutGst = $request->has('without_gst') && $request->without_gst;

            // Process each inventory item
            foreach ($request->items as $itemData) {
                $inventoryItem = InventoryItem::findOrFail($itemData['inventory_item_id']);

                // Check if enough stock is available
                if ($inventoryItem->quantity_available < $itemData['quantity']) {
                    throw new \Exception("Insufficient stock for {$inventoryItem->name}. Available: {$inventoryItem->quantity_available}, Requested: {$itemData['quantity']}");
                }

                // Create invoice item with GST calculations
                $invoiceItem = new InvoiceItem();
                $invoiceItem->invoice_id = $invoice->id;
                $invoiceItem->inventory_item_id = $inventoryItem->id;
                $invoiceItem->item_name = $inventoryItem->name;
                $invoiceItem->item_sku = $inventoryItem->sku_code;
                $invoiceItem->item_description = $inventoryItem->description;
                $invoiceItem->quantity = $itemData['quantity'];
                $invoiceItem->unit_price = $inventoryItem->unit_price;
                $invoiceItem->unit_of_measure = $inventoryItem->unit_of_measure;
                $invoiceItem->total_price = $inventoryItem->unit_price * $itemData['quantity'];

                // Copy GST settings from inventory item
                $invoiceItem->is_gst_applicable = $inventoryItem->is_gst_applicable && !$withoutGst;
                $invoiceItem->cgst_rate = $invoiceItem->is_gst_applicable ? $inventoryItem->cgst_rate : 0.00;
                $invoiceItem->sgst_rate = $invoiceItem->is_gst_applicable ? $inventoryItem->sgst_rate : 0.00;
                $invoiceItem->igst_rate = $invoiceItem->is_gst_applicable ? $inventoryItem->igst_rate : 0.00;

                // Calculate GST amounts
                if ($invoiceItem->is_gst_applicable) {
                    $invoiceItem->cgst_amount = ($invoiceItem->total_price * $invoiceItem->cgst_rate) / 100;
                    $invoiceItem->sgst_amount = ($invoiceItem->total_price * $invoiceItem->sgst_rate) / 100;
                    $invoiceItem->igst_amount = ($invoiceItem->total_price * $invoiceItem->igst_rate) / 100;
                    $invoiceItem->total_with_gst = $invoiceItem->total_price + $invoiceItem->cgst_amount + $invoiceItem->sgst_amount + $invoiceItem->igst_amount;
                } else {
                    $invoiceItem->cgst_amount = 0.00;
                    $invoiceItem->sgst_amount = 0.00;
                    $invoiceItem->igst_amount = 0.00;
                    $invoiceItem->total_with_gst = $invoiceItem->total_price;
                }

                $invoiceItem->save();

                $totalAmount += $invoiceItem->total_with_gst;

                // Update inventory stock
                $inventoryItem->quantity_available -= $itemData['quantity'];
                $inventoryItem->save();
                $inventoryItem->updateStatus();

                // Create inventory transaction
                InventoryTransaction::create([
                    'inventory_item_id' => $inventoryItem->id,
                    'transaction_type' => 'out',
                    'quantity' => $itemData['quantity'],
                    'quantity_before' => $inventoryItem->quantity_available + $itemData['quantity'],
                    'quantity_after' => $inventoryItem->quantity_available,
                    'reason' => 'Invoice creation',
                    'notes' => "Invoice: {$invoice_code}",
                    'created_by' => Auth::id()
                ]);
            }

            // Calculate GST and update invoice totals
            $invoice->updateInventoryTotals($withoutGst);

            // Add GST status to comments
            $comments = $request->comments;
            if ($withoutGst) {
                $comments .= ($comments ? ' | ' : '') . '[WITHOUT GST]';
            }
            $invoice->comments = $comments;

            // Update grand total
            $invoice->grand_total = $client_pending_amount + $invoice->total_amount_due;
            $invoice->save();

            // Log the inventory invoice creation
            InvoiceChangeLog::logChange(
                $invoice->id,
                'created',
                Auth::user()->name,
                null,
                $invoice->toArray(),
                'Inventory invoice created manually'
            );

            // Queue email if requested
            if ($request->has('send_email') && $request->send_email) {
                $this->queueInvoiceEmail($invoice->id, $request->client, true);
            }

            // Send notification alert for manual inventory invoice creation
            $notificationService = new NotificationAlertService();
            $notificationService->sendInvoiceCreationAlert($invoice, true); // true = manual creation

            // Update client pending amount
            DB::table('clients')->where('id', $request->client)->increment('pending_amount', $invoice->total_amount_due);

            // Create account ledger entry
            AccountLedger::create([
                'client_id' => $request->client,
                'transaction' => 'Inovice',
                'transaction_date' => $request->invoice_date,
                'detials' => 'Inventory Invoice ' . $invoice_code,
                'amount' => $invoice->total_amount_due,
                'payments' => NULL,
                'balance' => $client_pending_amount + $invoice->total_amount_due
            ]);

            DB::commit();

            $message = 'Inventory invoice created successfully.';
            if ($request->has('send_email') && $request->send_email) {
                $message .= ' Email has been queued for sending.';
            }

            return redirect()->route('invoices.index')->with('success', $message);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Inventory Invoice Creation Failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            return back()->withInput()->with('error', 'Error creating inventory invoice: ' . $e->getMessage());
        }
    }
}
