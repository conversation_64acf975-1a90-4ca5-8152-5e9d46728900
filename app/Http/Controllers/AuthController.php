<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuthController extends Controller
{
    public function showLoginForm()
    {
        return view('login');
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        // Check if user exists and is active
        $user = \App\Models\User::where('email', $request->email)->first();

        if ($user && $user->status == 0) {
            return back()->withErrors(['email' => 'Your account is inactive. Please contact the administrator.']);
        }

        $remember = $request->has('remember');

        if (Auth::attempt($credentials, $remember)) {
            return redirect()->route('dashboard')->with('success', 'Login Successful');
        }

        // Pass the error to the session error bag
        return back()->withErrors(['email' => 'Invalid Email or Password']);
    }


    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        if ($request->expectsJson() || $request->ajax()) {
            return response()->json([
                'status' => 'success',
                'message' => 'Logged out successfully',
                'redirect' => route('login')
            ]);
        }

        return redirect()->route('login')->with('success', 'Logged out successfully.');
    }

    /**
     * Handle session heartbeat to keep session alive
     */
    public function sessionHeartbeat(Request $request)
    {
        if (Auth::check()) {
            // Regenerate session to extend lifetime
            $request->session()->regenerate();

            return response()->json([
                'status' => 'success',
                'message' => 'Session refreshed',
                'timestamp' => now()->toISOString()
            ]);
        }

        return response()->json([
            'status' => 'error',
            'message' => 'Session expired'
        ], 401);
    }
}
