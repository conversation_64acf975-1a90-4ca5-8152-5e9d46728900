<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Client;
use App\Models\Service;
use App\Models\ServiceType;
use Illuminate\Http\Request;
use App\Models\ClientService;
use App\Models\NonBeddedType;
use App\Models\WeightCostRange;
use App\Models\ServiceChangeLog;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Controllers\InvoiceController;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\ServiceLogService;
use App\Services\NotificationAlertService;

class ServiceController extends Controller
{
    protected $invoiceController;

    public function __construct(InvoiceController $invoiceController)
    {
        $this->invoiceController = $invoiceController;
    }
    //
    public function index()
    {
        $clients =Client::where('status',1)->get();
        return view('services.list', compact('clients'));
    }
    public function getClientServices(Request $request)
    {
        if ($request->ajax()) {
            $clients = ClientService::select(['id', 'total_price', 'start_date', 'payment_cycle', 'next_invoice_date', 'client_id', 'service_type', 'service_id'])->where('status',1)->orderBy('id', 'desc');

            return DataTables::of($clients)

                ->addColumn('client', function ($row) {
                    return $row->client_id ? $row->client->name : '-';
                })
                ->addColumn('service', function ($row) {
                    return $row->service_id ? $row->service->user_label : '-';
                })

                ->addColumn('action', function ($row) {
                    $action='';
                    if($row->start_date>date('Y-m-d')){
                        $action .= '
                        <a href="javascript:void(0);" onclick="confirmCancelService(\'' . route('service.cancel_service', $row->id) . '\')"
                           class="w-32-px h-32-px bg-danger-100 text-danger-600 rounded-circle d-inline-flex align-items-center justify-content-center" title="Stop Service">
                            <iconify-icon icon="material-symbols:stop-circle-rounded"></iconify-icon>
                        </a>
                    ';

                    }
                    $user = auth()->user(); // Get authenticated user

                    // Check 'service-view' permission
                    if ($user->can('service-view')) {
                    $action.= '
                        <a href="' . route('service.show', $row->id) . '" class="w-32-px h-32-px bg-primary-light text-primary-600 rounded-circle d-inline-flex align-items-center justify-content-center">
                            <iconify-icon icon="iconamoon:eye-light"></iconify-icon>
                        </a>';
                    }

                    // Check 'service-edit' permission
                    if ($user->can('service-edit')) {
                    $action.= '
                        <a href="' . route('service.edit', $row->id) . '" class="w-32-px h-32-px bg-success-light text-success-600 rounded-circle d-inline-flex align-items-center justify-content-center ms-1">
                            <iconify-icon icon="lucide:edit"></iconify-icon>
                        </a>';
                    }
                    return $action;
                })
                ->filter(function ($query) use ($request) {
                    if ($request->has('searchService') && $request->searchService != '') {
                        $query->where('service_id', $request->searchService);
                    }
                    if ($request->has('client') && $request->client != '') {
                        $query->where('client_id', $request->client);
                    }
                    if ($request->has('payment_cycle') && $request->payment_cycle != '') {
                        $query->where('payment_cycle', $request->payment_cycle);
                    }
                })

                ->editColumn('start_date', function ($row) {
                    return $row->start_date ? date('d-m-Y', strtotime($row->start_date)) : 'NA';
                })
                ->editColumn('next_invoice_date', function ($row) {
                    return $row->next_invoice_date ? date('d-m-Y', strtotime($row->next_invoice_date)) : 'NA';
                })
                ->editColumn('total_price', function ($row) {
                    return '₹'.$row->total_price ;
                })

                ->rawColumns(['action']) // Ensure HTML rendering
                ->make(true);
        }
    }
    public function show($id)
    {
        $client_service = ClientService::with([
            'client',
            'service',
            'service_type_data',
            'weightCostRanges'
        ])->findOrFail($id);

        return view('services.view', compact('client_service'));
    }

    /**
     * Display change logs for a specific service
     */
    public function changeLogs($id)
    {
        $client_service = ClientService::with(['client', 'service', 'changeLogs' => function($query) {
            $query->orderBy('created_at', 'desc');
        }])->findOrFail($id);

        return view('services.change-logs', compact('client_service'));
    }

    public function edit($id)
    {
        $client_service = ClientService::with(['client', 'service', 'service_type_data', 'weightCostRanges'])->findOrFail($id);
        $clients = Client::where('status', 1)->get();
        $services = Service::where('status', 1)->where('user_label', '!=', '')->get();
        $service_types = ServiceType::where('status', 1)->get();
        $non_bedded_types = NonBeddedType::where('status', 1)->get();

        return view('services.edit', compact('client_service', 'clients', 'services', 'service_types', 'non_bedded_types'));
    }

    public function update(Request $request, $id)
    {
        try {
            DB::beginTransaction();

            $client_service = ClientService::findOrFail($id);

            // Store original data for logging
            $originalData = $client_service->toArray();

            // Validation rules based on service type
            $rules = [
                'client_id' => 'required|exists:clients,id',
                'payment_mode' => [
                    'required',
                    function ($attribute, $value, $fail) use ($request) {
                        // For weight-based services, only allow Monthly
                        if ($request->service == 3 && $value !== 'Monthly') {
                            $fail('Weight-based services only support Monthly payment cycle.');
                        }
                        // For other services, allow Monthly and Yearly
                        if (!in_array($value, ['Monthly', 'Yearly'])) {
                            $fail('Payment mode must be Monthly or Yearly.');
                        }
                    },
                ],
                'invoice_generation_type' => 'required|in:1,2,3',
                'custom_invoice_day' => 'nullable|required_if:invoice_generation_type,3|integer|min:1|max:31',
                'service_type' => 'required',
                'service' => 'required|in:1,2,3,4',
                'description' => 'required|string',
            ];

            // Total cost is not required for weight-based services (service type 3)
            if ($request->service != 3) {
                $rules['total_cost'] = 'required|numeric|min:0';
            } else {
                $rules['total_cost'] = 'nullable|numeric|min:0';
            }

            // Add conditional validation based on service type
            if ($request->service == 1) { // Bedded
                $rules['no_of_units'] = 'required|numeric|min:1';
                $rules['unit_price'] = 'required|numeric|min:1';
            } elseif ($request->service == 2) { // Non-bedded
                $rules['non_bedded_type'] = 'required';
            } elseif ($request->service == 4) { // Bedded with Fixed Price
                $rules['no_of_units'] = 'required|numeric|min:1';
                // non_bedded_type is not required for bedded services
            } elseif ($request->service == 3) { // Weight-based
                $rules['weight_cost_type'] = 'required|in:1,2,3,4';
                if ($request->weight_cost_type == 1 || $request->weight_cost_type == 4) {
                    $rules['weight_cost'] = 'required|numeric|min:0';
                }
                if ($request->weight_cost_type == 4) {
                    $rules['minimum_kgs'] = 'required|numeric|min:0';
                    $rules['minimum_cost'] = 'required|numeric|min:0';
                }
                if ($request->weight_cost_type == 2 || $request->weight_cost_type == 3) {
                    $rules['from_kg'] = 'required|array|min:1';
                    $rules['from_kg.*'] = 'required|numeric|min:0';
                    $rules['to_kg'] = 'required|array|min:1';
                    $rules['to_kg.*'] = 'required|numeric|min:0';
                    $rules['cost'] = 'required|array|min:1';
                    $rules['cost.*'] = 'required|numeric|min:0';
                }
            }

            $request->validate($rules);

            // Additional validation for weight ranges
            if ($request->service == 3 && ($request->weight_cost_type == 2 || $request->weight_cost_type == 3)) {
                $this->validateWeightRanges($request);
            }

            // Calculate total cost for bedded services
            $totalCost = $request->total_cost;
            if ($request->service == 1) { // Bedded Service
                $multiplier = ($request->payment_mode === "Monthly") ? 30 : 365;
                $calculatedCost = $request->no_of_units * $request->unit_price * $multiplier;
                $totalCost = max($calculatedCost, 2000); // Apply minimum total cost condition
            }

            // Check if invoice generation type or payment cycle changed
            $invoiceGenerationChanged = (
                $client_service->payment_cycle !== $request->payment_mode ||
                ($client_service->invoice_generation_type ?? 1) !== (int)$request->invoice_generation_type ||
                $client_service->custom_invoice_day !== $request->custom_invoice_day
            );

            // Update the service (excluding start_date to maintain data integrity)
            $client_service->update([
                'client_id' => $request->client_id,
                'service_type' => $request->service_type,
                'service_id' => $request->service,
                'beds_count' => $request->no_of_units ?? null,
                'unit_price' => $request->unit_price ?? null,
                'total_price' => $totalCost,
                'payment_cycle' => $request->payment_mode,
                'invoice_generation_type' => $request->invoice_generation_type ?? 1,
                'custom_invoice_day' => $request->custom_invoice_day,
                'description' => $request->description,
                'non_bedded_type' => $request->non_bedded_type ?? null,
                'weight_cost_type' => $request->weight_cost_type ?? null,
                'weight_cost' => $request->weight_cost ?? null,
                'minimum_kgs' => $request->minimum_kgs ?? null,
                'minimum_cost' => $request->minimum_cost ?? null,
            ]);

            // Recalculate next_invoice_date if invoice generation settings changed
            if ($invoiceGenerationChanged) {
                $newNextInvoiceDate = $this->calculateNextInvoiceDate(
                    strtotime($client_service->start_date),
                    $request->payment_mode,
                    $request->invoice_generation_type ?? 1,
                    $request->custom_invoice_day
                );

                $client_service->update(['next_invoice_date' => $newNextInvoiceDate]);

                // Log the next invoice date change
                Log::info("Next invoice date recalculated due to invoice generation type change", [
                    'service_id' => $client_service->id,
                    'old_payment_cycle' => $originalData['payment_cycle'],
                    'new_payment_cycle' => $request->payment_mode,
                    'old_invoice_generation_type' => $originalData['invoice_generation_type'] ?? 1,
                    'new_invoice_generation_type' => $request->invoice_generation_type ?? 1,
                    'old_custom_invoice_day' => $originalData['custom_invoice_day'],
                    'new_custom_invoice_day' => $request->custom_invoice_day,
                    'old_next_invoice_date' => $originalData['next_invoice_date'],
                    'new_next_invoice_date' => $newNextInvoiceDate,
                ]);
            }

            // Handle weight cost ranges for range-based types
            if ($request->service == 3 && ($request->weight_cost_type == 2 || $request->weight_cost_type == 3)) {
                // Delete existing ranges using the relationship
                $client_service->weightCostRanges()->delete();

                // Add new ranges using the relationship
                if ($request->has('from_kg') && $request->has('to_kg') && $request->has('cost')) {
                    $fromKgs = $request->from_kg;
                    $toKgs = $request->to_kg;
                    $costs = $request->cost;

                    for ($i = 0; $i < count($fromKgs); $i++) {
                        if (!empty($fromKgs[$i]) && !empty($toKgs[$i]) && !empty($costs[$i])) {
                            $client_service->weightCostRanges()->create([
                                'from_kg' => $fromKgs[$i],
                                'to_kg' => $toKgs[$i],
                                'cost' => $costs[$i],
                            ]);
                        }
                    }
                }
            } else {
                // If not range-based, delete any existing ranges
                $client_service->weightCostRanges()->delete();
            }

            // Log the changes using ServiceLogService
            $newData = $client_service->fresh()->toArray();
            ServiceLogService::logUpdated($client_service, $originalData, $newData, 'Service updated via edit form');

            // Log changes using ServiceChangeLog
            ServiceChangeLog::logChange(
                $client_service->id,
                'updated',
                Auth::user()->name,
                $originalData,
                $newData,
                'Service updated via edit form'
            );

            // Also log to Laravel log for backup
            Log::info("Service updated", [
                'service_id' => $client_service->id,
                'updated_by' => Auth::user()->name,
                'updated_at' => now(),
                'original_data' => $originalData,
                'new_data' => $newData
            ]);

            DB::commit();

            return redirect()->route('services.index')->with('success', 'Service updated successfully!');
        } catch (\Exception $e) {
            DB::rollback();
            Log::error("Failed to update service: " . $e->getMessage());
            return back()->with('error', 'Error updating service: ' . $e->getMessage())->withInput();
        }
    }
    public function assignService($client_id = null){
        $clients=Client::where('status',1)->get();
        $services=Service::where('status',1)->where('user_label','!=','')->get();
        $service_types=ServiceType::where('status',1)->get();
        $non_bedded_types=NonBeddedType::where('status',1)->get();

        // If client_id is provided, find the specific client for pre-selection
        $selectedClient = null;
        if ($client_id) {
            $selectedClient = Client::where('id', $client_id)->where('status', 1)->first();
            // If client not found or inactive, redirect to assign service without pre-selection
            if (!$selectedClient) {
                return redirect()->route('assign.service')->with('warning', 'Client not found or inactive. Please select a client.');
            }
        }

        return view('services.assign-service', compact('clients','services','service_types','non_bedded_types','selectedClient'));
    }
    public function clientService_old(Request $request)
    {
        // Validate the request data
        $request->validate([
            'client_id'      => 'required|exists:clients,id',
            'payment_mode'   => 'required|in:Monthly,Yearly',
            'service_type'   => 'required',
            'service'        => 'required|in:1,2,3,4', // 1 = Bedded, 2 = Non-Bedded, 3 = Weight-based, 4 = Bedded with Fixed Price
            'no_of_units'    => 'nullable|numeric|min:1|regex:/^\d+(\.\d{1,2})?$/|required_if:service,1|required_if:service,4',
            'unit_price'     => 'nullable|numeric|min:1|regex:/^\d+(\.\d{1,2})?$/|required_if:service,1',
            'total_cost'     => 'required|numeric',
            'non_bedded_type'=> 'nullable|required_if:service,2',
            'start_date'     => 'required',
            'description'    => 'required|string'
        ]);
        // Convert start_date to a timestamp
        $startDate = strtotime($request->start_date);
        $tomorrow = strtotime('tomorrow'); // Get tomorrow's timestamp
        // Calculate total cost for bedded services
        $totalCost = $request->total_cost;
        if ($request->service == 1) { // If service is bedded
            $multiplier = ($request->payment_mode === "Monthly") ? 30 : 365;
            $calculatedCost = $request->no_of_units * $request->unit_price * $multiplier;
            $totalCost = max($calculatedCost, 2000); // Apply minimum total cost condition
        } elseif ($request->service == 3) { // Weight-based service
            $totalCost = null; // No total cost for weight-based services
        }

        // Calculate next_invoice_date for new record using the new logic
        $nextInvoiceDate = $this->calculateNextInvoiceDate(
            $startDate,
            $request->payment_mode,
            $request->invoice_generation_type ?? 1, // Default to end of month if not provided
            $request->custom_invoice_day
        );

        // Handle existing active services for the same client
        $this->handleExistingServices($request->client_id, $startDate);

        // Insert new record
        $clientService = new ClientService();
        $clientService->client_id = $request->client_id;
        $clientService->payment_cycle = $request->payment_mode;
        $clientService->invoice_generation_type = $request->invoice_generation_type ?? 1;
        $clientService->custom_invoice_day = $request->custom_invoice_day;
        $clientService->service_type = $request->service_type;
        $clientService->service_id = $request->service;
        $clientService->beds_count = ($request->service == 1 || $request->service == 4) ? $request->no_of_units : null;
        $clientService->unit_price = $request->service == 1 ? $request->unit_price : null;
        $clientService->total_price = $totalCost;
        $clientService->non_bedded_type = ($request->service == 2 || $request->service == 4) ? $request->non_bedded_type : null;
        $clientService->start_date = date('Y-m-d', $startDate);
        $clientService->next_invoice_date = $nextInvoiceDate; // Store calculated next_invoice_date
        $clientService->description = $request->description;
        $clientService->created_by = Auth::user()->name;
        $clientService->save();
        $this->invoiceController->generateInvoices();
        // Return success response
        return redirect()->route('services.index')->with('success', 'Client Service successfully added.');
    }
    /**
     * Validate weight ranges to prevent overlapping and ensure proper sequence
     */
    private function validateWeightRanges(Request $request)
    {
        $fromKgs = $request->from_kg;
        $toKgs = $request->to_kg;
        $costs = $request->cost;

        if (!$fromKgs || !$toKgs || !$costs) {
            throw new \Illuminate\Validation\ValidationException(
                validator([], []),
                ['weight_ranges' => ['Weight ranges are required for this cost type.']]
            );
        }

        $ranges = [];
        for ($i = 0; $i < count($fromKgs); $i++) {
            if (!empty($fromKgs[$i]) && !empty($toKgs[$i]) && !empty($costs[$i])) {
                $from = (float) $fromKgs[$i];
                $to = (float) $toKgs[$i];

                // Validate individual range
                if ($from >= $to) {
                    throw new \Illuminate\Validation\ValidationException(
                        validator([], []),
                        ['weight_ranges' => ["Range " . ($i + 1) . ": 'From KG' must be less than 'To KG'."]]
                    );
                }

                if ($from < 0 || $to < 0) {
                    throw new \Illuminate\Validation\ValidationException(
                        validator([], []),
                        ['weight_ranges' => ["Range " . ($i + 1) . ": Weight values cannot be negative."]]
                    );
                }

                $ranges[] = ['from' => $from, 'to' => $to, 'index' => $i + 1];
            }
        }

        // Sort ranges by 'from' value
        usort($ranges, function($a, $b) {
            return $a['from'] <=> $b['from'];
        });

        // Check for overlapping ranges
        for ($i = 0; $i < count($ranges) - 1; $i++) {
            $current = $ranges[$i];
            $next = $ranges[$i + 1];

            // Check if ranges overlap
            if ($current['to'] >= $next['from']) {
                throw new \Illuminate\Validation\ValidationException(
                    validator([], []),
                    ['weight_ranges' => ["Ranges {$current['index']} and {$next['index']} overlap. Range {$current['index']} ends at {$current['to']} KG but Range {$next['index']} starts at {$next['from']} KG."]]
                );
            }
        }

        // Check for gaps between ranges (required for fixed range cost type)
        if ($request->weight_cost_type == 2) { // Fixed range cost type requires continuous ranges
            for ($i = 0; $i < count($ranges) - 1; $i++) {
                $current = $ranges[$i];
                $next = $ranges[$i + 1];

                // Check if there's a gap between ranges
                if ($current['to'] + 1 < $next['from']) {
                    $gapStart = $current['to'] + 1;
                    $gapEnd = $next['from'] - 1;
                    throw new \Illuminate\Validation\ValidationException(
                        validator([], []),
                        ['weight_ranges' => ["Gap detected between ranges {$current['index']} and {$next['index']}. Missing range: {$gapStart} KG to {$gapEnd} KG. Fixed range cost type requires continuous weight ranges without gaps."]]
                    );
                }

                // Check if ranges are not continuous (should be consecutive)
                if ($current['to'] + 1 != $next['from']) {
                    $expectedNextStart = $current['to'] + 1;
                    throw new \Illuminate\Validation\ValidationException(
                        validator([], []),
                        ['weight_ranges' => ["Ranges {$current['index']} and {$next['index']} are not continuous. Range {$current['index']} ends at {$current['to']} KG, so Range {$next['index']} should start at {$expectedNextStart} KG, but it starts at {$next['from']} KG. For Fixed Range Based cost type, ranges must be continuous (e.g., 1-100, 101-200, 201-300)."]]
                    );
                }
            }

            // Additional validation: First range should start from 1 (or 0)
            if (!empty($ranges) && $ranges[0]['from'] > 1) {
                throw new \Illuminate\Validation\ValidationException(
                    validator([], []),
                    ['weight_ranges' => ["First range should start from 1 KG or 0 KG. Current first range starts from {$ranges[0]['from']} KG."]]
                );
            }
        } else if ($request->weight_cost_type == 3) { // Floating range cost type allows gaps
            // For floating range, we only check for overlaps (already done above)
            // Gaps are allowed in floating range cost type
        }
    }

    /**
     * Calculate next invoice date based on invoice generation type
     */
    private function calculateNextInvoiceDate($startDate, $paymentMode, $invoiceGenerationType, $customInvoiceDay = null)
    {
        $startTimestamp = is_numeric($startDate) ? $startDate : strtotime($startDate);

        if ($paymentMode === "Monthly") {
            switch ($invoiceGenerationType) {
                case 1: // End of month
                    return date('Y-m-t', $startTimestamp); // Last day of the month

                case 2: // Based on start date
                    // For monthly services, invoice should be generated after completion of month
                    // Add 1 month to start date to get next invoice date
                    return date('Y-m-d', strtotime('+1 month', $startTimestamp));

                case 3: // Custom day
                    if (!$customInvoiceDay || $customInvoiceDay < 1 || $customInvoiceDay > 31) {
                        throw new \InvalidArgumentException('Custom invoice day must be between 1 and 31');
                    }

                    // For monthly services, invoice should be generated after completion of month
                    // Add 1 month to start date first, then apply custom day
                    $nextMonthTimestamp = strtotime('+1 month', $startTimestamp);
                    $year = date('Y', $nextMonthTimestamp);
                    $month = date('m', $nextMonthTimestamp);

                    // Create date with custom day in next month
                    $customDate = $year . '-' . $month . '-' . str_pad($customInvoiceDay, 2, '0', STR_PAD_LEFT);

                    // Check if the custom day exists in next month (e.g., Feb 30 doesn't exist)
                    if (!checkdate($month, $customInvoiceDay, $year)) {
                        // If custom day doesn't exist, use last day of next month
                        return date('Y-m-t', $nextMonthTimestamp);
                    }

                    return $customDate;

                default:
                    return date('Y-m-t', $startTimestamp); // Default to end of month
            }
        } else { // Yearly
            switch ($invoiceGenerationType) {
                case 1: // End of month (yearly) - first invoice at end of start month
                    return date('Y-m-t', $startTimestamp); // Last day of the start month

                case 2: // Based on start date (yearly) - first invoice on start date
                    return date('Y-m-d', $startTimestamp); // Same as start date

                case 3: // Custom day (yearly) - first invoice on custom day of start month
                    if (!$customInvoiceDay || $customInvoiceDay < 1 || $customInvoiceDay > 31) {
                        throw new \InvalidArgumentException('Custom invoice day must be between 1 and 31');
                    }

                    // For yearly, use the custom day in the start month
                    $year = date('Y', $startTimestamp);
                    $month = date('m', $startTimestamp);

                    // Create date with custom day
                    $customDate = $year . '-' . $month . '-' . str_pad($customInvoiceDay, 2, '0', STR_PAD_LEFT);

                    // Check if the custom day exists in this month
                    if (!checkdate($month, $customInvoiceDay, $year)) {
                        // If custom day doesn't exist, use last day of month
                        return date('Y-m-t', $startTimestamp);
                    }

                    return $customDate;

                default:
                    // Default to start date for yearly
                    return date('Y-m-d', $startTimestamp);
            }
        }
    }

    public function clientService(Request $request)
    {
        // Validate the request data
        $validationRules = [
            'client_id'         => 'required|exists:clients,id',
            'payment_mode'      => [
                'required',
                function ($attribute, $value, $fail) use ($request) {
                    // For weight-based services, only allow Monthly
                    if ($request->service == 3 && $value !== 'Monthly') {
                        $fail('Weight-based services only support Monthly payment cycle.');
                    }
                    // For other services, allow Monthly and Yearly
                    if (!in_array($value, ['Monthly', 'Yearly'])) {
                        $fail('Payment mode must be Monthly or Yearly.');
                    }
                },
            ],
            'invoice_generation_type' => 'required|in:1,2,3',
            'custom_invoice_day' => 'nullable|required_if:invoice_generation_type,3|integer|min:1|max:31',
            'service_type'      => 'required',
            'service'           => 'required|in:1,2,3,4',
            'no_of_units'       => 'nullable|numeric|min:1|required_if:service,1|required_if:service,4',
            'unit_price'        => 'nullable|numeric|min:1|required_if:service,1',
            'non_bedded_type'   => 'nullable|required_if:service,2',
            'start_date'        => 'required',
            'description'       => 'required|string',

            'weight_cost_type'  => 'required_if:service,3|in:1,2,3,4',
            'weight_cost'       => 'nullable|required_if:weight_cost_type,1,4|numeric|min:0',
            'minimum_kgs'       => 'nullable|required_if:weight_cost_type,4|numeric|min:0',
            'minimum_cost'      => 'nullable|required_if:weight_cost_type,4|numeric|min:0',
            'from_kg'           => 'required_if:weight_cost_type,2,3|array',
            'to_kg'             => 'required_if:weight_cost_type,2,3|array',
            'cost'              => 'required_if:weight_cost_type,2,3|array',
        ];

        // Total cost is not required for weight-based services (service type 3)
        if ($request->service != 3) {
            $validationRules['total_cost'] = 'required|numeric|min:0';
        } else {
            $validationRules['total_cost'] = 'nullable|numeric|min:0';
        }

        $request->validate($validationRules);

        // Additional validation for weight ranges
        if ($request->service == 3 && ($request->weight_cost_type == 2 || $request->weight_cost_type == 3)) {
            $this->validateWeightRanges($request);
        }

        // Convert start_date to a timestamp
        $startDate = strtotime($request->start_date);
        $tomorrow = strtotime('tomorrow'); // Get tomorrow's timestamp
        // Calculate total cost for bedded services
        $totalCost = $request->total_cost;
        if ($request->service == 1) { // If service is bedded
            $multiplier = ($request->payment_mode === "Monthly") ? 30 : 365;
            $calculatedCost = $request->no_of_units * $request->unit_price * $multiplier;
            $totalCost = max($calculatedCost, 2000); // Apply minimum total cost condition
        } elseif ($request->service == 3) { // Weight-based service
            $totalCost = null; // No total cost for weight-based services
        }

        // Calculate next_invoice_date for new record using the new logic
        $nextInvoiceDate = $this->calculateNextInvoiceDate(
            $startDate,
            $request->payment_mode,
            $request->invoice_generation_type ?? 1, // Default to end of month if not provided
            $request->custom_invoice_day
        );

        // Handle existing active services for the same client
        $this->handleExistingServices($request->client_id, $startDate);

        // Insert new record
        $clientService = new ClientService();
        $clientService->client_id = $request->client_id;
        $clientService->payment_cycle = $request->payment_mode;
        $clientService->invoice_generation_type = $request->invoice_generation_type ?? 1;
        $clientService->custom_invoice_day = $request->custom_invoice_day;
        $clientService->service_type = $request->service_type;
        $clientService->service_id = $request->service;
        $clientService->beds_count = ($request->service == 1 || $request->service == 4) ? $request->no_of_units : null;
        $clientService->unit_price = $request->service == 1 ? $request->unit_price : null;
        $clientService->total_price = $totalCost;
        $clientService->non_bedded_type = ($request->service == 2 || $request->service == 4) ? $request->non_bedded_type : null;
        $clientService->weight_cost_type = $request->service == 3 ? $request->weight_cost_type : null;
        $clientService->weight_cost = (($request->service == 3 && $request->weight_cost_type==1 )? $request->weight_cost : null);
        $clientService->minimum_kgs = (($request->service == 3 && $request->weight_cost_type==4 )? $request->minimum_kgs : null);
        $clientService->minimum_cost = (($request->service == 3 && $request->weight_cost_type==4 )? $request->minimum_cost : null);
        $clientService->start_date = date('Y-m-d', $startDate);
        $clientService->next_invoice_date = $nextInvoiceDate; // Store calculated next_invoice_date
        $clientService->description = $request->description;
        $clientService->created_by = Auth::user()->name;
        $clientService->save();
        if ($request->service == 3 && $request->weight_cost_type != 1 && $request->weight_cost_type != 4) {
            foreach ($request->from_kg as $index => $from) {
                $clientService->weightCostRanges()->create([
                    'from_kg' => $from,
                    'to_kg'   => $request->to_kg[$index],
                    'cost'    => $request->cost[$index],
                ]);
            }
        }

        // Log service creation
        ServiceChangeLog::logChange(
            $clientService->id,
            'created',
            Auth::user()->name,
            null,
            $clientService->toArray(),
            'Service created via add form'
        );

        // Send notification alert for service creation
        $notificationService = new NotificationAlertService();
        $notificationService->sendServiceCreationAlert($clientService);

        $this->invoiceController->generateInvoices();
        // Return success response
        return redirect()->route('services.index')->with('success', 'Client Service successfully added.');
    }
    //Implementing bulk upload client services
    public function bulkUpload(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:2048',
        ]);

        $file = $request->file('csv_file');
        $path = $file->getRealPath();

        // Open the file
        $handle = fopen($path, 'r');

        // Skip the header row
        $headers = fgetcsv($handle);

        $successCount = 0;
        $errorCount = 0;
        $errors = [];
        $row = 1;

        // Process each row
        while (($data = fgetcsv($handle)) !== false) {
            $row++;

            // Map CSV columns to variables
            $clientId = $data[0] ?? null;
            $clientName = $data[1] ?? null; // Not used but included for reference
            $clientCode = $data[2] ?? null; // Not used but included for reference
            $paymentMode = $data[3] ?? null;
            $serviceType = $data[4] ?? null;
            $service = $data[5] ?? null;
            $noOfUnits = $data[6] ?? null;
            $unitPrice = $data[7] ?? null;
            $totalCost = $data[8] ?? null;
            $nonBeddedType = $data[9] ?? null;
            $weightCostType = $data[10] ?? null;
            $weightCost = $data[11] ?? null;
            $minimumKgs = $data[12] ?? null;
            $minimumCost = $data[13] ?? null;
            $input_startDate = $data[14] ?? null;
            $description = $data[15] ?? null;

            // Validate data
            if (!$clientId || !is_numeric($clientId) || !$paymentMode || !$serviceType || !$service ||
                !$totalCost || !$input_startDate || !$description) {
                $errorCount++;
                $errors[] = "Row {$row}: Invalid data format";
                continue;
            }

            // Check if client exists
            $client = Client::find($clientId);
            if (!$client) {
                $errorCount++;
                $errors[] = "Row {$row}: Client not found";
                continue;
            }

            // Check if service exists
            if ($service == 1 && (!$noOfUnits || !$unitPrice)) {
                $errorCount++;
                $errors[] = "Row {$row}: Invalid data format for bedded service";
                continue;
            }

            // Check if non-bedded type exists
            if ($service == 2 && !$nonBeddedType) {
                $errorCount++;
                $errors[] = "Row {$row}: Invalid data format for non-bedded service";
                continue;
            }

            // Check if weight cost type exists
            if ($service == 3 && !$weightCostType) {
                $errorCount++;
                $errors[] = "Row {$row}: Invalid data format for weight cost service";
                continue;
            }

            // Check if bedded with fixed price service exists
            if ($service == 4 && (!$noOfUnits || !$nonBeddedType)) {
                $errorCount++;
                $errors[] = "Row {$row}: Invalid data format for bedded with fixed price service";
                continue;
            }

            // Process data
            // Convert start_date to a timestamp
        $startDate = strtotime($input_startDate);
        $tomorrow = strtotime('tomorrow'); // Get tomorrow's timestamp
        // Calculate total cost for bedded services
        $totalCost = $totalCost;
        if ($request->service == 1) { // If service is bedded
            $multiplier = ($paymentMode === "Monthly") ? 30 : 365;
            $calculatedCost = $noOfUnits * $unitPrice * $multiplier;
            $totalCost = max($calculatedCost, 2000); // Apply minimum total cost condition
        }

        // Calculate next_invoice_date for new record using the new logic
        // For bulk upload, use default values for new fields
        $invoiceGenerationType = 1; // Default to end of month
        $customInvoiceDay = null;

        $nextInvoiceDate = $this->calculateNextInvoiceDate(
            $startDate,
            $paymentMode,
            $invoiceGenerationType,
            $customInvoiceDay
        );

        // Check for existing records for the same client_id
        $existingRecord = ClientService::where('client_id', $request->client_id)
            ->where('status', 1) // Active records only
            ->orderBy('start_date', 'desc') // Get the latest active record
            ->first();

        if ($existingRecord) {
            $oldStartDate = strtotime($existingRecord->start_date);
            $oldNextInvoiceDate = strtotime($existingRecord->next_invoice_date);
            $oldEndDate = $existingRecord->end_date ? strtotime($existingRecord->end_date) : null;

            // If the existing record start_date is greater than the new start_date
            if ($oldStartDate > $startDate) {
                $existingRecord->status = 0; // Deactivate old record
                $existingRecord->end_date = date('Y-m-d', $oldStartDate); // Set end_date to old start_date
                $existingRecord->next_invoice_date = null; // Remove next invoice date
            }
            // If next_invoice_date is before new start_date
            elseif ($oldNextInvoiceDate < $startDate ) {
                if(!$oldEndDate || $oldEndDate > $startDate){
                    $existingRecord->end_date = date('Y-m-d', strtotime('-1 day', $startDate)); // New start_date - 1 day
                }
            }
            // If next_invoice_date is after or same as new start_date
            else {
                $calculatedEndDate = strtotime('-1 day', $startDate); // New start_date - 1 day
                // if ($calculatedEndDate < $tomorrow) {
                //     $calculatedEndDate = $tomorrow; // Ensure end_date is at least tomorrow
                // }

                $existingRecord->end_date = date('Y-m-d', $calculatedEndDate);
                if ($existingRecord->payment_cycle === "Monthly") {
                    $existingRecord->next_invoice_date = date('Y-m-d', $calculatedEndDate); // Set same as end_date
                } else {
                    $existingRecord->next_invoice_date = null; // Yearly case: next_invoice_date is null
                }
            }
            $existingRecord->save(); // Update the old record
        }

            try {
                // Insert new record
                $clientService = new ClientService();
                $clientService->client_id = $clientId;
                $clientService->payment_cycle = $paymentMode;
                $clientService->invoice_generation_type = 1; // Default to end of month for bulk upload
                $clientService->custom_invoice_day = null;
                $clientService->service_type = $serviceType;
                $clientService->service_id = $service;
                $clientService->beds_count = ($service == 1 || $service == 4) ? $noOfUnits : null;
                $clientService->unit_price = $service == 1 ? $unitPrice : null;
                $clientService->total_price = $totalCost;
                $clientService->non_bedded_type = ($service == 2 || $service == 4) ? $nonBeddedType : null;
                $clientService->weight_cost_type = $service == 3 ? $weightCostType : null;
                $clientService->weight_cost = $service == 3 ? $weightCost : null;
                $clientService->minimum_kgs = $service == 3 ? $minimumKgs : null;
                $clientService->minimum_cost = $service == 3 ? $minimumCost : null;
                $clientService->start_date = date('Y-m-d', $startDate);
                $clientService->next_invoice_date = $nextInvoiceDate; // Store calculated next_invoice_date
                $clientService->description = $description;
                $clientService->created_by = Auth::user()->name;
                $clientService->save();

                // Send notification alert for bulk service creation
                $notificationService = new NotificationAlertService();
                $notificationService->sendServiceCreationAlert($clientService);

                $successCount++;
            } catch (\Exception $e) {
                $errorCount++;
                $errors[] = "Row {$row}: " . $e->getMessage();
            }
        }
         $this->invoiceController->generateInvoices();

        fclose($handle);

        return response()->json([
            'success' => true,
            'message' => "Processed {$successCount} entries successfully. {$errorCount} entries failed.",
            'errors' => $errors
        ]);
    }
    //implementing bulk upload template download
    public function downloadBulkUploadTemplate()
    {
        $headers = [
            'Client ID',
            'Client Name',
            'Client Code',
            'Payment Mode',
            'Service Type',
            'Service',
            'No of Units',
            'Unit Price',
            'Total Cost',
            'Non Bedded Type',
            'Weight Cost Type',
            'Weight Cost',
            'Minimum Kgs',
            'Minimum Cost',
            'Start Date',
            'Description'
        ];

        $filename = 'bulk_upload_template.csv';
        $handle = fopen('php://temp', 'r+');

        // Add BOM for UTF-8 encoding
        fputs($handle, "\xEF\xBB\xBF");

        // Add headers
        fputcsv($handle, $headers);

        rewind($handle);
        $csv = stream_get_contents($handle);
        fclose($handle);

        // Return CSV as download
        return response($csv, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }
    public function cancel_service($id)
    {
        $client_service = ClientService::find($id);

        if ($client_service && $client_service->start_date >= date('Y-m-d')) {
            // Store original data for logging
            $originalData = $client_service->toArray();

            $client_service->update(['status' => 0]);

            // Log the service cancellation
            ServiceChangeLog::logChange(
                $client_service->id,
                'status_changed',
                Auth::user()->name,
                $originalData,
                $client_service->fresh()->toArray(),
                'Service cancelled before start date'
            );

            return response()->json([
                'success' => true,
                'message' => "Service has been cancelled successfully!"
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => "Service cannot be cancelled."
        ]);
    }
    public function end_service(Request $request)
    {
        $request->validate([
            'service_id' => 'required|exists:client_services,id',
            'end_date' => 'required|date',
        ]);

        $clientService = ClientService::find($request->service_id);

        if (!$clientService) {
            return response()->json(['success' => false, 'message' => 'Service not found!']);
        }

        // Store original data for logging
        $originalData = $clientService->toArray();

        $userEnteredEndDate = Carbon::parse($request->end_date);
        $startDate = Carbon::parse($clientService->start_date);
        $currentMonthStart = Carbon::now()->startOfMonth();

        // ✅ Ensure end_date is not before start_date
        if ($userEnteredEndDate < $startDate) {
            return response()->json(['success' => false, 'message' => 'End date cannot be before the start date!']);
        }

        // ✅ Ensure end_date is not from the previous month
        if ($userEnteredEndDate < $currentMonthStart) {
            return response()->json(['success' => false, 'message' => 'End date cannot be from a previous month!']);
        }

        // Update end_date
        $clientService->end_date = $userEnteredEndDate;

        // Update next_invoice_date if necessary
        if ($clientService->next_invoice_date && Carbon::parse($clientService->next_invoice_date) > $userEnteredEndDate) {
            $clientService->next_invoice_date = $userEnteredEndDate;
        }

        $clientService->save();

        // Log the service ending
        ServiceChangeLog::logChange(
            $clientService->id,
            'status_changed',
            Auth::user()->name,
            $originalData,
            $clientService->toArray(),
            'Service ended with end date: ' . $userEnteredEndDate->format('d-m-Y')
        );

        return response()->json(['success' => true, 'message' => 'Service ended successfully.']);
    }

    /**
     * Handle existing active services when assigning a new service
     * This method properly ends previous services based on the new service start date
     */
    private function handleExistingServices($clientId, $newStartDate)
    {
        // Get ALL active services for this client
        $existingServices = ClientService::where('client_id', $clientId)
            ->where('status', 1) // Active services only
            ->orderBy('start_date', 'desc')
            ->get();

        if ($existingServices->isEmpty()) {
            return; // No existing services to handle
        }

        $newStartDateFormatted = date('Y-m-d', $newStartDate);
        $dayBeforeNewStart = date('Y-m-d', strtotime('-1 day', $newStartDate));

        foreach ($existingServices as $existingService) {
            $existingStartDate = strtotime($existingService->start_date);
            $existingEndDate = $existingService->end_date ? strtotime($existingService->end_date) : null;

            // Store original data for logging
            $originalData = $existingService->toArray();

            // Case 1: Existing service starts AFTER the new service start date
            // This means the existing service should be completely replaced/deactivated
            if ($existingStartDate >= $newStartDate) {
                $existingService->status = 0; // Deactivate
                $existingService->end_date = $existingService->start_date; // End on its own start date
                $existingService->next_invoice_date = null;

                $this->logServiceChange($existingService, $originalData,
                    "Service deactivated due to new service starting on {$newStartDateFormatted}");
            }
            // Case 2: Existing service starts BEFORE the new service start date
            else {
                // Case 2a: Existing service has NO end date (ongoing service)
                if (!$existingEndDate) {
                    $existingService->end_date = $dayBeforeNewStart;

                    // Update next_invoice_date if it's after the new end date
                    if ($existingService->next_invoice_date &&
                        strtotime($existingService->next_invoice_date) > strtotime($dayBeforeNewStart)) {
                        $existingService->next_invoice_date = $dayBeforeNewStart;
                    }

                    $this->logServiceChange($existingService, $originalData,
                        "Service ended on {$dayBeforeNewStart} due to new service starting on {$newStartDateFormatted}");
                }
                // Case 2b: Existing service has an end date that's AFTER the new start date
                elseif ($existingEndDate >= $newStartDate) {
                    $existingService->end_date = $dayBeforeNewStart;

                    // Update next_invoice_date if it's after the new end date
                    if ($existingService->next_invoice_date &&
                        strtotime($existingService->next_invoice_date) > strtotime($dayBeforeNewStart)) {
                        $existingService->next_invoice_date = $dayBeforeNewStart;
                    }

                    $this->logServiceChange($existingService, $originalData,
                        "Service end date updated from " . date('Y-m-d', $existingEndDate) .
                        " to {$dayBeforeNewStart} due to new service starting on {$newStartDateFormatted}");
                }
                // Case 2c: Existing service already ends before the new start date
                // No action needed - service is already properly ended
            }

            $existingService->save();
        }
    }

    /**
     * Log service changes for audit trail
     */
    private function logServiceChange($service, $originalData, $description)
    {
        ServiceChangeLog::logChange(
            $service->id,
            'updated',
            Auth::user()->name,
            $originalData,
            $service->toArray(),
            $description
        );
    }

 public function customHike(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:2048',
        ]);

        $file = $request->file('csv_file');
        $path = $file->getRealPath();

        // Open the file
        $handle = fopen($path, 'r');

        // Skip the header row
        $headers = fgetcsv($handle);

        $successCount = 0;
        $errorCount = 0;
        $errors = [];
        $row = 1;
        // Process each row
        while (($data = fgetcsv($handle)) !== false) {

       // dd($data);
            $row++;

            // Map CSV columns to variables
            // $clientId = $data[0] ?? null;
            // $clientName = $data[1] ?? null; // Not used but included for reference
            $clientCode = $data[0] ?? null; // Not used but included for reference
            $paymentMode = $data[1] ?? null;
            $serviceType = $data[2] ?? null;
            $service = $data[3] ?? null;
            $noOfUnits = $data[4] ?? null;
            $unitPrice = $data[5] ?? null;
            $totalCost = $data[6] ?? null;
            $input_startDate = $data[7] ?? null;
            $nonBeddedType = $data[8] ?? null;
            // $input_startDate = $data[8] ?? null;
            $description = "Rate Hike";

            // Validate data
            if (!$clientCode  || !$paymentMode || !$serviceType || !$service ||
                !$totalCost || !$input_startDate || !$description) {
                $errorCount++;
                $errors[] = "Row {$row}: Invalid data format";
                continue;
            }

            // Check if client exists
            $client = Client::where('client_code', $clientCode)->first();
            if (!$client) {
                $errorCount++;
                $errors[] = "Row {$row}: Client not found";
                continue;
            }

            // Check if service exists
            if ($service == 1 && !$noOfUnits || !$unitPrice) {
                $errorCount++;
                $errors[] = "Row {$row}: Invalid data format for bedded service";
                continue;
            }

            // Check if non-bedded type exists
            if ($service == 2 && !$nonBeddedType) {
                $errorCount++;
                $errors[] = "Row {$row}: Invalid data format for non-bedded service";
                continue;
            }

            $clientId = $client->id;

            // Process data
            // Convert start_date to a timestamp
        $startDate = strtotime($input_startDate);
        $tomorrow = strtotime('tomorrow'); // Get tomorrow's timestamp
        // Calculate total cost for bedded services
        $totalCost = $totalCost;
        if ($request->service == 1) { // If service is bedded
            $multiplier = ($paymentMode === "Monthly") ? 30 : 365;
            $calculatedCost = $noOfUnits * $unitPrice * $multiplier;
            $totalCost = max($calculatedCost, 2000); // Apply minimum total cost condition
        }

        // Calculate next_invoice_date for new record using the new logic
        // For bulk upload, use default values for new fields
        $invoiceGenerationType = 1; // Default to end of month
        $customInvoiceDay = null;

        $nextInvoiceDate = $this->calculateNextInvoiceDate(
            $startDate,
            $paymentMode,
            $invoiceGenerationType,
            $customInvoiceDay
        );

        // Handle existing active services for the same client
        $this->handleExistingServices($clientId, $startDate);

            try {
                // Insert new record
                $clientService = new ClientService();
                $clientService->client_id = $clientId;
                $clientService->payment_cycle = $paymentMode;
                $clientService->invoice_generation_type = 1; // Default to end of month for bulk upload
                $clientService->custom_invoice_day = null;
                $clientService->service_type = $serviceType;
                $clientService->service_id = $service;
                $clientService->beds_count = ($service == 1 || $service == 4) ? $noOfUnits : null;
                $clientService->unit_price = $service == 1 ? $unitPrice : null;
                $clientService->total_price = $totalCost;
                $clientService->non_bedded_type = ($service == 2 || $service == 4) ? $nonBeddedType : null;
                $clientService->start_date = date('Y-m-d', $startDate);
                $clientService->next_invoice_date = $nextInvoiceDate; // Store calculated next_invoice_date
                $clientService->description = $description;
                $clientService->created_by = Auth::user()->name;
                $clientService->save();

                $successCount++;
            } catch (\Exception $e) {
                $errorCount++;
                $errors[] = "Row {$row}: " . $e->getMessage();
            }
        }
         $this->invoiceController->generateInvoices();

        fclose($handle);

        return response()->json([
            'success' => true,
            'message' => "Processed {$successCount} entries successfully. {$errorCount} entries failed.",
            'errors' => $errors
        ]);
    }

    /**
     * Export services with all fields and filters
     */
    public function exportServices(Request $request)
    {
        try {
            // Build the query with all relationships
            $query = ClientService::with(['client', 'service', 'service_type_data'])
                ->where('status', 1);

            // Apply filters (same as in getClientServices method)
            if ($request->has('searchService') && $request->searchService != '') {
                $query->where('service_id', $request->searchService);
            }

            if ($request->has('client') && $request->client != '') {
                $query->where('client_id', $request->client);
            }

            if ($request->has('payment_cycle') && $request->payment_cycle != '') {
                $query->where('payment_cycle', $request->payment_cycle);
            }

            // Get all services
            $services = $query->orderBy('start_date', 'desc')->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers - All available fields
            $headers = [
                'Service ID',
                'Client Name',
                'Client Code',
                'Service Name',
                'Service Type',
                'Payment Cycle',
                'Start Date',
                'End Date',
                'Next Invoice Date',
                'No of Units',
                'Unit Price',
                'Total Price',
                'Non Bedded Type',
                'Weight Cost Type',
                'Weight Cost',
                'Minimum Kgs',
                'Minimum Cost',
                'Description',
                'Status',
                'Weight Ranges',
                'Created Date',
                'Last Updated'
            ];

            $csvData[] = $headers;

            // Process each service
            foreach ($services as $service) {
                // Get weight cost ranges (simplified)
                $weightRanges = 'N/A';

                $row = [
                    $service->id,
                    $service->client ? $service->client->name : 'N/A',
                    $service->client ? $service->client->client_code : 'N/A',
                    $service->service ? $service->service->user_label : 'N/A',
                    $service->service_type_data ? $service->service_type_data->name : 'N/A',
                    $service->payment_cycle,
                    $service->start_date ? date('d/m/Y', strtotime($service->start_date)) : 'N/A',
                    $service->end_date ? date('d/m/Y', strtotime($service->end_date)) : 'N/A',
                    $service->next_invoice_date ? date('d/m/Y', strtotime($service->next_invoice_date)) : 'N/A',
                    $service->no_of_units ?: 'N/A',
                    $service->unit_price ? '₹' . number_format($service->unit_price, 2) : 'N/A',
                    '₹' . number_format($service->total_price, 2),
                    $service->non_bedded_type ?: 'N/A',
                    $service->weight_cost_type ?: 'N/A',
                    $service->weight_cost ? '₹' . number_format($service->weight_cost, 2) : 'N/A',
                    $service->minimum_kgs ?: 'N/A',
                    $service->minimum_cost ? '₹' . number_format($service->minimum_cost, 2) : 'N/A',
                    $service->description ?: 'N/A',
                    $service->status == 1 ? 'Active' : 'Inactive',
                    $weightRanges ?: 'No weight ranges',
                    $service->created_at ? $service->created_at->format('d/m/Y H:i:s') : 'N/A',
                    $service->updated_at ? $service->updated_at->format('d/m/Y H:i:s') : 'N/A'
                ];

                $csvData[] = $row;
            }

            // Generate simple filename with timestamp
            $filename = 'Services_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Fix next_invoice_date for existing yearly services
     * This method corrects services where next_invoice_date equals start_date for yearly services
     */
    public function fixYearlyServiceDates()
    {
        try {
            DB::beginTransaction();

            // Find yearly services where next_invoice_date equals start_date
            $yearlyServices = ClientService::where('payment_cycle', 'Yearly')
                ->where('status', 1) // Active services only
                ->whereColumn('next_invoice_date', '=', 'start_date')
                ->get();

            $fixedCount = 0;
            $errors = [];

            foreach ($yearlyServices as $service) {
                try {
                    // Recalculate the correct next_invoice_date
                    $newNextInvoiceDate = $this->calculateNextInvoiceDate(
                        strtotime($service->start_date),
                        $service->payment_cycle,
                        $service->invoice_generation_type ?? 1,
                        $service->custom_invoice_day
                    );

                    // Update the service
                    $service->update(['next_invoice_date' => $newNextInvoiceDate]);

                    // Log the change
                    ServiceChangeLog::logChange(
                        $service->id,
                        'next_invoice_date_fixed',
                        'System Auto-Fix',
                        ['next_invoice_date' => $service->start_date],
                        ['next_invoice_date' => $newNextInvoiceDate],
                        "Fixed incorrect next_invoice_date for yearly service. Changed from {$service->start_date} to {$newNextInvoiceDate}"
                    );

                    $fixedCount++;

                } catch (\Exception $e) {
                    $errors[] = "Service ID {$service->id}: " . $e->getMessage();
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "Fixed {$fixedCount} yearly services",
                'fixed_count' => $fixedCount,
                'total_found' => $yearlyServices->count(),
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error fixing yearly service dates: ' . $e->getMessage()
            ], 500);
        }
    }
}
