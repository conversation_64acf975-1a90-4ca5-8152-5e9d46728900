<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Client;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Employee;
use Endroid\QrCode\QrCode;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\AccountLedger;
use Illuminate\Http\Response;
use App\Models\DeletedPayment;
use App\Models\EmployeeClient;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\DB;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;
use App\Jobs\SendPaymentEmailJob;
use App\Traits\EmailHelperTrait;
use App\Services\PaymentLogService;
use App\Services\NotificationAlertService;

class PaymentController extends Controller
{
    use EmailHelperTrait;
    //
    public function index()
    {
        $user = Auth::user();
        if ($user->role_id == 3) {
            // Fetch the employee record for the logged-in user
            $employee = Employee::where('user_id', $user->id)->first();

            if ($employee) {
                // Get only the clients assigned to this employee
                $clientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');
                $clients = Client::whereIn('id', $clientIds)->where('status', 1)->get();
            } else {
                // If no employee record found, return an empty collection
                $clients = collect([]);
            }
        } else {
            // For other roles, return all active clients
            $clients = Client::where('status', 1)->get();
        }
        return view('payments.list',compact('clients'));
    }
    public function getPayments(Request $request)
    {
        if ($request->ajax()) {
            $user = Auth::user();

            $payments = Payment::select(['id', 'client_id', 'invoice_id', 'payment_mode', 'amount', 'paid_on'])
                ->orderBy('id', 'desc');

            if ($user->role_id == 3) {
                // Fetch the employee record for the logged-in user
                $employee = Employee::where('user_id', $user->id)->first();

                if ($employee) {
                    // Get only the clients assigned to this employee
                    $clientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');

                    // Filter payments for those clients only
                    $payments->whereIn('client_id', $clientIds);
                } else {
                    // If no employee record found, return empty payments
                    $payments->whereRaw('1 = 0'); // This ensures no records are returned
                }
            }
            // Apply Employee Filter
            if ($request->has('employee') && $request->employee != '') {
                $clientIds = DB::table('employee_client')
                    ->where('employee_id', $request->employee)
                    ->pluck('client_id')
                    ->toArray();

                $payments->whereIn('client_id', $clientIds);
            }
            return DataTables::of($payments)

                ->addColumn('client_name', function ($row) {
                    return $row->client_id ? $row->client->name : '-';
                })
                ->addColumn('invoice_code', function ($row) {
                    return $row->invoice_id ? $row->invoice->invoice_code : '-';
                })
                ->addColumn('client_code', function ($row) {
                    return $row->client_id ? $row->client->client_code : '-';
                })

                ->addColumn('action', function ($row) {
                    $user = auth()->user(); // Get authenticated user
                    $output = '';
                    // Check 'payment-view' permission
                    if ($user->can('payment-view')) {
                        $output.=  '
                        <a href="' . route('payments.show', $row->id) . '" class="w-32-px h-32-px bg-primary-light text-primary-600 rounded-circle d-inline-flex align-items-center justify-content-center">
                            <iconify-icon icon="iconamoon:eye-light"></iconify-icon>
                        </a>';
                    }
                    // Check 'payment-edit' permission
                    if ($user->can('payment-edit')) {
                        $output.=  '
                        <a href="' . route('payments.edit', $row->id) . '" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center" title="Edit Payment">
                            <iconify-icon icon="lucide:edit"></iconify-icon>
                        </a>';
                    }

                    // Add history button for all users with view permission
                    if ($user->can('payment-view')) {
                        $output.=  '
                        <a href="' . route('payments.history', $row->id) . '" class="w-32-px h-32-px bg-info-focus text-info-main rounded-circle d-inline-flex align-items-center justify-content-center" title="Payment History">
                            <iconify-icon icon="lucide:history"></iconify-icon>
                        </a>';
                    }

                    $output.=  '<a href="' . route('payments.download', $row->id) . '" class="w-32-px h-32-px bg-warning-focus text-warning-main rounded-circle d-inline-flex align-items-center justify-content-center">
                        <iconify-icon icon="lucide:download"></iconify-icon>
                    </a>';

                    // Add delete button with permission check
                    if ($user->can('payment-delete')) {
                        $output.=  '
                        <a href="javascript:void(0);" onclick="confirmDeletePayment(\'' . route('payments.delete', $row->id) . '\',\'' . $row->id . '\')"
                           class="w-32-px h-32-px bg-danger-100 text-danger-600 rounded-circle d-inline-flex align-items-center justify-content-center" title="Delete Payment">
                            <iconify-icon icon="material-symbols:delete-outline"></iconify-icon>
                        </a>';
                    }

                    return $output;
                })
                ->filter(function ($query) use ($request) {

                    if ($request->has('client') && $request->client != '') {
                        $query->where('client_id', $request->client);
                    }
                    // Filter by Date Range
                    if ($request->has('daterange') && !empty($request->daterange)) {
                        $dates = explode(' - ', $request->daterange);
                        if (count($dates) === 2) {
                            $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                            $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                            $query->whereBetween('paid_on', [$start_date, $end_date]);
                        }
                    }

                })

                ->editColumn('paid_on', function ($row) {
                    return $row->paid_on ? date('d-m-Y', strtotime($row->paid_on)) : 'NA';
                })


                ->rawColumns(['action']) // Ensure HTML rendering
                ->make(true);
        }
    }
    public function show($id)
    {
        $user = Auth::user();

        // Fetch the payment record
        $payment = Payment::where('id', $id)->firstOrFail();

        if ($user->role_id == 3) {
            // Get the employee record for the logged-in user
            $employee = Employee::where('user_id', $user->id)->first();

            if ($employee) {
                // Get the client's ID from the payment record
                $clientId = $payment->client_id;

                // Check if this client is assigned to the employee
                $isAssigned = EmployeeClient::where('employee_id', $employee->id)
                    ->where('client_id', $clientId)
                    ->exists();

                if (!$isAssigned) {
                    // If the client is not assigned to this employee, return 403
                    abort(Response::HTTP_FORBIDDEN, 'You do not have permission to view this payment.');
                }
            } else {
                // If no employee record found, deny access
                abort(Response::HTTP_FORBIDDEN, 'You do not have permission to view this payment.');
            }
        }

        $verificationUrl = $this->verify_payment($payment->id);
        return view('payments.view', compact('payment', 'verificationUrl'));
    }
    public function downloadPDF($id)
    {
        $payment = Payment::findOrFail($id);
        $verificationUrl = $this->verify_payment($payment->id);
        $pdf = Pdf::loadView('payments.receipt_template', compact('payment', 'verificationUrl'))->setPaper('a4', 'portrait');
        return $pdf->download("payment_receipt_{$payment->id}.pdf");
    }

    // Print Invoice
    public function print($id)
    {
        $payment = Payment::findOrFail($id);
        $verificationUrl = $this->verify_payment($payment->id);
        $print = true;
        return view('payments.receipt_template', compact('payment', 'verificationUrl', 'print'));
    }

    public function add($client_id = null)
    {
        $user = Auth::user();
        if ($user->role_id == 3) {
            // Fetch the employee record for the logged-in user
            $employee = Employee::where('user_id', $user->id)->first();

            if ($employee) {
                // Get only the clients assigned to this employee
                $clientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');
                $clients = Client::whereIn('id', $clientIds)->where('status', 1)->get();
            } else {
                // If no employee record found, return an empty collection
                $clients = collect([]);
            }
        } else {
            // For other roles, return all active clients
            $clients = Client::get();
        }

        // If client_id is provided, find the specific client for pre-selection
        $selectedClient = null;
        if ($client_id) {
            $selectedClient = Client::where('id', $client_id)->where('status', 1)->first();
            // If client not found or inactive, redirect to add payment without pre-selection
            if (!$selectedClient) {
                return redirect()->route('payments.add')->with('warning', 'Client not found or inactive. Please select a client.');
            }

            // For employees, check if the client is assigned to them
            if ($user->role_id == 3 && $employee) {
                $isAssigned = EmployeeClient::where('employee_id', $employee->id)
                    ->where('client_id', $client_id)
                    ->exists();
                if (!$isAssigned) {
                    return redirect()->route('payments.add')->with('warning', 'You are not assigned to this client.');
                }
            }
        }

        return view('payments.add', compact('clients', 'selectedClient'));
    }
    public function store(Request $request)
{
    // Validate request
    $request->validate([
        'client_id' => 'required|exists:clients,id',
        'amount' => 'required|numeric|min:1',
        'tds_amount' => 'nullable|numeric|min:0', // Ensure TDS is a valid number
        'payment_mode' => 'required',
        'payment_date' => 'required',
        'description' => 'required|string',
        'invoice_id' => 'nullable|exists:invoices,id'
    ]);

    $paidOn = Carbon::createFromFormat('d/m/Y', $request->payment_date)->format('Y-m-d');

    // Find invoice if provided
    $invoice = $request->invoice_id ? Invoice::find($request->invoice_id) : null;

    // Find client
    $client = Client::find($request->client_id);

    // Process Normal Payment
    $payment = new Payment();
    $payment->client_id = $request->client_id;
    $payment->invoice_id = $request->invoice_id;
    $payment->amount = $request->amount;
    $payment->payment_mode = $request->payment_mode;
    $payment->ref_number = $request->payment_reference;
    $payment->paid_on = $paidOn;
    $payment->comments = $request->description;
    $payment->created_by = Auth::user()->name;
    $payment->payment_status = 'Partially Paid';

    $payment->pending_amount =0;
    // Update Invoice and Client Data
    if ($invoice) {
        $invoice->paid_amount += $request->amount;
        $invoice->unpaid_amount -= $request->amount;

        if ($invoice->unpaid_amount <= 0) {
            $invoice->invoice_status = 'Paid';
            $payment->payment_status = 'Paid';
            $invoice->unpaid_amount = 0;
        } else {
            $invoice->invoice_status = 'Partially Paid';
        }
        $payment->pending_amount = $invoice->unpaid_amount;
        $invoice->save();
    }
    $payment->save();

    if ($client) {
        $client->pending_amount -= $request->amount;
        if ($client->pending_amount < 0) {
            $client->pending_amount = 0;
        }
        $client->save();
    }

    // Insert Normal Payment Record in Account Ledger
    AccountLedger::create([
        'client_id' => $request->client_id,
        'transaction' => 'Payment',
        'transaction_date' => $paidOn,
        'detials' => 'Payment Received for ' . ($invoice ? $invoice->invoice_code : 'No Invoice'),
        'amount' => NULL,
        'payments' => $request->amount,
        'balance' => $client ? $client->pending_amount : 0
    ]);

    // **Handle TDS Payment If Checked**
    if ($request->tds_amount && $request->tds_amount > 0) {
        $tdsPayment = new Payment();
        $tdsPayment->client_id = $request->client_id;
        $tdsPayment->invoice_id = $request->invoice_id;
        $tdsPayment->amount = $request->tds_amount;
        $tdsPayment->payment_mode = 'TDS'; // TDS Payment Mode
        $tdsPayment->ref_number = 'TDS-' . strtoupper(Str::random(8));
        $tdsPayment->paid_on = $paidOn;
        $tdsPayment->comments = 'TDS Deducted: ' . $request->description;
        $tdsPayment->created_by = Auth::user()->name;
        $tdsPayment->payment_status = 'Paid';

        $tdsPayment->pending_amount =0;
        // Deduct TDS from Invoice
        if ($invoice) {
            $invoice->paid_amount += $request->tds_amount;
            $invoice->unpaid_amount -= $request->tds_amount;

            if ($invoice->unpaid_amount <= 0) {
                $invoice->invoice_status = 'Paid';
                $tdsPayment->payment_status = 'Paid';
                $invoice->unpaid_amount = 0;
            }
            $tdsPayment->pending_amount = $invoice->unpaid_amount;
            $invoice->save();
        }
        $tdsPayment->save();

        // Deduct TDS from Client Balance
        if ($client) {
            $client->pending_amount -= $request->tds_amount;
            if ($client->pending_amount < 0) {
                $client->pending_amount = 0;
            }
            $client->save();
        }

        // Insert TDS Payment in Account Ledger
        AccountLedger::create([
            'client_id' => $request->client_id,
            'transaction' => 'TDS Deduction',
            'transaction_date' => $paidOn,
            'detials' => 'TDS Deducted for ' . ($invoice ? $invoice->invoice_code : 'No Invoice'),
            'amount' => NULL,
            'payments' => $request->tds_amount,
            'balance' => $client ? $client->pending_amount : 0
        ]);
    }

    // Log payment creation
    PaymentLogService::logCreated($payment, 'Payment created via manual entry');

    // Queue email for the payment if requested
    if ($request->has('send_email') && $request->send_email) {
        $this->queuePaymentEmail($payment->id, $request->client_id);
    }

    // Send notification alert for payment creation
    $notificationService = new NotificationAlertService();
    $notificationService->sendPaymentCreationAlert($payment);

    // Prepare success message based on TDS usage and email option
    $hasTds = $request->tds_amount && $request->tds_amount > 0;
    $message = $hasTds ? 'Payment recorded successfully with TDS!' : 'Payment recorded successfully!';

    if ($request->has('send_email') && $request->send_email) {
        $message .= ' Email has been queued for sending.';
    }

    return redirect()->route('payments.index')->with('success', $message);
}

    /**
     * Show the form for editing the specified payment.
     */
    public function edit($id)
    {
        $payment = Payment::findOrFail($id);
        $clients = Client::where('status', 1)->get();

        // Get pending invoices for the selected client
        $pendingInvoices = Invoice::where('client_id', $payment->client_id)
            ->where('unpaid_amount', '>', 0)
            ->get(['id', 'invoice_code', 'unpaid_amount']);

        return view('payments.edit', compact('payment', 'clients', 'pendingInvoices'));
    }

    /**
     * Update the specified payment in storage.
     */
    public function update(Request $request, $id)
    {
        // Validate request - Note: Sensitive fields are excluded
        $request->validate([
            'payment_mode' => 'required',
            'payment_date' => 'required',
            'description' => 'required|string',
            'payment_reference' => 'nullable|string'
        ]);

        try {
            DB::beginTransaction();

            $payment = Payment::findOrFail($id);

            // Store original values for audit trail
            $originalData = [
                'payment_mode' => $payment->payment_mode,
                'paid_on' => $payment->paid_on,
                'comments' => $payment->comments,
                'ref_number' => $payment->ref_number
            ];

            // Convert date format
            $paidOn = Carbon::createFromFormat('d/m/Y', $request->payment_date)->format('Y-m-d');

            // Update only non-sensitive fields
            $payment->payment_mode = $request->payment_mode;
            $payment->ref_number = $request->payment_reference;
            $payment->paid_on = $paidOn;
            $payment->comments = $request->description;
            $payment->save();

            // Prepare new data for logging
            $newData = [
                'payment_mode' => $payment->payment_mode,
                'paid_on' => $payment->paid_on,
                'comments' => $payment->comments,
                'ref_number' => $payment->ref_number
            ];

            // Log the changes using PaymentLogService
            PaymentLogService::logUpdated($payment, $originalData, $newData, 'Payment updated via edit form');

            // Also log to Laravel log for backup
            Log::info("Payment updated", [
                'payment_id' => $payment->id,
                'updated_by' => Auth::user()->name,
                'updated_at' => now(),
                'original_data' => $originalData,
                'new_data' => $newData
            ]);

            DB::commit();

            return redirect()->route('payments.index')->with('success', 'Payment updated successfully!');
        } catch (\Exception $e) {
            DB::rollback();
            Log::error("Failed to update payment: " . $e->getMessage());
            return back()->with('error', 'Error updating payment: ' . $e->getMessage());
        }
    }

    /**
     * Show payment change history
     */
    public function showHistory($id)
    {
        $payment = Payment::findOrFail($id);
        $history = PaymentLogService::getPaymentHistory($id);

        return view('payments.history', compact('payment', 'history'));
    }

    /**
     * Get payment logs for DataTables
     */
    public function getPaymentLogs(Request $request, $id)
    {
        if ($request->ajax()) {
            $logs = PaymentLogService::getPaymentHistory($id, 1000); // Get more for DataTable

            return DataTables::of($logs)
                ->addColumn('action_badge', function ($row) {
                    $badges = [
                        'created' => '<span class="badge bg-success">Created</span>',
                        'updated' => '<span class="badge bg-warning">Updated</span>',
                        'deleted' => '<span class="badge bg-danger">Deleted</span>'
                    ];
                    return $badges[$row->action] ?? '<span class="badge bg-secondary">' . ucfirst($row->action) . '</span>';
                })
                ->addColumn('change_description', function ($row) {
                    return $row->change_description;
                })
                ->addColumn('timestamp', function ($row) {
                    return $row->created_at->format('d/m/Y H:i:s');
                })
                ->addColumn('user_info', function ($row) {
                    $output = '<strong>' . $row->changed_by . '</strong>';
                    if ($row->ip_address) {
                        $output .= '<br><small class="text-muted">IP: ' . $row->ip_address . '</small>';
                    }
                    return $output;
                })
                ->rawColumns(['action_badge', 'user_info'])
                ->make(true);
        }
    }

    public function get_account_leger_data(Request $request)
    {
        if ($request->ajax()) {
            $invoices = AccountLedger::select(['id', 'client_id', 'transaction', 'transaction_date', 'detials',  \DB::raw('COALESCE(amount, 0) as amount'),
            \DB::raw('COALESCE(payments, 0) as payments'), 'balance'])
                ->orderBy('id', 'ASC');

            // Get start date from request
            if ($request->has('daterange') && !empty($request->daterange)) {
                $dates = explode(' - ', $request->daterange);
                if (count($dates) === 2) {
                    $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                    $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();

                    // Filter transactions based on date range
                    $invoices->whereBetween('transaction_date', [$start_date, $end_date]);

                    // Get the opening balance before the start date
                    // $opening_balance = AccountLedger::where('transaction_date', '<', $start_date)
                    // ->where('client_id', $request->client) // Move before value()
                    // ->orderBy('transaction_date', 'DESC')
                    // ->value('balance'); // Get the latest balance before the start date
                    //updated query without balance column
                    //dd($start_date,$request->client);
                    $opening_balance = AccountLedger::where('transaction_date', '<', $start_date)
                                        ->where('client_id', $request->client)
                                        ->selectRaw('SUM(COALESCE(amount, 0) - COALESCE(payments, 0)) as opening_balance')
                                        ->value('opening_balance');

                    // Ensure opening balance is set
                    $opening_balance = $opening_balance ?? 0;

                    // Manually insert an "Opening Balance" row at the beginning
                    $openingBalanceRow = [
                        'id' => null,
                        'client_id' => null,
                        'transaction' => 'Opening Balance',
                        'transaction_date' => $start_date->format('d-m-Y'),
                        'detials' => '',
                        'amount' => $opening_balance,
                        'payments' => 0,
                        'balance' => $opening_balance,
                    ];
                    //   dd($openingBalanceRow);
                }
            }

            $data = DataTables::of($invoices)
                ->filter(function ($query) use ($request) {
                    if ($request->has('client') && $request->client != '') {
                        $query->where('client_id', $request->client);
                    }
                })
                ->editColumn('transaction_date', function ($row) {
                    return $row->transaction_date ? date('d-m-Y', strtotime($row->transaction_date)) : 'NA';
                })
                // ->editColumn('amount', function ($row) {
                //     return $row->amount ? '₹' . Controller::IND_money_format($row->amount, 2) : '';
                // })
                // ->editColumn('payments', function ($row) {
                //     return $row->payments ? '₹' . Controller::IND_money_format($row->payments, 2) : '';
                // })
                // ->editColumn('balance', function ($row) {
                //     return $row->balance ? '₹' . Controller::IND_money_format($row->balance, 2) :0;
                // })
                ->rawColumns(['transaction', 'balance']) // Ensure HTML rendering
                ->toArray(); // Convert DataTables response to an array

            // Prepend the opening balance row if it exists
            if (isset($openingBalanceRow)) {
                array_unshift($data['data'], $openingBalanceRow);
            }

            return response()->json($data);
        }
    }
    public function getAccountSummary(Request $request)
    {
        $daterange = $request->daterange;
        $client_id = $request->client;

        // Extract date range
        $dates = explode(' - ', $daterange);
        $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
        $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();

        // Get opening balance before the start date
        $opening_balance =  AccountLedger::where('transaction_date', '<', $start_date)
                                        ->where('client_id', $request->client)
                                        ->selectRaw('SUM(COALESCE(amount, 0) - COALESCE(payments, 0)) as balance')
                                        ->value('balance')?? 0;
        // AccountLedger::where('client_id', $client_id)
        //     ->where('transaction_date', '<', $start_date)
        //     ->orderBy('transaction_date', 'desc')
        //     ->value('balance') ?? 0;
        // Get total invoice amount in range
        $invoiced_amount = AccountLedger::where('client_id', $client_id)
            ->whereBetween('transaction_date', [$start_date, $end_date])
            ->sum('amount');

        // Get total payment amount in range
        $amount_received = AccountLedger::where('client_id', $client_id)
            ->whereBetween('transaction_date', [$start_date, $end_date])
            ->sum('payments');

        // Calculate balance due
        $balance_due = $opening_balance + $invoiced_amount - $amount_received;
        $advance_amount=Client::findOrFail($client_id)->advance_amount;

        // Get total transaction count in the date range
        $total_transactions = AccountLedger::where('client_id', $client_id)
            ->whereBetween('transaction_date', [$start_date, $end_date])
            ->count();

        return response()->json([
            'date_range' => $daterange,
            'opening_balance' => Controller::IND_money_format($opening_balance, 2),
            'invoiced_amount' => Controller::IND_money_format($invoiced_amount, 2),
            'amount_received' => Controller::IND_money_format($amount_received, 2),
            'balance_due' => Controller::IND_money_format($balance_due, 2),
            'advanc_amount' => Controller::IND_money_format($advance_amount ? $advance_amount : 0, 2),
            'total_transactions' => $total_transactions,
        ]);
    }
    public function paymentDelete(Request $request, $id)
    {
        try {
            DB::beginTransaction();

            $payment = Payment::findOrFail($id);
            $invoice = null;

            // Validate reason
            if (empty($request->reason)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Reason is required for deleting payment'
                ]);
            }

            //insert into deleted payment table
            DeletedPayment::create([
                'client_id' => $payment->client_id,
                'invoice_id' => $payment->invoice_id,
                'payment_mode' => $payment->payment_mode,
                'amount' => $payment->amount,
                'paid_on' => $payment->paid_on,
                'ref_number' => $payment->ref_number,
                'payment_status' => $payment->payment_status,
                'comments' => $payment->comments,
                'created_by' => $payment->created_by,
                'pending_amount' => $payment->pending_amount,
                'payment_created_at' => $payment->created_at,
                'payment_updated_at' => $payment->updated_at,
                'deleted_by' => Auth::user()->name,
                'deleted_at' => Carbon::now(),
                'deleted_reason' => $request->reason,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // If payment is linked to an invoice, update invoice amounts
            if ($payment->invoice_id) {
                $invoice = Invoice::findOrFail($payment->invoice_id);

                // Revert the payment amount from invoice
                $invoice->paid_amount -= $payment->amount;
                $invoice->unpaid_amount += $payment->amount;

                // Update invoice status based on new unpaid amount
                if ($invoice->paid_amount == 0) {
                    $invoice->invoice_status = 'Pending';
                } else if ($invoice->paid_amount > 0) {
                    $invoice->invoice_status = 'Partially Paid';
                }

                $invoice->save();
            }

            // Update client pending amount
            if ($payment->client_id) {
                $client = Client::findOrFail($payment->client_id);
                $client->pending_amount += $payment->amount;
                $client->save();
            }

            // Delete account ledger entry for this payment
            AccountLedger::where('client_id', $payment->client_id)
                ->where(function($query) use ($payment, $invoice) {
                    $query->where('detials', 'like', '%Payment Received for ' . ($invoice ? $invoice->invoice_code : 'No Invoice') . '%')
                          ->where('transaction_date', $payment->paid_on)
                          ->where('payments', $payment->amount);
                })
                ->delete();

            // Log payment deletion before deleting
            PaymentLogService::logDeleted($payment, $request->reason);

            // Delete the payment
            $payment->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Payment deleted successfully!'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    public function mailSent($id)
    {
        try {
            $payment = Payment::findOrFail($id);

            // Get the appropriate email address based on environment
            $recipientEmail = $this->getValidatedEmailAddress($payment->client);

            if (!$recipientEmail) {
                return back()->with('error', 'No valid email address found for this client.');
            }

            // Queue the email job for immediate processing
            SendPaymentEmailJob::dispatch($id, $recipientEmail);

            // Log the queuing attempt
            $this->logEmailAttempt('payment', $id, $recipientEmail, 'queued', 'Manual send');

            return back()->with('success', 'Payment receipt email has been queued for sending to: ' . $recipientEmail);

        } catch (\Exception $e) {
            Log::error("Failed to queue payment email for payment ID {$id}: " . $e->getMessage());
            return back()->with('error', 'Failed to queue payment email. Please try again.');
        }
    }

    /**
     * Get email configuration for the modal
     */
    public function getEmailConfig($id)
    {
        try {
            $payment = Payment::findOrFail($id);

            // Get email environment from company settings
            $emailEnvironment = $this->getEmailEnvironment();

            // Get company email from settings
            $companyEmail = DB::table('company_settings')
                            ->where('setting_key', 'email')
                            ->value('setting_value') ?: config('mail.from.address');

            $config = [
                'app_env' => $emailEnvironment,
                'client_email' => $payment->client->email,
                'tester_email' => $this->getTesterEmail(),
                'sender_name' => config('app.name'),
                'sender_email' => $companyEmail,
                'payment_ref' => $payment->ref_number ?: 'Payment ID ' . $payment->id,
                'client_name' => $payment->client->business_name
            ];

            return response()->json($config);

        } catch (\Exception $e) {
            Log::error("Failed to get email config for payment ID {$id}: " . $e->getMessage());
            return response()->json(['error' => 'Failed to load email configuration'], 500);
        }
    }

    /**
     * Send email with custom recipient
     */
    public function sendEmailWithCustomRecipient(Request $request, $id)
    {
        try {
            $request->validate([
                'recipient_email' => 'required|email'
            ]);

            $payment = Payment::findOrFail($id);
            $recipientEmail = $request->recipient_email;

            // Validate email format
            if (!$this->isValidEmail($recipientEmail)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid email address format.'
                ], 400);
            }

            // Queue the email job for immediate processing
            SendPaymentEmailJob::dispatch($id, $recipientEmail);

            // Log the queuing attempt
            $this->logEmailAttempt('payment', $id, $recipientEmail, 'queued', 'Manual send via modal');

            return response()->json([
                'success' => true,
                'message' => 'Payment receipt email has been queued for sending to: ' . $recipientEmail
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $e->validator->errors()->all())
            ], 422);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment not found.'
            ], 404);
        } catch (\Exception $e) {
            Log::error("Failed to queue payment email for payment ID {$id}: " . $e->getMessage());

            // Provide more specific error message if possible
            $errorMessage = 'Failed to queue payment email. Please try again.';
            if (str_contains($e->getMessage(), 'mail')) {
                $errorMessage = 'Email configuration error. Please check your email settings.';
            } elseif (str_contains($e->getMessage(), 'queue')) {
                $errorMessage = 'Queue system error. Please contact administrator.';
            }

            return response()->json([
                'success' => false,
                'message' => $errorMessage
            ], 500);
        }
    }
    /**
     * Queue payment email based on environment settings
     */
    private function queuePaymentEmail($paymentId, $clientId)
    {
        try {
            // Get client information
            $client = Client::find($clientId);

            if (!$client) {
                Log::warning("Client not found for payment ID: {$paymentId}");
                return;
            }

            // Get the appropriate email address
            $recipientEmail = $this->getValidatedEmailAddress($client);

            if (!$recipientEmail) {
                Log::warning("No valid email address found for client {$client->id} (Payment ID: {$paymentId})");
                return;
            }

            // Queue the email job for immediate processing (payments are always manual)
            SendPaymentEmailJob::dispatch($paymentId, $recipientEmail);

            // Log the queuing attempt
            $this->logEmailAttempt('payment', $paymentId, $recipientEmail, 'queued', 'Manual creation');

        } catch (\Exception $e) {
            Log::error("Failed to queue payment email for payment ID {$paymentId}: " . $e->getMessage());
        }
    }

    public function verify_payment($id)
    {
        $encrypted = Crypt::encryptString($id);
        $verificationUrl = route('payment.verify', ['encrypted' => $encrypted]);
        $payment_url = new \Endroid\QrCode\QrCode($verificationUrl);
        $writer = new \Endroid\QrCode\Writer\PngWriter();
        $result = $writer->write($payment_url);
        // Return base64 for inline display
        $url = $result->getDataUri();
        return $url;
    }

    public function verify($encrypted)
    {
        try {
            $paymentId = Crypt::decryptString($encrypted);
            $payment = Payment::findOrFail($paymentId);
            $print = true;
            return view('payments.receipt_template', compact('payment', 'print'));
        } catch (\Exception $e) {
            abort(404, 'Invalid or expired verification link.');
        }
    }
    public function deletedPayments()
    {
        return view('payments.deleted_payments');
    }
    public function getDeletedPayments(Request $request)
    {
        if ($request->ajax()) {
            $user = Auth::user();

            $payments = DeletedPayment::select(['id', 'client_id', 'invoice_id', 'payment_mode', 'amount', 'paid_on', 'ref_number', 'payment_status', 'comments','deleted_at','deleted_by','deleted_reason'])
                ->orderBy('id', 'desc');

            return DataTables::of($payments)
                ->addColumn('client_name', function ($row) {
                    return $row->client_id ? $row->client->name : '-';
                })
                ->addColumn('client_code', function ($row) {
                    return $row->client_id ? $row->client->client_code : '-';
                })
                ->addColumn('invoice_code', function ($row) {
                    return $row->invoice_id ? $row->invoice->invoice_code : '-';
                })
                // ->addColumn('amount', function ($row) {
                //     return '₹' . Controller::IND_money_format($row->amount, 2);
                // })
                ->addColumn('paid_on', function ($row) {
                    return $row->paid_on ? date('d-m-Y', strtotime($row->paid_on)) : 'NA';
                })
                ->rawColumns(['action'])
                ->make(true);
        }
    }

    /**
     * Export payments with all fields and filters
     */
    public function exportPayments(Request $request)
    {
        try {
            $user = Auth::user();

            // Build the query with all relationships
            $query = Payment::with(['client', 'invoice'])
                ->orderBy('id', 'desc');

            // Apply role-based filtering (same as in getPayments method)
            if ($user->role_id == 3) {
                $employee = Employee::where('user_id', $user->id)->first();
                if ($employee) {
                    $clientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');
                    $query->whereIn('client_id', $clientIds);
                } else {
                    $query->whereRaw('1 = 0');
                }
            }

            // Apply filters (same as in getPayments method)
            if ($request->has('client') && $request->client != '') {
                $query->where('client_id', $request->client);
            }

            if ($request->has('daterange') && !empty($request->daterange)) {
                $dates = explode(' - ', $request->daterange);
                if (count($dates) === 2) {
                    $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                    $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                    $query->whereBetween('paid_on', [$start_date, $end_date]);
                }
            }

            // Get all payments
            $payments = $query->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers - All available fields
            $headers = [
                'Payment ID',
                'Client Name',
                'Client Code',
                'Invoice Code',
                'Payment Amount',
                'Payment Mode',
                'Payment Date',
                'Reference Number',
                'Comments',
                'Payment Status',
                'Created By',
                'Created Date',
                'Last Updated'
            ];

            $csvData[] = $headers;

            // Process each payment
            foreach ($payments as $payment) {
                $row = [
                    $payment->id,
                    $payment->client ? $payment->client->name : 'N/A',
                    $payment->client ? $payment->client->client_code : 'N/A',
                    $payment->invoice ? $payment->invoice->invoice_code : 'No Invoice',
                    '₹' . number_format($payment->amount, 2),
                    $payment->payment_mode,
                    $payment->paid_on ? date('d/m/Y', strtotime($payment->paid_on)) : 'N/A',
                    $payment->ref_number ?: 'N/A',
                    $payment->comments ?: 'N/A',
                    $payment->payment_status ?: 'N/A',
                    $payment->created_by ?: 'N/A',
                    $payment->created_at ? $payment->created_at->format('d/m/Y H:i:s') : 'N/A',
                    $payment->updated_at ? $payment->updated_at->format('d/m/Y H:i:s') : 'N/A'
                ];

                $csvData[] = $row;
            }

            // Generate simple filename with timestamp
            $filename = 'Payments_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
