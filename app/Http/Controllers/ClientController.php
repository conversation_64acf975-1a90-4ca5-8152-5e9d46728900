<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Client;
use App\Models\Invoice;
use App\Models\Service;
use App\Models\District;
use App\Models\Employee;
use App\Models\ServiceType;
use Illuminate\Http\Request;
use App\Models\ClientService;
use Illuminate\Http\Response;
use App\Models\ClientDiscount;
use App\Models\ClientServiceWeight;
use App\Models\EmployeeClient;
use App\Models\Payment;
use App\Models\AccountLedger;
use App\Models\ClientChangeLog;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\DB;
use App\Models\State as ModelsState;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Crypt;
use App\Traits\EmailHelperTrait;
use App\Services\NotificationAlertService;

class ClientController extends Controller
{
    use EmailHelperTrait;
    public function index()
    {
        $client_areas = Client::distinct()->orderBy('area')->pluck('area');
        $districts = District::where('status', 1)
                    ->whereHas('clients') // Only districts that have clients
                    ->orderBy('name')
                    ->get();
        return view('clients.list', compact('client_areas','districts'));
    }
    public function add()
    {

        return view('clients.add');
    }
    //
    // Store method to save client data
    public function store(Request $request)
    {
        // Validate form data
        $validated = $request->validate([
            'name'          => 'required|string|max:255',
            'business_name' => 'required|string|max:255',
            'phone'         => 'required|digits:10',
            'secondary_phone' => 'nullable|digits:10',
            'email'         => 'required|email|unique:clients,email',
            'secondary_email' => 'nullable|email',
            'address'       => 'required|string',
            'city'          => 'required|string|max:255',
            'area'          => 'required|string|max:255',
            'state_id'      => 'required|integer|exists:states,id',
            'district_id'   => 'required|integer|exists:districts,id',
            'pincode'       => 'required|digits:6',
            'hcf_no'        => 'required|string|max:255',
            'hcf_type'      => 'required|string|max:255',
            'description'   => 'required|string',
            'client_type'   => 'required|integer',

            // Optional fields
            'gst'           => 'nullable|string|max:15|regex:/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/',
            'pan'           => 'nullable|string|max:10|regex:/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/',
            'lang_lat'      => 'nullable|string|max:255',
            'advance_amount' => 'nullable|numeric|min:0',
            'logo'          => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048', // Optional image upload (Max 2MB)
        ]);
        $data = $request->all();
        // Generate client_code (e.g., ABC0001)
        $lastClient = Client::latest('id')->first();
        $lastNumber = $lastClient ? intval(substr($lastClient->client_code, 3)) : 0;
        $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        $data['client_code'] = config('company.details.company_suffix') . $newNumber;
        $data['user_id'] = 1;

        // Handle file upload
        if ($request->hasFile('logo')) {
            $file = $request->file('logo');

            // Generate a unique name using timestamp and original extension
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();

            // Store the file in storage/app/public/clients/
            $filePath = $file->storeAs('clients', $filename, 'public');

            // Save the file path in the database
            $data['logo'] = $filePath;
        }

        // Save data to database
        $client = Client::create($data);

        // Log the client creation
        ClientChangeLog::logChange(
            $client->id,
            'created',
            Auth::user()->name,
            null,
            $client->toArray(),
            'Client created via registration form'
        );

        // Send notification alert for client creation
        $notificationService = new NotificationAlertService();
        $notificationService->sendClientCreationAlert($client);

        // Redirect to client list page with success message
        return redirect()->route('clients.index')->with('success', $data['name'] . ' created successfully!');
    }
    public function edit($id)
    {
        $client = Client::where('id', $id)->first();
        $states=ModelsState::where('status',1)->get();
        $districts=District::where('status',1)->get();

        return view('clients.edit', compact('client','states','districts'));
    }
    public function update(Request $request, Client $client)
    {
        // Validate form data
        $validated = $request->validate([
            'name'          => 'required|string|max:255',
            'business_name' => 'required|string|max:255',
            'phone'         => 'required|digits:10',
            'secondary_phone' => 'nullable|digits:10',
            'email'         => 'required|email|unique:clients,email,' . $client->id,
            'secondary_email' => 'nullable|email',
            'address'       => 'required|string',
            'city'          => 'required|string|max:255',
            'area'          => 'required|string|max:255',
            'state_id'      => 'required|integer|exists:states,id',
            'district_id'   => 'required|integer|exists:districts,id',
            'pincode'       => 'required|digits:6',
            'hcf_no'        => 'required|string|max:100',
            'hcf_type'      => 'required|string|max:100',
            'description'   => 'required|string',
            'client_type'   => 'required|integer',

            // Optional fields
            'gst'           => 'nullable|string|max:15|regex:/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/',
            'pan'           => 'nullable|string|max:10|regex:/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/',
            'lang_lat'      => 'nullable|string|max:255',
            'advance_amount' => 'nullable|numeric|min:0',
            'logo'          => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048', // Optional image upload (Max 2MB)
        ]);

        $data = $request->except(['logo']); // Exclude logo from mass update initially

        // Handle file upload
        if ($request->hasFile('logo')) {
            $file = $request->file('logo');

            // Generate a unique name using timestamp and original extension
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();

            // Store the file in storage/app/public/clients/
            $filePath = $file->storeAs('clients', $filename, 'public');

            // Delete the old logo if exists
            if ($client->logo && Storage::disk('public')->exists($client->logo)) {
                Storage::disk('public')->delete($client->logo);
            }

            // Save the new file path
            $data['logo'] = $filePath;
        }

        // Store old values before update
        $oldValues = $client->getOriginal();

        // Update client data
        $client->update($data);

        // Log the client update
        ClientChangeLog::logChange(
            $client->id,
            'updated',
            Auth::user()->name,
            $oldValues,
            $client->fresh()->toArray(),
            'Client information updated via edit form'
        );

        // Redirect to client list page with success message
        return redirect()->route('clients.index')->with('success', $client->name . ' updated successfully!');
    }


    public function show($id)
    {
        $user = Auth::user();

        // Fetch client details
        $client = Client::where('id', $id)->firstOrFail();

        // If user has role_id == 3, check if the client is assigned to them
        if ($user->role_id == 3) {
            // Get the employee record
            $employee = Employee::where('user_id', $user->id)->first();

            if (!$employee) {
                abort(Response::HTTP_FORBIDDEN, 'You do not have permission to view this Client.');
            }

            // Check if the client is assigned to the employee
            $isAssigned = EmployeeClient::where('client_id', $client->id)
                ->where('employee_id', $employee->id)
                ->exists();

            if (!$isAssigned) {

                abort(Response::HTTP_FORBIDDEN, 'You do not have permission to view this Client.');
            }
        }

        // Fetch employees and assigned employee
        $employees = Employee::where('status', 1)->get();
        $assignedEmployee = EmployeeClient::where('client_id', $client->id)
            ->with('employee')
            ->first();

        // Fetch client services
        $client_services = ClientService::where('client_id', $client->id)
            ->orderBy('start_date', 'DESC')
            ->get();

        return view('clients.view', compact('client', 'employees', 'assignedEmployee', 'client_services'));
    }

    public function getClients(Request $request)
    {
        if ($request->ajax()) {
             // Get the logged-in user
            $user = Auth::user();

            // Fetch base client query
            $clients = Client::select(['id', 'client_code', 'name', 'phone', 'hcf_type','district_id', 'area', 'status', 'created_at']);

            // If the user has role_id == 3, filter based on employee_client table
            if ($user->role_id == 3) {
                // Get the employee ID of the logged-in user
                $employee = Employee::where('user_id', $user->id)->first();

                if ($employee) {
                    // Get client IDs assigned to this employee
                    $clientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');

                    // Filter clients
                    $clients->whereIn('id', $clientIds);
                } else {
                    // If employee not found, return empty result
                    $clients->whereRaw('1 = 0');
                }
            }

            return DataTables::of($clients)
                ->addColumn('status', function ($row) {
                    if ($row->status == 1) {
                        return '<span class="badge badge-sm text-sm fw-normal text-success-600 bg-success-100 px-20 py-9 radius-4 text-white">Active</span>';
                    } else {
                        return '<span class="badge badge-sm text-sm fw-normal text-danger-600 bg-danger-100 px-20 py-9 radius-4 text-white">Inactive</span>';
                    }
                })
                ->addColumn('district', function ($row) {
                   return $row->district->name;
                })
                ->addColumn('action', function ($row) {
                    $user = auth()->user(); // Get authenticated user
                    $output = '';
                    // Check 'client-view' permission
                    if ($user->can('client-view')) {
                        $output .= '
                            <a href="' . route('clients.show', $row->id) . '" class="w-32-px h-32-px bg-primary-light text-primary-600 rounded-circle d-inline-flex align-items-center justify-content-center">
                                <iconify-icon icon="iconamoon:eye-light"></iconify-icon>
                            </a>';
                    }

                    // Check 'client-edit' permission
                    if ($user->can('client-edit')) {
                        $output .= '
                            <a href="' . route('clients.edit', $row->id) . '" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center">
                                <iconify-icon icon="lucide:edit"></iconify-icon>
                            </a>';
                    }

                    // Check if the client has any active services and user has view permission
                    if ($user->can('client-view')) {
                        $hasActiveService = DB::table('client_services')
                        ->where('client_id', $row->id)
                        ->where('status', 1)
                        ->exists();

                        if ($hasActiveService) {
                            $output .= ' <a href="' . route('clients.download', $row->id) . '" class="w-32-px h-32-px bg-warning-focus text-warning-main rounded-circle d-inline-flex align-items-center justify-content-center">
                                <iconify-icon icon="lucide:download"></iconify-icon>
                            </a>';
                        }
                    }
                    return $output;
                })
                ->filter(function ($query) use ($request) {
                    if ($request->has('searchStatus') && $request->searchStatus != '') {
                        $query->where('status', $request->searchStatus);
                    }
                    if ($request->has('searchDistrict') && $request->searchDistrict != '') {
                        $query->where('district_id', $request->searchDistrict);
                    }
                    if ($request->has('searchArea') && $request->searchArea != '') {
                        $query->where('area', $request->searchArea);
                    }
                    if ($request->has('searchkey') && $request->searchkey != '') {
                        $query->where(function ($q) use ($request) {
                            $q->where('name', 'like', "%{$request->searchkey}%")
                                ->orWhere('client_code', 'like', "%{$request->searchkey}%")
                                ->orWhere('hcf_type', 'like', "%{$request->searchkey}%")
                                ->orWhere('phone', 'like', "%{$request->searchkey}%")
                                ->orWhere('secondary_phone', 'like', "%{$request->searchkey}%");
                        });
                    }
                })

                ->rawColumns(['status', 'action']) // Ensure HTML rendering
                ->make(true);
        }
    }
    public function download($id)
    {
        $client = Client::with('district')->findOrFail($id);
        $client_service = ClientService::where('client_id', $id)
                            ->where('status', 1)
                            // ->whereDate('start_date', '<=', now())
                            ->first();
        $validity_date = null;
        if ($client_service) {
            if ($client_service->payment_cycle == 'Monthly') {
                // If end_date is empty, set expiry date to the end of the current year
                // Otherwise, take end_date
                $validity_date = $client_service->end_date
                    ? $client_service->end_date
                    : now()->endOfYear()->toDateString();
            } elseif ($client_service->payment_cycle == 'Yearly') {
                // If end_date is available, take it; otherwise, take next_invoice_date
                $validity_date = $client_service->end_date
                    ? $client_service->end_date
                    : $client_service->next_invoice_date;
            } else {
                $validity_date = null; // Handle cases where payment_cycle is neither monthly nor yearly
            }
        }

        // Get certificate template from settings
        $certificateTemplate = DB::table('company_settings')
            ->where('setting_key', 'certificate_template')
            ->value('setting_value') ?? 'clients.certificate_v2'; // Default to v2 template

        $pdf = Pdf::loadView($certificateTemplate, compact('client','validity_date','client_service'));

        return $pdf->download($client->business_name . '_certificate.pdf');
    }

    public function viewCertificate($id)
    {
        $client = Client::with('district')->findOrFail($id);
        $client_service = ClientService::where('client_id', $id)
                            ->where('status', 1)
                            ->first();
        $validity_date = null;
        if ($client_service) {
            if ($client_service->payment_cycle == 'Monthly') {
                $validity_date = $client_service->end_date
                    ? $client_service->end_date
                    : now()->endOfYear()->toDateString();
            } elseif ($client_service->payment_cycle == 'Yearly') {
                $validity_date = $client_service->end_date
                    ? $client_service->end_date
                    : $client_service->next_invoice_date;
            } else {
                $validity_date = null;
            }
        }

        // Get certificate template from settings
        $certificateTemplate = DB::table('company_settings')
            ->where('setting_key', 'certificate_template')
            ->value('setting_value') ?? 'clients.certificate_v2'; // Default to v2 template

        // Get company details for the certificate
        $company_details = DB::table('company_settings')->pluck('setting_value', 'setting_key')->toArray();

        return view($certificateTemplate, compact('client','validity_date','client_service', 'company_details'));
    }

    public function updateStatus(Request $request)
    {
        $client = Client::find($request->client_id);
        if ($client) {
            $oldStatus = $client->status;
            $client->status = $request->status;
            $client->save();

            // Log the status change
            ClientChangeLog::logChange(
                $client->id,
                'status_changed',
                Auth::user()->name,
                ['status' => $oldStatus],
                ['status' => $request->status],
                'Client status changed via toggle switch'
            );

            if($request->status==0){
                ClientService::where('client_id', $client->id)
                ->where('status', 1)
                ->update([
                    'end_date' => now()->toDateString(),
                    'next_invoice_date' => now()->toDateString()
                ]);
            }

            return response()->json(['message' => 'Status updated successfully!']);
        }
        return response()->json(['message' => 'Client not found!'], 404);
    }
    public function assignEmployee(Request $request)
    {
        $client_id = $request->client_id;
        $employee_id = $request->employee_id;

        // Check if the client exists
        $client = Client::find($client_id);
        if (!$client) {
            return response()->json([
                'status' => 'error',
                'message' => 'Client not found!'
            ]);
        }

        // Check if an assignment already exists
        $existingAssignment = EmployeeClient::where('client_id', $client_id)->first();
        $oldEmployeeId = $existingAssignment ? $existingAssignment->employee_id : null;

        if ($existingAssignment) {
            // Update the existing assignment
            $existingAssignment->employee_id = $employee_id;
            $existingAssignment->updated_at = now();
            $existingAssignment->save();
        } else {
            // Insert new assignment
            EmployeeClient::create([
                'client_id' => $client_id,
                'employee_id' => $employee_id
            ]);
        }

        // Get employee names for logging
        $oldEmployee = $oldEmployeeId ? Employee::find($oldEmployeeId) : null;
        $newEmployee = Employee::find($employee_id);

        // Log the employee assignment change
        ClientChangeLog::logChange(
            $client_id,
            'employee_assigned',
            Auth::user()->name,
            ['assigned_employee' => $oldEmployee ? $oldEmployee->emp_name : 'Not Assigned'],
            ['assigned_employee' => $newEmployee ? $newEmployee->emp_name : 'Not Assigned'],
            'Client assigned to employee: ' . ($newEmployee ? $newEmployee->emp_name : 'Not Assigned')
        );

        return response()->json([
            'status' => 'success',
            'employee_name' => $newEmployee ? $newEmployee->emp_name : 'Not Assigned'
        ]);
    }

    public function applyDiscount(Request $request, $id)
    {
        $client = Client::findOrFail($id);
        $discountAmount = $request->discount_amount;
        $remarks = $request->remarks;
        // Validate discount amount
        $request->validate([
            'discount_amount' => 'required|numeric|max:' . $client->pending_amount,
        ]);
        $client_balance=$client->pending_amount-$discountAmount;
        // 1. Insert record in client_discounts table
        DB::table('client_discounts')->insert([
            'client_id' => $client->id,
            'discount_amount' => $discountAmount,
            'remarks' => $remarks,
            'created_by' => Auth::user()->name,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // 2. Insert record in account_ledgers table
        DB::table('account_ledgers')->insert([
            'client_id' => $client->id,
            'transaction' => 'Discount',
            'transaction_date' => now(),
            'detials' => 'Discount Applied',
            'amount' => NULL,
            'payments' => $discountAmount,
            'balance' => $client_balance, // You can calculate and update balance if required
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // 3. Apply discount to invoices in ASC order
        $remainingDiscount = $discountAmount;

        // Fetch invoices in ASC order where paid_amount > 0
        $invoices = DB::table('invoices')
            ->where('client_id', $client->id)
            ->where('unpaid_amount', '>', 0)
            ->orderBy('id', 'asc')
            ->get();

        foreach ($invoices as $invoice) {
            if ($remainingDiscount <= 0) {
                break;
            }

            $invoiceId = $invoice->id;
            $paidAmount = $invoice->paid_amount;
            $unpaidAmount = $invoice->unpaid_amount;

            // Determine how much of the discount can be applied to this invoice
            $discountToApply = min($remainingDiscount, $unpaidAmount);

            // Determine payment status
            $paymentStatus = ($unpaidAmount - $discountToApply) == 0 ? 'Paid' : 'Partially Paid';

            // Update invoice paid & unpaid amount
            DB::table('invoices')
                ->where('id', $invoiceId)
                ->update([
                    'paid_amount' => $paidAmount + $discountToApply,
                    'unpaid_amount' => $unpaidAmount - $discountToApply,
                    'invoice_status' => $paymentStatus,
                    'updated_at' => now(),
                ]);



            // 4. Insert record in payments table
            DB::table('payments')->insert([
                'client_id' => $client->id,
                'invoice_id' => $invoiceId,
                'payment_mode' => 'Discount',
                'amount' => $discountToApply,
                'paid_on' => now(),
                'payment_status' => $paymentStatus,
                'comments' => $remarks,
                'created_by' => auth()->user()->name,
                'pending_amount' => $unpaidAmount - $discountToApply,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Deduct applied discount amount from the remaining discount
            $remainingDiscount -= $discountToApply;
        }

        // 5. Update pending_amount in clients table
        $client->pending_amount -= $discountAmount;
        $client->save();

        return response()->json(['message' => 'Discount applied successfully!']);
    }
    public function settleAccount(Request $request)
    {
        $clientId = $request->client_id;
        $client = Client::findOrFail($clientId);
        $advanceAmount = $client->advance_amount;

        if ($advanceAmount <= 0) {
            return response()->json(['message' => 'No advance amount available for settlement.'], 400);
        }

        DB::beginTransaction();

        try {
            // Fetch unpaid invoices for the client in ascending order
            $invoices = Invoice::where('client_id', $clientId)
                ->where('unpaid_amount', '>', 0)
                ->orderBy('created_at', 'asc')
                ->get();

            $remainingAdvance = $advanceAmount;

            foreach ($invoices as $invoice) {
                if ($remainingAdvance <= 0) {
                    break;
                }

                $invoiceId = $invoice->id;
                $paidAmount = $invoice->paid_amount;
                $unpaidAmount = $invoice->unpaid_amount;
                $invoice_code = $invoice->invoice_code;

                // Determine how much of the advance can be applied to this invoice
                $amountToApply = min($remainingAdvance, $unpaidAmount);

                // Determine payment status
                $paymentStatus = ($unpaidAmount - $amountToApply) == 0 ? 'Paid' : 'Partially Paid';

                // Update invoice amounts
                DB::table('invoices')
                    ->where('id', $invoiceId)
                    ->update([
                        'paid_amount' => $paidAmount + $amountToApply,
                        'unpaid_amount' => $unpaidAmount - $amountToApply,
                        'invoice_status' => $paymentStatus,
                        'updated_at' => now(),
                    ]);

                // Insert record in payments table
                DB::table('payments')->insert([
                    'client_id' => $client->id,
                    'invoice_id' => $invoiceId,
                    'payment_mode' => 'Advance Adjustment',
                    'amount' => $amountToApply,
                    'paid_on' => now(),
                    'payment_status' => $paymentStatus,
                    'comments' => 'Advance amount adjusted against invoice for '.$invoice_code,
                    'created_by' => auth()->user()->name,
                    'pending_amount' => $unpaidAmount - $amountToApply,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // Insert into account_ledger
                DB::table('account_ledgers')->insert([
                    'client_id' => $client->id,
                    'transaction' => 'Advance Adjustment',
                    'transaction_date' => now(),
                    'detials' => 'Advance used for invoice payment for '.$invoice_code,
                    'amount' => NULL,
                    'payments' => $amountToApply,
                    'balance' => $client->pending_amount - $amountToApply, // Update balance accordingly
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // Deduct applied advance amount
                $remainingAdvance -= $amountToApply;
            }

            // Update client’s pending amount & advance amount
            $client->pending_amount -= ($advanceAmount - $remainingAdvance);
            $client->advance_amount = $remainingAdvance;
            $client->save();

            DB::commit();

            return response()->json(['message' => 'Account settlement successful.']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Account settlement failed. ' . $e->getMessage()], 500);
        }
    }

    public function getDiscounts(Request $request)
    {
        if ($request->ajax()) {
            $user = Auth::user();

            $discounts = ClientDiscount::select(['id', 'client_id', 'discount_amount', 'remarks',  'created_at'])
                ->orderBy('id', 'desc');

            if ($user->role_id == 3) {
                // Fetch the employee record for the logged-in user
                $employee = Employee::where('user_id', $user->id)->first();

                if ($employee) {
                    // Get only the clients assigned to this employee
                    $clientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');

                    // Filter payments for those clients only
                    $discounts->whereIn('client_id', $clientIds);
                } else {
                    // If no employee record found, return empty payments
                    $discounts->whereRaw('1 = 0'); // This ensures no records are returned
                }
            }
            // Apply Employee Filter
            if ($request->has('employee') && $request->employee != '') {
                $clientIds = DB::table('employee_client')
                    ->where('employee_id', $request->employee)
                    ->pluck('client_id')
                    ->toArray();

                $discounts->whereIn('client_id', $clientIds);
            }
            return DataTables::of($discounts)




                ->filter(function ($query) use ($request) {

                    if ($request->has('client') && $request->client != '') {
                        $query->where('client_id', $request->client);
                    }
                    // Filter by Date Range
                    if ($request->has('daterange') && !empty($request->daterange)) {
                        $dates = explode(' - ', $request->daterange);
                        if (count($dates) === 2) {
                            $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                            $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                            $query->whereBetween('paid_on', [$start_date, $end_date]);
                        }
                    }

                })

                ->editColumn('created_at', function ($row) {
                    return $row->created_at ? date('d-m-Y', strtotime($row->created_at)) : 'NA';
                })

                ->editColumn('discount_amount', function ($row) {
                    return $row->discount_amount ;
                })

                ->make(true);
        }
    }

    public function verify_certificate($id)
    {
        $encrypted = Crypt::encryptString($id);
        $verificationUrl = route('certificate.verify', ['encrypted' => $encrypted]);
        $certificate_url = new \Endroid\QrCode\QrCode($verificationUrl);
        $writer = new \Endroid\QrCode\Writer\PngWriter();
        $result = $writer->write($certificate_url);
        // Return base64 for inline display
        $url = $result->getDataUri();
        return $url;
    }

    public function verifyCertificate($encrypted)
    {
        try {
            $clientId = Crypt::decryptString($encrypted);
            $client = Client::findOrFail($clientId);
            $client_service = ClientService::where('client_id', $client->id)->where('status', 1)->first();

            // Calculate validity date
            $validity_date = null;
            if ($client_service) {
                if ($client_service->payment_cycle == 'Monthly') {
                    $validity_date = Carbon::parse($client_service->next_invoice_date)->subDay()->format('Y-m-d');
                } elseif ($client_service->payment_cycle == 'Yearly') {
                    $validity_date = Carbon::parse($client_service->next_invoice_date)->subDay()->format('Y-m-d');
                }
            }

            // Get certificate template from settings
            $certificateTemplate = DB::table('company_settings')
                ->where('setting_key', 'certificate_template')
                ->value('setting_value') ?? 'clients.certificate_v2'; // Default to v2 template

            $print = true;
            return view($certificateTemplate, compact('client', 'client_service', 'validity_date', 'print'));
        } catch (\Exception $e) {
            abort(404, 'Invalid or expired verification link.');
        }
    }

    public function certificateV2($id)
    {
        $client = Client::findOrFail($id);
        $client_service = ClientService::where('client_id', $client->id)->where('status', 1)->first();

        // Calculate validity date
        $validity_date = null;
        if ($client_service) {
            if ($client_service->payment_cycle == 'monthly') {
                $validity_date = Carbon::parse($client_service->next_invoice_date)->subDay()->format('Y-m-d');
            } elseif ($client_service->payment_cycle == 'yearly') {
                $validity_date = Carbon::parse($client_service->next_invoice_date)->subDay()->format('Y-m-d');
            }
        }

        $pdf = Pdf::loadView('htmls.certificate_v2', compact('client', 'client_service', 'validity_date'));
        return $pdf->download($client->business_name . '_certificate_v2.pdf');
    }

    /**
     * Get email information for a client (used by AJAX in invoice creation)
     */
    public function getEmailInfo($clientId)
    {
        try {
            $client = Client::find($clientId);

            if (!$client) {
                return response()->json([
                    'error' => 'Client not found'
                ], 404);
            }

            // Get email environment from company settings
            $emailEnvironment = $this->getEmailEnvironment();
            $response = [
                'app_env' => $emailEnvironment,
                'client_email' => $client->email,
                'tester_email' => null
            ];

            // Get tester email if not in live environment
            if ($emailEnvironment !== 'live') {
                $response['tester_email'] = $this->getTesterEmail();
            }

            return response()->json($response);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to get email information'
            ], 500);
        }
    }

    /**
     * Export clients with all fields and filters
     */
    public function exportClients(Request $request)
    {
        try {
            // Get the logged-in user
            $user = Auth::user();

            // Build the query with all relationships
            $query = Client::with(['state', 'district', 'clientServices.service', 'clientServices.service_type_data'])
                ->select([
                    'id', 'client_code', 'name', 'business_name', 'phone', 'secondary_phone',
                    'email', 'secondary_email', 'address', 'city', 'area', 'state_id',
                    'district_id', 'pincode', 'hcf_no', 'hcf_type', 'description', 'status',
                    'pending_amount', 'logo', 'created_at', 'updated_at'
                ]);

            // Apply role-based filtering (same as in getClients method)
            if ($user->role_id == 3) {
                $employee = Employee::where('user_id', $user->id)->first();
                if ($employee) {
                    $clientIds = EmployeeClient::where('employee_id', $employee->id)->pluck('client_id');
                    $query->whereIn('id', $clientIds);
                } else {
                    $query->whereRaw('1 = 0');
                }
            }

            // Apply filters (same as in getClients method)
            if ($request->has('searchkey') && $request->searchkey != '') {
                $query->where(function ($q) use ($request) {
                    $q->where('name', 'like', "%{$request->searchkey}%")
                        ->orWhere('client_code', 'like', "%{$request->searchkey}%")
                        ->orWhere('hcf_type', 'like', "%{$request->searchkey}%")
                        ->orWhere('phone', 'like', "%{$request->searchkey}%");
                });
            }

            if ($request->has('searchStatus') && $request->searchStatus != '') {
                $query->where('status', $request->searchStatus);
            }

            if ($request->has('searchArea') && $request->searchArea != '') {
                $query->where('area', 'like', "%{$request->searchArea}%");
            }

            if ($request->has('searchDistrict') && $request->searchDistrict != '') {
                $query->where('district_id', $request->searchDistrict);
            }

            // Get all clients
            $clients = $query->orderBy('client_code')->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers - All available fields
            $headers = [
                'Client Code',
                'Name',
                'Business Name',
                'Phone',
                'Secondary Phone',
                'Email',
                'Secondary Email',
                'Address',
                'City',
                'Area',
                'State',
                'District',
                'Pincode',
                'HCF Number',
                'HCF Type',
                'Description',
                'Status',
                'Pending Amount',
                'Active Services',
                'Service Types',
                'Total Service Value',
                'Last Service Date',
                'Next Invoice Date',
                'Assigned Employee',
                'Created Date',
                'Last Updated'
            ];

            $csvData[] = $headers;

            // Process each client
            foreach ($clients as $client) {
                // Get active services
                $activeServices = $client->clientServices->where('status', 1);
                $serviceNames = $activeServices->pluck('service.user_label')->filter()->implode(', ');
                $serviceTypes = $activeServices->pluck('service_type_data.name')->filter()->unique()->implode(', ');
                $totalServiceValue = $activeServices->sum('total_price');
                $lastServiceDate = $activeServices->max('start_date');
                $nextInvoiceDate = $activeServices->min('next_invoice_date');

                // Get assigned employee
                $assignedEmployee = EmployeeClient::where('client_id', $client->id)
                    ->with('employee')
                    ->first();
                $employeeName = $assignedEmployee ? $assignedEmployee->employee->emp_name : 'Not Assigned';

                $row = [
                    $client->client_code,
                    $client->name,
                    $client->business_name,
                    $client->phone,
                    $client->secondary_phone ?: '',
                    $client->email,
                    $client->secondary_email ?: '',
                    $client->address,
                    $client->city,
                    $client->area,
                    $client->state ? $client->state->name : '',
                    $client->district ? $client->district->name : '',
                    $client->pincode,
                    $client->hcf_no,
                    $client->hcf_type,
                    $client->description,
                    $client->status == 1 ? 'Active' : 'Inactive',
                    '₹' . number_format($client->pending_amount, 2),
                    $serviceNames ?: 'No Active Services',
                    $serviceTypes ?: 'N/A',
                    $totalServiceValue ? '₹' . number_format($totalServiceValue, 2) : '₹0.00',
                    $lastServiceDate ? date('d/m/Y', strtotime($lastServiceDate)) : 'N/A',
                    $nextInvoiceDate ? date('d/m/Y', strtotime($nextInvoiceDate)) : 'N/A',
                    $employeeName,
                    $client->created_at ? $client->created_at->format('d/m/Y H:i:s') : 'N/A',
                    $client->updated_at ? $client->updated_at->format('d/m/Y H:i:s') : 'N/A'
                ];

                $csvData[] = $row;
            }

            // Generate simple filename with timestamp
            $filename = 'Clients_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export client services for a specific client
     */
    public function exportClientServices(Request $request, $clientId)
    {
        try {
            $client = Client::findOrFail($clientId);

            // Get client services
            $services = ClientService::with(['service', 'service_type_data'])
                ->where('client_id', $clientId)
                ->orderBy('start_date', 'desc')
                ->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers
            $headers = [
                'Service Name',
                'Service Type',
                'Start Date',
                'End Date',
                'Next Invoice Date',
                'Beds Count',
                'Unit Price',
                'Total Price',
                'Payment Cycle',
                'Status',
                'Description'
            ];

            $csvData[] = $headers;

            // Process each service
            foreach ($services as $service) {
                $row = [
                    $service->service ? $service->service->user_label : 'N/A',
                    $service->service_type_data ? $service->service_type_data->name : 'N/A',
                    $service->start_date ? date('d/m/Y', strtotime($service->start_date)) : 'N/A',
                    $service->end_date ? date('d/m/Y', strtotime($service->end_date)) : 'N/A',
                    $service->next_invoice_date ? date('d/m/Y', strtotime($service->next_invoice_date)) : 'N/A',
                    $service->beds_count ?: 'N/A',
                    $service->unit_price ? '₹' . number_format($service->unit_price, 2) : 'N/A',
                    '₹' . number_format($service->total_price, 2),
                    $service->payment_cycle,
                    $service->status == 1 ? 'Active' : 'Completed',
                    $service->description ?: 'N/A'
                ];

                $csvData[] = $row;
            }

            // Generate filename
            $filename = $client->client_code . '_Services_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export client invoices for a specific client
     */
    public function exportClientInvoices(Request $request, $clientId)
    {
        try {
            $client = Client::findOrFail($clientId);

            // Get client invoices with date filter
            $query = Invoice::with(['service', 'service_type_data'])
                ->where('client_id', $clientId);

            // Apply date range filter if provided
            if ($request->has('daterange') && !empty($request->daterange)) {
                $dates = explode(' - ', $request->daterange);
                if (count($dates) === 2) {
                    $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                    $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                    $query->whereBetween('invoice_date', [$start_date, $end_date]);
                }
            }

            $invoices = $query->orderBy('invoice_date', 'desc')->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers
            $headers = [
                'Invoice Code',
                'Invoice Date',
                'Service Name',
                'Service Type',
                'Invoice Status',
                'Total Amount Due',
                'Paid Amount',
                'Unpaid Amount',
                'Grand Total',
                'Taxable Amount',
                'CGST Amount',
                'SGST Amount',
                'From Date',
                'To Date',
                'Created By',
                'Created Date'
            ];

            $csvData[] = $headers;

            // Process each invoice
            foreach ($invoices as $invoice) {
                $row = [
                    $invoice->invoice_code,
                    $invoice->invoice_date ? date('d/m/Y', strtotime($invoice->invoice_date)) : 'N/A',
                    $invoice->service ? $invoice->service->user_label : 'N/A',
                    $invoice->service_type_data ? $invoice->service_type_data->name : 'N/A',
                    $invoice->invoice_status,
                    '₹' . number_format($invoice->total_amount_due, 2),
                    '₹' . number_format($invoice->paid_amount, 2),
                    '₹' . number_format($invoice->unpaid_amount, 2),
                    $invoice->grand_total ? '₹' . number_format($invoice->grand_total, 2) : '₹' . number_format($invoice->total_amount_due, 2),
                    $invoice->taxable_amount ? '₹' . number_format($invoice->taxable_amount, 2) : 'N/A',
                    $invoice->cgst_amount ? '₹' . number_format($invoice->cgst_amount, 2) : 'N/A',
                    $invoice->sgst_amount ? '₹' . number_format($invoice->sgst_amount, 2) : 'N/A',
                    $invoice->from_invoice_to ? date('d/m/Y', strtotime($invoice->from_invoice_to)) : 'N/A',
                    $invoice->from_invoice_to && $invoice->days_for_invoice ?
                        date('d/m/Y', strtotime($invoice->from_invoice_to . ' + ' . ($invoice->days_for_invoice - 1) . ' days')) : 'N/A',
                    $invoice->created_by ?: 'N/A',
                    $invoice->created_at ? $invoice->created_at->format('d/m/Y H:i:s') : 'N/A'
                ];

                $csvData[] = $row;
            }

            // Generate filename
            $filename = $client->client_code . '_Invoices_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export client payments for a specific client
     */
    public function exportClientPayments(Request $request, $clientId)
    {
        try {
            $client = Client::findOrFail($clientId);

            // Get client payments with date filter
            $query = Payment::with(['invoice'])
                ->where('client_id', $clientId);

            // Apply date range filter if provided
            if ($request->has('daterange') && !empty($request->daterange)) {
                $dates = explode(' - ', $request->daterange);
                if (count($dates) === 2) {
                    $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                    $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                    $query->whereBetween('paid_on', [$start_date, $end_date]);
                }
            }

            $payments = $query->orderBy('paid_on', 'desc')->get();

            // Prepare CSV data
            $csvData = [];

            // CSV Headers
            $headers = [
                'Payment Date',
                'Invoice Code',
                'Payment Amount',
                'Payment Mode',
                'Reference Number',
                'Payment Status',
                'Comments',
                'Created By',
                'Created Date'
            ];

            $csvData[] = $headers;

            // Process each payment
            foreach ($payments as $payment) {
                $row = [
                    $payment->paid_on ? date('d/m/Y', strtotime($payment->paid_on)) : 'N/A',
                    $payment->invoice ? $payment->invoice->invoice_code : 'No Invoice',
                    '₹' . number_format($payment->amount, 2),
                    $payment->payment_mode,
                    $payment->ref_number ?: 'N/A',
                    $payment->payment_status ?: 'N/A',
                    $payment->comments ?: 'N/A',
                    $payment->created_by ?: 'N/A',
                    $payment->created_at ? $payment->created_at->format('d/m/Y H:i:s') : 'N/A'
                ];

                $csvData[] = $row;
            }

            // Generate filename
            $filename = $client->client_code . '_Payments_Export_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export client account ledger with summary and statement of accounts
     */
    public function exportClientLedger(Request $request, $clientId)
    {
        try {
            $client = Client::findOrFail($clientId);

            // Determine date range
            $hasDateFilter = $request->has('daterange') && !empty($request->daterange);
            if ($hasDateFilter) {
                $dates = explode(' - ', $request->daterange);
                $start_date = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                $end_date = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                $dateRangeText = trim($dates[0]) . ' to ' . trim($dates[1]);
            } else {
                // If no date filter, use all time
                $start_date = Carbon::createFromFormat('Y-m-d', '2020-01-01')->startOfDay();
                $end_date = Carbon::now()->endOfDay();
                $dateRangeText = 'All Time';
            }

            // Calculate Account Summary (same logic as PaymentController)
            $opening_balance = 0;
            if ($hasDateFilter) {
                $opening_balance = AccountLedger::where('transaction_date', '<', $start_date)
                    ->where('client_id', $clientId)
                    ->selectRaw('SUM(COALESCE(amount, 0) - COALESCE(payments, 0)) as balance')
                    ->value('balance') ?? 0;
            }

            $invoiced_amount = AccountLedger::where('client_id', $clientId)
                ->whereBetween('transaction_date', [$start_date, $end_date])
                ->sum('amount');

            $amount_received = AccountLedger::where('client_id', $clientId)
                ->whereBetween('transaction_date', [$start_date, $end_date])
                ->sum('payments');

            $balance_due = $opening_balance + $invoiced_amount - $amount_received;
            $advance_amount = $client->advance_amount ?? 0;

            // Get ledger entries for the period
            $ledgerEntries = AccountLedger::where('client_id', $clientId)
                ->whereBetween('transaction_date', [$start_date, $end_date])
                ->orderBy('transaction_date', 'asc')
                ->get();

            // Prepare CSV data
            $csvData = [];

            // Add Statement Header
            $csvData[] = ['STATEMENT OF ACCOUNTS'];
            $csvData[] = [''];
            $csvData[] = ['Client Name:', $client->name];
            $csvData[] = ['Client Code:', $client->client_code];
            $csvData[] = ['Period:', $dateRangeText];
            $csvData[] = ['Generated On:', date('d/m/Y H:i:s')];
            $csvData[] = [''];

            // Add Account Summary
            $csvData[] = ['ACCOUNT SUMMARY'];
            $csvData[] = [''];
            $csvData[] = ['Opening Balance:', '₹' . number_format($opening_balance, 2)];
            $csvData[] = ['Total Invoiced Amount:', '₹' . number_format($invoiced_amount, 2)];
            $csvData[] = ['Total Amount Received:', '₹' . number_format($amount_received, 2)];
            $csvData[] = ['Current Balance Due:', '₹' . number_format($balance_due, 2)];
            $csvData[] = ['Advance Amount:', '₹' . number_format($advance_amount, 2)];
            $csvData[] = [''];
            $csvData[] = [''];

            // Add Transaction Details Header
            $csvData[] = ['TRANSACTION DETAILS'];
            $csvData[] = [''];

            // CSV Headers for transactions
            $headers = [
                'Date',
                'Transaction Type',
                'Details',
                'Invoice Amount',
                'Payment Amount',
                'Running Balance'
            ];

            $csvData[] = $headers;

            // Add opening balance row if date filter is applied
            if ($hasDateFilter && $opening_balance != 0) {
                $csvData[] = [
                    trim($dates[0]),
                    'Opening Balance',
                    'Balance brought forward',
                    '',
                    '',
                    '₹' . number_format($opening_balance, 2)
                ];
            }

            // Process each ledger entry with running balance
            $running_balance = $opening_balance;
            foreach ($ledgerEntries as $entry) {
                // Calculate running balance
                if ($entry->amount) {
                    $running_balance += $entry->amount;
                }
                if ($entry->payments) {
                    $running_balance -= $entry->payments;
                }

                $row = [
                    $entry->transaction_date ? date('d/m/Y', strtotime($entry->transaction_date)) : 'N/A',
                    $entry->transaction,
                    $entry->detials ?: 'N/A',
                    $entry->amount ? '₹' . number_format($entry->amount, 2) : '',
                    $entry->payments ? '₹' . number_format($entry->payments, 2) : '',
                    '₹' . number_format($running_balance, 2)
                ];

                $csvData[] = $row;
            }

            // Add closing balance summary
            $csvData[] = [''];
            $csvData[] = ['CLOSING SUMMARY'];
            $csvData[] = [''];
            $csvData[] = ['Closing Balance:', '₹' . number_format($balance_due, 2)];
            if ($balance_due > 0) {
                $csvData[] = ['Status:', 'Amount Due'];
            } elseif ($balance_due < 0) {
                $csvData[] = ['Status:', 'Credit Balance'];
            } else {
                $csvData[] = ['Status:', 'Settled'];
            }

            // Generate filename
            $filename = $client->client_code . '_Statement_of_Accounts_' . date('Y-m-d_H-i-s') . '.csv';

            // Create CSV content
            $csvContent = '';
            foreach ($csvData as $row) {
                $csvContent .= '"' . implode('","', $row) . '"' . "\n";
            }

            // Add BOM for proper UTF-8 encoding
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            // Return CSV download
            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=UTF-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show change logs for a client
     */
    public function changeLogs($id)
    {
        $client = Client::findOrFail($id);
        return view('clients.change-logs', compact('client'));
    }
}
