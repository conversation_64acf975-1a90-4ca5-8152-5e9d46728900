<?php

namespace App\Http\Controllers;

use App\Models\NonBeddedType;
use App\Models\Service;
use App\Models\ServiceType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use App\Services\DailyReportService;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    //
    public function settings()
    {
        $services=Service::orderBy('custom_order')->get();
        $service_types=ServiceType::get();
        $non_beded_types=NonBeddedType::get();
        $settings = DB::table('company_settings')->pluck('setting_value', 'setting_key')->toArray();

        return view('settings.settings', compact('settings','services','service_types','non_beded_types'));
    }

    public function updateSettings(Request $request)
    {
        $fields = [
            'brand_name' => $request->brand_name,
            'legal_name' => $request->company_name, // or use a separate field if you have
            //'logo' => '', // handle separately if you have file upload
            'phone' => $request->phone,
            'email' => $request->email,
            'gst' => $request->gst,
            'bank_details' => $request->bank_details,
            'address' => $request->address,
            'website' => $request->website,
            'company_suffix' => $request->company_suffix,
            'cin' => $request->cin,
            'pan' => $request->pan,
            'upi' => $request->upi,
            'company_type' => $request->company_type,
            'certificate_template' => $request->certificate_template,

        ];
        // Handle file upload
        if ($request->hasFile('logo')) {
            $file = $request->file('logo');
            $filename = 'logo.'. $file->getClientOriginalExtension();
            $path = $file->storeAs('company_settings', $filename, 'public');

            DB::table('company_settings')->where('setting_key', 'logo')->update([
                'setting_value' => $path,
                'updated_at' => now(),
            ]);
        }
        if ($request->hasFile('auth_sign')) {
            $file = $request->file('auth_sign');
            $filename = 'auth_sign.'. $file->getClientOriginalExtension();
            $path = $file->storeAs('company_settings', $filename, 'public');

            DB::table('company_settings')->where('setting_key', 'auth_sign')->update([
                'setting_value' => $path,
                'updated_at' => now(),
            ]);
        }

        foreach ($fields as $key => $value) {
            DB::table('company_settings')->where('setting_key', $key)->update([
                'setting_value' => $value,
                'updated_at' => now(),
            ]);
        }

        // Handle logo upload separately if file uploaded

        return redirect()->back()->with('success', 'Settings updated successfully.');
    }

    /**
     * Update session timeout settings
     */
    public function updateSessionTimeout(Request $request)
    {
        $request->validate([
            'session_lifetime_minutes' => 'required|integer|min:5|max:1440',
            'session_warning_minutes' => 'required|integer|min:1|max:30',
            'session_check_interval_seconds' => 'required|integer|min:30|max:300',
            'session_heartbeat_interval_minutes' => 'required|integer|min:1|max:30',
        ]);

        $sessionSettings = [
            'session_timeout_enabled' => $request->has('session_timeout_enabled') ? '1' : '0',
            'session_lifetime_minutes' => $request->session_lifetime_minutes,
            'session_warning_minutes' => $request->session_warning_minutes,
            'session_check_interval_seconds' => $request->session_check_interval_seconds,
            'session_heartbeat_interval_minutes' => $request->session_heartbeat_interval_minutes,
            'session_expire_on_close' => $request->has('session_expire_on_close') ? '1' : '0',
        ];

        foreach ($sessionSettings as $key => $value) {
            DB::table('company_settings')->where('setting_key', $key)->update([
                'setting_value' => $value,
                'updated_at' => now(),
            ]);
        }

        // Update Laravel session configuration dynamically
        config(['session.lifetime' => $request->session_lifetime_minutes]);
        config(['session.expire_on_close' => $request->has('session_expire_on_close')]);

        return redirect()->back()->with('success', 'Session timeout settings updated successfully. Changes will take effect on next login.');
    }

    public function storeServiceType(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:service_types,name',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Create new service type
        $serviceType = new ServiceType();
        $serviceType->name = $request->name;
        $serviceType->status =1;
        $serviceType->save();

        return response()->json([
            'success' => true,
            'message' => 'Service type created successfully!',
            'data' => $serviceType
        ]);
    }

    public function updateServiceTypeStatus(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:service_types,id',
            'status' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Find and update the service type
        $serviceType = ServiceType::find($request->id);

        if (!$serviceType) {
            return response()->json([
                'success' => false,
                'message' => 'Service type not found'
            ], 404);
        }

        $serviceType->status = $request->status;
        $serviceType->save();

        $statusText = $request->status ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Service type {$serviceType->name} has been {$statusText} successfully!",
            'data' => $serviceType
        ]);
    }
    public function storeNonBeddedType(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:non_bedded_types,name',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Create new non bedded type
        $nonBeddedType = new NonBeddedType();
        $nonBeddedType->name = $request->name;
        $nonBeddedType->status =1;
        $nonBeddedType->save();

        return response()->json([
            'success' => true,
            'message' => 'Non bedded type created successfully!',
            'data' => $nonBeddedType
        ]);
    }
    public function updateNonBeddedTypeStatus(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:non_bedded_types,id',
            'status' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Find and update the non bedded type
        $nonBeddedType = NonBeddedType::find($request->id);

        if (!$nonBeddedType) {
            return response()->json([
                'success' => false,
                'message' => 'Non bedded type not found'
            ], 404);
        }

        $nonBeddedType->status = $request->status;
        $nonBeddedType->save();

        $statusText = $request->status ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Non bedded type {$nonBeddedType->name} has been {$statusText} successfully!",
            'data' => $nonBeddedType
        ]);
    }
    public function updateServiceStatus(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:services,id',
            'status' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Find and update the service
        $service = Service::find($request->id);

        if (!$service) {
            return response()->json([
                'success' => false,
                'message' => 'Service not found'
            ], 404);
        }

        $service->status = $request->status;
        $service->save();

        $statusText = $request->status ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Service {$service->service_name} has been {$statusText} successfully!",
            'data' => $service
        ]);
    }
    public function updateServiceLabel(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:services,id',
            'label' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Find and update the service
        $service = Service::find($request->id);

        if (!$service) {
            return response()->json([
                'success' => false,
                'message' => 'Service not found'
            ], 404);
        }

        $service->user_label = $request->label;
        $service->save();

        return response()->json([
            'success' => true,
            'message' => "Service label has been updated successfully!",
            'data' => $service
        ]);
    }

    /**
     * Update email configuration settings
     */
    public function updateEmailConfig(Request $request)
    {
        $request->validate([
            'email_environment' => 'required|in:live,test',
            'tester_mailid' => 'nullable|email'
        ]);

        $emailSettings = [
            'email_environment' => $request->email_environment,
            'tester_mailid' => $request->tester_mailid
        ];

        foreach ($emailSettings as $key => $value) {
            DB::table('company_settings')->where('setting_key', $key)->update([
                'setting_value' => $value,
                'updated_at' => now(),
            ]);
        }

        return redirect()->back()->with('success', 'Email configuration updated successfully.');
    }

    /**
     * Update notification alerts settings
     */
    public function updateNotificationAlerts(Request $request)
    {
        $request->validate([
            'invoice_alert_emails' => 'nullable|string',
            'payment_alert_emails' => 'nullable|string',
            'client_alert_emails' => 'nullable|string',
            'employee_alert_emails' => 'nullable|string',
            'service_alert_emails' => 'nullable|string',
            'daily_report_alert_emails' => 'nullable|string',
        ]);

        $notificationSettings = [
            // Invoice Creation Alerts
            'invoice_alert_enabled' => $request->has('invoice_alert_enabled') ? '1' : '0',
            'invoice_alert_emails' => $request->invoice_alert_emails ?: '',
            'invoice_alert_manual_creation' => $request->has('invoice_alert_manual_creation') ? '1' : '0',
            'invoice_alert_auto_creation' => $request->has('invoice_alert_auto_creation') ? '1' : '0',

            // Payment Creation Alerts
            'payment_alert_enabled' => $request->has('payment_alert_enabled') ? '1' : '0',
            'payment_alert_emails' => $request->payment_alert_emails ?: '',

            // Client Creation Alerts
            'client_alert_enabled' => $request->has('client_alert_enabled') ? '1' : '0',
            'client_alert_emails' => $request->client_alert_emails ?: '',

            // Employee Creation Alerts
            'employee_alert_enabled' => $request->has('employee_alert_enabled') ? '1' : '0',
            'employee_alert_emails' => $request->employee_alert_emails ?: '',

            // Service Creation Alerts
            'service_alert_enabled' => $request->has('service_alert_enabled') ? '1' : '0',
            'service_alert_emails' => $request->service_alert_emails ?: '',

            // Daily Report Alerts
            'daily_report_alert_enabled' => $request->has('daily_report_alert_enabled') ? '1' : '0',
            'daily_report_alert_emails' => $request->daily_report_alert_emails ?: '',
            'daily_report_time' => $request->daily_report_time ?: '08:00',
        ];

        foreach ($notificationSettings as $key => $value) {
            DB::table('company_settings')->where('setting_key', $key)->update([
                'setting_value' => $value,
                'updated_at' => now(),
            ]);
        }

        return redirect()->back()->with('success', 'Notification alert settings updated successfully.');
    }

    /**
     * Get current email environment configuration (AJAX endpoint)
     */
    public function getEmailEnvironment()
    {
        try {
            $emailEnvironment = DB::table('company_settings')
                              ->where('setting_key', 'email_environment')
                              ->value('setting_value') ?: 'live';

            $testerEmail = DB::table('company_settings')
                         ->where('setting_key', 'tester_mailid')
                         ->value('setting_value');

            return response()->json([
                'environment' => $emailEnvironment,
                'tester_email' => $testerEmail
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to get email environment'
            ], 500);
        }
    }

    /**
     * Generate and send daily report manually
     */
    public function generateDailyReport(Request $request)
    {
        try {
            $request->validate([
                'report_date' => 'nullable|date'
            ]);

            $reportDate = $request->report_date ? \Carbon\Carbon::parse($request->report_date) : \Carbon\Carbon::yesterday();

            $reportService = new DailyReportService();
            $result = $reportService->generateAndSendDailyReport($reportDate);

            if ($result['success']) {
                return redirect()->back()->with('success', $result['message'] . ' for ' . $reportDate->format('d M Y'));
            } else {
                return redirect()->back()->with('error', $result['message']);
            }

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to generate daily report: ' . $e->getMessage());
        }
    }

    /**
     * Preview daily report data (AJAX)
     */
    public function previewDailyReport(Request $request)
    {
        try {
            $request->validate([
                'report_date' => 'nullable|date'
            ]);

            $reportDate = $request->report_date ? \Carbon\Carbon::parse($request->report_date) : \Carbon\Carbon::yesterday();

            $reportService = new DailyReportService();
            $reportData = $reportService->generateReportData($reportDate);

            return response()->json([
                'success' => true,
                'report_data' => $reportData,
                'report_date' => $reportDate->format('Y-m-d')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate report preview: ' . $e->getMessage()
            ], 500);
        }
    }
}
