<?php

namespace App\Http\Controllers;

use App\Models\ExpenseCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;

class ExpenseCategoryController extends Controller
{
    /**
     * Display a listing of expense categories
     */
    public function index()
    {
        return view('expenses.categories.list');
    }

    /**
     * Get categories data for DataTables
     */
    public function getCategories(Request $request)
    {
        $query = ExpenseCategory::with('createdBy')
            ->select(['id', 'name', 'description', 'color', 'is_active', 'created_by', 'created_at']);

        if ($request->searchkey) {
            $query->where('name', 'like', '%' . $request->searchkey . '%');
        }

        return DataTables::of($query)
            ->addColumn('status_badge', function ($row) {
                $class = $row->is_active ? 'success' : 'danger';
                $text = $row->is_active ? 'Active' : 'Inactive';
                return '<span class="badge bg-' . $class . '">' . $text . '</span>';
            })
            ->addColumn('color_preview', function ($row) {
                return '<div style="width: 20px; height: 20px; background-color: ' . $row->color . '; border-radius: 50%; border: 1px solid #ddd;"></div>';
            })
            ->addColumn('created_by_name', function ($row) {
                return $row->createdBy ? $row->createdBy->name : 'N/A';
            })
            ->addColumn('formatted_created_at', function ($row) {
                return $row->created_at->format('d/m/Y H:i:s');
            })
            ->addColumn('action', function ($row) {
                $actions = '<div class="d-flex align-items-center gap-10 justify-content-end">';

                if (auth()->user()->can('expense-category-edit')) {
                    $actions .= '<button type="button" onclick="editCategory(' . $row->id . ')" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                        <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                    </button>';
                }

                if (auth()->user()->can('expense-category-delete')) {
                    $actions .= '<button type="button" onclick="deleteCategory(' . $row->id . ')" class="bg-danger-focus bg-hover-danger-200 text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                        <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                    </button>';
                }

                $actions .= '</div>';
                return $actions;
            })
            ->rawColumns(['status_badge', 'color_preview', 'action'])
            ->make(true);
    }

    /**
     * Store a newly created category
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:expense_categories,name',
            'description' => 'nullable|string',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean'
        ]);

        ExpenseCategory::create([
            'name' => $request->name,
            'description' => $request->description,
            'color' => $request->color,
            'is_active' => $request->has('is_active'),
            'created_by' => Auth::id()
        ]);

        return response()->json(['success' => true, 'message' => 'Category created successfully!']);
    }

    /**
     * Show the specified category for editing
     */
    public function show($id)
    {
        $category = ExpenseCategory::findOrFail($id);
        return response()->json($category);
    }

    /**
     * Update the specified category
     */
    public function update(Request $request, $id)
    {
        $category = ExpenseCategory::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255|unique:expense_categories,name,' . $id,
            'description' => 'nullable|string',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean'
        ]);

        $category->update([
            'name' => $request->name,
            'description' => $request->description,
            'color' => $request->color,
            'is_active' => $request->has('is_active')
        ]);

        return response()->json(['success' => true, 'message' => 'Category updated successfully!']);
    }

    /**
     * Remove the specified category
     */
    public function destroy($id)
    {
        $category = ExpenseCategory::findOrFail($id);

        // Check if category has expenses
        if ($category->expenses()->count() > 0) {
            return response()->json(['success' => false, 'message' => 'Cannot delete category that has expenses!']);
        }

        $category->delete();

        return response()->json(['success' => true, 'message' => 'Category deleted successfully!']);
    }
}
