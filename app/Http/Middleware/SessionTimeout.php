<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SessionTimeout
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check()) {
            // Get session timeout settings from database
            $companySettings = \Illuminate\Support\Facades\DB::table('company_settings')
                ->whereIn('setting_key', ['session_timeout_enabled', 'session_lifetime_minutes'])
                ->pluck('setting_value', 'setting_key')
                ->toArray();

            $timeoutEnabled = ($companySettings['session_timeout_enabled'] ?? '1') == '1';

            // Only check timeout if enabled
            if ($timeoutEnabled) {
                $sessionLifetime = ($companySettings['session_lifetime_minutes'] ?? config('session.lifetime', 120)) * 60; // Convert to seconds
                $lastActivity = Session::get('last_activity', time());

                // Check if session has expired
                if (time() - $lastActivity > $sessionLifetime) {
                    Auth::logout();
                    Session::flush();

                    if ($request->expectsJson() || $request->ajax()) {
                        return response()->json([
                            'status' => 'error',
                            'message' => 'Session expired',
                            'redirect' => route('login'),
                            'logout' => true
                        ], 401);
                    }

                    return redirect()->route('login')
                        ->with('warning', 'Your session has expired due to inactivity. Please login again.');
                }
            }

            // Update last activity timestamp
            Session::put('last_activity', time());
        }

        return $next($request);
    }
}
