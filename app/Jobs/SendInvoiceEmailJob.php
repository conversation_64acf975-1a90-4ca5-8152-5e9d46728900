<?php

namespace App\Jobs;

use App\Models\Invoice;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Crypt;
use Barryvdh\DomPDF\Facade\Pdf;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;

class SendInvoiceEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $invoiceId;
    protected $recipientEmail;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     */
    public $backoff = 60;

    /**
     * Create a new job instance.
     */
    public function __construct($invoiceId, $recipientEmail = null)
    {
        $this->invoiceId = $invoiceId;
        $this->recipientEmail = $recipientEmail;

        // Set queue name from config
        $this->onQueue(config('email-queue.queue', 'emails'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $invoice = Invoice::findOrFail($this->invoiceId);

            // Determine recipient email
            $recipientEmail = $this->recipientEmail ?: $this->getRecipientEmail($invoice);

            if (!$recipientEmail) {
                Log::warning("No email address found for invoice {$invoice->invoice_code}");
                return;
            }

            // Generate PDF
            $qr_url = $this->generatePaymentQr($invoice->unpaid_amount);
            $verificationUrl = $this->generateVerificationUrl($invoice->id);
            $pdf = Pdf::loadView('invoices.invoice_template_v2', compact('invoice', 'qr_url', 'verificationUrl'))
                     ->setPaper('a4', 'portrait');

            // Create temp filename
            $invoice_code = str_replace(['/', '\\'], '-', $invoice->invoice_code);
            $fileName = "invoice_{$invoice_code}.pdf";
            $filePath = storage_path("app/public/temp/{$fileName}");

            // Ensure temp directory exists
            $tempDir = storage_path('app/public/temp');
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0777, true);
            }

            // Save PDF temporarily
            file_put_contents($filePath, $pdf->output());

            // Get company email from settings
            $companyEmail = DB::table('company_settings')
                            ->where('setting_key', 'email')
                            ->value('setting_value') ?: config('mail.from.address');

            // Send email
            Mail::send('email.invoice', ['invoice' => $invoice], function ($message) use ($recipientEmail, $filePath, $fileName, $invoice, $companyEmail) {
                $message->to($recipientEmail, $invoice->client->business_name ?? config('app.name'))
                        ->subject("Invoice - " . $invoice->invoice_code . ' from ' . config('app.name'))
                        ->from($companyEmail, config('app.name'))
                        ->replyTo($companyEmail, config('app.name'))
                        ->attach($filePath, [
                            'as' => $fileName,
                            'mime' => 'application/pdf',
                        ]);
            });

            // Clean up temp file
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            Log::info("Invoice email sent successfully for invoice {$invoice->invoice_code} to {$recipientEmail}");

        } catch (\Exception $e) {
            Log::error("Failed to send invoice email for invoice ID {$this->invoiceId}: " . $e->getMessage());
            throw $e; // Re-throw to trigger job retry if configured
        }
    }

    /**
     * Determine the recipient email based on company settings
     */
    private function getRecipientEmail($invoice)
    {
        // Get email environment from company settings
        $emailEnvironment = DB::table('company_settings')
                            ->where('setting_key', 'email_environment')
                            ->value('setting_value') ?: 'live';

        if ($emailEnvironment === 'live') {
            // Use actual client email in live environment
            return $invoice->client->email ?? null;
        } else {
            // Use tester email from company_settings in test environment
            $testerEmail = DB::table('company_settings')
                           ->where('setting_key', 'tester_mailid')
                           ->value('setting_value');

            return $testerEmail ?: '<EMAIL>'; // Fallback email
        }
    }

    /**
     * Generate payment QR code
     */
    private function generatePaymentQr($amount)
    {
        $upi_id = env('UPI_ID', 'test@upi');
        $upi_name = env('UPI_NAME', 'Test Company');
        $upi_url = "upi://pay?pa={$upi_id}&pn={$upi_name}&am={$amount}";
        $qr = new QrCode($upi_url);
        $writer = new PngWriter();
        $result = $writer->write($qr);
        return $result->getDataUri();
    }

    /**
     * Generate verification URL QR code
     */
    private function generateVerificationUrl($invoiceId)
    {
        $encrypted = Crypt::encryptString($invoiceId);
        $verificationUrl = route('invoice.verify', ['encrypted' => $encrypted]);
        $invoice_url = new QrCode($verificationUrl);
        $writer = new PngWriter();
        $result = $writer->write($invoice_url);
        return $result->getDataUri();
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("SendInvoiceEmailJob failed for invoice ID {$this->invoiceId}: " . $exception->getMessage());
    }
}
