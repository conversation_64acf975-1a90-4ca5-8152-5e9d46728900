<?php

namespace App\Jobs;

use App\Models\Payment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Crypt;
use Barryvdh\DomPDF\Facade\Pdf;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;

class SendPaymentEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $paymentId;
    protected $recipientEmail;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     *
     * @var int
     */
    public $timeout = 120;

    /**
     * Create a new job instance.
     */
    public function __construct($paymentId, $recipientEmail = null)
    {
        $this->paymentId = $paymentId;
        $this->recipientEmail = $recipientEmail;
        $this->onQueue(config('email-queue.queue', 'emails'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $payment = Payment::findOrFail($this->paymentId);

            // Determine recipient email
            $recipientEmail = $this->recipientEmail ?: $this->getRecipientEmail($payment);

            if (!$recipientEmail) {
                Log::warning("No email address found for payment ID {$payment->id}");
                return;
            }

            // Generate verification URL
            $verificationUrl = $this->generateVerificationUrl($payment->id);

            // Generate PDF
            $pdf = Pdf::loadView('payments.receipt_template', compact('payment', 'verificationUrl'))
                     ->setPaper('a4', 'portrait');

            // Create temp filename
            $fileName = "payment_receipt_{$payment->id}.pdf";
            $filePath = storage_path("app/public/{$fileName}");

            // Save PDF temporarily
            file_put_contents($filePath, $pdf->output());

            // Get company email from settings
            $companyEmail = DB::table('company_settings')
                            ->where('setting_key', 'email')
                            ->value('setting_value') ?: config('mail.from.address');

            // Send email
            Mail::send('email.payment', ['payment' => $payment], function ($message) use ($recipientEmail, $filePath, $fileName, $payment, $companyEmail) {
                $message->to($recipientEmail, $payment->client->business_name ?? config('app.name'))
                        ->subject("Payment Receipt - " . ($payment->ref_number ?: 'Payment ID ' . $payment->id) . ' from ' . config('app.name'))
                        ->from($companyEmail, config('app.name'))
                        ->replyTo($companyEmail, config('app.name'))
                        ->attach($filePath, [
                            'as' => $fileName,
                            'mime' => 'application/pdf',
                        ]);
            });

            // Clean up temp file
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            Log::info("Payment email sent successfully for payment ID {$payment->id} to {$recipientEmail}");

        } catch (\Exception $e) {
            Log::error("Failed to send payment email for payment ID {$this->paymentId}: " . $e->getMessage());
            throw $e; // Re-throw to trigger job retry if configured
        }
    }

    /**
     * Determine the recipient email based on company settings
     */
    private function getRecipientEmail($payment)
    {
        // Get email environment from company settings
        $emailEnvironment = DB::table('company_settings')
                            ->where('setting_key', 'email_environment')
                            ->value('setting_value') ?: 'live';

        if ($emailEnvironment === 'live') {
            // Use actual client email in live environment
            return $payment->client->email ?? null;
        } else {
            // Use tester email from company_settings in test environment
            $testerEmail = DB::table('company_settings')
                           ->where('setting_key', 'tester_mailid')
                           ->value('setting_value');

            return $testerEmail ?: '<EMAIL>'; // Fallback email
        }
    }

    /**
     * Generate verification URL QR code
     */
    private function generateVerificationUrl($paymentId)
    {
        $encrypted = Crypt::encryptString($paymentId);
        $verificationUrl = route('payment.verify', ['encrypted' => $encrypted]);
        $payment_url = new QrCode($verificationUrl);
        $writer = new PngWriter();
        $result = $writer->write($payment_url);
        return $result->getDataUri();
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("Payment email job failed for payment ID {$this->paymentId}: " . $exception->getMessage());
    }
}
