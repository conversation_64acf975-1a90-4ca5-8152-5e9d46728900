<?php

namespace App\Traits;

use Illuminate\Support\Facades\Auth;

trait PermissionHelperTrait
{
    /**
     * Check if the current user has the specified permission
     *
     * @param string $permission
     * @return bool
     */
    protected function hasPermission($permission)
    {
        return Auth::check() && Auth::user()->can($permission);
    }

    /**
     * Check if the current user has any of the specified permissions
     *
     * @param array $permissions
     * @return bool
     */
    protected function hasAnyPermission(array $permissions)
    {
        if (!Auth::check()) {
            return false;
        }

        foreach ($permissions as $permission) {
            if (Auth::user()->can($permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if the current user has all of the specified permissions
     *
     * @param array $permissions
     * @return bool
     */
    protected function hasAllPermissions(array $permissions)
    {
        if (!Auth::check()) {
            return false;
        }

        foreach ($permissions as $permission) {
            if (!Auth::user()->can($permission)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Generate action buttons for DataTables based on permissions
     *
     * @param object $row
     * @param array $actions
     * @return string
     */
    protected function generateActionButtons($row, array $actions)
    {
        $user = Auth::user();
        $output = '';

        foreach ($actions as $action) {
            if (!isset($action['permission']) || $user->can($action['permission'])) {
                $class = $action['class'] ?? 'w-32-px h-32-px bg-primary-light text-primary-600 rounded-circle d-inline-flex align-items-center justify-content-center';
                $icon = $action['icon'] ?? 'iconamoon:eye-light';
                $url = $action['url'];
                $title = $action['title'] ?? '';
                
                // Replace placeholders in URL
                $url = str_replace('{id}', $row->id, $url);
                
                $output .= '<a href="' . $url . '" class="' . $class . '" title="' . $title . '">
                    <iconify-icon icon="' . $icon . '"></iconify-icon>
                </a>';
            }
        }

        return $output;
    }

    /**
     * Get permission-based menu items
     *
     * @param array $menuItems
     * @return array
     */
    protected function getPermissionBasedMenu(array $menuItems)
    {
        $user = Auth::user();
        $filteredMenu = [];

        foreach ($menuItems as $item) {
            if (!isset($item['permission']) || $user->can($item['permission'])) {
                $filteredMenu[] = $item;
            }
        }

        return $filteredMenu;
    }

    /**
     * Check permission and abort if not authorized
     *
     * @param string $permission
     * @param int $statusCode
     * @param string $message
     * @return void
     */
    protected function checkPermissionOrAbort($permission, $statusCode = 403, $message = 'Unauthorized action.')
    {
        if (!$this->hasPermission($permission)) {
            abort($statusCode, $message);
        }
    }

    /**
     * Get user permissions for frontend
     *
     * @return array
     */
    protected function getUserPermissions()
    {
        if (!Auth::check()) {
            return [];
        }

        return Auth::user()->getAllPermissions()->pluck('name')->toArray();
    }

    /**
     * Check if user can perform CRUD operations on a resource
     *
     * @param string $resource
     * @return array
     */
    protected function getResourcePermissions($resource)
    {
        $user = Auth::user();
        
        return [
            'can_list' => $user->can($resource . '-list'),
            'can_view' => $user->can($resource . '-view'),
            'can_create' => $user->can($resource . '-create'),
            'can_edit' => $user->can($resource . '-edit'),
            'can_delete' => $user->can($resource . '-delete'),
            'can_export' => $user->can($resource . '-export'),
        ];
    }

    /**
     * Filter data based on user permissions and role
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $resource
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function applyPermissionFilters($query, $resource)
    {
        $user = Auth::user();

        // If user is an employee (role_id = 3), filter data based on assignments
        if ($user->role_id == 3) {
            $employee = $user->employee;
            
            if ($employee && $resource === 'client') {
                // Filter clients assigned to this employee
                $assignedClientIds = $employee->assignedClients()->pluck('client_id')->toArray();
                $query->whereIn('id', $assignedClientIds);
            }
        }

        return $query;
    }

    /**
     * Get permission-based export options
     *
     * @param string $resource
     * @return array
     */
    protected function getExportOptions($resource)
    {
        $user = Auth::user();
        $options = [];

        if ($user->can($resource . '-export')) {
            $options[] = [
                'text' => 'Export CSV',
                'icon' => 'hugeicons:csv-02',
                'action' => 'csv'
            ];
            
            $options[] = [
                'text' => 'Export Excel',
                'icon' => 'hugeicons:xls-02',
                'action' => 'excel'
            ];
        }

        return $options;
    }
}
