<?php

namespace App\Traits;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

trait EmailHelperTrait
{
    /**
     * Get the appropriate email address based on environment and client
     *
     * @param object $client The client object
     * @param string|null $fallbackEmail Optional fallback email
     * @return string|null
     */
    public function getEmailAddress($client, $fallbackEmail = null)
    {
        // Check email environment setting from company_settings
        $emailEnvironment = $this->getEmailEnvironment();

        if ($emailEnvironment === 'live') {
            // Use actual client email in live environment
            $email = $client->email ?? $fallbackEmail;

            if (!$email) {
                Log::warning("No email address found for client {$client->id} in live environment");
            }

            return $email;
        } else {
            // Use tester email from company_settings in test environment
            $testerEmail = $this->getTesterEmail();

            if (!$testerEmail) {
                Log::warning("No tester email configured in company_settings");
                return $fallbackEmail ?: '<EMAIL>';
            }

            return $testerEmail;
        }
    }

    /**
     * Get email environment setting from company_settings table
     *
     * @return string
     */
    public function getEmailEnvironment()
    {
        try {
            $environment = DB::table('company_settings')
                           ->where('setting_key', 'email_environment')
                           ->value('setting_value');

            // Default to 'live' if not set
            return $environment ?: 'live';
        } catch (\Exception $e) {
            Log::error("Failed to fetch email environment from company_settings: " . $e->getMessage());
            return 'live'; // Default to live on error
        }
    }

    /**
     * Get tester email from company_settings table
     *
     * @return string|null
     */
    public function getTesterEmail()
    {
        try {
            return DB::table('company_settings')
                     ->where('setting_key', 'tester_mailid')
                     ->value('setting_value');
        } catch (\Exception $e) {
            Log::error("Failed to fetch tester email from company_settings: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Validate email address format
     *
     * @param string $email
     * @return bool
     */
    public function isValidEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Get environment-appropriate email with validation
     *
     * @param object $client
     * @param string|null $fallbackEmail
     * @return string|null
     */
    public function getValidatedEmailAddress($client, $fallbackEmail = null)
    {
        $email = $this->getEmailAddress($client, $fallbackEmail);

        if ($email && $this->isValidEmail($email)) {
            return $email;
        }

        Log::warning("Invalid email address format: {$email}");
        return null;
    }

    /**
     * Log email sending attempt
     *
     * @param string $type (e.g., 'invoice', 'payment')
     * @param string $identifier (e.g., invoice_code, payment_id)
     * @param string $email
     * @param string $status ('queued', 'sent', 'failed')
     * @param string|null $message
     */
    public function logEmailAttempt($type, $identifier, $email, $status, $message = null)
    {
        $logMessage = "Email {$status} - Type: {$type}, ID: {$identifier}, Email: {$email}";

        if ($message) {
            $logMessage .= ", Message: {$message}";
        }

        switch ($status) {
            case 'sent':
                Log::info($logMessage);
                break;
            case 'queued':
                Log::info($logMessage);
                break;
            case 'failed':
                Log::error($logMessage);
                break;
            default:
                Log::debug($logMessage);
        }
    }
}
