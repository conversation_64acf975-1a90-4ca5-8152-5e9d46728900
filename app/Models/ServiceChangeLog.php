<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ServiceChangeLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_service_id',
        'action',
        'changed_by',
        'old_values',
        'new_values',
        'changed_fields',
        'description',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'changed_fields' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the client service that this log belongs to
     */
    public function clientService()
    {
        return $this->belongsTo(ClientService::class, 'client_service_id');
    }

    /**
     * Create a change log entry
     */
    public static function logChange($clientServiceId, $action, $changedBy, $oldValues = null, $newValues = null, $description = null)
    {
        $changedFields = [];

        if ($oldValues && $newValues) {
            foreach ($newValues as $key => $newValue) {
                if (isset($oldValues[$key]) && $oldValues[$key] != $newValue) {
                    $changedFields[] = $key;
                }
            }
        }

        return self::create([
            'client_service_id' => $clientServiceId,
            'action' => $action,
            'changed_by' => $changedBy,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'changed_fields' => $changedFields,
            'description' => $description,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }

    /**
     * Get formatted description of changes
     */
    public function getFormattedChangesAttribute()
    {
        if (empty($this->changed_fields)) {
            return $this->description ?? 'No specific changes recorded';
        }

        $changes = [];
        foreach ($this->changed_fields as $field) {
            $oldValue = $this->formatFieldValue($field, $this->old_values[$field] ?? null);
            $newValue = $this->formatFieldValue($field, $this->new_values[$field] ?? null);
            $fieldLabel = $this->getFieldLabel($field);
            $changes[] = "{$fieldLabel}: '{$oldValue}' → '{$newValue}'";
        }

        return implode(', ', $changes);
    }

    /**
     * Get human-readable field label
     */
    public function getFieldLabel($field)
    {
        $labels = [
            'service_id' => 'Service',
            'client_id' => 'Client',
            'service_type' => 'Service Type',
            'payment_cycle' => 'Payment Cycle',
            'beds_count' => 'Number of Units',
            'unit_price' => 'Unit Price',
            'total_price' => 'Total Price',
            'non_bedded_type' => 'Non-Bedded Type',
            'weight_cost_type' => 'Weight Cost Type',
            'weight_cost' => 'Weight Cost',
            'minimum_kgs' => 'Minimum KGs',
            'minimum_cost' => 'Minimum Cost',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'next_invoice_date' => 'Next Invoice Date',
            'status' => 'Status',
            'description' => 'Description',
            'created_by' => 'Created By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];

        return $labels[$field] ?? ucfirst(str_replace('_', ' ', $field));
    }

    /**
     * Format field value for display
     */
    public function formatFieldValue($field, $value)
    {
        if ($value === null || $value === '') {
            return 'N/A';
        }

        switch ($field) {
            case 'service_id':
                return $this->getServiceName($value);
            case 'client_id':
                return $this->getClientName($value);
            case 'service_type':
                return $this->getServiceTypeName($value);
            case 'weight_cost_type':
                return $this->getWeightCostTypeName($value);
            case 'status':
                return $value == 1 ? 'Active' : 'Inactive';
            case 'start_date':
            case 'end_date':
            case 'next_invoice_date':
                return $value ? date('d-m-Y', strtotime($value)) : 'N/A';
            case 'created_at':
            case 'updated_at':
                if (!$value) return 'N/A';
                try {
                    // Handle both ISO format and regular timestamp
                    $timestamp = Carbon::parse($value);
                    return $timestamp->format('d-m-Y h:i A');
                } catch (\Exception) {
                    return $value; // Return original if parsing fails
                }
            case 'unit_price':
            case 'total_price':
            case 'weight_cost':
            case 'minimum_cost':
                return $value ? '₹' . number_format($value, 2) : 'N/A';
            default:
                return $value;
        }
    }

    /**
     * Get service name by ID
     */
    private function getServiceName($serviceId)
    {
        $services = [
            1 => 'Bedded Service',
            2 => 'Non-Bedded Service',
            3 => 'Weight-Based Service',
            4 => 'Bedded with Fixed Price'
        ];
        return $services[$serviceId] ?? "Service ID: {$serviceId}";
    }

    /**
     * Get client name by ID
     */
    private function getClientName($clientId)
    {
        $client = \App\Models\Client::find($clientId);
        return $client ? $client->name : "Client ID: {$clientId}";
    }

    /**
     * Get service type name by ID
     */
    private function getServiceTypeName($serviceTypeId)
    {
        $serviceType = \App\Models\ServiceType::find($serviceTypeId);
        return $serviceType ? $serviceType->name : "Service Type ID: {$serviceTypeId}";
    }

    /**
     * Get weight cost type name
     */
    private function getWeightCostTypeName($type)
    {
        $types = [
            1 => 'Fixed Cost',
            2 => 'Fixed Range Based',
            3 => 'Floating Range Based',
            4 => 'Fixed with Minimum Qty'
        ];
        return $types[$type] ?? "Type: {$type}";
    }
}
