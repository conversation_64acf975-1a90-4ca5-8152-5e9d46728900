<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Client extends Model
{
    use HasFactory;

    // Define fillable fields for mass assignment
    protected $fillable = [
        'name', 'business_name', 'phone', 'secondary_phone', 'email', 'secondary_email',
        'address', 'city', 'area', 'state_id', 'district_id', 'pincode',
        'hcf_no', 'hcf_type', 'description', 'status', 'pending_amount', 'logo',
        'client_code', 'user_id', 'client_type', 'gst', 'pan', 'lang_lat', 'advance_amount'
    ];
    public function state()
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    public function district()
    {
        return $this->belongsTo(District::class, 'district_id');
    }

    public function clientServiceWeights()
    {
        return $this->hasMany(ClientServiceWeight::class);
    }

    public function clientServices()
    {
        return $this->hasMany(ClientService::class);
    }

    /**
     * Get the change logs for this client
     */
    public function changeLogs()
    {
        return $this->hasMany(ClientChangeLog::class, 'client_id');
    }
}
