<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class EmployeeChangeLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'action',
        'changed_by',
        'old_values',
        'new_values',
        'changed_fields',
        'description',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'changed_fields' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the employee that this log belongs to
     */
    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id');
    }

    /**
     * Create a change log entry
     */
    public static function logChange($employeeId, $action, $changedBy, $oldValues = null, $newValues = null, $description = null)
    {
        $changedFields = [];

        if ($oldValues && $newValues) {
            foreach ($newValues as $key => $newValue) {
                if (isset($oldValues[$key]) && $oldValues[$key] != $newValue) {
                    $changedFields[] = $key;
                }
            }
        }

        return self::create([
            'employee_id' => $employeeId,
            'action' => $action,
            'changed_by' => $changedBy,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'changed_fields' => $changedFields,
            'description' => $description,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }

    /**
     * Get human-readable field label
     */
    public function getFieldLabel($field)
    {
        $labels = [
            'name' => 'Name',
            'email' => 'Email',
            'phone' => 'Phone',
            'address' => 'Address',
            'designation' => 'Designation',
            'department' => 'Department',
            'salary' => 'Salary',
            'joining_date' => 'Joining Date',
            'status' => 'Status',
            'created_by' => 'Created By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];

        return $labels[$field] ?? ucfirst(str_replace('_', ' ', $field));
    }

    /**
     * Format field value for display
     */
    public function formatFieldValue($field, $value)
    {
        if ($value === null || $value === '') {
            return 'N/A';
        }

        switch ($field) {
            case 'status':
                return $value == 1 ? 'Active' : 'Inactive';
            case 'joining_date':
                return $value ? date('d-m-Y', strtotime($value)) : 'N/A';
            case 'created_at':
            case 'updated_at':
                if (!$value) return 'N/A';
                try {
                    $timestamp = Carbon::parse($value);
                    return $timestamp->format('d-m-Y h:i A');
                } catch (\Exception) {
                    return $value;
                }
            case 'salary':
                return $value ? '₹' . number_format($value, 2) : 'N/A';
            default:
                return $value;
        }
    }
}
