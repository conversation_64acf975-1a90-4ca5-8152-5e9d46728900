<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeClient extends Model
{
    use HasFactory;
    protected $table = 'employee_client'; // Table name

    protected $guarded  = [];

    // Relationship with Employee
    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id');
    }

    // Relationship with Client
    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }
}
