<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ClientChangeLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'action',
        'changed_by',
        'old_values',
        'new_values',
        'changed_fields',
        'description',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'changed_fields' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the client that this log belongs to
     */
    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    /**
     * Create a change log entry
     */
    public static function logChange($clientId, $action, $changedBy, $oldValues = null, $newValues = null, $description = null)
    {
        $changedFields = [];

        if ($oldValues && $newValues) {
            foreach ($newValues as $key => $newValue) {
                if (isset($oldValues[$key]) && $oldValues[$key] != $newValue) {
                    $changedFields[] = $key;
                }
            }
        }

        return self::create([
            'client_id' => $clientId,
            'action' => $action,
            'changed_by' => $changedBy,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'changed_fields' => $changedFields,
            'description' => $description,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }

    /**
     * Get human-readable field label
     */
    public function getFieldLabel($field)
    {
        $labels = [
            'name' => 'Client Name',
            'client_code' => 'Client Code',
            'email' => 'Email',
            'phone' => 'Phone',
            'address' => 'Address',
            'city' => 'City',
            'state_id' => 'State',
            'district_id' => 'District',
            'pincode' => 'Pincode',
            'gst_number' => 'GST Number',
            'pan_number' => 'PAN Number',
            'contact_person' => 'Contact Person',
            'contact_person_phone' => 'Contact Person Phone',
            'status' => 'Status',
            'pending_amount' => 'Pending Amount',
            'created_by' => 'Created By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];

        return $labels[$field] ?? ucfirst(str_replace('_', ' ', $field));
    }

    /**
     * Format field value for display
     */
    public function formatFieldValue($field, $value)
    {
        if ($value === null || $value === '') {
            return 'N/A';
        }

        switch ($field) {
            case 'state_id':
                return $this->getStateName($value);
            case 'district_id':
                return $this->getDistrictName($value);
            case 'status':
                return $value == 1 ? 'Active' : 'Inactive';
            case 'created_at':
            case 'updated_at':
                if (!$value) return 'N/A';
                try {
                    $timestamp = Carbon::parse($value);
                    return $timestamp->format('d-m-Y h:i A');
                } catch (\Exception) {
                    return $value;
                }
            case 'pending_amount':
                return $value ? '₹' . number_format($value, 2) : '₹0.00';
            default:
                return $value;
        }
    }

    /**
     * Get state name by ID
     */
    private function getStateName($stateId)
    {
        $state = \App\Models\State::find($stateId);
        return $state ? $state->name : "State ID: {$stateId}";
    }

    /**
     * Get district name by ID
     */
    private function getDistrictName($districtId)
    {
        $district = \App\Models\District::find($districtId);
        return $district ? $district->name : "District ID: {$districtId}";
    }
}
