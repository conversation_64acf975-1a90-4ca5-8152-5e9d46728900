<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class InvoiceChangeLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_id',
        'action',
        'changed_by',
        'old_values',
        'new_values',
        'changed_fields',
        'description',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'changed_fields' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the invoice that this log belongs to
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }

    /**
     * Create a change log entry
     */
    public static function logChange($invoiceId, $action, $changedBy, $oldValues = null, $newValues = null, $description = null)
    {
        $changedFields = [];

        if ($oldValues && $newValues) {
            foreach ($newValues as $key => $newValue) {
                if (isset($oldValues[$key]) && $oldValues[$key] != $newValue) {
                    $changedFields[] = $key;
                }
            }
        }

        return self::create([
            'invoice_id' => $invoiceId,
            'action' => $action,
            'changed_by' => $changedBy,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'changed_fields' => $changedFields,
            'description' => $description,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }

    /**
     * Get human-readable field label
     */
    public function getFieldLabel($field)
    {
        $labels = [
            'client_id' => 'Client',
            'client_service_id' => 'Service',
            'invoice_number' => 'Invoice Number',
            'invoice_date' => 'Invoice Date',
            'due_date' => 'Due Date',
            'taxable_amount' => 'Taxable Amount',
            'cgst_amount' => 'CGST Amount',
            'sgst_amount' => 'SGST Amount',
            'total_amount' => 'Total Amount',
            'status' => 'Status',
            'payment_status' => 'Payment Status',
            'weight_qty' => 'Weight/Quantity',
            'unit_price' => 'Unit Price',
            'created_by' => 'Created By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];

        return $labels[$field] ?? ucfirst(str_replace('_', ' ', $field));
    }

    /**
     * Format field value for display
     */
    public function formatFieldValue($field, $value)
    {
        if ($value === null || $value === '') {
            return 'N/A';
        }

        switch ($field) {
            case 'client_id':
                return $this->getClientName($value);
            case 'status':
            case 'payment_status':
                return $value == 1 ? 'Active' : 'Inactive';
            case 'invoice_date':
            case 'due_date':
                return $value ? date('d-m-Y', strtotime($value)) : 'N/A';
            case 'created_at':
            case 'updated_at':
                if (!$value) return 'N/A';
                try {
                    $timestamp = Carbon::parse($value);
                    return $timestamp->format('d-m-Y h:i A');
                } catch (\Exception) {
                    return $value;
                }
            case 'taxable_amount':
            case 'cgst_amount':
            case 'sgst_amount':
            case 'total_amount':
            case 'total_amount_due':
            case 'paid_amount':
            case 'unpaid_amount':
            case 'grand_total':
            case 'gross_amount':
            case 'unit_price':
            case 'weight_qty':
                return $value ? '₹' . number_format($value, 2) : 'N/A';
            default:
                return $value;
        }
    }

    /**
     * Get client name by ID
     */
    private function getClientName($clientId)
    {
        $client = \App\Models\Client::find($clientId);
        return $client ? $client->name : "Client ID: {$clientId}";
    }
}
