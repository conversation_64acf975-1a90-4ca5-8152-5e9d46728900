<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventoryItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'name',
        'sku_code',
        'description',
        'image_url',
        'quantity_available',
        'minimum_required',
        'unit_of_measure',
        'unit_price',
        'cgst_rate',
        'sgst_rate',
        'igst_rate',
        'is_gst_applicable',
        'status',
        'is_active'
    ];

    protected $casts = [
        'unit_price' => 'decimal:2',
        'cgst_rate' => 'decimal:2',
        'sgst_rate' => 'decimal:2',
        'igst_rate' => 'decimal:2',
        'is_gst_applicable' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the category that owns the inventory item
     */
    public function category()
    {
        return $this->belongsTo(InventoryCategory::class, 'category_id');
    }

    /**
     * Get the transactions for this inventory item
     */
    public function transactions()
    {
        return $this->hasMany(InventoryTransaction::class, 'inventory_item_id');
    }

    /**
     * Update the status based on quantity
     */
    public function updateStatus()
    {
        if ($this->quantity_available <= 0) {
            $this->status = 'out_of_stock';
        } elseif ($this->quantity_available <= $this->minimum_required) {
            $this->status = 'low_stock';
        } else {
            $this->status = 'in_stock';
        }
        $this->save();
    }

    /**
     * Scope to get only active items
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get items by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get low stock items
     */
    public function scopeLowStock($query)
    {
        return $query->where('status', 'low_stock');
    }

    /**
     * Scope to get out of stock items
     */
    public function scopeOutOfStock($query)
    {
        return $query->where('status', 'out_of_stock');
    }

    /**
     * Scope to get in stock items
     */
    public function scopeInStock($query)
    {
        return $query->where('status', 'in_stock');
    }
}
