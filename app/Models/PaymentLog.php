<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PaymentLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'payment_id',
        'action',
        'field_name',
        'old_value',
        'new_value',
        'changed_by',
        'ip_address',
        'user_agent',
        'reason',
        'full_data',
        'old_values',
        'new_values',
        'changed_fields',
        'description'
    ];

    protected $casts = [
        'full_data' => 'array',
        'old_values' => 'array',
        'new_values' => 'array',
        'changed_fields' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the payment that this log belongs to
     */
    public function payment()
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * Get the user who made the change
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'changed_by', 'name');
    }

    /**
     * Scope for filtering by action
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope for filtering by payment
     */
    public function scopeByPayment($query, $paymentId)
    {
        return $query->where('payment_id', $paymentId);
    }

    /**
     * Create a change log entry (new method)
     */
    public static function logChange($paymentId, $action, $changedBy, $oldValues = null, $newValues = null, $description = null)
    {
        $changedFields = [];

        if ($oldValues && $newValues) {
            foreach ($newValues as $key => $newValue) {
                if (isset($oldValues[$key]) && $oldValues[$key] != $newValue) {
                    $changedFields[] = $key;
                }
            }
        }

        return self::create([
            'payment_id' => $paymentId,
            'action' => $action,
            'changed_by' => $changedBy,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'changed_fields' => $changedFields,
            'description' => $description,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }

    /**
     * Get human-readable field label
     */
    public function getFieldLabel($field)
    {
        $labels = [
            'client_id' => 'Client',
            'amount' => 'Amount',
            'payment_date' => 'Payment Date',
            'payment_method' => 'Payment Method',
            'reference_number' => 'Reference Number',
            'notes' => 'Notes',
            'status' => 'Status',
            'created_by' => 'Created By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];

        return $labels[$field] ?? ucfirst(str_replace('_', ' ', $field));
    }

    /**
     * Format field value for display
     */
    public function formatFieldValue($field, $value)
    {
        if ($value === null || $value === '') {
            return 'N/A';
        }

        switch ($field) {
            case 'client_id':
                return $this->getClientName($value);
            case 'status':
                return $value == 1 ? 'Active' : 'Inactive';
            case 'payment_date':
                return $value ? date('d-m-Y', strtotime($value)) : 'N/A';
            case 'created_at':
            case 'updated_at':
                if (!$value) return 'N/A';
                try {
                    $timestamp = Carbon::parse($value);
                    return $timestamp->format('d-m-Y h:i A');
                } catch (\Exception) {
                    return $value;
                }
            case 'amount':
                return $value ? '₹' . number_format($value, 2) : 'N/A';
            default:
                return $value;
        }
    }

    /**
     * Get client name by ID
     */
    private function getClientName($clientId)
    {
        $client = \App\Models\Client::find($clientId);
        return $client ? $client->name : "Client ID: {$clientId}";
    }

    /**
     * Get formatted change description (legacy method)
     */
    public function getChangeDescriptionAttribute()
    {
        switch ($this->action) {
            case 'created':
                return "Payment created";
            case 'updated':
                if ($this->field_name) {
                    return "Changed {$this->field_name} from '{$this->old_value}' to '{$this->new_value}'";
                }
                return "Payment updated";
            case 'deleted':
                return "Payment deleted";
            default:
                return "Unknown action: {$this->action}";
        }
    }
}
