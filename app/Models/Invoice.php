<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'from_invoice_to',
        'days_for_invoice'
    ];

    public function service()
    {
        return $this->belongsTo(Service::class, 'service_id');
    }
    public function service_type_data()
    {
        return $this->belongsTo(ServiceType::class, 'service_type');
    }
    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    /**
     * Get the change logs for this invoice
     */
    public function changeLogs()
    {
        return $this->hasMany(InvoiceChangeLog::class, 'invoice_id');
    }

    /**
     * Get the invoice items for this invoice
     */
    public function invoiceItems()
    {
        return $this->hasMany(InvoiceItem::class, 'invoice_id');
    }

    /**
     * Check if this is an inventory-based invoice
     */
    public function isInventoryInvoice()
    {
        return $this->invoiceItems()->exists();
    }

    /**
     * Calculate total amount from invoice items
     */
    public function calculateInventoryTotal()
    {
        return $this->invoiceItems()->sum('total_price');
    }

    /**
     * Calculate GST amounts for inventory invoice
     */
    public function calculateInventoryGST($withoutGst = false)
    {
        $invoiceItems = $this->invoiceItems;

        $grossAmount = 0;
        $cgstAmount = 0;
        $sgstAmount = 0;
        $igstAmount = 0;
        $totalAmount = 0;

        foreach ($invoiceItems as $item) {
            $grossAmount += $item->total_price;

            if (!$withoutGst && $item->is_gst_applicable) {
                $cgstAmount += $item->cgst_amount;
                $sgstAmount += $item->sgst_amount;
                $igstAmount += $item->igst_amount;
            }

            $totalAmount += $item->total_with_gst;
        }

        return [
            'gross_amount' => $grossAmount,
            'cgst_amount' => $cgstAmount,
            'sgst_amount' => $sgstAmount,
            'igst_amount' => $igstAmount,
            'total_amount_due' => $totalAmount
        ];
    }

    /**
     * Update invoice totals based on invoice items
     */
    public function updateInventoryTotals($withoutGst = false)
    {
        $totals = $this->calculateInventoryGST($withoutGst);

        $this->gross_amount = $totals['gross_amount'];
        $this->cgst_amount = $totals['cgst_amount'];
        $this->sgst_amount = $totals['sgst_amount'];
        $this->total_amount_due = $totals['total_amount_due'];
        $this->unpaid_amount = $totals['total_amount_due'];

        return $this;
    }
}
