<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientService extends Model
{
    use HasFactory;
    protected $guarded  = [];
    public function service()
    {
        return $this->belongsTo(Service::class, 'service_id');
    }
    public function service_type_data()
    {
        return $this->belongsTo(ServiceType::class, 'service_type');
    }
    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }
    public function weightCostRanges()
    {
        return $this->hasMany(WeightCostRange::class, 'client_service_id');
    }

    public function changeLogs()
    {
        return $this->hasMany(ServiceChangeLog::class, 'client_service_id');
    }
}
