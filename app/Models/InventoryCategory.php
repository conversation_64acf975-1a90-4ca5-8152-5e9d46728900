<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventoryCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the inventory items for this category
     */
    public function inventoryItems()
    {
        return $this->hasMany(InventoryItem::class, 'category_id');
    }

    /**
     * Get active inventory items for this category
     */
    public function activeInventoryItems()
    {
        return $this->hasMany(InventoryItem::class, 'category_id')->where('is_active', true);
    }

    /**
     * Scope to get only active categories
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
