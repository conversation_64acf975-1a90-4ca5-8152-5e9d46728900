<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ServiceLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'service_id',
        'action',
        'field_name',
        'old_value',
        'new_value',
        'changed_by',
        'ip_address',
        'user_agent',
        'reason',
        'full_data'
    ];

    protected $casts = [
        'full_data' => 'array'
    ];

    public function service()
    {
        return $this->belongsTo(ClientService::class, 'service_id');
    }
}
