<?php

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\WeightEntryController;
use App\Http\Controllers\ChangePasswordController;
use App\Http\Controllers\ForgotPasswordController;
use App\Http\Controllers\QueueMonitorController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\ExpenseCategoryController;
use App\Http\Controllers\InventoryCategoryController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('login');
});

Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login'])->name('login.submit');
Route::get('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');
Route::post('/session/heartbeat', [AuthController::class, 'sessionHeartbeat'])->name('session.heartbeat')->middleware('auth');

Route::get('/forgot-password', function () {
    return view('forgot-password');
});

Route::get('/forgot-password', [ForgotPasswordController::class, 'showForgotForm'])->name('forgot.password.form');
Route::post('/forgot-password', [ForgotPasswordController::class, 'sendToken'])->name('forgot.password');

Route::get('/token_verify/{token}', [ForgotPasswordController::class, 'token_verify']);
// Route::post('/verify-otp', [ForgotPasswordController::class, 'verifyOtp'])->name('otp.verify');

Route::get('/reset-password', [ForgotPasswordController::class, 'showResetForm'])->name('reset.password.form');
Route::post('/reset-password', [ForgotPasswordController::class, 'resetPassword'])->name('reset.password');
// Handle Change Password Submission
Route::post('/change-password', [ChangePasswordController::class, 'updatePassword'])
    ->middleware('auth')
    ->name('password.change');
//Auth routes
Route::middleware(['auth'])->group(function () {

    //Dashboard routes start here
    Route::middleware('permission:dashboard')->group(function () {
        Route::get('/dashboard',  [Controller::class, 'dashboard'])->name('dashboard');
        Route::post('/get-invoice-data', [Controller::class, 'getInvoiceData'])->name('dashboard.invoice_date');
        Route::post('/dashboard/get-payment-data', [Controller::class, 'getPaymentData'])->name('dashboard.getPaymentData');
        Route::post('/dashboard/top-due-clients', [Controller::class, 'getTopDueClients'])->name('dashboard.top_due_clients');
        Route::get('/dashboard/updates', [Controller::class, 'getDashboardUpdates'])->name('dashboard.updates');

        // Enhanced Analytics Routes
        Route::get('/dashboard/monthly-invoice-analytics', [Controller::class, 'getMonthlyInvoiceAnalytics'])->name('dashboard.monthly_invoice_analytics');
        Route::get('/dashboard/payment-trends-analytics', [Controller::class, 'getPaymentTrendsAnalytics'])->name('dashboard.payment_trends_analytics');
        Route::get('/dashboard/pending-amount-analytics', [Controller::class, 'getPendingAmountAnalytics'])->name('dashboard.pending_amount_analytics');
        Route::get('/dashboard/client-growth-analytics', [Controller::class, 'getClientGrowthAnalytics'])->name('dashboard.client_growth_analytics');
    });
    //Dashboard routes end here

    //Clients routes start here
    Route::middleware('permission:client-list')->group(function () {
        Route::get('/clients',  [ClientController::class, 'index'])->name('clients.index');
        Route::get('/clients-data', [ClientController::class, 'getClients'])->name('clients.data');
        Route::get('/clients/download/{id}', [ClientController::class, 'download'])->name('clients.download');
        Route::get('/clients/{id}/certificate', [ClientController::class, 'viewCertificate'])->name('clients.certificate');
        Route::get('/clients/export', [ClientController::class, 'exportClients'])->name('clients.export');
    });

    Route::middleware('permission:client-view')->group(function () {
        Route::get('/clients/view/{id}', [ClientController::class, 'show'])->name('clients.show');
        Route::post('/clients/update-status', [ClientController::class, 'updateStatus'])->name('clients.update-status');
        Route::post('/assign-employee', [ClientController::class, 'assignEmployee'])->name('assign.employee');
        Route::get('/discounts-data', [ClientController::class, 'getDiscounts'])->name('discounts.data');

        // Client View Export Options
        Route::get('/clients/{id}/export-services', [ClientController::class, 'exportClientServices'])->name('clients.export.services');
        Route::get('/clients/{id}/export-invoices', [ClientController::class, 'exportClientInvoices'])->name('clients.export.invoices');
        Route::get('/clients/{id}/export-payments', [ClientController::class, 'exportClientPayments'])->name('clients.export.payments');
        Route::get('/clients/{id}/export-ledger', [ClientController::class, 'exportClientLedger'])->name('clients.export.ledger');

        //Accounts
        Route::get('/accounts-data', [PaymentController::class, 'get_account_leger_data'])->name('accounts.data');
        Route::get('/accounts/summary', [PaymentController::class, 'getAccountSummary'])->name('accounts.summary');
    });

    Route::middleware('permission:client-change-logs')->group(function () {
        Route::get('/clients/{id}/change-logs', [ClientController::class, 'changeLogs'])->name('clients.change-logs');
    });

    Route::middleware('permission:client-create')->group(function () {
        Route::get('/clients/add', [ClientController::class, 'add'])->name('clients.add');
        Route::post('/clients/store', [ClientController::class, 'store'])->name('clients.store');
    });
    Route::middleware('permission:client-discount')->group(function () {
        Route::post('/clients/{id}/apply-discount', [ClientController::class, 'applyDiscount'])->name('apply.discount');
    });

        Route::post('/clients/accounts/settle', [ClientController::class, 'settleAccount'])->name('client.settle_account');
    Route::middleware('permission:client-edit')->group(function () {
        Route::get('/clients/edit/{id}', [ClientController::class, 'edit'])->name('clients.edit');
        Route::put('/clients/update/{client}', [ClientController::class, 'update'])->name('clients.update');
    });

    // Email info route for invoice creation
    Route::get('/clients/{id}/email-info', [ClientController::class, 'getEmailInfo'])->name('clients.email-info');

    // General email environment route
    Route::get('/get-email-environment', [SettingsController::class, 'getEmailEnvironment'])->name('get-email-environment');
    //Clients routes end here

    //Services routes start here
    Route::middleware('permission:service-list')->group(function () {
        Route::get('/services', [ServiceController::class, 'index'])->name('services.index');
        Route::get('/clients-service-data', [ServiceController::class, 'getClientServices'])->name('services.data');
        Route::post('/services/cancel_service/{id}', [ServiceController::class, 'cancel_service'])->name('service.cancel_service');
        Route::get('/services/export', [ServiceController::class, 'exportServices'])->name('services.export');
    });

    Route::middleware('permission:service-view')->group(function () {
        Route::get('/services/view/{id}', [ServiceController::class, 'show'])->name('service.show');
        Route::post('/service/end', [ServiceController::class, 'end_service'])->name('service.end_service');
        Route::get('/generate-invoice/{client_service_id?}', [InvoiceController::class, 'generateInvoices']);
    });

    Route::middleware('permission:service-change-logs')->group(function () {
        Route::get('/services/{id}/change-logs', [ServiceController::class, 'changeLogs'])->name('service.change-logs');
    });

    Route::middleware('permission:service-edit')->group(function () {
        Route::get('/services/edit/{id}', [ServiceController::class, 'edit'])->name('service.edit');
        Route::put('/services/update/{id}', [ServiceController::class, 'update'])->name('service.update');
    });

    Route::middleware('permission:service-assign')->group(function () {
        Route::get('/services/assign/{client_id?}', [ServiceController::class, 'assignService'])->name('assign.service');
        Route::post('/client-service', [ServiceController::class, 'clientService'])->name('serive.clientService');
        Route::post('/client-service/bulk-upload', [ServiceController::class, 'customHike'])->name('serive.bulkUpload');
        Route::get('/download-bulk-upload-template', [ServiceController::class, 'downloadBulkUploadTemplate'])->name('download-bulk-upload-template');

        // Fix yearly service dates route
        Route::post('/services/fix-yearly-dates', [ServiceController::class, 'fixYearlyServiceDates'])->name('services.fix-yearly-dates');
    });
    //Services routes end here

    //Invoice routes start here
    Route::middleware('permission:invoice-list')->group(function () {
        Route::get('/invoices', [InvoiceController::class, 'index'])->name('invoices.index');
        Route::get('/invoice-data', [InvoiceController::class, 'getInvoices'])->name('invoices.data');
        Route::get('/invoices/{id}/download', [InvoiceController::class, 'downloadPDF'])->name('invoice.download');
        Route::post('/download-bulk-invoices', [InvoiceController::class, 'bulkInvoiceDownload'])->name('download-bulk-invoices');
        Route::get('/invoices/export', [InvoiceController::class, 'exportInvoices'])->name('invoices.export');
    });

    Route::middleware('permission:invoice-view')->group(function () {
        Route::get('/invoices/view/{id}', [InvoiceController::class, 'show'])->name('invoices.show');
        Route::get('/invoices/{id}/print', [InvoiceController::class, 'print'])->name('invoice.print');
        Route::get('/invoices/{id}/email-config', [InvoiceController::class, 'getEmailConfig'])->name('invoice.email-config');
        Route::post('/invoices/{id}/send-email', [InvoiceController::class, 'sendEmailWithCustomRecipient'])->name('invoice.send-email');
    });

    Route::middleware('permission:invoice-change-logs')->group(function () {
        Route::get('/invoices/{id}/change-logs', [InvoiceController::class, 'changeLogs'])->name('invoices.change-logs');
    });

    Route::middleware('permission:invoice-cancelled-list')->group(function () {
        Route::get('/invoices/cancelled', [InvoiceController::class, 'cancelledInvoices'])->name('invoices.cancelled');
        Route::get('/invoices/cancelled-data', [InvoiceController::class, 'getCancelledInvoices'])->name('invoices.cancelled.data');
    });
    Route::middleware('permission:invoice-delete')->group(function () {
        Route::post('/invoices/{id}/delete', [InvoiceController::class, 'invociceDelete'])->name('invoices.delete');
    });

    Route::middleware('permission:invoice-create')->group(function () {
        Route::get('/invoices/add/{client_id?}', [InvoiceController::class, 'add'])->name('invoices.add');
        Route::post('/invoices/store', [InvoiceController::class, 'manul_invoice_store'])->name('invoices.store');

        // Inventory invoice routes
        Route::get('/invoices/add-inventory', [InvoiceController::class, 'addInventoryInvoice'])->name('invoices.add-inventory');
        Route::post('/invoices/store-inventory', [InvoiceController::class, 'storeInventoryInvoice'])->name('invoices.store-inventory');
        Route::get('/api/inventory-items', [InvoiceController::class, 'getInventoryItems'])->name('api.inventory-items');
        Route::get('/api/inventory-items/{id}', [InvoiceController::class, 'getInventoryItemDetails'])->name('api.inventory-item-details');
    });
    Route::get('/cancelled-invoices', [InvoiceController::class, 'cancelledInvoices'])->name('invoices.cancelled');
    Route::get('/invoices/getCancelledInvoices', [InvoiceController::class, 'getCancelledInvoices'])->name('invoices.getCancelledInvoices');
    //Invoice routes end here

    //Payment routes start here
    Route::middleware('permission:payment-list')->group(function () {
        Route::get('/payments', [PaymentController::class, 'index'])->name('payments.index');
        Route::get('/payments-data', [PaymentController::class, 'getPayments'])->name('payments.data');
        Route::get('/payments/{id}/download', [PaymentController::class, 'downloadPDF'])->name('payments.download');
        Route::get('/payments/{id}/print', [PaymentController::class, 'print'])->name('payments.print');
        Route::get('/payments/export', [PaymentController::class, 'exportPayments'])->name('payments.export');
    });

    Route::middleware('permission:payment-view')->group(function () {
        Route::get('/payments/view/{id}', [PaymentController::class, 'show'])->name('payments.show');
        Route::get('/payments/{id}/email-config', [PaymentController::class, 'getEmailConfig'])->name('payment.email-config');
        Route::post('/payments/{id}/send-email', [PaymentController::class, 'sendEmailWithCustomRecipient'])->name('payment.send-email');
    });

    Route::middleware('permission:payment-create')->group(function () {
        Route::get('/payments/add/{client_id?}', [PaymentController::class, 'add'])->name('payments.add');
        Route::post('/payments/store', [PaymentController::class, 'store'])->name('payments.store');
        Route::get('/get-pending-invoices', [InvoiceController::class, 'getPendingInvoices']);
    });

    Route::middleware('permission:payment-edit')->group(function () {
        Route::get('/payments/edit/{id}', [PaymentController::class, 'edit'])->name('payments.edit');
        Route::put('/payments/update/{id}', [PaymentController::class, 'update'])->name('payments.update');
    });

    Route::middleware('permission:payment-history')->group(function () {
        Route::get('/payments/history/{id}', [PaymentController::class, 'showHistory'])->name('payments.history');
        Route::get('/payments/logs/{id}', [PaymentController::class, 'getPaymentLogs'])->name('payments.logs');
    });

    Route::middleware('permission:payment-deleted-list')->group(function () {
        Route::get('/payments/deleted', [PaymentController::class, 'deletedPayments'])->name('payments.deleted');
        Route::get('/payments/deleted-data', [PaymentController::class, 'getDeletedPayments'])->name('payments.deleted.data');
    });

    Route::middleware('permission:payment-delete')->group(function () {
        Route::post('/payments/{id}/delete', [PaymentController::class, 'paymentDelete'])->name('payments.delete');
    });
    Route::get('/deleted-payments', [PaymentController::class, 'deletedPayments'])->name('payments.deleted');
    Route::get('/payments/getDeletedPayments', [PaymentController::class, 'getDeletedPayments'])->name('payments.getDeletedPayments');
    //Payment routes end here

    //Employee routes start here
    Route::middleware('permission:employee-list')->group(function () {
        Route::get('/employees',  [EmployeeController::class, 'index'])->name('employees.index');
        Route::get('/employees-data', [EmployeeController::class, 'getClients'])->name('employees.data');
        Route::get('/employees/export', [EmployeeController::class, 'exportEmployees'])->name('employees.export');
    });

    Route::middleware('permission:employee-view')->group(function () {
        Route::get('/employees/view/{id}', [EmployeeController::class, 'show'])->name('employees.show');
        Route::post('/employees/assign-clients', [EmployeeController::class, 'assignClients'])->name('employees.assign-clients');
        Route::get('/employees/get-clients-for-assignment', [EmployeeController::class, 'getClientsForAssignment'])->name('employees.get-clients-for-assignment');

        // Employee View Export Options
        Route::get('/employees/{id}/export-clients', [EmployeeController::class, 'exportEmployeeClients'])->name('employees.export.clients');
        Route::get('/employees/{id}/export-invoices', [EmployeeController::class, 'exportEmployeeInvoices'])->name('employees.export.invoices');
        Route::get('/employees/{id}/export-payments', [EmployeeController::class, 'exportEmployeePayments'])->name('employees.export.payments');
    });

    Route::middleware('permission:employee-change-logs')->group(function () {
        Route::get('/employees/{id}/change-logs', [EmployeeController::class, 'changeLogs'])->name('employees.change-logs');
    });

    Route::middleware('permission:employee-create')->group(function () {
        Route::get('/employees/add', [EmployeeController::class, 'add'])->name('employees.add');
        Route::post('/employees/store', [EmployeeController::class, 'store'])->name('employees.store');
    });

    Route::middleware('permission:employee-edit')->group(function () {
        Route::get('/employees/edit/{id}', [EmployeeController::class, 'edit'])->name('employees.edit');
        Route::put('/employees/update/{id}', [EmployeeController::class, 'update'])->name('employees.update');
    });

    //Employee routes end here

    //Roles routes start here
    Route::middleware('permission:role-list')->group(function () {
        Route::get('/roles', [AdminController::class, 'index'])->name('roles');
    });

    Route::middleware('permission:role-permission')->group(function () {
        Route::get('/roles/permissions/{id}', [AdminController::class, 'rolePermissions'])->name('roles.permissions');
        Route::post('/roles/assign-permissions', [AdminController::class, 'assignPermissions'])->name('roles.assign-permissions');
    });

    Route::middleware('permission:role-create')->group(function () {
        Route::post('/roles/create', [AdminController::class, 'createRole'])->name('roles.create');
        Route::post('/roles/clone', [AdminController::class, 'cloneRole'])->name('roles.clone');
    });

    Route::middleware('permission:role-permission')->group(function () {
        Route::post('/roles/apply-template', [AdminController::class, 'applyPermissionTemplate'])->name('roles.apply-template');
        Route::post('/roles/bulk-update-permissions', [AdminController::class, 'bulkUpdatePermissions'])->name('roles.bulk-update-permissions');
        Route::get('/roles/stats', [AdminController::class, 'getRoleStats'])->name('roles.stats');
    });

    //Role routes end here

    //Reports routes start here
    Route::get('/reports/client', [ReportController::class, 'client_report'])->name('reports.client');
    Route::get('/getClientReport', [ReportController::class, 'getClientReport'])->name('reports.getClientReport');
    Route::get('/reports/client/export', [ReportController::class, 'exportClientReport'])->name('reports.client.export');

    Route::get('/reports/employee', [ReportController::class, 'employee_report'])->name('reports.employee');
    Route::get('/getEmployeeReport', [ReportController::class, 'getEmployeeReport'])->name('reports.getEmployeeReport');
    Route::get('/reports/employee/export', [ReportController::class, 'exportEmployeeReport'])->name('reports.employee.export');

    Route::middleware('permission:client-services-report')->group(function () {
        Route::get('/reports/client-services', [ReportController::class, 'clientServicesReport'])->name('reports.client-services');
        Route::get('/getClientServicesReport', [ReportController::class, 'getClientServicesReport'])->name('reports.getClientServicesReport');
        Route::get('/reports/client-services/export', [ReportController::class, 'exportClientServicesReport'])->name('reports.client-services.export');
    });

    Route::get('/reports/employee_payments', [ReportController::class, 'employee_payment_report'])->name('reports.employee.payments');
    Route::get('getEmployeePaymentReport', [ReportController::class, 'getEmployeePaymentReport'])->name('reports.getEmployeePaymentReport');
    Route::get('/reports/employee_payments/export', [ReportController::class, 'exportEmployeePaymentsReport'])->name('reports.employee.payments.export');

    Route::get('/reports/gst', [ReportController::class, 'gst_report'])->name('reports.gst');
    Route::get('/getGstReport', [ReportController::class, 'getGstReport'])->name('reports.getGstReport');
    Route::get('/reports/gst/export', [ReportController::class, 'exportGstReport'])->name('reports.gst.export');
    Route::post('/getGstSummary', [ReportController::class, 'getGstSummary'])->name('reports.getGstSummary');

    // Client Dues Report Routes
    Route::middleware('permission:client-dues-report')->group(function () {
        Route::get('/reports/client-dues', [ReportController::class, 'clientDuesReport'])->name('reports.client-dues');
        Route::get('/getClientDuesReport', [ReportController::class, 'getClientDuesReport'])->name('reports.getClientDuesReport');
        Route::get('/reports/client-dues/export', [ReportController::class, 'exportClientDuesReport'])->name('reports.client-dues.export');
        Route::post('/getClientDuesSummary', [ReportController::class, 'getClientDuesSummary'])->name('reports.getClientDuesSummary');
        Route::post('/getDynamicAmountRanges', [ReportController::class, 'getDynamicAmountRanges'])->name('reports.getDynamicAmountRanges');
    });

    // Queue Monitor Routes
    Route::get('/reports/queue-monitor', [QueueMonitorController::class, 'index'])->name('queue-monitor.index');
    Route::get('/queue-monitor/stats', [QueueMonitorController::class, 'getStats'])->name('queue-monitor.stats');
    Route::get('/queue-monitor/failed-jobs', [QueueMonitorController::class, 'getFailedJobs'])->name('queue-monitor.failed-jobs');
    Route::get('/queue-monitor/success-jobs', [QueueMonitorController::class, 'getSuccessJobs'])->name('queue-monitor.success-jobs');
    Route::post('/queue-monitor/retry', [QueueMonitorController::class, 'retryJob'])->name('queue-monitor.retry');
    Route::post('/queue-monitor/delete', [QueueMonitorController::class, 'deleteJob'])->name('queue-monitor.delete');
    Route::post('/queue-monitor/clear-failed', [QueueMonitorController::class, 'clearFailedJobs'])->name('queue-monitor.clear-failed');
    Route::post('/queue-monitor/process', [QueueMonitorController::class, 'processQueue'])->name('queue-monitor.process');
    //Reports routes end here


});

Route::post('/employees/update-status', [EmployeeController::class, 'updateStatus'])->name('employees.update-status')->middleware('permission:employee-edit');

//Roles routes (duplicate - removed)

//cron Jobs
Route::get('/generate-invoices', [InvoiceController::class, 'generateInvoices']);

// Route::get('/updateAccountLedger', [InvoiceController::class, 'updateAccountLedger']);

//Common Links
Route::get('/verify-invoice/{encrypted}', [InvoiceController::class, 'verify'])->name('invoice.verify');
Route::get('/invoice-payments/{id}', [InvoiceController::class, 'getInvoicePayments'])->name('invoice.payments');
Route::get('/verify-payment/{encrypted}', [PaymentController::class, 'verify'])->name('payment.verify');

Route::get('/mail', [EmployeeController::class, 'mail_test']);

Route::get('/invoice/mail/{id}', [InvoiceController::class, 'mail_sent'])->name('invoice.mail');
Route::get('/payments/mail/{id}', [PaymentController::class, 'mailSent'])->name('payments.mail');
Route::get('/reports', function () {
    return view('reports/revenue-report');
});
    //Inventory routes start here
    Route::middleware('permission:inventory-list')->group(function () {
        Route::get('/inventory', [InventoryController::class, 'index'])->name('inventory.index');
        Route::get('/inventory/data', [InventoryController::class, 'getInventoryData'])->name('inventory.data');
    });

    Route::middleware('permission:inventory-history')->group(function () {
        Route::get('/inventory/{id}/history', [InventoryController::class, 'getStockHistory'])->name('inventory.history');
    });

    Route::middleware('permission:inventory-create')->group(function () {
        Route::post('/inventory', [InventoryController::class, 'store'])->name('inventory.store');
    });

    Route::middleware('permission:inventory-edit')->group(function () {
        Route::get('/inventory/{id}', [InventoryController::class, 'show'])->name('inventory.show');
        Route::put('/inventory/{id}', [InventoryController::class, 'update'])->name('inventory.update');
        Route::post('/inventory/{id}/update-stock', [InventoryController::class, 'updateStock'])->name('inventory.updateStock');
    });

    Route::middleware('permission:inventory-delete')->group(function () {
        Route::delete('/inventory/{id}', [InventoryController::class, 'destroy'])->name('inventory.destroy');
    });
    //Inventory routes end here

    //Inventory Categories routes start here
    Route::middleware('permission:inventory-list')->group(function () {
        Route::get('/categories', [InventoryCategoryController::class, 'index'])->name('categories.index');
        Route::get('/categories/data', [InventoryCategoryController::class, 'getCategoriesData'])->name('categories.data');
    });

    Route::middleware('permission:inventory-create')->group(function () {
        Route::post('/categories', [InventoryCategoryController::class, 'store'])->name('categories.store');
    });

    Route::middleware('permission:inventory-edit')->group(function () {
        Route::get('/categories/{id}', [InventoryCategoryController::class, 'show'])->name('categories.show');
        Route::put('/categories/{id}', [InventoryCategoryController::class, 'update'])->name('categories.update');
    });

    Route::middleware('permission:inventory-delete')->group(function () {
        Route::delete('/categories/{id}', [InventoryCategoryController::class, 'destroy'])->name('categories.destroy');
    });
    //Inventory Categories routes end here

    //Expenses routes start here
    Route::middleware('permission:expense-list')->group(function () {
        Route::get('/expenses', [ExpenseController::class, 'index'])->name('expenses.index');
        Route::get('/expenses-data', [ExpenseController::class, 'getExpenses'])->name('expenses.data');
        Route::get('/expenses/export', [ExpenseController::class, 'exportExpenses'])->name('expenses.export');
    });

    Route::middleware('permission:expense-view')->group(function () {
        Route::get('/expenses/view/{id}', [ExpenseController::class, 'show'])->name('expenses.show');
    });

    Route::middleware('permission:expense-change-logs')->group(function () {
        Route::get('/expenses/{id}/change-logs', [ExpenseController::class, 'changeLogs'])->name('expenses.change-logs');
    });

    Route::middleware('permission:expense-create')->group(function () {
        Route::get('/expenses/add', [ExpenseController::class, 'create'])->name('expenses.create');
        Route::post('/expenses/store', [ExpenseController::class, 'store'])->name('expenses.store');
    });

    Route::middleware('permission:expense-edit')->group(function () {
        Route::get('/expenses/edit/{id}', [ExpenseController::class, 'edit'])->name('expenses.edit');
        Route::put('/expenses/update/{id}', [ExpenseController::class, 'update'])->name('expenses.update');
    });

    Route::middleware('permission:expense-approve')->group(function () {
        Route::post('/expenses/update-status', [ExpenseController::class, 'updateStatus'])->name('expenses.update-status');
    });

    Route::middleware('permission:expense-delete')->group(function () {
        Route::delete('/expenses/{id}', [ExpenseController::class, 'destroy'])->name('expenses.destroy');
    });
    //Expenses routes end here

    //Expense Categories routes start here
    Route::middleware('permission:expense-category-list')->group(function () {
        Route::get('/expense-categories', [ExpenseCategoryController::class, 'index'])->name('expense-categories.index');
        Route::get('/expense-categories-data', [ExpenseCategoryController::class, 'getCategories'])->name('expense-categories.data');
    });

    Route::middleware('permission:expense-category-create')->group(function () {
        Route::post('/expense-categories', [ExpenseCategoryController::class, 'store'])->name('expense-categories.store');
    });

    Route::middleware('permission:expense-category-edit')->group(function () {
        Route::get('/expense-categories/{id}', [ExpenseCategoryController::class, 'show'])->name('expense-categories.show');
        Route::put('/expense-categories/{id}', [ExpenseCategoryController::class, 'update'])->name('expense-categories.update');
    });

    Route::middleware('permission:expense-category-delete')->group(function () {
        Route::delete('/expense-categories/{id}', [ExpenseCategoryController::class, 'destroy'])->name('expense-categories.destroy');
    });
    //Expense Categories routes end here
Route::get('/notifications', function () {
     return view('notifications/notifications');
});
//settings
Route::get('/settings', [SettingsController::class, 'settings'])->name('settings');
Route::post('/settings/update', [SettingsController::class, 'updateSettings'])->name('settings.update');
Route::post('/settings/session-timeout', [SettingsController::class, 'updateSessionTimeout'])->name('settings.updateSessionTimeout');
Route::post('/settings/email-config', [SettingsController::class, 'updateEmailConfig'])->name('settings.update-email-config');
Route::post('/settings/notification-alerts', [SettingsController::class, 'updateNotificationAlerts'])->name('settings.update-notification-alerts');
Route::post('/settings/generate-daily-report', [SettingsController::class, 'generateDailyReport'])->name('settings.generate-daily-report');
Route::post('/settings/preview-daily-report', [SettingsController::class, 'previewDailyReport'])->name('settings.preview-daily-report');
Route::post('/settings/service-types', [SettingsController::class, 'storeServiceType'])->name('service-types.store');
Route::post('/settings/service-types/update-status', [SettingsController::class, 'updateServiceTypeStatus'])->name('service-types.update-status');
Route::post('/settings/non-bedded-types', [SettingsController::class, 'storeNonBeddedType'])->name('non-bedded-types.store');
Route::post('/settings/non-bedded-types/update-status', [SettingsController::class, 'updateNonBeddedTypeStatus'])->name('non-bedded-types.update-status');
Route::post('/services/update-status', [SettingsController::class, 'updateServiceStatus'])->name('services.update-status');
Route::post('/settings/services/update-label', [SettingsController::class, 'updateServiceLabel'])->name('services.update-label');

// Weight Entries routes
Route::middleware('permission:weight-entry-list')->group(function () {
    Route::get('/weight-entries', [WeightEntryController::class, 'index'])->name('weight-entries.index');
    Route::get('/weight-entries-data', [WeightEntryController::class, 'getWeightEntries'])->name('weight-entries.data');
    Route::post('/weight-entries/store', [WeightEntryController::class, 'store'])->name('weight-entries.store');
    Route::get('/weight-entries/download-template', [WeightEntryController::class, 'downloadTemplate'])->name('weight-entries.download-template');
    Route::post('/weight-entries/bulk-upload', [WeightEntryController::class, 'bulkUpload'])->name('weight-entries.bulk-upload');
});

// Weight Entries routes
Route::middleware('permission:weight-entry-list')->group(function () {
    Route::get('/weight-entries', [WeightEntryController::class, 'index'])->name('weight-entries.index');
    Route::get('/weight-entries-data', [WeightEntryController::class, 'getWeightEntries'])->name('weight-entries.data');
    Route::post('/weight-entries/store', [WeightEntryController::class, 'store'])->name('weight-entries.store');
    Route::get('/weight-entries/download-template', [WeightEntryController::class, 'downloadTemplate'])->name('weight-entries.download-template');
    Route::post('/weight-entries/bulk-upload', [WeightEntryController::class, 'bulkUpload'])->name('weight-entries.bulk-upload');
});

Route::get('employees/assigned-clients', [App\Http\Controllers\EmployeeController::class, 'getAssignedClients'])->name('employees.assigned-clients');

Route::get('/verify-certificate/{encrypted}', [ClientController::class, 'verifyCertificate'])->name('certificate.verify');

// Fallback route for 404 errors - redirect to login if not authenticated
Route::fallback(function () {
    if (!Auth::check()) {
        return redirect()->route('login')->with('error', 'Page not found. Please login to continue.');
    }

    // If authenticated, show proper 404 page
    abort(404);
});

