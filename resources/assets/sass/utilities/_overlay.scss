/* Overlay Css Start */
.gradient-overlay {
    position: relative;
    z-index: 1;
    &.top-0 {
        &::before {
            transform: rotate(180deg);
        }
    }
    &::before {
        position: absolute;
        content: "";
        width: 100%;
        height: 50%;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.25) 22%, rgba(0, 0, 0, 0.69) 57.92%, rgba(0, 0, 0, 0.70) 100%);
        left: inherit;
        bottom: inherit;
    }
}
/* Overlay Css End */