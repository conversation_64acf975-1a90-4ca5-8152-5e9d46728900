// when parent div hover change bg color
@each $color, $shades in $colors {
  @each $shade, $value in $shades {
    .group-item {
      &:hover {
        .group-hover\:bg-#{$color}-#{$shade} {
          background-color: var(--#{$color}-#{$shade}) !important;
        }
      }
    }
  }
}

@each $color, $shades in $semantic-colors {
  @each $shade, $value in $shades {
    .group-item {
      &:hover {
        .group-hover\:bg-#{$color}-#{$shade} {
          background-color: var(--#{$color}-#{$shade}) !important;
        }
      }
    }
  }
}

@each $property, $value in $extra-colors {
  .group-item {
    &:hover {
      .group-hover\:bg-#{$property} {
        background-color: var(--#{$property}) !important;
      }
    }
  }
}

// when parent div hover change text color
@each $color, $shades in $colors {
  @each $shade, $value in $shades {
    .group-item {
      &:hover {
        .group-hover\:text-#{$color}-#{$shade} {
          color: var(--#{$color}-#{$shade}) !important;
        }
      }
    }
  }
}

@each $color, $shades in $semantic-colors {
  @each $shade, $value in $shades {
    .group-item {
      &:hover {
        .group-hover\:text-#{$color}-#{$shade} {
          color: var(--#{$color}-#{$shade}) !important;
        }
      }
    }
  }
}

@each $property, $value in $extra-colors {
  .text-#{$property} {
    color: var(--#{$property});
  }
  .group-item {
    &:hover {
      .group-hover\:text-#{$property} {
        color: var(--#{$property}) !important;
      }
    }
  }
}

.group-item {
  &:hover {
    .group-hover\:text-white {
      color: var(--base);
    }
  }
}


.text-hover {
  &-white {
    &:hover {
      color: #fff !important;
    }
  }
}

.cursor-pointer {
  cursor: pointer;
}
.cursor-default {
  cursor: default !important;
}

.hover-scale-img {
  &__img {
    transition: .2s linear;
  }
  &:hover {
    .hover-scale-img__img {
      transform: scale(1.1);
    }
  }
}

.visibility-hidden {
  visibility: hidden;
}