/* Slick Slider Css Start  */
.slick-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  .slick-active button {
    background-color: var(--primary-600);
    width: 20px;
  }
  button {
    width: 10px;
    height: 4px;
    background-color: var(--primary-50);
    text-indent: -999999px;
    border-radius: 50px;
    color: var(--primary-50);
  }
}

/* Dots Circle Style */
.dots-style-circle {
  .slick-dots {
    gap: 8px;
    .slick-active button {
      background-color: var(--primary-600);
      width: 12px;
      height: 12px;
    }
    button {
      width: 8px;
      height: 8px;
      background-color: #5A75AE;
      text-indent: -999999px;
      border-radius: 50%;
    }
  }  
}

/* Dots Bottom Position Style */
.dots-positioned {
  .slick-dots {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 24px;
  }
}

/* Slick Arrows Css */
.slick-arrow {
  color: #fff;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 16px;
  font-size: 32px;
  line-height: 0;
  z-index: 1;
  &.slick-next {
    left: auto;
    right: 16px;
  }
}
/* Slick Slider Css End */


/* Slider Progress Css Start */
.slider-progress{
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 5px;
  span{
    position: absolute;
    left: 0;
    top: 0;
    display: block;
    width: 0px;
    height: 100%;
    background-color: var(--primary-600);
    transition: all 0s linear;
    &.active{
      width: 100%;
    }
  }
}
/* Slider Progress Css End */
