/* === Invoice Templates CSS === */

/* Bulk Invoice Template */
.invoice-bulk-template {
  @media print {
    @page {
      size: A4;
      margin: 20mm 15mm 20mm 15mm;
    }
    .page-break {
      page-break-before: always;
    }
  }

  body {
    font-size: 14px;
    margin: 0;
    padding: 0;
    font-family: "Poppins", Arial, sans-serif;
    background-color: #fff;
    color: #333;
  }

  .container {
    max-width: 210mm;
    margin: 0 auto;
    position: relative;
  }

  table {
    width: 100%;
    border-collapse: collapse;
  }

  .no-border {
    border: none !important;
  }

  .text-end {
    text-align: right;
  }

  .text-center {
    text-align: center;
  }

  .qr-code {
    width: 100px;
    height: 100px;
  }

  .qr-label {
    font-size: 10px;
    text-align: center;
    margin-top: 5px;
  }

  .invoice-page {
    page-break-after: always;
    
    &:last-child {
      page-break-after: avoid;
    }
  }
}

/* Invoice Template V2 */
.invoice-template-v2 {
  @media print {
    @page {
      size: A4;
      margin: 20mm 15mm 20mm 15mm;
    }
    .page-break {
      page-break-before: always;
    }
  }

  body {
    margin: 0;
    padding: 0;
    font-family: "Poppins", Arial, sans-serif;
    font-size: 14px;
    background-color: #fff;
    color: #333;
  }

  .container {
    max-width: 210mm;
    margin: 0 auto;
    position: relative;
  }

  h4 {
    margin: 0;
    font-size: 24px;
    color: #2c3e50;
  }

  table {
    width: 100%;
    border-collapse: collapse;
  }

  .no-border {
    border: none !important;
  }

  .text-center {
    text-align: center;
  }

  .text-right {
    text-align: right;
  }

  .footer-note {
    font-size: 12px;
  }

  .qr-code {
    width: 100px;
    height: 100px;
  }

  .company-info {
    font-size: 12px;
    line-height: 1.4;
  }

  .invoice-details {
    background-color: #f8f9fa;
    padding: 10px;
  }

  .amount-section {
    background-color: #e9ecef;
    padding: 8px;
    font-weight: bold;
  }

  .text-end {
    text-align: right;
  }

  .text-center {
    text-align: center;
  }

  .fw-bold {
    font-weight: 600;
  }

  .text-upper {
    text-transform: uppercase;
  }

  /* Global 1‑px border so print/PDF never drops grid lines */
  table,
  th,
  td {
    border: 1px solid #b0b0b0;
  }

  /* Utility to suppress borders where we don't want them */
  .no-border,
  .no-border > th,
  .no-border > td {
    border: none !important;
  }

  th {
    background-color: #f5f5f5;
    font-weight: 600;
  }

  td,
  th {
    padding: 6px 4px;
    vertical-align: top;
  }

  /* Avoid cutting rows across pages */
  tr {
    page-break-inside: avoid;
  }

  .qr-label {
    font-size: 10px;
    margin-top: 2px;
    color: #555;
    text-transform: uppercase;
    letter-spacing: 0.4px;
  }

  .amount-in-words {
    background-color: #f8f9fa;
    padding: 4px;
    border-radius: 4px;
  }

  .footer {
    margin-top: 10px;
    border-top: 2px solid #e0e0e0;
    padding-top: 4px;
  }

  .footer-note {
    font-size: 12px;
  }
}

/* Standard Invoice Template */
.invoice-template-standard {
  @media print {
    @page {
      size: A5;
      margin: 10mm;
    }
  }

  body {
    font-size: 12px;
    margin: 0;
    padding: 0;
    font-family: Arial, Helvetica, sans-serif;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
  }

  table,
  td,
  th {
    border-color: #7e7e7e;
  }

  td,
  th {
    padding: 5px;
    text-align: left;
  }

  h4 {
    font-size: 1.2rem;
    margin: 0;
  }

  address,
  p {
    font-size: 12px;
    margin: 0;
  }

  .text-end {
    text-align: right;
  }

  .text-center {
    text-align: center;
  }

  .badge {
    display: inline-block;
    padding: .25em .4em;
    font-size: 75%;
    font-weight: 700;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .375rem;
    background-color: #28a745;
    color: #fff;
  }

  .footer p {
    font-size: 12px;
  }

  .company-logo {
    width: 34px;
  }

  .qr-code {
    width: 100px;
  }

  .signature-img {
    width: 80px;
  }
}

/* Payment Receipt Template */
.payment-receipt-template {
  @media print {
    @page {
      size: A5;
      margin: 10mm;
    }
  }

  body {
    font-size: 10px;
    margin: 0;
    padding: 0;
    font-family: Arial, Helvetica, sans-serif;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    font-size: 9px;
  }

  table,
  td,
  th {
    border-color: #7e7e7e;
  }

  td,
  th {
    padding: 5px;
    text-align: left;
  }

  h4 {
    font-size: 1.2rem;
    margin: 0;
  }

  address,
  p {
    font-size: 9px;
    margin: 0;
  }

  .text-end {
    text-align: right;
  }

  .text-center {
    text-align: center;
  }

  .badge {
    display: inline-block;
    padding: .25em .4em;
    font-size: 75%;
    font-weight: 700;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .375rem;
    background-color: #28a745;
    color: #fff;
  }

  .footer p {
    font-size: 9px;
  }

  .receipt-header {
    font-weight: bold;
    font-size: 12px;
  }

  .receipt-amount {
    font-size: 14px;
    font-weight: bold;
  }
}

/* Certificate Template */
.certificate-template {
  body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
  }

  .container {
    width: 100%;
    max-width: 800px;
    background-color: #ffffff;
    text-align: center;
  }

  .header {
    padding: 10px;
    border-bottom: 2px solid #008cba;
  }

  .header img {
    max-width: 100px;
    margin-bottom: 10px;
  }

  .header-text {
    text-align: left;
    margin-left: 16px;
  }

  .header h1 {
    margin: 5px 0;
    font-size: 18px;
  }

  .certificate-title {
    font-size: 36px;
    font-weight: bold;
    color: #008cba;
    margin: 20px 0;
  }

  .certificate-content {
    padding: 20px;
    font-size: 16px;
    line-height: 1.6;
  }

  .signature-section {
    margin-top: 40px;
    display: flex;
    justify-content: space-between;
    padding: 0 40px;
  }

  .signature-box {
    text-align: center;
    width: 200px;
  }

  .signature-line {
    border-top: 1px solid #000;
    margin-top: 40px;
    padding-top: 5px;
  }

  .certificate-header {
    text-align: center;
    margin-bottom: 20px;
  }

  .certificate-header h2 {
    color: #008cba;
    font-size: 24px;
    margin-bottom: 10px;
  }

  .certificate-body {
    text-align: center;
    margin-top: 18px;
    font-size: 16px;
  }

  .certificate-body .c-name {
    font-size: 28px;
    margin-bottom: 5px;
    font-family: "Teko", serif;
    font-optical-sizing: auto;
    font-weight: 500;
    font-style: normal;
  }

  .certificate-footer {
    margin-top: 120px;
    text-align: right;
    font-size: 14px;
    font-weight: bold;
  }

  .certificate-footer small {
    font-size: 11px;
  }

  .footer {
    font-size: 12px;
    color: #777777;
    text-align: center;
    line-height: 1.6;
    padding: 20px;
  }

  .footer p {
    margin: 0;
  }

  .footer a {
    color: #008cba;
    text-decoration: none;
  }

  .footer a:hover {
    text-decoration: underline;
  }
}

/* Common invoice utilities */
.invoice-watermark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 72px;
  color: rgba(0, 0, 0, 0.1);
  z-index: -1;
  pointer-events: none;
}

.invoice-page-break {
  page-break-before: always;
}

.invoice-avoid-break {
  page-break-inside: avoid;
}
