.modal {
  &-header {
    border-color: var(--input-form-light);
    .btn-close {
      margin-inline-start: auto;
      margin-inline-end: 0;
    }
  }
  &-content {
    background-color: var(--white);
  }
  &-footer {
    border-color: var(--input-form-light);
  }
}

#snackbar {
  position: fixed;
  top: 0;
  right: 20px;
  min-width: 300px;
  max-width: 400px;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--success-100);
  border: 1px solid var(--success-200);
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.4s ease-in-out;
  z-index: 9999;

  &.show {
      transform: translateY(0);
      opacity: 1;
  }

  .remove-button {
      background: transparent;
      border: none;
      cursor: pointer;
      font-size: 20px;
  }
}
