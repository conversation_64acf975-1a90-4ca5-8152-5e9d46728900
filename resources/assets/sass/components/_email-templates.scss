/* === Email Templates CSS === */

/* Monthly Alert Email Template */
.email-monthly-alert {
  body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    padding: 0;
  }

  .container {
    max-width: 600px;
    margin: 20px auto;
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  }

  .header {
    text-align: center;
    padding-bottom: 20px;
    border-bottom: 2px solid #ddd;
  }

  .content {
    padding: 20px 0;
  }

  .button {
    display: inline-block;
    padding: 10px 20px;
    background: #007bff;
    color: #ffffff;
    text-decoration: none;
    border-radius: 5px;
    margin-top: 20px;
  }

  .footer {
    margin-top: 20px;
    text-align: center;
    font-size: 12px;
    color: #777;
  }
}

/* Employee Creation Email Template */
.email-emp-creation {
  body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    padding: 0;
  }

  .container {
    max-width: 600px;
    margin: 20px auto;
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  }

  .header {
    text-align: center;
    padding-bottom: 20px;
    border-bottom: 2px solid #ddd;
  }

  .content {
    padding: 20px 0;
  }

  .button {
    display: inline-block;
    padding: 10px 20px;
    background: #007bff;
    color: #ffffff;
    text-decoration: none;
    border-radius: 5px;
    margin-top: 20px;
  }

  .footer {
    margin-top: 20px;
    text-align: center;
    font-size: 12px;
    color: #777;
  }
}

/* Invoice Email Template */
.email-invoice {
  body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    margin: 0;
    padding: 0;
  }

  .container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
  }

  .header {
    text-align: center;
    padding: 20px 0;
    border-bottom: 2px solid #f0f0f0;
  }

  .logo {
    max-width: 150px;
    margin-bottom: 20px;
  }

  .content {
    padding: 30px 0;
  }

  .invoice-details {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
  }

  .button {
    display: inline-block;
    padding: 12px 24px;
    background-color: #4CAF50;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    margin: 20px 0;
  }

  .footer {
    text-align: center;
    padding: 20px 0;
    border-top: 2px solid #f0f0f0;
    font-size: 12px;
    color: #666;
  }

  .table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
  }

  .table th,
  .table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
  }

  .table th {
    background-color: #f5f5f5;
  }
}

/* Common email utilities */
.email-text-center {
  text-align: center !important;
}

.email-text-left {
  text-align: left !important;
}

.email-text-right {
  text-align: right !important;
}

.email-mb-20 {
  margin-bottom: 20px !important;
}

.email-mt-20 {
  margin-top: 20px !important;
}

.email-p-20 {
  padding: 20px !important;
}

.email-border-radius-8 {
  border-radius: 8px !important;
}

.email-shadow-light {
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1) !important;
}
