<aside class="sidebar">
    <button type="button" class="sidebar-close-btn">
        <iconify-icon icon="radix-icons:cross-2"></iconify-icon>
    </button>
    <div>
        <a href="/dashboard" class="sidebar-logo">
            <img src="/assets/images/logo-text.svg" alt="site logo" class="light-logo">
            <img src="/assets/images/logo-text.svg" alt="site logo" class="dark-logo">
            <img src="/assets/images/logo-symbol.svg" alt="site logo" class="logo-icon">
        </a>
    </div>
    <div class="sidebar-menu-area">
        <ul class="sidebar-menu" id="sidebar-menu">
            @can('dashboard')
            <li>
                <a href="/dashboard"  class="{{ (request()->is('dashboard') ) ? 'active-page' : '' }}">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="menu-icon"></iconify-icon>
                    <span>Dashboard</span>
                </a>
            </li>
            @endcan
            @can('client-list')
            <li>
                <a href="/clients" class="{{ (request()->is('clients') || request()->is('clients/*') ) ? 'active-page' : '' }}">
                    <iconify-icon icon="hugeicons:user-group" class="menu-icon"></iconify-icon>
                    <span>Client Management</span>
                </a>
            </li>

            @endcan
            @can('service-list')
            <li>
                <a href="/services" class="{{ (request()->is('services') || request()->is('services/*') ) ? 'active-page' : '' }}">
                    <iconify-icon icon="hugeicons:user-settings-01" class="menu-icon"></iconify-icon>
                    <span>Services</span>
                </a>
            </li>
            @endcan
            @can('weight-entry-list')
            <li>
                <a href="/weight-entries" class="{{ (request()->is('weight-entries') || request()->is('weight-entries/*') ) ? 'active-page' : '' }}">
                    <iconify-icon icon="hugeicons:weight-scale" class="menu-icon"></iconify-icon>
                    <span>Weight Entries</span>
                </a>
            </li>
            @endcan
            @can('invoice-list')
            <li>
                <a href="/invoices" class="{{ (request()->is('invoices') || request()->is('invoices/*') ) ? 'active-page' : '' }}">
                    <iconify-icon icon="hugeicons:invoice-03" class="menu-icon"></iconify-icon>
                    <span>Invoice Management</span>
                </a>
            </li>
            @endcan
            @can('payment-list')
            <li>
                <a href="/payments" class="{{ (request()->is('payments') || request()->is('payments/*') ) ? 'active-page' : '' }}">
                    <iconify-icon icon="hugeicons:payment-01" class="menu-icon"></iconify-icon>
                    <span>Trasaction/ Payments</span>
                </a>
            </li>
            @endcan
            @can('inventory-list')
            <li class="dropdown">
                <a href="javascript:void(0)" class="{{ (request()->is('inventory*') || request()->is('categories*')) ? 'active-page' : '' }}">
                    <iconify-icon icon="hugeicons:package" class="menu-icon"></iconify-icon>
                    <span>Inventory Management</span>
                </a>
                <ul class="sidebar-submenu">
                    <li>
                        <a href="/inventory" class="{{ request()->is('inventory') && !request()->is('categories*') ? 'active-page' : '' }}">
                            
                            <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i>
                            Items
                        </a>
                    </li>
                    <li>
                        <a href="/categories" class="{{ request()->is('categories*') ? 'active-page' : '' }}">
                            <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i>
                            Categories
                        </a>
                    </li>
                </ul>
            </li>
            @endcan
            @can('expense-list')
            <li class="dropdown">
                <a href="javascript:void(0)" class="{{ (request()->is('expenses*') || request()->is('expense-categories*')) ? 'active-page' : '' }}">
                    <iconify-icon icon="hugeicons:money-bag-02" class="menu-icon"></iconify-icon>
                    <span>Expense Management</span>
                </a>
                <ul class="sidebar-submenu">
                    <li>
                        <a href="/expenses" class="{{ request()->is('expenses') && !request()->is('expense-categories*') ? 'active-page' : '' }}">
                            <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i>
                            Expenses
                        </a>
                    </li>
                    <li>
                        <a href="/expense-categories" class="{{ request()->is('expense-categories*') ? 'active-page' : '' }}">
                            <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i>
                            Categories
                        </a>
                    </li>
                </ul>
            </li>
            @endcan
            @can('employee-list')
            <li>
                <a href="/employees"  class="{{ (request()->is('employees') || request()->is('employees/*') ) ? 'active-page' : '' }}">
                    <iconify-icon icon="hugeicons:user-account" class="menu-icon"></iconify-icon>
                    <span>Employees</span>
                </a>
            </li>
            @endcan
            @can('role-list')
            <li>
                <a href="/roles" class="{{ (request()->is('roles') || request()->is('roles/*') ) ? 'active-page' : '' }}">
                    <iconify-icon icon="hugeicons:user-switch" class="menu-icon"></iconify-icon>
                    <span>Roles</span>
                </a>
            </li>
            @endcan
            <li class="dropdown">
                <a href="javascript:void(0)" class="{{ (request()->is('reports') || request()->is('reports/*') ) ? 'active-page' : '' }}">
                    <iconify-icon icon="hugeicons:school-report-card" class="menu-icon"></iconify-icon>
                    <span>Reports</span>
                </a>
                <ul class="sidebar-submenu">
                    <li>
                        <a href="/reports/client">
                            <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i>
                            Client
                        </a>
                    </li>
                    @can('client-services-report')
                    <li>
                        <a href="{{ route('reports.client-services') }}">
                            <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i>
                            Client Services
                        </a>
                    </li>
                    @endcan
                    @can('client-dues-report')
                    <li>
                        <a href="{{ route('reports.client-dues') }}">
                            <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i>
                            Client Dues
                        </a>
                    </li>
                    @endcan
                    <li>
                        <a href="/reports/employee">
                            <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i>
                            Employee
                        </a>
                    </li>
                    <li>
                        <a href="/reports/employee_payments">
                            <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i>
                            Employee Payments
                        </a>
                    </li>
                    <li>
                        <a href="/reports/gst">
                            <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i>
                            GST
                        </a>
                    </li>
                    <li>
                        <a href="/reports/queue-monitor">
                            <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i>
                            Queue Monitor
                        </a>
                    </li>

                </ul>
            </li>
            <li>
                <a href="/settings" class="{{ (request()->is('settings') || request()->is('settings/*') ) ? 'active-page' : '' }}">
                    <iconify-icon icon="hugeicons:settings-01" class="menu-icon"></iconify-icon>
                    <span>Settings</span>
                </a>
            </li>


        </ul>
    </div>
</aside>
