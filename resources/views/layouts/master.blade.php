<!-- meta tags and other links -->
<!DOCTYPE html>
<html lang="en" data-theme="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="session-lifetime" content="{{ $company_details['session_lifetime_minutes'] ?? config('session.lifetime', 120) }}">
    <meta name="session-warning" content="{{ $company_details['session_warning_minutes'] ?? 5 }}">
    <meta name="session-timeout-enabled" content="{{ ($company_details['session_timeout_enabled'] ?? '1') == '1' ? 'true' : 'false' }}">
    <meta name="session-check-interval" content="{{ ($company_details['session_check_interval_seconds'] ?? 60) * 1000 }}">
    <meta name="session-heartbeat-interval" content="{{ ($company_details['session_heartbeat_interval_minutes'] ?? 5) * 60000 }}">
    <title> @yield('title', 'Paidash')</title>
    <link rel="icon" type="image/png" href="/assets/images/favicon.ico" sizes="16x16">
    <!-- remix icon font css  -->
    <link rel="stylesheet" href="/assets/css/remixicon.css">
    <!-- FontAwesome CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- BootStrap css -->
    <link rel="stylesheet" href="/assets/css/lib/bootstrap.min.css">
    <!-- Apex Chart css -->
    <link rel="stylesheet" href="/assets/css/lib/apexcharts.css">
    <!-- Data Table css -->
    <link rel="stylesheet" href="/assets/css/lib/dataTables.min.css">
    <!-- Text Editor css -->
    <!-- <link rel="stylesheet" href="/assets/css/lib/editor-katex.min.css"> -->
    <!-- <link rel="stylesheet" href="/assets/css/lib/editor.atom-one-dark.min.css">
    <link rel="stylesheet" href="/assets/css/lib/editor.quill.snow.css"> -->
    <!-- Date picker css -->
    <link rel="stylesheet" href="/assets/css/lib/flatpickr.min.css">
    <!-- Calendar css -->
    <!-- <link rel="stylesheet" href="/assets/css/lib/full-calendar.css"> -->
    <!-- Vector Map css -->
    <!-- <link rel="stylesheet" href="/assets/css/lib/jquery-jvectormap-2.0.5.css"> -->
    <!-- Popup css -->
    <!-- <link rel="stylesheet" href="/assets/css/lib/magnific-popup.css"> -->
    <!-- Slick Slider css -->
    <!-- <link rel="stylesheet" href="/assets/css/lib/slick.css"> -->
    <!-- prism css -->
    <link rel="stylesheet" href="/assets/css/lib/prism.css">
    <!-- file upload css -->
    <link rel="stylesheet" href="/assets/css/lib/file-upload.css">
    <!-- Include Daterangepicker CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">

    <!-- <link rel="stylesheet" href="/assets/css/lib/audioplayer.css"> -->
    <!-- Include Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" rel="stylesheet" />
     <!-- DataTables CSS -->
     <!-- <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.3.6/css/buttons.dataTables.min.css"> -->

    <!-- main css -->
    {{-- <link rel="stylesheet" href="/assets/css/style.css"> --}}
    @vite(['resources/assets/sass/main.scss', 'resources/assets/js/app.js'])
    <!-- Session Timeout CSS -->
    <link rel="stylesheet" href="/assets/css/session-timeout.css">
    @yield('css')
    @stack('styles')

</head>

<body>

@include('layouts.sidebar')

    <main class="dashboard-main">

@include('layouts.navbar')
        @yield('content')

        <footer class="d-footer">
            <div class="row align-items-center justify-content-between">
                <div class="col-auto">
                    <p class="mb-0 text-small">© {{ date('Y') }} Paidash. All Rights Reserved.</p>
                </div>
                <div class="col-auto">
                    <p class="mb-0">Design and developed by <a href="https://mvitsol.com" target="_blank" class="text-primary-600">MVIT Solutions</a></p>
                </div>
            </div>
        </footer>
    </main>

    <!-- jQuery library js -->
    <script src="/assets/js/lib/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap js -->
    <script src="/assets/js/lib/bootstrap.bundle.min.js"></script>
    <!-- Data Table js -->
    <script src="/assets/js/lib/dataTables.min.js"></script>
    <!-- Iconify Font js -->
    <script src="/assets/js/lib/iconify-icon.min.js"></script>
    <!-- jQuery UI js -->
    <!-- <script src="/assets/js/lib/jquery-ui.min.js"></script> -->
    <!-- Vector Map js
    <script src="/assets/js/lib/jquery-jvectormap-2.0.5.min.js"></script>
    <script src="/assets/js/lib/jquery-jvectormap-world-mill-en.js"></script> -->
    <!-- Popup js -->
    <!-- <script src="/assets/js/lib/magnifc-popup.min.js"></script> -->
    <!-- Slick Slider js -->
    <!-- <script src="/assets/js/lib/slick.min.js"></script> -->
    <!-- prism js -->
    <script src="/assets/js/lib/prism.js"></script>
    <!-- file upload js -->
    <script src="/assets/js/lib/file-upload.js"></script>
    <!-- audioplayer -->
    <!-- <script src="/assets/js/lib/audioplayer.js"></script> -->

    <!-- main js -->
    <script src="/assets/js/app.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
    <!-- Include Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.min.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/buttons/2.3.6/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.html5.min.js"></script>
    <!-- <script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.print.min.js"></script> -->

    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts.js"></script> -->
    <!-- Moment.js (Required for Daterangepicker) -->
    <script src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>

    <!-- Daterangepicker JS -->
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

    <!-- Session Timeout Manager -->
    <script src="/assets/js/session-timeout.js"></script>

    @yield('script')
    <script>
        $(document).ready(function () {
            function closeSnackbar() {
                $(".snackbar").fadeOut(500, function () {
                    $(this).remove();
                });
            }

            // Close when clicking the close button
            $(".remove-button").on("click", function () {
                closeSnackbar();
            });

            // Auto close after 10 seconds
            setTimeout(closeSnackbar, 10000);
        });
    </script>
    </body>

    </html>
