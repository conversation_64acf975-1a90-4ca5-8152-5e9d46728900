<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Daily Report - {{ $report_date->format('d M Y') }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 12px 12px 0 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header .date {
            font-size: 18px;
            margin-top: 10px;
            opacity: 0.9;
        }
        .content {
            background-color: white;
            padding: 0;
            border: 1px solid #dee2e6;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .footer {
            background-color: #6c757d;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 0 0 12px 12px;
            font-size: 14px;
        }
        .summary-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }
        .summary-card {
            flex: 1;
            min-width: 200px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid;
        }
        .summary-card.revenue { border-left-color: #28a745; }
        .summary-card.collections { border-left-color: #007bff; }
        .summary-card.outstanding { border-left-color: #dc3545; }
        .summary-card.efficiency { border-left-color: #ffc107; }
        .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            text-transform: uppercase;
            color: #6c757d;
            font-weight: 600;
        }
        .summary-card .value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .section {
            padding: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .section:last-child {
            border-bottom: none;
        }
        .section h2 {
            margin: 0 0 20px 0;
            font-size: 20px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 3px solid #007bff;
        }
        .stat-box h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #6c757d;
            text-transform: uppercase;
        }
        .stat-box .number {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        .table tr:hover {
            background-color: #f8f9fa;
        }
        .amount {
            font-weight: bold;
            color: #28a745;
        }
        .amount.negative {
            color: #dc3545;
        }
        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .badge.success { background-color: #d4edda; color: #155724; }
        .badge.warning { background-color: #fff3cd; color: #856404; }
        .badge.danger { background-color: #f8d7da; color: #721c24; }
        .badge.info { background-color: #d1ecf1; color: #0c5460; }
        .activity-item {
            padding: 10px;
            border-left: 3px solid #007bff;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 0 4px 4px 0;
        }
        .activity-time {
            font-size: 12px;
            color: #6c757d;
        }
        @media (max-width: 600px) {
            .summary-cards {
                flex-direction: column;
            }
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Daily Business Report</h1>
        <div class="date">{{ $report_date->format('l, d F Y') }}</div>
        <div style="margin-top: 10px; font-size: 16px;">{{ $company_name }}</div>
    </div>

    <div class="content">
        <!-- Summary Cards -->
        <div class="summary-cards">
            <div class="summary-card revenue">
                <h3>Total Revenue</h3>
                <div class="value">₹{{ number_format($report_data['summary']['total_revenue'], 2) }}</div>
            </div>
            <div class="summary-card collections">
                <h3>Collections</h3>
                <div class="value">₹{{ number_format($report_data['summary']['total_collections'], 2) }}</div>
            </div>
            <div class="summary-card outstanding">
                <h3>Expenses</h3>
                <div class="value">₹{{ number_format($report_data['summary']['total_expenses'], 2) }}</div>
            </div>
            <div class="summary-card efficiency">
                <h3>Net Profit</h3>
                <div class="value {{ $report_data['summary']['net_profit'] >= 0 ? '' : 'negative' }}">₹{{ number_format($report_data['summary']['net_profit'], 2) }}</div>
            </div>
        </div>

        <!-- Invoice Statistics -->
        <div class="section">
            <h2>📄 Invoice Statistics</h2>
            <div class="stats-grid">
                <div class="stat-box">
                    <h4>Total Invoices</h4>
                    <div class="number">{{ $report_data['invoices']['total_count'] }}</div>
                </div>
                <div class="stat-box">
                    <h4>Total Amount</h4>
                    <div class="number">₹{{ number_format($report_data['invoices']['total_amount'], 2) }}</div>
                </div>
                <div class="stat-box">
                    <h4>Paid Invoices</h4>
                    <div class="number">{{ $report_data['invoices']['paid_count'] }}</div>
                </div>
                <div class="stat-box">
                    <h4>Pending Invoices</h4>
                    <div class="number">{{ $report_data['invoices']['pending_count'] }}</div>
                </div>
            </div>

            @if($report_data['invoices']['total_count'] > 0)
            <p><strong>Average Invoice Amount:</strong> ₹{{ number_format($report_data['invoices']['average_amount'], 2) }}</p>
            @endif
        </div>

        <!-- Payment Statistics -->
        <div class="section">
            <h2>💰 Payment Statistics</h2>
            <div class="stats-grid">
                <div class="stat-box">
                    <h4>Total Payments</h4>
                    <div class="number">{{ $report_data['payments']['total_count'] }}</div>
                </div>
                <div class="stat-box">
                    <h4>Total Amount</h4>
                    <div class="number">₹{{ number_format($report_data['payments']['total_amount'], 2) }}</div>
                </div>
                <div class="stat-box">
                    <h4>Cash Payments</h4>
                    <div class="number">{{ $report_data['payments']['cash_count'] }} (₹{{ number_format($report_data['payments']['cash_amount'], 2) }})</div>
                </div>
                <div class="stat-box">
                    <h4>Online Payments</h4>
                    <div class="number">{{ $report_data['payments']['online_count'] }} (₹{{ number_format($report_data['payments']['online_amount'], 2) }})</div>
                </div>
            </div>
        </div>

        <!-- Expense Statistics -->
        <div class="section">
            <h2>💸 Expense Statistics</h2>
            <div class="stats-grid">
                <div class="stat-box">
                    <h4>Total Expenses</h4>
                    <div class="number">{{ $report_data['expenses']['total_count'] }}</div>
                </div>
                <div class="stat-box">
                    <h4>Total Amount</h4>
                    <div class="number">₹{{ number_format($report_data['expenses']['total_amount'], 2) }}</div>
                </div>
                <div class="stat-box">
                    <h4>Pending Expenses</h4>
                    <div class="number">{{ $report_data['expenses']['pending_count'] }} (₹{{ number_format($report_data['expenses']['pending_amount'], 2) }})</div>
                </div>
                <div class="stat-box">
                    <h4>Approved Expenses</h4>
                    <div class="number">{{ $report_data['expenses']['approved_count'] }} (₹{{ number_format($report_data['expenses']['approved_amount'], 2) }})</div>
                </div>
            </div>

            @if($report_data['expenses']['total_count'] > 0)
            <div class="stats-grid" style="margin-top: 15px;">
                <div class="stat-box">
                    <h4>Paid Expenses</h4>
                    <div class="number">{{ $report_data['expenses']['paid_count'] }} (₹{{ number_format($report_data['expenses']['paid_amount'], 2) }})</div>
                </div>
                <div class="stat-box">
                    <h4>Reimbursable</h4>
                    <div class="number">{{ $report_data['expenses']['reimbursable_count'] }} (₹{{ number_format($report_data['expenses']['reimbursable_amount'], 2) }})</div>
                </div>
                <div class="stat-box">
                    <h4>Average Amount</h4>
                    <div class="number">₹{{ number_format($report_data['expenses']['average_amount'], 2) }}</div>
                </div>
                <div class="stat-box">
                    <h4>Highest Expense</h4>
                    <div class="number">₹{{ number_format($report_data['expenses']['highest_amount'], 2) }}</div>
                </div>
            </div>
            @endif
        </div>

        <!-- Financial Summary -->
        <div class="section">
            <h2>📊 Financial Summary</h2>
            <div class="stats-grid">
                <div class="stat-box">
                    <h4>Net Profit</h4>
                    <div class="number {{ $report_data['financial']['net_profit'] >= 0 ? '' : 'amount negative' }}">₹{{ number_format($report_data['financial']['net_profit'], 2) }}</div>
                </div>
                <div class="stat-box">
                    <h4>Profit Margin</h4>
                    <div class="number {{ $report_data['financial']['profit_margin'] >= 0 ? '' : 'amount negative' }}">{{ number_format($report_data['financial']['profit_margin'], 1) }}%</div>
                </div>
                <div class="stat-box">
                    <h4>Expense Ratio</h4>
                    <div class="number">{{ number_format($report_data['financial']['expense_ratio'], 1) }}%</div>
                </div>
                <div class="stat-box">
                    <h4>Cash Flow</h4>
                    <div class="number {{ $report_data['financial']['cash_flow'] >= 0 ? '' : 'amount negative' }}">₹{{ number_format($report_data['financial']['cash_flow'], 2) }}</div>
                </div>
            </div>
        </div>

        <!-- Client & Service Statistics -->
        <div class="section">
            <h2>👥 Business Growth</h2>
            <div class="stats-grid">
                <div class="stat-box">
                    <h4>New Clients</h4>
                    <div class="number">{{ $report_data['clients']['new_clients_count'] }}</div>
                </div>
                <div class="stat-box">
                    <h4>New Services</h4>
                    <div class="number">{{ $report_data['services']['new_services_count'] }}</div>
                </div>
                <div class="stat-box">
                    <h4>New Employees</h4>
                    <div class="number">{{ $report_data['employees']['new_employees_count'] }}</div>
                </div>
                <div class="stat-box">
                    <h4>Total Active Clients</h4>
                    <div class="number">{{ $report_data['clients']['active_clients'] }}</div>
                </div>
            </div>
        </div>

        <!-- Outstanding Amounts -->
        <div class="section">
            <h2>⚠️ Outstanding Amounts</h2>
            <div class="stats-grid">
                <div class="stat-box">
                    <h4>Pending Invoices</h4>
                    <div class="number">{{ $report_data['outstanding']['pending_invoices_count'] }}</div>
                </div>
                <div class="stat-box">
                    <h4>Pending Amount</h4>
                    <div class="number amount">₹{{ number_format($report_data['outstanding']['pending_amount'], 2) }}</div>
                </div>
                <div class="stat-box">
                    <h4>Overdue Invoices</h4>
                    <div class="number">{{ $report_data['outstanding']['overdue_invoices_count'] }}</div>
                </div>
                <div class="stat-box">
                    <h4>Overdue Amount</h4>
                    <div class="number amount negative">₹{{ number_format($report_data['outstanding']['overdue_amount'], 2) }}</div>
                </div>
            </div>
        </div>

        <!-- Top Clients -->
        @if(count($report_data['top_clients']) > 0)
        <div class="section">
            <h2>🏆 Top Clients by Revenue</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>Client Name</th>
                        <th>Invoices</th>
                        <th>Revenue</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($report_data['top_clients'] as $client)
                    <tr>
                        <td>{{ $client->business_name }}</td>
                        <td>{{ $client->invoice_count }}</td>
                        <td class="amount">₹{{ number_format($client->total_revenue, 2) }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @endif

        <!-- Recent Activities -->
        @if(count($report_data['recent_activities']) > 0)
        <div class="section">
            <h2>🕒 Recent Activities</h2>
            @foreach($report_data['recent_activities'] as $activity)
            <div class="activity-item">
                <div>
                    <span style="margin-right: 8px;">{{ $activity['icon'] ?? '📋' }}</span>
                    {{ $activity['description'] }}
                    @if($activity['amount'] != 0)
                        <span class="amount {{ $activity['amount'] < 0 ? 'negative' : '' }}" style="float: right;">
                            {{ $activity['amount'] > 0 ? '+' : '' }}₹{{ number_format(abs($activity['amount']), 2) }}
                        </span>
                    @endif
                </div>
                <div class="activity-time">{{ \Carbon\Carbon::parse($activity['time'])->format('h:i A') }}</div>
            </div>
            @endforeach
        </div>
        @endif
    </div>

    <div class="footer">
        <p><strong>{{ $company_name }}</strong> - Daily Business Report</p>
        <p>Generated on {{ now()->format('d M Y, h:i A') }}</p>
        <p style="margin-top: 15px; font-size: 12px; opacity: 0.8;">
            This is an automated report. Please do not reply to this email.<br>
            For questions or support, contact your system administrator.
        </p>
    </div>
</body>
</html>
