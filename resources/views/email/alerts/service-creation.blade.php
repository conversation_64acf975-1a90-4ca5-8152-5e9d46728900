<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Service Creation Alert</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #6f42c1;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 30px;
            border: 1px solid #dee2e6;
        }
        .footer {
            background-color: #6c757d;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 12px;
        }
        .alert-badge {
            background-color: #17a2b8;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .info-table th,
        .info-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .info-table th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .service-type {
            font-size: 16px;
            font-weight: bold;
            color: #6f42c1;
        }
        .amount {
            font-size: 16px;
            font-weight: bold;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>⚙️ Service Creation Alert</h2>
        <span class="alert-badge">New Service</span>
    </div>

    <div class="content">
        <p><strong>Hello,</strong></p>
        
        <p>A new service has been created in {{ $company_name }}. Here are the details:</p>

        <table class="info-table">
            <tr>
                <th>Client</th>
                <td><span class="service-type">{{ $service->client->business_name }}</span></td>
            </tr>
            <tr>
                <th>Service Type</th>
                <td>{{ $service->serviceType->name ?? 'Not specified' }}</td>
            </tr>
            <tr>
                <th>Start Date</th>
                <td>{{ date('d M Y', strtotime($service->start_date)) }}</td>
            </tr>
            @if($service->end_date)
            <tr>
                <th>End Date</th>
                <td>{{ date('d M Y', strtotime($service->end_date)) }}</td>
            </tr>
            @endif
            @if($service->unit_price)
            <tr>
                <th>Unit Price</th>
                <td><span class="amount">₹{{ number_format($service->unit_price, 2) }}</span></td>
            </tr>
            @endif
            @if($service->no_of_units)
            <tr>
                <th>Number of Units</th>
                <td>{{ $service->no_of_units }}</td>
            </tr>
            @endif
            @if($service->employee && $service->employee->name)
            <tr>
                <th>Assigned Employee</th>
                <td>{{ $service->employee->name }}</td>
            </tr>
            @endif
            <tr>
                <th>Status</th>
                <td>{{ $service->status == 1 ? 'Active' : 'Inactive' }}</td>
            </tr>
            <tr>
                <th>Created At</th>
                <td>{{ date('d M Y, h:i A', strtotime($service->created_at)) }}</td>
            </tr>
        </table>

        @if($service->comments)
        <div style="background-color: #e2e3e5; border: 1px solid #d6d8db; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <strong>Comments:</strong><br>
            {{ $service->comments }}
        </div>
        @endif

        <p style="margin-top: 30px;">
            <strong>Client Details:</strong><br>
            {{ $service->client->business_name }}<br>
            @if($service->client->address)
                {{ $service->client->address }}<br>
            @endif
            @if($service->client->phone)
                Phone: {{ $service->client->phone }}<br>
            @endif
            @if($service->client->email)
                Email: {{ $service->client->email }}
            @endif
        </p>

        @if($service->serviceType)
        <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <strong>Service Type Details:</strong><br>
            Name: {{ $service->serviceType->name }}<br>
            @if($service->serviceType->description)
                Description: {{ $service->serviceType->description }}<br>
            @endif
            Category: {{ $service->serviceType->category ?? 'General' }}
        </div>
        @endif

        <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <strong>Next Steps:</strong><br>
            • Review service configuration<br>
            • Set up billing schedule if needed<br>
            • Coordinate with assigned employee<br>
            • Monitor service delivery<br>
            • Schedule first invoice generation
        </div>
    </div>

    <div class="footer">
        <p>This is an automated notification from {{ $company_name }}.<br>
        Please do not reply to this email.</p>
        <p>Generated on {{ date('d M Y, h:i A') }}</p>
    </div>
</body>
</html>
