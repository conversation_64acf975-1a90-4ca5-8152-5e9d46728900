<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Invoice Creation Alert</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 30px;
            border: 1px solid #dee2e6;
        }
        .footer {
            background-color: #6c757d;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 12px;
        }
        .alert-badge {
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .info-table th,
        .info-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .info-table th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .amount {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>📄 Invoice Creation Alert</h2>
        <span class="alert-badge">{{ $creation_type }}</span>
    </div>

    <div class="content">
        <p><strong>Hello,</strong></p>
        
        <p>A new invoice has been created in {{ $company_name }}. Here are the details:</p>

        <table class="info-table">
            <tr>
                <th>Invoice Code</th>
                <td><strong>{{ $invoice->invoice_code }}</strong></td>
            </tr>
            <tr>
                <th>Client</th>
                <td>{{ $invoice->client->business_name }}</td>
            </tr>
            <tr>
                <th>Invoice Date</th>
                <td>{{ date('d M Y', strtotime($invoice->invoice_date)) }}</td>
            </tr>
            <tr>
                <th>Total Amount</th>
                <td><span class="amount">₹{{ number_format($invoice->total_amount_due, 2) }}</span></td>
            </tr>
            <tr>
                <th>Status</th>
                <td>{{ $invoice->invoice_status }}</td>
            </tr>
            <tr>
                <th>Creation Type</th>
                <td>{{ $creation_type }}</td>
            </tr>
            <tr>
                <th>Created By</th>
                <td>{{ $invoice->created_by ?? 'System' }}</td>
            </tr>
            <tr>
                <th>Created At</th>
                <td>{{ date('d M Y, h:i A', strtotime($invoice->created_at)) }}</td>
            </tr>
        </table>

        @if($invoice->comments)
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <strong>Comments:</strong><br>
            {{ $invoice->comments }}
        </div>
        @endif

        <p style="margin-top: 30px;">
            <strong>Client Details:</strong><br>
            {{ $invoice->client->business_name }}<br>
            @if($invoice->client->address)
                {{ $invoice->client->address }}<br>
            @endif
            @if($invoice->client->phone)
                Phone: {{ $invoice->client->phone }}<br>
            @endif
            @if($invoice->client->email)
                Email: {{ $invoice->client->email }}
            @endif
        </p>
    </div>

    <div class="footer">
        <p>This is an automated notification from {{ $company_name }}.<br>
        Please do not reply to this email.</p>
        <p>Generated on {{ date('d M Y, h:i A') }}</p>
    </div>
</body>
</html>
