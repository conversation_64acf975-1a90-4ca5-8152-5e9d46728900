<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Client Creation Alert</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #17a2b8;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 30px;
            border: 1px solid #dee2e6;
        }
        .footer {
            background-color: #6c757d;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 12px;
        }
        .alert-badge {
            background-color: #ffc107;
            color: #212529;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .info-table th,
        .info-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .info-table th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .client-name {
            font-size: 18px;
            font-weight: bold;
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>👥 Client Creation Alert</h2>
        <span class="alert-badge">New Client</span>
    </div>

    <div class="content">
        <p><strong>Hello,</strong></p>
        
        <p>A new client has been added to {{ $company_name }}. Here are the details:</p>

        <table class="info-table">
            <tr>
                <th>Business Name</th>
                <td><span class="client-name">{{ $client->business_name }}</span></td>
            </tr>
            <tr>
                <th>Client Code</th>
                <td>{{ $client->client_code }}</td>
            </tr>
            <tr>
                <th>Contact Person</th>
                <td>{{ $client->name }}</td>
            </tr>
            @if($client->phone)
            <tr>
                <th>Phone</th>
                <td>{{ $client->phone }}</td>
            </tr>
            @endif
            @if($client->email)
            <tr>
                <th>Email</th>
                <td>{{ $client->email }}</td>
            </tr>
            @endif
            @if($client->address)
            <tr>
                <th>Address</th>
                <td>{{ $client->address }}</td>
            </tr>
            @endif
            @if($client->area)
            <tr>
                <th>Area</th>
                <td>{{ $client->area }}</td>
            </tr>
            @endif
            @if($client->city)
            <tr>
                <th>City</th>
                <td>{{ $client->city }}</td>
            </tr>
            @endif
            @if($client->district && $client->district->name)
            <tr>
                <th>District</th>
                <td>{{ $client->district->name }}</td>
            </tr>
            @endif
            @if($client->state && $client->state->name)
            <tr>
                <th>State</th>
                <td>{{ $client->state->name }}</td>
            </tr>
            @endif
            @if($client->pincode)
            <tr>
                <th>Pincode</th>
                <td>{{ $client->pincode }}</td>
            </tr>
            @endif
            @if($client->hcf_type)
            <tr>
                <th>HCF Type</th>
                <td>{{ $client->hcf_type }}</td>
            </tr>
            @endif
            <tr>
                <th>Status</th>
                <td>{{ $client->status == 1 ? 'Active' : 'Inactive' }}</td>
            </tr>
            <tr>
                <th>Created At</th>
                <td>{{ date('d M Y, h:i A', strtotime($client->created_at)) }}</td>
            </tr>
        </table>

        @if($client->gst_number)
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <strong>GST Number:</strong> {{ $client->gst_number }}
        </div>
        @endif

        <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <strong>Next Steps:</strong><br>
            • Review client information for accuracy<br>
            • Set up services for the client if needed<br>
            • Assign employee if required<br>
            • Configure billing preferences
        </div>
    </div>

    <div class="footer">
        <p>This is an automated notification from {{ $company_name }}.<br>
        Please do not reply to this email.</p>
        <p>Generated on {{ date('d M Y, h:i A') }}</p>
    </div>
</body>
</html>
