<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Employee Creation Alert</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #ffc107;
            color: #212529;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 30px;
            border: 1px solid #dee2e6;
        }
        .footer {
            background-color: #6c757d;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 12px;
        }
        .alert-badge {
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .info-table th,
        .info-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .info-table th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .employee-name {
            font-size: 18px;
            font-weight: bold;
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>👤 Employee Creation Alert</h2>
        <span class="alert-badge">New Employee</span>
    </div>

    <div class="content">
        <p><strong>Hello,</strong></p>
        
        <p>A new employee has been added to {{ $company_name }}. Here are the details:</p>

        <table class="info-table">
            <tr>
                <th>Employee Name</th>
                <td><span class="employee-name">{{ $employee->name }}</span></td>
            </tr>
            <tr>
                <th>Employee Code</th>
                <td>{{ $employee->employee_code ?? 'Not assigned' }}</td>
            </tr>
            @if($employee->email)
            <tr>
                <th>Email</th>
                <td>{{ $employee->email }}</td>
            </tr>
            @endif
            @if($employee->phone)
            <tr>
                <th>Phone</th>
                <td>{{ $employee->phone }}</td>
            </tr>
            @endif
            @if($employee->address)
            <tr>
                <th>Address</th>
                <td>{{ $employee->address }}</td>
            </tr>
            @endif
            @if($employee->designation)
            <tr>
                <th>Designation</th>
                <td>{{ $employee->designation }}</td>
            </tr>
            @endif
            @if($employee->department)
            <tr>
                <th>Department</th>
                <td>{{ $employee->department }}</td>
            </tr>
            @endif
            @if($employee->joining_date)
            <tr>
                <th>Joining Date</th>
                <td>{{ date('d M Y', strtotime($employee->joining_date)) }}</td>
            </tr>
            @endif
            @if($employee->salary)
            <tr>
                <th>Salary</th>
                <td>₹{{ number_format($employee->salary, 2) }}</td>
            </tr>
            @endif
            <tr>
                <th>Status</th>
                <td>{{ $employee->status == 1 ? 'Active' : 'Inactive' }}</td>
            </tr>
            <tr>
                <th>Created At</th>
                <td>{{ date('d M Y, h:i A', strtotime($employee->created_at)) }}</td>
            </tr>
        </table>

        @if($employee->user)
        <div style="background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <strong>User Account Created:</strong><br>
            Username: {{ $employee->user->name }}<br>
            Email: {{ $employee->user->email }}<br>
            Role: {{ $employee->user->role->name ?? 'Not assigned' }}
        </div>
        @endif

        @if($employee->emergency_contact)
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <strong>Emergency Contact:</strong><br>
            {{ $employee->emergency_contact }}
        </div>
        @endif

        <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <strong>Next Steps:</strong><br>
            • Complete employee onboarding process<br>
            • Assign clients if applicable<br>
            • Set up user permissions<br>
            • Provide system training<br>
            • Update organizational chart
        </div>
    </div>

    <div class="footer">
        <p>This is an automated notification from {{ $company_name }}.<br>
        Please do not reply to this email.</p>
        <p>Generated on {{ date('d M Y, h:i A') }}</p>
    </div>
</body>
</html>
