<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Payment Creation Alert</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #28a745;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 30px;
            border: 1px solid #dee2e6;
        }
        .footer {
            background-color: #6c757d;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 12px;
        }
        .alert-badge {
            background-color: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .info-table th,
        .info-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .info-table th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .amount {
            font-size: 18px;
            font-weight: bold;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>💰 Payment Creation Alert</h2>
        <span class="alert-badge">New Payment</span>
    </div>

    <div class="content">
        <p><strong>Hello,</strong></p>
        
        <p>A new payment has been recorded in {{ $company_name }}. Here are the details:</p>

        <table class="info-table">
            <tr>
                <th>Payment Amount</th>
                <td><span class="amount">₹{{ number_format($payment->amount, 2) }}</span></td>
            </tr>
            <tr>
                <th>Client</th>
                <td>{{ $payment->client->business_name }}</td>
            </tr>
            <tr>
                <th>Payment Date</th>
                <td>{{ date('d M Y', strtotime($payment->paid_on)) }}</td>
            </tr>
            <tr>
                <th>Payment Mode</th>
                <td>{{ $payment->payment_mode }}</td>
            </tr>
            @if($payment->ref_number)
            <tr>
                <th>Reference Number</th>
                <td>{{ $payment->ref_number }}</td>
            </tr>
            @endif
            @if($payment->invoice)
            <tr>
                <th>Invoice</th>
                <td>{{ $payment->invoice->invoice_code }}</td>
            </tr>
            @endif
            <tr>
                <th>Payment Status</th>
                <td>{{ $payment->payment_status }}</td>
            </tr>
            <tr>
                <th>Created By</th>
                <td>{{ $payment->created_by ?? 'System' }}</td>
            </tr>
            <tr>
                <th>Created At</th>
                <td>{{ date('d M Y, h:i A', strtotime($payment->created_at)) }}</td>
            </tr>
        </table>

        @if($payment->comments)
        <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <strong>Comments:</strong><br>
            {{ $payment->comments }}
        </div>
        @endif

        <p style="margin-top: 30px;">
            <strong>Client Details:</strong><br>
            {{ $payment->client->business_name }}<br>
            @if($payment->client->address)
                {{ $payment->client->address }}<br>
            @endif
            @if($payment->client->phone)
                Phone: {{ $payment->client->phone }}<br>
            @endif
            @if($payment->client->email)
                Email: {{ $payment->client->email }}
            @endif
        </p>

        @if($payment->invoice)
        <div style="background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <strong>Related Invoice:</strong><br>
            Invoice Code: {{ $payment->invoice->invoice_code }}<br>
            Invoice Amount: ₹{{ number_format($payment->invoice->total_amount_due, 2) }}<br>
            Remaining Amount: ₹{{ number_format($payment->invoice->unpaid_amount, 2) }}
        </div>
        @endif
    </div>

    <div class="footer">
        <p>This is an automated notification from {{ $company_name }}.<br>
        Please do not reply to this email.</p>
        <p>Generated on {{ date('d M Y, h:i A') }}</p>
    </div>
</body>
</html>
