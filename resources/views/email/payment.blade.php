<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Receipt</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 2px solid #f0f0f0;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 20px;
        }
        .content {
            padding: 30px 0;
        }
        .payment-details {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        .table th, .table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .table th {
            width: 40%;
        }
        .footer {
            text-align: center;
            padding: 20px 0;
            border-top: 2px solid #f0f0f0;
            font-size: 0.9em;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Payment Receipt</h2>
        </div>

        <div class="content">
            <p>Dear {{ $payment->client->business_name }},</p>

            <p>Thank you for your payment. Please find the receipt details below:</p>

            <div class="payment-details">
                <table class="table">
                    <tr>
                        <th>Payment Date:</th>
                        <td>{{ date('d-m-Y', strtotime($payment->paid_on)) }}</td>
                    </tr>
                    <tr>
                        <th>Reference Number:</th>
                        <td>{{ $payment->ref_number ? $payment->ref_number : 'NA' }}</td>
                    </tr>
                    <tr>
                        <th>Payment Mode:</th>
                        <td>{{ $payment->payment_mode }}</td>
                    </tr>
                    <tr>
                        <th>Amount:</th>
                        <td>₹{{ $payment->amount }}</td>
                    </tr>
                    <tr>
                        <th>Invoice Number:</th>
                        <td>{{ $payment->invoice->invoice_code }}</td>
                    </tr>
                </table>
            </div>

            <p>Please find the attached payment receipt for your records.</p>

            <p>If you have any questions about this payment, please don't hesitate to contact our support team.</p>

            <p>Best regards,<br>
            {{ config('company.details.legal_name') }} Team</p>
        </div>

        <div class="footer">
            <p>This is an automated message, please do not reply directly to this email.</p>
            <p>&copy; {{ date('Y') }} {{ config('company.details.legal_name') }}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
