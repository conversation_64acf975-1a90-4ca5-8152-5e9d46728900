@extends('layouts.master')
@section('title', 'Settings - Paidash')

@section('content')
<div class="dashboard-main-body">
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Company Settings</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Settings</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-body p-24">
            <ul class="w-100 nav border-gradient-tab nav-pills mb-20 d-inline-flex" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center px-24 active" id="profile-tab" data-bs-toggle="pill" data-bs-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="true">
                        <iconify-icon icon="solar:user-circle-outline" class="me-2"></iconify-icon>
                        Profile
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center px-24" id="app-config-tab" data-bs-toggle="pill" data-bs-target="#app-config" type="button" role="tab" aria-controls="app-config" aria-selected="false">
                        <iconify-icon icon="solar:settings-outline" class="me-2"></iconify-icon>
                        App Configurations
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center px-24" id="session-timeout-tab" data-bs-toggle="pill" data-bs-target="#session-timeout" type="button" role="tab" aria-controls="session-timeout" aria-selected="false">
                        <iconify-icon icon="solar:clock-circle-outline" class="me-2"></iconify-icon>
                        Session Timeout
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center px-24" id="certificate-config-tab" data-bs-toggle="pill" data-bs-target="#certificate-config" type="button" role="tab" aria-controls="certificate-config" aria-selected="false">
                        <iconify-icon icon="solar:document-text-outline" class="me-2"></iconify-icon>
                        Certificate Template
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center px-24" id="email-config-tab" data-bs-toggle="pill" data-bs-target="#email-config" type="button" role="tab" aria-controls="email-config" aria-selected="false">
                        <iconify-icon icon="solar:letter-outline" class="me-2"></iconify-icon>
                        Email Configuration
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center px-24" id="notification-alerts-tab" data-bs-toggle="pill" data-bs-target="#notification-alerts" type="button" role="tab" aria-controls="notification-alerts" aria-selected="false">
                        <iconify-icon icon="solar:bell-outline" class="me-2"></iconify-icon>
                        Notification Alerts
                    </button>
                </li>
                {{-- <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center px-24" id="invoice-config-tab" data-bs-toggle="pill" data-bs-target="#invoice-config" type="button" role="tab" aria-controls="invoice-config" aria-selected="false">
                        <iconify-icon icon="solar:document-outline" class="me-2"></iconify-icon>
                        Invoice Configuration
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center px-24" id="notifications-tab" data-bs-toggle="pill" data-bs-target="#notifications" type="button" role="tab" aria-controls="notifications" aria-selected="false">
                        <iconify-icon icon="solar:bell-outline" class="me-2"></iconify-icon>
                        Notification Center
                    </button>
                </li> --}}
            </ul>

            <div class="tab-content" id="settingsTabsContent">
                <!-- Profile Tab -->
                <div class="tab-pane fade show active" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                    <div class="row gy-4">
                        <div class="col-md-12">
                            <div class="card shadow-sm border-0 rounded-3">
                                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                    <iconify-icon icon="mdi:office-building" class="me-2 icon-size-20"></iconify-icon>
                                    Company Profile
                                </div>
                                <div class="card-body bg-light">
                                    <h6 class="mb-4 text-xl">Fill the required Details to add new client</h6>
                                    <div class="form-wizard">
                                        <form action="{{ route('settings.update') }}" method="POST" enctype="multipart/form-data">
                                            @csrf
                                            <fieldset class="wizard-fieldset show">
                                                <div class="row gy-3">
                                                    <div class="col-sm-12">
                                                        <label class="form-label">Client Logo</label>
                                                        <div class="position-relative">
                                                            <div class="upload-image-wrapper d-flex align-items-center gap-3">
                                                                <div
                                                                        class="uploaded-img position-relative h-120-px w-120-px border input-form-light radius-8 overflow-hidden border-dashed bg-neutral-50
                                                                    {{ $settings['logo'] ? '' : 'd-none' }}">
                                                                        <button type="button"
                                                                            class="uploaded-img__remove position-absolute top-0 end-0 z-1 text-2xxl line-height-1 me-8 mt-8 d-flex">
                                                                            <iconify-icon icon="radix-icons:cross-2"
                                                                                class="text-xl text-danger-600"></iconify-icon>
                                                                        </button>
                                                                        <img id="uploaded-img__preview" class="w-100 h-100 object-fit-cover"
                                                                            src="{{ $settings['logo'] ? asset('storage/' . $settings['logo']) : asset('assets/images/user.png') }}"
                                                                            alt="image">
                                                                    </div>

                                                                <label
                                                                    class="upload-file h-120-px w-120-px border input-form-light radius-8 overflow-hidden border-dashed bg-neutral-50 bg-hover-neutral-200 d-flex align-items-center flex-column justify-content-center gap-1"
                                                                    for="upload-file">
                                                                    <iconify-icon icon="solar:camera-outline"
                                                                        class="text-xl text-secondary-light"></iconify-icon>
                                                                    <span class="fw-semibold text-secondary-light">Upload</span>
                                                                    <input id="upload-file" type="file" hidden name="logo">
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">Company Registered Name</label>
                                                        <input type="text" name="company_name" class="form-control" value="{{ $settings['legal_name'] ?? '' }}" required>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">Company Brand Name</label>
                                                        <input type="text" name="brand_name" class="form-control" value="{{ $settings['brand_name'] ?? '' }}" required>
                                                    </div>



                                                    <div class="col-md-6">
                                                        <label class="form-label">Phone</label>
                                                        <input type="text" name="phone" class="form-control" value="{{ $settings['phone'] ?? '' }}"  required>
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">Email</label>
                                                        <input type="email" name="email" class="form-control"  value="{{ $settings['email'] ?? '' }}" required>
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">Contact Person Name</label>
                                                        <input type="text" name="contact_name" class="form-control">
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">Contact Person Phone</label>
                                                        <input type="text" name="contact_phone" class="form-control">
                                                    </div>
                                                    <div class="col-md-12">
                                                        <label class="form-label">Bank Details</label>
                                                        <textarea name="bank_details" rows="3" class="form-control" required>{!! $company_details['bank_details'] !!}</textarea>
                                                    </div>

                                                    <div class="col-md-12">
                                                        <label class="form-label">Address</label>
                                                        <textarea name="address" rows="3" class="form-control" required>{!! $company_details['address'] !!}</textarea>
                                                    </div>


                                                    <hr/>
                                                    <div class="col-md-12">
                                                    <h6 class="text-md text-neutral-500 mb-0">Business Info</h6>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">GST Number</label>
                                                        <input type="text" name="gst" value="{{ $settings['gst'] ?? '' }}" class="form-control">
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">Company Suffix</label>
                                                        <input type="text" name="company_suffix" class="form-control" value="{{ $settings['company_suffix'] ?? '' }}">
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">Website</label>
                                                        <input type="text" name="website" class="form-control" value="{{ $settings['website'] ?? '' }}">
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">CIN</label>
                                                        <input type="text" name="cin" class="form-control" value="{{ $settings['cin'] ?? '' }}">
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">Company Type</label>
                                                        <select name="company_type" class="form-select">
                                                            <option value="">Select Type</option>
                                                            <option value="Private Limited">Private Limited</option>
                                                            <option value="Public Limited">Public Limited</option>
                                                            <option value="LLP">LLP</option>
                                                            <option value="Partnership">Partnership</option>
                                                            <option value="Proprietorship">Proprietorship</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">PAN</label>
                                                        <input type="text" name="pan" class="form-control" value="{{ $settings['pan'] ?? '' }}">
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">UPI</label>
                                                        <input type="text" name="upi" class="form-control" value="{{ $settings['upi'] ?? '' }}">
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">Certificate Template</label>
                                                        <select name="certificate_template" class="form-select">
                                                            <option value="clients.certificate" {{ ($settings['certificate_template'] ?? 'clients.certificate') == 'clients.certificate' ? 'selected' : '' }}>
                                                                Template 1 (clients.certificate)
                                                            </option>
                                                            <option value="clients.certificate_v2" {{ ($settings['certificate_template'] ?? '') == 'clients.certificate_v2' ? 'selected' : '' }}>
                                                                Template 2 (clients.certificate_v2)
                                                            </option>
                                                        </select>
                                                        <small class="text-muted">Select which certificate template to use for PDF generation</small>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">Authorized Signature</label>
                                                        <div class="position-relative">
                                                            <div class="upload-image-wrapper d-flex align-items-center gap-3">
                                                                <div
                                                                    class="uploaded-auth-sign position-relative h-120-px w-120-px border input-form-light radius-8 overflow-hidden border-dashed bg-neutral-50
                                                                    {{ $settings['auth_sign'] ? '' : 'd-none' }}">
                                                                    <button type="button"
                                                                        class="uploaded-auth-sign__remove position-absolute top-0 end-0 z-1 text-2xxl line-height-1 me-8 mt-8 d-flex">
                                                                        <iconify-icon icon="radix-icons:cross-2" class="text-xl text-danger-600"></iconify-icon>
                                                                    </button>
                                                                    <img id="uploaded-auth-sign__preview" class="w-100 h-100 object-fit-cover"
                                                                        src="{{ $settings['auth_sign'] ? asset('storage/' . $settings['auth_sign']) : asset('assets/images/user.png') }}"
                                                                        alt="Authorized Signatory">
                                                                </div>

                                                                <label
                                                                    class="upload-file h-120-px w-120-px border input-form-light radius-8 overflow-hidden border-dashed bg-neutral-50 bg-hover-neutral-200 d-flex align-items-center flex-column justify-content-center gap-1"
                                                                    for="upload-auth-sign">
                                                                    <iconify-icon icon="solar:camera-outline" class="text-xl text-secondary-light"></iconify-icon>
                                                                    <span class="fw-semibold text-secondary-light">Upload</span>
                                                                    <input id="upload-auth-sign" type="file" hidden name="auth_sign">
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>





                                                    <div class="form-group d-flex align-items-center justify-content-end gap-8">
                                                        <a href="{{ route('clients.index') }}" class="btn btn-neutral-500 border-neutral-100 px-32">Cancel</a>
                                                        <button type="submit" class="form-wizard-next-btn btn btn-primary-600 px-32">Save</button>
                                                    </div>
                                                </div>
                                            </fieldset>

                                        </form>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- App Configurations Tab -->
                <div class="tab-pane fade" id="app-config" role="tabpanel" aria-labelledby="app-config-tab">
                    <div class="row gy-4">
                        <!-- State and District Configuration -->
                        <div class="col-md-12" hidden>
                            <div class="card shadow-sm border-0 rounded-3">
                                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                    <iconify-icon icon="mdi:map-marker-multiple" class="me-2 icon-size-20"></iconify-icon>
                                    State and District Configuration
                                </div>
                                <div class="card-body bg-light">
                                    <form action="#" method="POST">
                                        @csrf
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Select State</label>
                                                <select name="state" class="form-select" id="stateSelect">
                                                    <option value="">Select State</option>
                                                    <option value="maharashtra">Maharashtra</option>
                                                    <option value="karnataka">Karnataka</option>
                                                    <option value="tamil_nadu">Tamil Nadu</option>
                                                    <option value="kerala">Kerala</option>
                                                    <option value="gujarat">Gujarat</option>
                                                    <option value="delhi">Delhi</option>
                                                    <option value="west_bengal">West Bengal</option>
                                                    <option value="rajasthan">Rajasthan</option>
                                                    <option value="andhra_pradesh">Andhra Pradesh</option>
                                                    <option value="telangana">Telangana</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Select Districts</label>
                                                <select name="districts[]" class="form-select select2" id="districtSelect" multiple>
                                                    <option value="mumbai">Mumbai</option>
                                                    <option value="pune">Pune</option>
                                                    <option value="nagpur">Nagpur</option>
                                                    <option value="thane">Thane</option>
                                                    <option value="nashik">Nashik</option>
                                                    <option value="aurangabad">Aurangabad</option>
                                                    <option value="solapur">Solapur</option>
                                                    <option value="amravati">Amravati</option>
                                                    <option value="kolhapur">Kolhapur</option>
                                                    <option value="ratnagiri">Ratnagiri</option>
                                                </select>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Service Configuration -->
                        <div class="col-md-12">
                            <div class="card shadow-sm border-0 rounded-3">
                                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <iconify-icon icon="mdi:medical-bag" class="me-2 icon-size-20"></iconify-icon>
                                        Service Configuration
                                    </div>
                                    <button type="button" class="btn btn-sm btn-light d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#addServiceTypeModal">
                                        <iconify-icon icon="mdi:plus"></iconify-icon> Add Service Type
                                    </button>
                                </div>
                                <div class="card-body bg-light">
                                    <form action="#" method="POST">
                                        @csrf
                                        <div class="row">
                                            <!-- Service Types Section -->
                                            <div class="col-md-12 mb-4">
                                                <h6 class="mb-4 text-xl d-flex align-items-center">
                                                    <iconify-icon icon="mdi:format-list-bulleted-type" class="me-2"></iconify-icon>
                                                    Service Types
                                                </h6>
                                                <div class="row g-3">
                                                    @foreach ($service_types as $service_type)
                                                       <div class="col-md-4">
                                                        <div class="card h-100 border-0 shadow-sm hover-shadow">
                                                            <div class="card-body">
                                                                <div class="form-check d-flex align-items-center">
                                                                    <input class="form-check-input hover-scale service-type-status" type="checkbox" name="service_types[]" value="{{ $service_type->id }}" id="serviceType{{ $service_type->id }}" {{ ($service_type->status==1) ? 'checked' : '' }} data-id="{{ $service_type->id }}">
                                                                    <label class="form-check-label hover-text-primary cursor-pointer" for="serviceType{{ $service_type->id }}">{{ $service_type->name }}</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    @endforeach
                                                </div>
                                            </div>

                                            <!-- Separator -->
                                            <div class="col-md-12 px-4 my-24">
                                                <hr class="border-2">
                                            </div>

                                            <!-- Service Names Section -->
                                            <div class="col-md-12">
                                                <h6 class="mb-4 text-xl d-flex align-items-center">
                                                    <iconify-icon icon="mdi:format-list-checks" class="me-2"></iconify-icon>
                                                    Service Names
                                                </h6>
                                                <div class="row g-3">
                                                    @foreach ($services as $service)
                                                    @if($service->id==2)
                                                    <div class="col-md-4">
                                                        <div class="card h-100 border-0 shadow-sm hover-shadow">
                                                            <div class="card-body">
                                                                <div class="d-flex align-items-center justify-content-between mb-2">
                                                                    <div class="form-check d-flex align-items-center">
                                                                        <input class="form-check-input hover-scale service-status" type="checkbox" name="service_names[]" value="{{ $service->id }}" id="service{{ $service->id }}" {{ ($service->status==1) ? 'checked' : '' }} data-id="{{ $service->id }}">
                                                                        <label class="form-check-label hover-text-primary cursor-pointer d-flex align-items-center" for="service{{ $service->id }}">{{ $service->service_name }}</label>
                                                                    </div>
                                                                    <button type="button" class="btn btn-sm btn-link p-0" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ $service->custom_label }}">
                                                                        <iconify-icon icon="mdi:information-outline" class="text-primary"></iconify-icon>
                                                                    </button>
                                                                </div>
                                                                <div class="input-group">
                                                                        <input type="text" class="form-control form-control-sm" id="serviceLabel{{ $service->id }}" placeholder="Custom Label" value="{{ $service->user_label }}">
                                                                        <button type="button" class="btn btn-sm btn-primary update-service-label" data-id="{{ $service->id }}">
                                                                            <iconify-icon icon="mdi:content-save"></iconify-icon>
                                                                        </button>
                                                                    </div>

                                                                <!-- Non-Bedded Types Selection -->
                                                                <div id="nonBeddedTypes" class="mt-3 {{ ($service->status==1) ? '' : 'd-none-important' }}">
                                                                    <div class="card border-0 shadow-sm">
                                                                        <div class="card-header bg-white border-0 py-2">
                                                                            <div class="d-flex align-items-center justify-content-between">
                                                                                <h6 class="mb-0 fw-semibold">Non-Bedded Types</h6>
                                                                            </div>
                                                                        </div>
                                                                        <div class="card-body">
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" id="selectAllNonBedded">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="selectAllNonBedded">Select All</label>
                                                                            </div>
                                                                            <hr/>
                                                                            @foreach ($non_beded_types as $non_beded_type)
                                                                                <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale non-beded-type-status" type="checkbox" name="non_bedded_types[]" value="{{$non_beded_type->name}}" id="{{$non_beded_type->name}}"  {{ ($non_beded_type->status==1) ? 'checked' : '' }} data-id="{{$non_beded_type->id}}">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="{{$non_beded_type->name}}">{{$non_beded_type->name}}</label>
                                                                            </div>
                                                                            @endforeach
                                                                        </div>
                                                                        <div class="card-footer bg-white border-0 py-2">
                                                                            <button type="button" class="btn btn-sm btn-light d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#addNonBeddedTypeModal">
                                                                                <iconify-icon icon="mdi:plus"></iconify-icon> Add New Type
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    @else
                                                        <div class="col-md-4">
                                                            <div class="card h-100 border-0 shadow-sm hover-shadow">
                                                                <div class="card-body">
                                                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                                                        <div class="form-check d-flex align-items-center">
                                                                            <input class="form-check-input hover-scale service-status" type="checkbox" name="service_names[]" value="{{ $service->id }}" id="{{ $service->id }}" {{ ($service->status==1) ? 'checked' : '' }} data-id="{{ $service->id }}">
                                                                            <label class="form-check-label hover-text-primary cursor-pointer" for="{{ $service->id }}">{{ $service->service_name }}</label>
                                                                        </div>
                                                                        <button type="button" class="btn btn-sm btn-link p-0" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ $service->description }}">
                                                                            <iconify-icon icon="mdi:information-outline" class="text-primary"></iconify-icon>
                                                                        </button>
                                                                    </div>
                                                                    <div class="input-group">
                                                                        <input type="text" class="form-control form-control-sm" id="serviceLabel{{ $service->id }}" placeholder="Custom Label" value="{{ $service->user_label }}">
                                                                        <button type="button" class="btn btn-sm btn-primary update-service-label" data-id="{{ $service->id }}">
                                                                            <iconify-icon icon="mdi:content-save"></iconify-icon>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endif
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Certificate Configuration Tab -->
                <div class="tab-pane fade" id="certificate-config" role="tabpanel" aria-labelledby="certificate-config-tab">
                    <div class="row gy-4">
                        <div class="col-md-12">
                            <div class="card shadow-sm border-0 rounded-3">
                                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                    <iconify-icon icon="solar:document-text-outline" class="me-2 icon-size-20"></iconify-icon>
                                    Certificate Template Configuration
                                </div>
                                <div class="card-body bg-light">
                                    <form action="{{ route('settings.update') }}" method="POST">
                                        @csrf
                                        <div class="row">
                                            <!-- Template Selection -->
                                            <div class="col-md-12 mb-24">
                                                <div class="card border-0 shadow-sm hover-shadow">
                                                    <div class="card-body">
                                                        <div class="d-flex align-items-center mb-3">
                                                            <iconify-icon icon="solar:document-text-outline" class="me-2 text-primary icon-size-20"></iconify-icon>
                                                            <h6 class="mb-0 text-xl">Select Certificate Template</h6>
                                                        </div>
                                                        <p class="text-muted mb-4">Choose which template to use for generating client certificates</p>

                                                        <div class="row g-3">
                                                            <div class="col-md-6">
                                                                <div class="form-check border rounded p-3 {{ ($settings['certificate_template'] ?? 'clients.certificate') == 'clients.certificate' ? 'border-primary bg-primary-50' : 'border-neutral-200' }}">
                                                                    <input class="form-check-input" type="radio" name="certificate_template" value="clients.certificate" id="template1"
                                                                           {{ ($settings['certificate_template'] ?? 'clients.certificate') == 'clients.certificate' ? 'checked' : '' }}>
                                                                    <label class="form-check-label w-100" for="template1">
                                                                        <div class="d-flex align-items-center">
                                                                            <iconify-icon icon="solar:document-outline" class="me-2 text-primary icon-size-24"></iconify-icon>
                                                                            <div>
                                                                                <h6 class="mb-1">Template 1 (Classic)</h6>
                                                                                <small class="text-muted">clients.certificate</small>
                                                                            </div>
                                                                        </div>
                                                                    </label>
                                                                </div>
                                                            </div>

                                                            <div class="col-md-6">
                                                                <div class="form-check border rounded p-3 {{ ($settings['certificate_template'] ?? '') == 'clients.certificate_v2' ? 'border-primary bg-primary-50' : 'border-neutral-200' }}">
                                                                    <input class="form-check-input" type="radio" name="certificate_template" value="clients.certificate_v2" id="template2"
                                                                           {{ ($settings['certificate_template'] ?? '') == 'clients.certificate_v2' ? 'checked' : '' }}>
                                                                    <label class="form-check-label w-100" for="template2">
                                                                        <div class="d-flex align-items-center">
                                                                            <iconify-icon icon="solar:document-text-outline" class="me-2 text-success icon-size-24"></iconify-icon>
                                                                            <div>
                                                                                <h6 class="mb-1">Template 2 (Modern)</h6>
                                                                                <small class="text-muted">clients.certificate_v2</small>
                                                                            </div>
                                                                        </div>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="mt-4">
                                                            <div class="alert alert-info d-flex align-items-center">
                                                                <iconify-icon icon="solar:info-circle-outline" class="me-2 icon-size-20"></iconify-icon>
                                                                <div>
                                                                    <strong>Note:</strong> This setting will affect all future certificate downloads.
                                                                    You can preview templates by viewing a sample certificate from any client.
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="mt-3">
                                                            <div class="d-flex gap-2 flex-wrap">
                                                                <a href="/clients/1/certificate" target="_blank" class="btn btn-outline-primary btn-sm d-flex align-items-center">
                                                                    <iconify-icon icon="solar:eye-outline" class="me-1"></iconify-icon>
                                                                    Preview Certificate
                                                                </a>
                                                                <a href="/clients/download/1" class="btn btn-outline-success btn-sm d-flex align-items-center">
                                                                    <iconify-icon icon="solar:download-outline" class="me-1"></iconify-icon>
                                                                    Download Sample PDF
                                                                </a>
                                                            </div>
                                                            <small class="text-muted d-block mt-2 d-flex align-items-center">
                                                                <iconify-icon icon="solar:info-circle-outline" class="me-1"></iconify-icon>
                                                                Preview uses the first client in your database for demonstration
                                                            </small>
                                                        </div>

                                                        <div class="d-flex justify-content-end mt-4">
                                                            <button type="submit" class="btn btn-primary-600 px-32">
                                                                <iconify-icon icon="solar:check-circle-outline" class="me-1"></iconify-icon>
                                                                Save Template Settings
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Email Configuration Tab -->
                <div class="tab-pane fade" id="email-config" role="tabpanel" aria-labelledby="email-config-tab">
                    <div class="row gy-4">
                        <div class="col-md-12">
                            <div class="card shadow-sm border-0 rounded-3">
                                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                    <iconify-icon icon="solar:letter-outline" class="me-2 icon-size-20"></iconify-icon>
                                    Email Configuration
                                </div>
                                <div class="card-body p-4">
                                    <form action="{{ route('settings.update-email-config') }}" method="POST">
                                        @csrf

                                        <!-- Email Environment Setting -->
                                        <div class="row mb-4">
                                            <div class="col-md-12">
                                                <label for="email_environment" class="form-label fw-semibold">
                                                    <iconify-icon icon="solar:settings-outline" class="me-1"></iconify-icon>
                                                    Email Environment
                                                </label>
                                                <select class="form-select" id="email_environment" name="email_environment" required>
                                                    <option value="live" {{ ($settings['email_environment'] ?? 'live') == 'live' ? 'selected' : '' }}>
                                                        Live Environment (Use Client Emails)
                                                    </option>
                                                    <option value="test" {{ ($settings['email_environment'] ?? 'live') == 'test' ? 'selected' : '' }}>
                                                        Test Environment (Use Test Email)
                                                    </option>
                                                </select>
                                                <div class="form-text">
                                                    <iconify-icon icon="material-symbols:info" class="me-1"></iconify-icon>
                                                    <strong>Live:</strong> Emails will be sent to actual client email addresses.<br>
                                                    <strong>Test:</strong> All emails will be sent to the configured test email address below.
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Test Email Configuration -->
                                        <div class="row mb-4">
                                            <div class="col-md-12">
                                                <label for="tester_mailid" class="form-label fw-semibold">
                                                    <iconify-icon icon="solar:letter-outline" class="me-1"></iconify-icon>
                                                    Test Email Address
                                                </label>
                                                <input type="email" class="form-control" id="tester_mailid" name="tester_mailid"
                                                       value="{{ $settings['tester_mailid'] ?? '' }}"
                                                       placeholder="<EMAIL>">
                                                <div class="form-text">
                                                    <iconify-icon icon="material-symbols:info" class="me-1"></iconify-icon>
                                                    This email address will receive all emails when in test environment.
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Current Configuration Display -->
                                        <div class="row mb-4">
                                            <div class="col-md-12">
                                                <div class="alert alert-info d-flex align-items-start gap-3">
                                                    <iconify-icon icon="material-symbols:info" class="text-xl mt-1"></iconify-icon>
                                                    <div>
                                                        <h6 class="mb-2">Current Email Configuration</h6>
                                                        <div id="currentEmailConfig">
                                                            @if(($settings['email_environment'] ?? 'live') == 'live')
                                                                <div class="d-flex align-items-center gap-2 mb-2">
                                                                    <iconify-icon icon="material-symbols:check-circle" class="text-success"></iconify-icon>
                                                                    <strong>Live Environment Active</strong>
                                                                </div>
                                                                <div>Emails are being sent to actual client email addresses.</div>
                                                            @else
                                                                <div class="d-flex align-items-center gap-2 mb-2">
                                                                    <iconify-icon icon="material-symbols:science" class="text-info"></iconify-icon>
                                                                    <strong>Test Environment Active</strong>
                                                                </div>
                                                                <div>All emails are being sent to: <strong>{{ $settings['tester_mailid'] ?? 'Not configured' }}</strong></div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-end">
                                            <button type="submit" class="btn btn-primary px-4">
                                                <iconify-icon icon="solar:diskette-outline" class="me-1"></iconify-icon>
                                                Save Email Configuration
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notification Alerts Tab -->
                <div class="tab-pane fade" id="notification-alerts" role="tabpanel" aria-labelledby="notification-alerts-tab">
                    <div class="row gy-4">
                        <div class="col-md-12">
                            <div class="card shadow-sm border-0 rounded-3">
                                <div class="card-header bg-warning-400 text-white fw-bold d-flex align-items-center">
                                    <iconify-icon icon="solar:bell-outline" class="me-2 icon-size-20"></iconify-icon>
                                    Notification Alerts Configuration
                                </div>
                                <div class="card-body p-4">
                                    <form action="{{ route('settings.update-notification-alerts') }}" method="POST">
                                        @csrf

                                        <!-- Invoice Creation Alerts -->
                                        <div class="mb-5">
                                            <div class="card border border-primary-200">
                                                <div class="card-header bg-primary-50">
                                                    <h6 class="mb-0 fw-semibold d-flex align-items-center">
                                                        <iconify-icon icon="solar:document-text-outline" class="me-2 text-primary"></iconify-icon>
                                                        Invoice Creation Alerts
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-12 mb-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="invoice_alert_enabled" name="invoice_alert_enabled"
                                                                       {{ ($settings['invoice_alert_enabled'] ?? '0') == '1' ? 'checked' : '' }}>
                                                                <label class="form-check-label fw-semibold" for="invoice_alert_enabled">
                                                                    Enable Invoice Creation Alerts
                                                                </label>
                                                            </div>
                                                            <small class="text-muted">Send email notifications when invoices are created</small>
                                                        </div>

                                                        <div class="col-md-12 mb-3">
                                                            <label for="invoice_alert_emails" class="form-label fw-semibold">
                                                                <iconify-icon icon="material-symbols:email" class="me-1"></iconify-icon>
                                                                Alert Email Addresses
                                                            </label>
                                                            <input type="text" class="form-control" id="invoice_alert_emails" name="invoice_alert_emails"
                                                                   value="{{ $settings['invoice_alert_emails'] ?? '' }}"
                                                                   placeholder="<EMAIL>, <EMAIL>">
                                                            <small class="text-muted">Enter multiple email addresses separated by commas</small>
                                                        </div>

                                                        <div class="col-md-6 mb-3">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="invoice_alert_manual_creation" name="invoice_alert_manual_creation"
                                                                       {{ ($settings['invoice_alert_manual_creation'] ?? '0') == '1' ? 'checked' : '' }}>
                                                                <label class="form-check-label" for="invoice_alert_manual_creation">
                                                                    <strong>Manual Invoice Creation</strong>
                                                                </label>
                                                                <div><small class="text-muted">Alert when invoices are created manually</small></div>
                                                            </div>
                                                        </div>

                                                        <div class="col-md-6 mb-3">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="invoice_alert_auto_creation" name="invoice_alert_auto_creation"
                                                                       {{ ($settings['invoice_alert_auto_creation'] ?? '0') == '1' ? 'checked' : '' }}>
                                                                <label class="form-check-label" for="invoice_alert_auto_creation">
                                                                    <strong>Auto Invoice Generation</strong>
                                                                </label>
                                                                <div><small class="text-muted">Alert when invoices are auto-generated</small></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Payment Creation Alerts -->
                                        <div class="mb-5">
                                            <div class="card border border-success-200">
                                                <div class="card-header bg-success-50">
                                                    <h6 class="mb-0 fw-semibold d-flex align-items-center">
                                                        <iconify-icon icon="hugeicons:money-send-square" class="me-2 text-success"></iconify-icon>
                                                        Payment Creation Alerts
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-12 mb-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="payment_alert_enabled" name="payment_alert_enabled"
                                                                       {{ ($settings['payment_alert_enabled'] ?? '0') == '1' ? 'checked' : '' }}>
                                                                <label class="form-check-label fw-semibold" for="payment_alert_enabled">
                                                                    Enable Payment Creation Alerts
                                                                </label>
                                                            </div>
                                                            <small class="text-muted">Send email notifications when payments are recorded</small>
                                                        </div>

                                                        <div class="col-md-12 mb-3">
                                                            <label for="payment_alert_emails" class="form-label fw-semibold">
                                                                <iconify-icon icon="material-symbols:email" class="me-1"></iconify-icon>
                                                                Alert Email Addresses
                                                            </label>
                                                            <input type="text" class="form-control" id="payment_alert_emails" name="payment_alert_emails"
                                                                   value="{{ $settings['payment_alert_emails'] ?? '' }}"
                                                                   placeholder="<EMAIL>, <EMAIL>">
                                                            <small class="text-muted">Enter multiple email addresses separated by commas</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Client Creation Alerts -->
                                        <div class="mb-5">
                                            <div class="card border border-info-200">
                                                <div class="card-header bg-info-50">
                                                    <h6 class="mb-0 fw-semibold d-flex align-items-center">
                                                        <iconify-icon icon="solar:users-group-rounded-outline" class="me-2 text-info"></iconify-icon>
                                                        Client Creation Alerts
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-12 mb-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="client_alert_enabled" name="client_alert_enabled"
                                                                       {{ ($settings['client_alert_enabled'] ?? '0') == '1' ? 'checked' : '' }}>
                                                                <label class="form-check-label fw-semibold" for="client_alert_enabled">
                                                                    Enable Client Creation Alerts
                                                                </label>
                                                            </div>
                                                            <small class="text-muted">Send email notifications when new clients are added</small>
                                                        </div>

                                                        <div class="col-md-12 mb-3">
                                                            <label for="client_alert_emails" class="form-label fw-semibold">
                                                                <iconify-icon icon="material-symbols:email" class="me-1"></iconify-icon>
                                                                Alert Email Addresses
                                                            </label>
                                                            <input type="text" class="form-control" id="client_alert_emails" name="client_alert_emails"
                                                                   value="{{ $settings['client_alert_emails'] ?? '' }}"
                                                                   placeholder="<EMAIL>, <EMAIL>">
                                                            <small class="text-muted">Enter multiple email addresses separated by commas</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Employee Creation Alerts -->
                                        <div class="mb-5">
                                            <div class="card border border-warning-200">
                                                <div class="card-header bg-warning-50">
                                                    <h6 class="mb-0 fw-semibold d-flex align-items-center">
                                                        <iconify-icon icon="solar:user-plus-outline" class="me-2 text-warning"></iconify-icon>
                                                        Employee Creation Alerts
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-12 mb-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="employee_alert_enabled" name="employee_alert_enabled"
                                                                       {{ ($settings['employee_alert_enabled'] ?? '0') == '1' ? 'checked' : '' }}>
                                                                <label class="form-check-label fw-semibold" for="employee_alert_enabled">
                                                                    Enable Employee Creation Alerts
                                                                </label>
                                                            </div>
                                                            <small class="text-muted">Send email notifications when new employees are added</small>
                                                        </div>

                                                        <div class="col-md-12 mb-3">
                                                            <label for="employee_alert_emails" class="form-label fw-semibold">
                                                                <iconify-icon icon="material-symbols:email" class="me-1"></iconify-icon>
                                                                Alert Email Addresses
                                                            </label>
                                                            <input type="text" class="form-control" id="employee_alert_emails" name="employee_alert_emails"
                                                                   value="{{ $settings['employee_alert_emails'] ?? '' }}"
                                                                   placeholder="<EMAIL>, <EMAIL>">
                                                            <small class="text-muted">Enter multiple email addresses separated by commas</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Service Creation Alerts -->
                                        <div class="mb-5">
                                            <div class="card border border-purple-200">
                                                <div class="card-header bg-purple-50">
                                                    <h6 class="mb-0 fw-semibold d-flex align-items-center">
                                                        <iconify-icon icon="solar:settings-outline" class="me-2 text-purple"></iconify-icon>
                                                        Service Creation Alerts
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-12 mb-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="service_alert_enabled" name="service_alert_enabled"
                                                                       {{ ($settings['service_alert_enabled'] ?? '0') == '1' ? 'checked' : '' }}>
                                                                <label class="form-check-label fw-semibold" for="service_alert_enabled">
                                                                    Enable Service Creation Alerts
                                                                </label>
                                                            </div>
                                                            <small class="text-muted">Send email notifications when new services are created</small>
                                                        </div>

                                                        <div class="col-md-12 mb-3">
                                                            <label for="service_alert_emails" class="form-label fw-semibold">
                                                                <iconify-icon icon="material-symbols:email" class="me-1"></iconify-icon>
                                                                Alert Email Addresses
                                                            </label>
                                                            <input type="text" class="form-control" id="service_alert_emails" name="service_alert_emails"
                                                                   value="{{ $settings['service_alert_emails'] ?? '' }}"
                                                                   placeholder="<EMAIL>, <EMAIL>">
                                                            <small class="text-muted">Enter multiple email addresses separated by commas</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Daily Report Alerts -->
                                        <div class="mb-5">
                                            <div class="card border border-secondary-200">
                                                <div class="card-header bg-secondary-50">
                                                    <h6 class="mb-0 fw-semibold d-flex align-items-center">
                                                        <iconify-icon icon="solar:chart-outline" class="me-2 text-secondary"></iconify-icon>
                                                        Daily Report Alerts
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-12 mb-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" id="daily_report_alert_enabled" name="daily_report_alert_enabled"
                                                                       {{ ($settings['daily_report_alert_enabled'] ?? '0') == '1' ? 'checked' : '' }}>
                                                                <label class="form-check-label fw-semibold" for="daily_report_alert_enabled">
                                                                    Enable Daily Report Alerts
                                                                </label>
                                                            </div>
                                                            <small class="text-muted">Send daily summary reports via email</small>
                                                        </div>

                                                        <div class="col-md-8 mb-3">
                                                            <label for="daily_report_alert_emails" class="form-label fw-semibold">
                                                                <iconify-icon icon="material-symbols:email" class="me-1"></iconify-icon>
                                                                Alert Email Addresses
                                                            </label>
                                                            <input type="text" class="form-control" id="daily_report_alert_emails" name="daily_report_alert_emails"
                                                                   value="{{ $settings['daily_report_alert_emails'] ?? '' }}"
                                                                   placeholder="<EMAIL>, <EMAIL>">
                                                            <small class="text-muted">Enter multiple email addresses separated by commas</small>
                                                        </div>

                                                        <div class="col-md-4 mb-3">
                                                            <label for="daily_report_time" class="form-label fw-semibold">
                                                                <iconify-icon icon="material-symbols:schedule" class="me-1"></iconify-icon>
                                                                Report Time
                                                            </label>
                                                            <input type="time" class="form-control" id="daily_report_time" name="daily_report_time"
                                                                   value="{{ $settings['daily_report_time'] ?? '08:00' }}">
                                                            <small class="text-muted">Daily report sending time</small>
                                                        </div>

                                                        <!-- Manual Report Generation -->
                                                        <div class="col-md-12">
                                                            <div class="border-top pt-3">
                                                                <h6 class="fw-bold mb-3 d-flex align-items-center">
                                                                    <iconify-icon icon="solar:play-outline" class="me-2 text-primary"></iconify-icon>
                                                                    Manual Report Generation
                                                                </h6>
                                                                <div class="row">
                                                                    <div class="col-md-6 mb-3">
                                                                        <label for="manual_report_date" class="form-label fw-semibold">Report Date</label>
                                                                        <input type="date" class="form-control" id="manual_report_date"
                                                                               value="{{ \Carbon\Carbon::yesterday()->format('Y-m-d') }}">
                                                                        <small class="text-muted">Select date for report generation</small>
                                                                    </div>
                                                                    <div class="col-md-6 mb-3 d-flex align-items-end">
                                                                        <div class="btn-group w-100" role="group">
                                                                            <button type="button" class="btn btn-outline-info" id="previewReportBtn">
                                                                                <iconify-icon icon="solar:eye-outline" class="me-1"></iconify-icon>
                                                                                Preview
                                                                            </button>
                                                                            <button type="button" class="btn btn-info" id="generateReportBtn">
                                                                                <iconify-icon icon="solar:send-outline" class="me-1"></iconify-icon>
                                                                                Generate & Send
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <!-- Report Preview Container -->
                                                                <div id="reportPreviewContainer" class="mt-3" style="display: none;">
                                                                    <div class="alert alert-light border">
                                                                        <h6 class="fw-bold mb-2 d-flex align-items-center">
                                                                            <iconify-icon icon="solar:chart-outline" class="me-2"></iconify-icon>
                                                                            Report Preview
                                                                        </h6>
                                                                        <div id="reportPreviewContent"></div>
                                                                    </div>
                                                                </div>

                                                                <!-- Info Alert -->
                                                                <div class="alert alert-success d-flex align-items-center mt-3">
                                                                    <iconify-icon icon="material-symbols:check-circle" class="me-2"></iconify-icon>
                                                                    <div>
                                                                        <strong>Daily Report System Active!</strong> You can now generate and send comprehensive daily business reports.
                                                                        Reports include invoice statistics, payment summaries, client growth, and outstanding amounts.
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-end">
                                            <button type="submit" class="btn btn-warning px-4">
                                                <iconify-icon icon="solar:diskette-outline" class="me-1"></iconify-icon>
                                                Save Notification Settings
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Configuration Tab -->
                <div class="tab-pane fade" id="invoice-config" role="tabpanel" aria-labelledby="invoice-config-tab">
                    <div class="row gy-4">
                        <div class="col-md-12">
                            <div class="card shadow-sm border-0 rounded-3">
                                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                    <iconify-icon icon="mdi:file-document-edit" class="me-2 icon-size-20"></iconify-icon>
                                    Invoice Template Configuration
                                </div>
                                <div class="card-body bg-light">
                                    <form action="#" method="POST" enctype="multipart/form-data">
                                        @csrf
                                        <div class="row">
                                            <!-- Default Template Toggle -->
                                            <div class="col-md-12 mb-24">
                                                <div class="card border-0 shadow-sm hover-shadow">
                                                    <div class="card-body">
                                                        <div class="d-flex align-items-center justify-content-between">
                                                            <div class="d-flex align-items-center">
                                                                <iconify-icon icon="mdi:file-document-check" class="me-2 text-primary icon-size-20"></iconify-icon>
                                                                <h6 class="mb-4 text-xl">Use Default Template</h6>
                                                            </div>
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input hover-scale" type="checkbox" id="useDefaultTemplate" name="use_default_template" checked>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Header Configuration -->
                                            <div class="col-md-12 mb-24">
                                                <div class="card border-0 shadow-sm hover-shadow">
                                                    <div class="card-header bg-white border-0">
                                                        <h6 class="mb-4 text-xl">
                                                            <iconify-icon icon="mdi:file-document-edit" class="me-2 text-primary"></iconify-icon>
                                                            Invoice Header Configuration
                                                        </h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="row g-3">
                                                            <div class="col-md-12">
                                                                <div class="d-flex gap-4 mb-3">
                                                                    <div class="form-check d-flex align-items-center">
                                                                        <input class="form-check-input hover-scale" type="radio" name="header_type" value="image" id="headerImage">
                                                                        <label class="form-check-label hover-text-primary cursor-pointer d-flex align-items-center" for="headerImage">
                                                                            <iconify-icon icon="mdi:image" class="me-1"></iconify-icon>
                                                                            Image
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check d-flex align-items-center">
                                                                        <input class="form-check-input hover-scale" type="radio" name="header_type" value="html" id="headerHTML">
                                                                        <label class="form-check-label hover-text-primary cursor-pointer d-flex align-items-center" for="headerHTML">
                                                                            <iconify-icon icon="mdi:code-tags" class="me-1"></iconify-icon>
                                                                            HTML
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <div id="headerImageUpload" class="mb-3 d-none-important">
                                                                    <label class="form-label d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:upload" class="me-2 text-primary"></iconify-icon>
                                                                        Upload Header Image
                                                                    </label>
                                                                    <input type="file" class="form-control" name="header_image" accept="image/*">
                                                                    <small class="text-muted mt-1 d-block">Recommended size: 800x200px, Max file size: 2MB</small>
                                                                </div>
                                                                <div id="headerHTMLInput" class="mb-3 d-none-important">
                                                                    <label class="form-label d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:code-tags" class="me-2 text-primary"></iconify-icon>
                                                                        HTML Code
                                                                    </label>
                                                                    <textarea class="form-control" name="header_html" rows="4" placeholder="Enter HTML code for header"></textarea>
                                                                    <small class="text-muted mt-1 d-block">Use HTML tags to customize your header</small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Footer Configuration -->
                                            <div class="col-md-12">
                                                <div class="card border-0 shadow-sm hover-shadow">
                                                    <div class="card-header bg-white border-0">
                                                        <h6 class="mb-0 fw-semibold d-flex align-items-center">
                                                            <iconify-icon icon="mdi:file-document-edit" class="me-2 text-primary"></iconify-icon>
                                                            Invoice Footer Configuration
                                                        </h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="row g-3">
                                                            <div class="col-md-12">
                                                                <div class="d-flex gap-4 mb-3">
                                                                    <div class="form-check d-flex align-items-center">
                                                                        <input class="form-check-input hover-scale" type="radio" name="footer_type" value="image" id="footerImage">
                                                                        <label class="form-check-label hover-text-primary cursor-pointer d-flex align-items-center" for="footerImage">
                                                                            <iconify-icon icon="mdi:image" class="me-1"></iconify-icon>
                                                                            Image
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check d-flex align-items-center">
                                                                        <input class="form-check-input hover-scale" type="radio" name="footer_type" value="html" id="footerHTML">
                                                                        <label class="form-check-label hover-text-primary cursor-pointer d-flex align-items-center" for="footerHTML">
                                                                            <iconify-icon icon="mdi:code-tags" class="me-1"></iconify-icon>
                                                                            HTML
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <div id="footerImageUpload" class="mb-3 d-none-important">
                                                                    <label class="form-label d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:upload" class="me-2 text-primary"></iconify-icon>
                                                                        Upload Footer Image
                                                                    </label>
                                                                    <input type="file" class="form-control" name="footer_image" accept="image/*">
                                                                    <small class="text-muted mt-1 d-block">Recommended size: 800x200px, Max file size: 2MB</small>
                                                                </div>
                                                                <div id="footerHTMLInput" class="mb-3 d-none-important">
                                                                <label class="form-label d-flex align-items-center d-flex align-items-center">
                                                                    <iconify-icon icon="mdi:code-tags" class="me-2 text-primary"></iconify-icon>
                                                                    HTML Code
                                                                </label>
                                                                <textarea class="form-control" name="footer_html" rows="4" placeholder="Enter HTML code for footer"></textarea>
                                                                <small class="text-muted mt-1 d-block">Use HTML tags to customize your footer</small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notification Center Tab -->
                <div class="tab-pane fade" id="notifications" role="tabpanel" aria-labelledby="notifications-tab">
                    <div class="row gy-4">
                        <div class="col-md-12">
                            <div class="card shadow-sm border-0 rounded-3">
                                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                    <iconify-icon icon="mdi:bell-outline" class="me-2 icon-size-20"></iconify-icon>
                                    Notification Preferences
                                </div>
                                <div class="card-body bg-light">
                                    <h6 class="mb-4 text-xl">Notification Settings</h6>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="card">
                                                <div class="card-body">
                                                    <form action="#" method="POST">
                                                        @csrf
                                                        <div class="row">
                                                            <div class="col-md-12 mb-4">
                                                                <h6 class="mb-3">Email Notifications</h6>
                                                                <div class="form-check form-switch mb-3">
                                                                    <input class="form-check-input" type="checkbox" id="invoiceNotifications" checked>
                                                                    <label class="form-check-label" for="invoiceNotifications">Invoice Notifications</label>
                                                                </div>
                                                                <div class="form-check form-switch mb-3">
                                                                    <input class="form-check-input" type="checkbox" id="paymentNotifications" checked>
                                                                    <label class="form-check-label" for="paymentNotifications">Payment Notifications</label>
                                                                </div>
                                                                <div class="form-check form-switch mb-3">
                                                                    <input class="form-check-input" type="checkbox" id="systemNotifications" checked>
                                                                    <label class="form-check-label" for="systemNotifications">System Notifications</label>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12 mb-4">
                                                                <h6 class="mb-3">Push Notifications</h6>
                                                                <div class="form-check form-switch mb-3">
                                                                    <input class="form-check-input" type="checkbox" id="mobileNotifications" checked>
                                                                    <label class="form-check-label" for="mobileNotifications">Mobile Notifications</label>
                                                                </div>
                                                                <div class="form-check form-switch mb-3">
                                                                    <input class="form-check-input" type="checkbox" id="desktopNotifications" checked>
                                                                    <label class="form-check-label" for="desktopNotifications">Desktop Notifications</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form-group d-flex align-items-center justify-content-end gap-8">
                                                            <button type="submit" class="btn btn-primary-600 px-32">Save Changes</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Session Timeout Tab -->
                <div class="tab-pane fade" id="session-timeout" role="tabpanel" aria-labelledby="session-timeout-tab">
                    <div class="row gy-4">
                        <div class="col-md-12">
                            <div class="card shadow-sm border-0 rounded-3">
                                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                    <iconify-icon icon="solar:clock-circle-outline" class="me-2 icon-size-20"></iconify-icon>
                                    Session Timeout Configuration
                                </div>
                                <div class="card-body bg-light">
                                    <h6 class="mb-4 text-xl">Configure automatic session timeout settings for enhanced security</h6>
                                    <form action="{{ route('settings.updateSessionTimeout') }}" method="POST">
                                        @csrf
                                        <div class="row gy-3">
                                            <!-- Enable/Disable Session Timeout -->
                                            <div class="col-md-12">
                                                <div class="form-switch switch-success d-flex align-items-center gap-3 mb-3">
                                                    <input class="form-check-input" type="checkbox" role="switch" id="sessionTimeoutEnabled"
                                                           name="session_timeout_enabled" value="1"
                                                           {{ ($settings['session_timeout_enabled'] ?? '1') == '1' ? 'checked' : '' }}>
                                                    <div>
                                                        <label class="form-check-label fw-semibold mb-0" for="sessionTimeoutEnabled">
                                                            {{ ($settings['session_timeout_enabled'] ?? '1') == '1' ? 'Enabled' : 'Disabled' }}
                                                        </label>
                                                        <small class="form-text text-muted d-block">
                                                            When enabled, users will be automatically logged out after a period of inactivity
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Session Lifetime -->
                                            <div class="col-md-6">
                                                <label class="form-label fw-semibold">Session Lifetime (Minutes)</label>
                                                <input type="number" name="session_lifetime_minutes" class="form-control"
                                                       value="{{ $settings['session_lifetime_minutes'] ?? '120' }}"
                                                       min="5" max="1440" required>
                                                <small class="form-text text-muted">
                                                    How long a session remains active (5-1440 minutes). Default: 120 minutes (2 hours)
                                                </small>
                                            </div>

                                            <!-- Warning Time -->
                                            <div class="col-md-6">
                                                <label class="form-label fw-semibold">Warning Time (Minutes)</label>
                                                <input type="number" name="session_warning_minutes" class="form-control"
                                                       value="{{ $settings['session_warning_minutes'] ?? '5' }}"
                                                       min="1" max="30" required>
                                                <small class="form-text text-muted">
                                                    Minutes before expiry to show warning dialog. Default: 5 minutes
                                                </small>
                                            </div>

                                            <!-- Check Interval -->
                                            <div class="col-md-6">
                                                <label class="form-label fw-semibold">Check Interval (Seconds)</label>
                                                <input type="number" name="session_check_interval_seconds" class="form-control"
                                                       value="{{ $settings['session_check_interval_seconds'] ?? '60' }}"
                                                       min="30" max="300" required>
                                                <small class="form-text text-muted">
                                                    How often to check session status (30-300 seconds). Default: 60 seconds
                                                </small>
                                            </div>

                                            <!-- Heartbeat Interval -->
                                            <div class="col-md-6">
                                                <label class="form-label fw-semibold">Heartbeat Interval (Minutes)</label>
                                                <input type="number" name="session_heartbeat_interval_minutes" class="form-control"
                                                       value="{{ $settings['session_heartbeat_interval_minutes'] ?? '5' }}"
                                                       min="1" max="30" required>
                                                <small class="form-text text-muted">
                                                    How often to send heartbeat when user is active. Default: 5 minutes
                                                </small>
                                            </div>

                                            <!-- Expire on Close -->
                                            <div class="col-md-12">
                                                <div class="form-switch switch-success d-flex align-items-center gap-3 mb-3">
                                                    <input class="form-check-input" type="checkbox" role="switch" id="sessionExpireOnClose"
                                                           name="session_expire_on_close" value="1"
                                                           {{ ($settings['session_expire_on_close'] ?? '0') == '1' ? 'checked' : '' }}>
                                                    <div>
                                                        <label class="form-check-label fw-semibold mb-0" for="sessionExpireOnClose">
                                                            {{ ($settings['session_expire_on_close'] ?? '0') == '1' ? 'Enabled' : 'Disabled' }}
                                                        </label>
                                                        <small class="form-text text-muted d-block">
                                                            When enabled, sessions will expire immediately when the browser is closed
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Info Box -->
                                            <div class="col-md-12">
                                                <div class="alert alert-info d-flex align-items-start">
                                                    <iconify-icon icon="solar:info-circle-outline" class="me-2 mt-1 text-info" style="font-size: 20px;"></iconify-icon>
                                                    <div>
                                                        <strong>How Session Timeout Works:</strong>
                                                        <ul class="mb-0 mt-2">
                                                            <li>Users receive a warning dialog before their session expires</li>
                                                            <li>They can choose to "Stay Logged In" or "Logout Now"</li>
                                                            <li>Active users (moving mouse, typing, etc.) get automatic heartbeat to keep session alive</li>
                                                            <li>Inactive users are automatically logged out for security</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Save Button -->
                                            <div class="col-md-12">
                                                <div class="form-group d-flex align-items-center justify-content-end gap-8">
                                                    <button type="submit" class="btn btn-primary-600 px-32 d-flex align-items-center">
                                                        <iconify-icon icon="solar:diskette-outline" class="me-1"></iconify-icon>
                                                        Save Session Settings
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Service Type Modal -->
<div class="modal fade" id="addServiceTypeModal" tabindex="-1" aria-labelledby="addServiceTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addServiceTypeModalLabel">Add New Service Type</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addServiceTypeForm">
                    @csrf
                    <div class="mb-3">
                        <label for="newServiceType" class="form-label">Service Type Name</label>
                        <input type="text" class="form-control" id="newServiceType" name="name" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="saveServiceType">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Non-Bedded Type Modal -->
<div class="modal fade" id="addNonBeddedTypeModal" tabindex="-1" aria-labelledby="addNonBeddedTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addNonBeddedTypeModalLabel">Add New Non-Bedded Type</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addNonBeddedTypeForm">
                    @csrf
                    <div class="mb-3">
                        <label for="newNonBeddedType" class="form-label">Type Name</label>
                        <input type="text" class="form-control" id="newNonBeddedType" name="name" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="saveNonBeddedType">Save</button>
            </div>
        </div>
    </div>
</div>

@stop

@section('script')
<script>
    const fileInput = document.getElementById("upload-file");
    const imagePreview = document.getElementById("uploaded-img__preview");
    const uploadedImgContainer = document.querySelector(".uploaded-img");
    const removeButton = document.querySelector(".uploaded-img__remove");

    fileInput.addEventListener("change", (e) => {
        if (e.target.files.length) {
            const src = URL.createObjectURL(e.target.files[0]);
            imagePreview.src = src;
            uploadedImgContainer.classList.remove('d-none');
        }
    });
    removeButton.addEventListener("click", () => {
        imagePreview.src = "";
        uploadedImgContainer.classList.add('d-none');
        fileInput.value = "";
    });
    // =============================== Upload Single Image js End here ================================================

    const authSignInput = document.getElementById("upload-auth-sign");
    const authSignPreview = document.getElementById("uploaded-auth-sign__preview");
    const uploadedAuthSignContainer = document.querySelector(".uploaded-auth-sign");
    const removeAuthSignButton = document.querySelector(".uploaded-auth-sign__remove");

    authSignInput.addEventListener("change", (e) => {
        if (e.target.files.length) {
            const src = URL.createObjectURL(e.target.files[0]);
            authSignPreview.src = src;
            uploadedAuthSignContainer.classList.remove('d-none');
        }
    });

    removeAuthSignButton.addEventListener("click", () => {
        authSignPreview.src = "";
        uploadedAuthSignContainer.classList.add('d-none');
        authSignInput.value = "";
    });

    // Initialize Bootstrap tabs
    var settingsTabs = new bootstrap.Tab(document.querySelector('#profile-tab'));

    // Initialize Select2 for districts
    $('.select2').select2({
        placeholder: "Select Districts",
        allowClear: true,
        width: '100%'
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Invoice Template Configuration
    $('input[name="header_type"]').change(function() {
        if ($(this).val() === 'image') {
            $('#headerImageUpload').slideDown();
            $('#headerHTMLInput').slideUp();
        } else {
            $('#headerImageUpload').slideUp();
            $('#headerHTMLInput').slideDown();
        }
    });

    $('input[name="footer_type"]').change(function() {
        if ($(this).val() === 'image') {
            $('#footerImageUpload').slideDown();
            $('#footerHTMLInput').slideUp();
        } else {
            $('#footerImageUpload').slideUp();
            $('#footerHTMLInput').slideDown();
        }
    });

    $('#useDefaultTemplate').change(function() {
        if ($(this).is(':checked')) {
            $('input[name="header_type"], input[name="footer_type"]').prop('disabled', true);
            $('#headerImageUpload, #headerHTMLInput, #footerImageUpload, #footerHTMLInput').slideUp();
        } else {
            $('input[name="header_type"], input[name="footer_type"]').prop('disabled', false);
        }
    });

    // Add New Service Type
    $('#saveServiceType').click(function() {
        // Get form data
        var name = $('#newServiceType').val();

        // Validate form
        if (!name) {
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: 'Service Type Name is required',
                confirmButtonColor: '#d33'
            });
            return;
        }

        // Show loading state
        $(this).prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...');

        // Send AJAX request
        $.ajax({
            url: "{{ route('service-types.store') }}",
            type: "POST",
            data: {
                name: name,
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                if (response.success) {
                    // Reset form
                    $('#newServiceType').val('');

                    // Close modal
                    $('#addServiceTypeModal').modal('hide');

                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: response.message,
                        confirmButtonColor: '#3085d6'
                    }).then((result) => {
                        // Refresh the page to show the updated service types
                        location.reload();
                    });
                }
            },
            error: function(xhr) {
                // Reset button state
                $('#saveServiceType').prop('disabled', false).html('Save');

                var errors = xhr.responseJSON.errors;
                var errorMessage = '';

                $.each(errors, function(key, value) {
                    errorMessage += value + '<br>';
                });

                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    html: errorMessage,
                    confirmButtonColor: '#d33'
                });
            },
            complete: function() {
                // Reset button state
                $('#saveServiceType').prop('disabled', false).html('Save');
            }
        });
    });

    //add non bedded type
    $('#saveNonBeddedType').click(function() {
        // Get form data
        var name = $('#newNonBeddedType').val();

        // Validate form
        if (!name) {
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: 'Non-Bedded Type Name is required',
                confirmButtonColor: '#d33'
            });
            return;
        }

        // Show loading state
        $(this).prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...');

        // Send AJAX request
        $.ajax({
            url: "{{ route('non-bedded-types.store') }}",
            type: "POST",
            data: {
                name: name,
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                if (response.success) {
                    // Reset form
                    $('#newNonBeddedType').val('');

                    // Close modal
                    $('#addNonBeddedTypeModal').modal('hide');

                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: response.message,
                        confirmButtonColor: '#3085d6'
                    }).then((result) => {
                        // Refresh the page to show the updated non bedded types
                        location.reload();
                    });
                }
            },
            error: function(xhr) {
                // Reset button state
                $('#saveNonBeddedType').prop('disabled', false).html('Save');

                var errors = xhr.responseJSON.errors;
                var errorMessage = '';

                $.each(errors, function(key, value) {
                    errorMessage += value + '<br>';
                });

                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    html: errorMessage,
                    confirmButtonColor: '#d33'
                });
            },
            complete: function() {
                // Reset button state
                $('#saveNonBeddedType').prop('disabled', false).html('Save');
            }
        });
    });
    //non bedded type status change
    $(document).on('change', '.non-beded-type-status', function() {
        var nonBeddedTypeId = $(this).data('id');
        var status = $(this).is(':checked') ? 1 : 0;
        var checkboxElement = $(this);

        // Disable checkbox during the request
        checkboxElement.prop('disabled', true);

        $.ajax({
            url: "{{ route('non-bedded-types.update-status') }}",
            type: "POST",
            data: {
                id: nonBeddedTypeId,
                status: status,
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: response.message,
                        confirmButtonColor: '#3085d6',
                        timer: 2000,
                        timerProgressBar: true
                    });
                } else {
                    // If update failed, revert the checkbox
                    checkboxElement.prop('checked', !status);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: response.message,
                        confirmButtonColor: '#d33'
                    });
                }
            },
            error: function(xhr) {
                // If request failed, revert the checkbox
                checkboxElement.prop('checked', !status);

                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Failed to update non bedded type status.',
                    confirmButtonColor: '#d33'
                });
            },
            complete: function() {
                // Re-enable checkbox
                checkboxElement.prop('disabled', false);
            }
        });
    });

    // Add hover effects for checkboxes
    $('.hover-scale').hover(
        function() {
            $(this).addClass('scale-110');
        },
        function() {
            $(this).removeClass('scale-110');
        }
    );

    // Non-Bedded Type Selection
    $('#nonBedded').change(function() {
        if ($(this).is(':checked')) {
            $('#nonBeddedTypes').slideDown();
        } else {
            $('#nonBeddedTypes').slideUp();
        }
    });

    // Select All Non-Bedded Types
    $('#selectAllNonBedded').change(function() {
        $('input[name="non_bedded_types[]"]').prop('checked', $(this).is(':checked'));
    });

    // Add New Non-Bedded Type
    $('#saveNonBeddedType').click(function() {
        var newType = $('#newNonBeddedType').val();
        if (newType) {
            var newCheckbox = `
                <div class="form-check">
                    <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="${newType.toLowerCase().replace(/\s+/g, '_')}" id="${newType.toLowerCase().replace(/\s+/g, '_')}">
                    <label class="form-check-label hover-text-primary cursor-pointer" for="${newType.toLowerCase().replace(/\s+/g, '_')}">${newType}</label>
                </div>
            `;
            $('.col-md-4').first().append(newCheckbox);
            $('#addNonBeddedTypeModal').modal('hide');
            $('#newNonBeddedType').val('');
        }
    });

    // Service Type Status Toggle
    $(document).on('change', '.service-type-status', function() {
        var serviceTypeId = $(this).data('id');
        var status = $(this).is(':checked') ? 1 : 0;
        var checkboxElement = $(this);

        // Disable checkbox during the request
        checkboxElement.prop('disabled', true);

        $.ajax({
            url: "{{ route('service-types.update-status') }}",
            type: "POST",
            data: {
                id: serviceTypeId,
                status: status,
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: response.message,
                        confirmButtonColor: '#3085d6',
                        timer: 2000,
                        timerProgressBar: true
                    });
                } else {
                    // If update failed, revert the checkbox
                    checkboxElement.prop('checked', !status);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: response.message,
                        confirmButtonColor: '#d33'
                    });
                }
            },
            error: function(xhr) {
                // If request failed, revert the checkbox
                checkboxElement.prop('checked', !status);

                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Failed to update service type status.',
                    confirmButtonColor: '#d33'
                });
            },
            complete: function() {
                // Re-enable checkbox
                checkboxElement.prop('disabled', false);
            }
        });
    });

    // Service Status Toggle with Non-Bedded Types visibility
    $(document).on('change', '.service-status', function() {
        var serviceId = $(this).data('id');
        var status = $(this).is(':checked') ? 1 : 0;
        var checkboxElement = $(this);
        var nonBeddedTypesSection = $(this).closest('.card-body').find('#nonBeddedTypes');

        // Toggle Non-Bedded Types section visibility
        if (serviceId == 2) { // Assuming service ID 2 is for Non-Bedded
            if (status) {
                nonBeddedTypesSection.slideDown();
            } else {
                nonBeddedTypesSection.slideUp();
            }
        }

        // Disable checkbox during the request
        checkboxElement.prop('disabled', true);

        $.ajax({
            url: "{{ route('services.update-status') }}",
            type: "POST",
            data: {
                id: serviceId,
                status: status,
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: response.message,
                        confirmButtonColor: '#3085d6',
                        timer: 2000,
                        timerProgressBar: true
                    });
                } else {
                    // If update failed, revert the checkbox and visibility
                    checkboxElement.prop('checked', !status);
                    if (serviceId == 2) {
                        if (!status) {
                            nonBeddedTypesSection.slideDown();
                        } else {
                            nonBeddedTypesSection.slideUp();
                        }
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: response.message,
                        confirmButtonColor: '#d33'
                    });
                }
            },
            error: function(xhr) {
                // If request failed, revert the checkbox and visibility
                checkboxElement.prop('checked', !status);
                if (serviceId == 2) {
                    if (!status) {
                        nonBeddedTypesSection.slideDown();
                    } else {
                        nonBeddedTypesSection.slideUp();
                    }
                }

                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Failed to update service status.',
                    confirmButtonColor: '#d33'
                });
            },
            complete: function() {
                // Re-enable checkbox
                checkboxElement.prop('disabled', false);
            }
        });
    });

    // Service Label Update
    $(document).on('click', '.update-service-label', function() {
        var serviceId = $(this).data('id');
        var label = $('#serviceLabel' + serviceId).val();
        var buttonElement = $(this);

        // Disable button during the request
        buttonElement.prop('disabled', true);
        buttonElement.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>');

        $.ajax({
            url: "{{ route('services.update-label') }}",
            type: "POST",
            data: {
                id: serviceId,
                label: label,
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: response.message,
                        confirmButtonColor: '#3085d6',
                        timer: 2000,
                        timerProgressBar: true
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: response.message,
                        confirmButtonColor: '#d33'
                    });
                }
            },
            error: function(xhr) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Failed to update service label.',
                    confirmButtonColor: '#d33'
                });
            },
            complete: function() {
                // Reset button state
                buttonElement.prop('disabled', false);
                buttonElement.html('<iconify-icon icon="mdi:content-save"></iconify-icon>');
            }
        });
    });

    // Session Timeout Switch Handlers
    $("#sessionTimeoutEnabled").on("change", function() {
        let isChecked = $(this).prop("checked");
        let labelElement = $(this).next("div").find("label");

        if (isChecked) {
            labelElement.text("Enabled");
        } else {
            labelElement.text("Disabled");
        }
    });

    $("#sessionExpireOnClose").on("change", function() {
        let isChecked = $(this).prop("checked");
        let labelElement = $(this).next("div").find("label");

        if (isChecked) {
            labelElement.text("Enabled");
        } else {
            labelElement.text("Disabled");
        }
    });

    // Certificate Template Selection
    $('input[name="certificate_template"]').change(function() {
        // Remove active styling from all template cards
        $('.form-check').removeClass('border-primary bg-primary-50').addClass('border-neutral-200');

        // Add active styling to selected template card
        $(this).closest('.form-check').removeClass('border-neutral-200').addClass('border-primary bg-primary-50');

        // Show success message
        if ($(this).val() === 'clients.certificate') {
            toastr.info('Template 1 (Classic) selected. Save to apply changes.');
        } else {
            toastr.info('Template 2 (Modern) selected. Save to apply changes.');
        }
    });

    // Email Configuration Dynamic Updates
    $('#email_environment').change(function() {
        const environment = $(this).val();
        const testerEmail = $('#tester_mailid').val();

        let configHtml = '';
        let alertClass = 'alert-info';

        if (environment === 'live') {
            configHtml = `
                <div class="d-flex align-items-center gap-2 mb-2">
                    <iconify-icon icon="material-symbols:check-circle" class="text-success"></iconify-icon>
                    <strong>Live Environment Active</strong>
                </div>
                <div>Emails are being sent to actual client email addresses.</div>
            `;
            alertClass = 'alert-success';
        } else {
            if (testerEmail) {
                configHtml = `
                    <div class="d-flex align-items-center gap-2 mb-2">
                        <iconify-icon icon="material-symbols:science" class="text-info"></iconify-icon>
                        <strong>Test Environment Active</strong>
                    </div>
                    <div>All emails are being sent to: <strong>${testerEmail}</strong></div>
                `;
                alertClass = 'alert-info';
            } else {
                configHtml = `
                    <div class="d-flex align-items-center gap-2 mb-2">
                        <iconify-icon icon="material-symbols:warning" class="text-warning"></iconify-icon>
                        <strong>Test Environment - No Test Email</strong>
                    </div>
                    <div>Please configure a test email address below.</div>
                `;
                alertClass = 'alert-warning';
            }
        }

        $('#currentEmailConfig').html(configHtml);
        $('.alert').removeClass('alert-info alert-success alert-warning').addClass(alertClass);
    });

    // Update configuration display when test email changes
    $('#tester_mailid').on('input', function() {
        if ($('#email_environment').val() === 'test') {
            $('#email_environment').trigger('change');
        }
    });

    // Daily Report Functionality
    $('#previewReportBtn').click(function() {
        const reportDate = $('#manual_report_date').val();
        const btn = $(this);
        const originalText = btn.html();

        btn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1"></span>Loading...');

        $.ajax({
            url: '{{ route("settings.preview-daily-report") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                report_date: reportDate
            },
            success: function(response) {
                if (response.success) {
                    displayReportPreview(response.report_data, response.report_date);
                    $('#reportPreviewContainer').slideDown();
                } else {
                    toastr.error(response.message || 'Failed to generate report preview');
                }
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON?.message || 'Failed to generate report preview';
                toastr.error(errorMsg);
            },
            complete: function() {
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    $('#generateReportBtn').click(function() {
        const reportDate = $('#manual_report_date').val();
        const btn = $(this);
        const originalText = btn.html();

        if (!confirm('Are you sure you want to generate and send the daily report for ' + reportDate + '?')) {
            return;
        }

        btn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1"></span>Sending...');

        // Create a form and submit it
        const form = $('<form>', {
            method: 'POST',
            action: '{{ route("settings.generate-daily-report") }}'
        });

        form.append($('<input>', {
            type: 'hidden',
            name: '_token',
            value: '{{ csrf_token() }}'
        }));

        form.append($('<input>', {
            type: 'hidden',
            name: 'report_date',
            value: reportDate
        }));

        $('body').append(form);
        form.submit();
    });

    function displayReportPreview(reportData, reportDate) {
        const formatCurrency = (amount) => '₹' + parseFloat(amount).toLocaleString('en-IN', {minimumFractionDigits: 2});

        const html = `
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="card bg-success-50 border-success">
                        <div class="card-body text-center">
                            <h6 class="text-success mb-1">Total Revenue</h6>
                            <h5 class="text-success mb-0">${formatCurrency(reportData.summary.total_revenue)}</h5>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-info-50 border-info">
                        <div class="card-body text-center">
                            <h6 class="text-info mb-1">Collections</h6>
                            <h5 class="text-info mb-0">${formatCurrency(reportData.summary.total_collections)}</h5>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-warning-50 border-warning">
                        <div class="card-body text-center">
                            <h6 class="text-warning mb-1">Expenses</h6>
                            <h5 class="text-warning mb-0">${formatCurrency(reportData.summary.total_expenses)}</h5>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card ${reportData.summary.net_profit >= 0 ? 'bg-success-50 border-success' : 'bg-danger-50 border-danger'}">
                        <div class="card-body text-center">
                            <h6 class="${reportData.summary.net_profit >= 0 ? 'text-success' : 'text-danger'} mb-1">Net Profit</h6>
                            <h5 class="${reportData.summary.net_profit >= 0 ? 'text-success' : 'text-danger'} mb-0">${formatCurrency(reportData.summary.net_profit)}</h5>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-4">
                    <h6 class="fw-bold">📄 Invoices</h6>
                    <ul class="list-unstyled">
                        <li>Total: ${reportData.invoices.total_count} (${formatCurrency(reportData.invoices.total_amount)})</li>
                        <li>Paid: ${reportData.invoices.paid_count}</li>
                        <li>Pending: ${reportData.invoices.pending_count}</li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h6 class="fw-bold">💰 Payments</h6>
                    <ul class="list-unstyled">
                        <li>Total: ${reportData.payments.total_count} (${formatCurrency(reportData.payments.total_amount)})</li>
                        <li>Cash: ${reportData.payments.cash_count}</li>
                        <li>Online: ${reportData.payments.online_count}</li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h6 class="fw-bold">💸 Expenses</h6>
                    <ul class="list-unstyled">
                        <li>Total: ${reportData.expenses.total_count} (${formatCurrency(reportData.expenses.total_amount)})</li>
                        <li>Pending: ${reportData.expenses.pending_count}</li>
                        <li>Approved: ${reportData.expenses.approved_count}</li>
                    </ul>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <h6 class="fw-bold">📈 Growth Metrics</h6>
                    <ul class="list-unstyled">
                        <li>New Clients: ${reportData.clients.new_clients_count}</li>
                        <li>New Services: ${reportData.services.new_services_count}</li>
                        <li>New Employees: ${reportData.employees.new_employees_count}</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="fw-bold">📊 Financial Metrics</h6>
                    <ul class="list-unstyled">
                        <li>Profit Margin: ${parseFloat(reportData.summary.profit_margin || 0).toFixed(1)}%</li>
                        <li>Expense Ratio: ${parseFloat(reportData.summary.expense_ratio || 0).toFixed(1)}%</li>
                        <li>Outstanding: ${formatCurrency(reportData.summary.net_outstanding)}</li>
                    </ul>
                </div>
            </div>
        `;

        $('#reportPreviewContent').html(html);
    }

</script>
@stop
