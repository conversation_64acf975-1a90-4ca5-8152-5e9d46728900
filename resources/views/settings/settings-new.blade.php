@extends('layouts.master')
@section('title', 'Settings - Paidash')

@section('content')
<div class="dashboard-main-body">
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Company Settings</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Settings</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-body p-24">
            <ul class="w-100 nav border-gradient-tab nav-pills mb-20 d-inline-flex" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center px-24 active" id="profile-tab" data-bs-toggle="pill" data-bs-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="true">
                        <iconify-icon icon="solar:user-circle-outline" class="me-2"></iconify-icon>
                        Profile
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center px-24" id="app-config-tab" data-bs-toggle="pill" data-bs-target="#app-config" type="button" role="tab" aria-controls="app-config" aria-selected="false">
                        <iconify-icon icon="solar:settings-outline" class="me-2"></iconify-icon>
                        App Configurations
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center px-24" id="invoice-config-tab" data-bs-toggle="pill" data-bs-target="#invoice-config" type="button" role="tab" aria-controls="invoice-config" aria-selected="false">
                        <iconify-icon icon="solar:document-outline" class="me-2"></iconify-icon>
                        Invoice Configuration
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center px-24" id="notifications-tab" data-bs-toggle="pill" data-bs-target="#notifications" type="button" role="tab" aria-controls="notifications" aria-selected="false">
                        <iconify-icon icon="solar:bell-outline" class="me-2"></iconify-icon>
                        Notification Center
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="settingsTabsContent">
                <!-- Profile Tab -->
                <div class="tab-pane fade show active" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                    <div class="row gy-4">
                        <div class="col-md-12">
                            <div class="card shadow-sm border-0 rounded-3">
                                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                    <iconify-icon icon="mdi:office-building" class="me-2 icon-size-20"></iconify-icon>
                                    Company Profile
                                </div>
                                <div class="card-body bg-light">
                                    <h6 class="mb-4 text-xl">Fill the required Details to add new client</h6>
                                    <div class="form-wizard">
                                        <form action="{{ route('settings.update') }}" method="POST" enctype="multipart/form-data">
                                            @csrf
                                            <fieldset class="wizard-fieldset show">
                                                <div class="row gy-3">
                                                    <div class="col-sm-12">
                                                        <label class="form-label">Client Logo</label>
                                                        <div class="position-relative">
                                                            <div class="upload-image-wrapper d-flex align-items-center gap-3">
                                                                <div
                                                                        class="uploaded-img position-relative h-120-px w-120-px border input-form-light radius-8 overflow-hidden border-dashed bg-neutral-50
                                                                    {{ $settings['logo'] ? '' : 'd-none' }}">
                                                                        <button type="button"
                                                                            class="uploaded-img__remove position-absolute top-0 end-0 z-1 text-2xxl line-height-1 me-8 mt-8 d-flex">
                                                                            <iconify-icon icon="radix-icons:cross-2"
                                                                                class="text-xl text-danger-600"></iconify-icon>
                                                                        </button>
                                                                        <img id="uploaded-img__preview" class="w-100 h-100 object-fit-cover"
                                                                            src="{{ $settings['logo'] ? asset('storage/' . $settings['logo']) : asset('assets/images/user.png') }}"
                                                                            alt="image">
                                                                    </div>

                                                                <label
                                                                    class="upload-file h-120-px w-120-px border input-form-light radius-8 overflow-hidden border-dashed bg-neutral-50 bg-hover-neutral-200 d-flex align-items-center flex-column justify-content-center gap-1"
                                                                    for="upload-file">
                                                                    <iconify-icon icon="solar:camera-outline"
                                                                        class="text-xl text-secondary-light"></iconify-icon>
                                                                    <span class="fw-semibold text-secondary-light">Upload</span>
                                                                    <input id="upload-file" type="file" hidden name="logo">
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">Company Registered Name</label>
                                                        <input type="text" name="company_name" class="form-control" value="{{ $settings['legal_name'] ?? '' }}" required>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">Company Brand Name</label>
                                                        <input type="text" name="brand_name" class="form-control" value="{{ $settings['brand_name'] ?? '' }}" required>
                                                    </div>



                                                    <div class="col-md-6">
                                                        <label class="form-label">Phone</label>
                                                        <input type="text" name="phone" class="form-control" value="{{ $settings['phone'] ?? '' }}"  required>
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">Email</label>
                                                        <input type="email" name="email" class="form-control"  value="{{ $settings['email'] ?? '' }}" required>
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">Contact Person Name</label>
                                                        <input type="text" name="contact_name" class="form-control">
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">Contact Person Phone</label>
                                                        <input type="text" name="contact_phone" class="form-control">
                                                    </div>
                                                    <div class="col-md-12">
                                                        <label class="form-label">Bank Details</label>
                                                        <textarea name="bank_details" rows="3" class="form-control" required>{!! $company_details['bank_details'] !!}</textarea>
                                                    </div>

                                                    <div class="col-md-12">
                                                        <label class="form-label">Address</label>
                                                        <textarea name="address" rows="3" class="form-control" required>{!! $company_details['address'] !!}</textarea>
                                                    </div>


                                                    <hr/>
                                                    <div class="col-md-12">
                                                    <h6 class="text-md text-neutral-500 mb-0">Business Info</h6>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">GST Number</label>
                                                        <input type="text" name="gst" value="{{ $settings['gst'] ?? '' }}" class="form-control">
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">Invoice Suffix</label>
                                                        <input type="text" name="invoice_suffix" class="form-control" value="{{ $settings['invoice_suffix'] ?? '' }}">
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">Website</label>
                                                        <input type="text" name="website" class="form-control" value="{{ $settings['website'] ?? '' }}">
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">CIN</label>
                                                        <input type="text" name="cin" class="form-control" value="{{ $settings['cin'] ?? '' }}">
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">Company Type</label>
                                                        <select name="company_type" class="form-select">
                                                            <option value="">Select Type</option>
                                                            <option value="Private Limited">Private Limited</option>
                                                            <option value="Public Limited">Public Limited</option>
                                                            <option value="LLP">LLP</option>
                                                            <option value="Partnership">Partnership</option>
                                                            <option value="Proprietorship">Proprietorship</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">PAN</label>
                                                        <input type="text" name="pan" class="form-control" value="{{ $settings['pan'] ?? '' }}">
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">Authorized Signature</label>
                                                        <div class="position-relative">
                                                            <div class="upload-image-wrapper d-flex align-items-center gap-3">
                                                                <div
                                                                    class="uploaded-auth-sign position-relative h-120-px w-120-px border input-form-light radius-8 overflow-hidden border-dashed bg-neutral-50
                                                                    {{ $settings['auth_sign'] ? '' : 'd-none' }}">
                                                                    <button type="button"
                                                                        class="uploaded-auth-sign__remove position-absolute top-0 end-0 z-1 text-2xxl line-height-1 me-8 mt-8 d-flex">
                                                                        <iconify-icon icon="radix-icons:cross-2" class="text-xl text-danger-600"></iconify-icon>
                                                                    </button>
                                                                    <img id="uploaded-auth-sign__preview" class="w-100 h-100 object-fit-cover"
                                                                        src="{{ $settings['auth_sign'] ? asset('storage/' . $settings['auth_sign']) : asset('assets/images/user.png') }}"
                                                                        alt="Authorized Signatory">
                                                                </div>

                                                                <label
                                                                    class="upload-file h-120-px w-120-px border input-form-light radius-8 overflow-hidden border-dashed bg-neutral-50 bg-hover-neutral-200 d-flex align-items-center flex-column justify-content-center gap-1"
                                                                    for="upload-auth-sign">
                                                                    <iconify-icon icon="solar:camera-outline" class="text-xl text-secondary-light"></iconify-icon>
                                                                    <span class="fw-semibold text-secondary-light">Upload</span>
                                                                    <input id="upload-auth-sign" type="file" hidden name="auth_sign">
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>





                                                    <div class="form-group d-flex align-items-center justify-content-end gap-8">
                                                        <a href="{{ route('clients.index') }}" class="btn btn-neutral-500 border-neutral-100 px-32">Cancel</a>
                                                        <button type="submit" class="form-wizard-next-btn btn btn-primary-600 px-32">Save</button>
                                                    </div>
                                                </div>
                                            </fieldset>

                                        </form>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- App Configurations Tab -->
                <div class="tab-pane fade" id="app-config" role="tabpanel" aria-labelledby="app-config-tab">
                    <div class="row gy-4">
                        <!-- State and District Configuration -->
                        <div class="col-md-12">
                            <div class="card shadow-sm border-0 rounded-3">
                                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                    <iconify-icon icon="mdi:map-marker-multiple" class="me-2 icon-size-20"></iconify-icon>
                                    State and District Configuration
                                </div>
                                <div class="card-body bg-light">
                                    <form action="#" method="POST">
                                        @csrf
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Select State</label>
                                                <select name="state" class="form-select" id="stateSelect">
                                                    <option value="">Select State</option>
                                                    <option value="maharashtra">Maharashtra</option>
                                                    <option value="karnataka">Karnataka</option>
                                                    <option value="tamil_nadu">Tamil Nadu</option>
                                                    <option value="kerala">Kerala</option>
                                                    <option value="gujarat">Gujarat</option>
                                                    <option value="delhi">Delhi</option>
                                                    <option value="west_bengal">West Bengal</option>
                                                    <option value="rajasthan">Rajasthan</option>
                                                    <option value="andhra_pradesh">Andhra Pradesh</option>
                                                    <option value="telangana">Telangana</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Select Districts</label>
                                                <select name="districts[]" class="form-select select2" id="districtSelect" multiple>
                                                    <option value="mumbai">Mumbai</option>
                                                    <option value="pune">Pune</option>
                                                    <option value="nagpur">Nagpur</option>
                                                    <option value="thane">Thane</option>
                                                    <option value="nashik">Nashik</option>
                                                    <option value="aurangabad">Aurangabad</option>
                                                    <option value="solapur">Solapur</option>
                                                    <option value="amravati">Amravati</option>
                                                    <option value="kolhapur">Kolhapur</option>
                                                    <option value="ratnagiri">Ratnagiri</option>
                                                </select>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Service Configuration -->
                        <div class="col-md-12">
                            <div class="card shadow-sm border-0 rounded-3">
                                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <iconify-icon icon="mdi:medical-bag" class="me-2 icon-size-20"></iconify-icon>
                                        Service Configuration
                                    </div>
                                    <button type="button" class="btn btn-sm btn-light d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#addServiceTypeModal">
                                        <iconify-icon icon="mdi:plus"></iconify-icon> Add Service Type
                                    </button>
                                </div>
                                <div class="card-body bg-light">
                                    <form action="#" method="POST">
                                        @csrf
                                        <div class="row">
                                            <!-- Service Types Section -->
                                            <div class="col-md-12 mb-4">
                                                <h6 class="mb-3 fw-semibold d-flex align-items-center">
                                                    <iconify-icon icon="mdi:format-list-bulleted-type" class="me-2"></iconify-icon>
                                                    Service Types
                                                </h6>
                                                <div class="row g-3">
                                                    <div class="col-md-4">
                                                        <div class="card h-100 border-0 shadow-sm hover-shadow">
                                                            <div class="card-body">
                                                                <div class="form-check d-flex align-items-center">
                                                                    <input class="form-check-input hover-scale" type="checkbox" name="service_types[]" value="bio_medical_waste" id="bioMedicalWaste">
                                                                    <label class="form-check-label hover-text-primary cursor-pointer" for="bioMedicalWaste">Bio Medical Waste</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="card h-100 border-0 shadow-sm hover-shadow">
                                                            <div class="card-body">
                                                                <div class="form-check d-flex align-items-center">
                                                                    <input class="form-check-input hover-scale" type="checkbox" name="service_types[]" value="covid_bio_medical" id="covidBioMedical">
                                                                    <label class="form-check-label hover-text-primary cursor-pointer" for="covidBioMedical">COVID Bio Medical</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Separator -->
                                            <div class="col-md-12 px-4 my-24">
                                                <hr class="border-2">
                                            </div>

                                            <!-- Service Names Section -->
                                            <div class="col-md-12">
                                                <h6 class="mb-3 fw-semibold d-flex align-items-center">
                                                    <iconify-icon icon="mdi:format-list-checks" class="me-2"></iconify-icon>
                                                    Service Names
                                                </h6>
                                                <div class="row g-3">
                                                    <div class="col-md-4">
                                                        <div class="card h-100 border-0 shadow-sm hover-shadow">
                                                            <div class="card-body">
                                                                <div class="d-flex align-items-center justify-content-between mb-2">
                                                                    <div class="form-check d-flex align-items-center">
                                                                        <input class="form-check-input hover-scale" type="checkbox" name="service_names[]" value="bedded" id="bedded">
                                                                        <label class="form-check-label hover-text-primary cursor-pointer" for="bedded">Bedded</label>
                                                                    </div>
                                                                    <button type="button" class="btn btn-sm btn-link p-0" data-bs-toggle="tooltip" data-bs-placement="top" title="Per bed per day cost">
                                                                        <iconify-icon icon="mdi:information-outline" class="text-primary"></iconify-icon>
                                                                    </button>
                                                                </div>
                                                                <input type="text" class="form-control form-control-sm" name="service_labels[bedded]" placeholder="Custom Label" value="Bedded">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="col-md-4">
                                                        <div class="card h-100 border-0 shadow-sm hover-shadow">
                                                            <div class="card-body">
                                                                <div class="d-flex align-items-center justify-content-between mb-2">
                                                                    <div class="form-check d-flex align-items-center">
                                                                        <input class="form-check-input hover-scale" type="checkbox" name="service_names[]" value="bedded_fixed_price" id="beddedFixedPrice">
                                                                        <label class="form-check-label hover-text-primary cursor-pointer" for="beddedFixedPrice">Bedded with Fixed Price</label>
                                                                    </div>
                                                                    <button type="button" class="btn btn-sm btn-link p-0" data-bs-toggle="tooltip" data-bs-placement="top" title="No. of beds with fixed price">
                                                                        <iconify-icon icon="mdi:information-outline" class="text-primary"></iconify-icon>
                                                                    </button>
                                                                </div>
                                                                <input type="text" class="form-control form-control-sm" name="service_labels[bedded_fixed_price]" placeholder="Custom Label" value="Bedded with Fixed Price">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4"> 
                                                        <div class="card h-100 border-0 shadow-sm hover-shadow">
                                                            <div class="card-body">
                                                                <div class="d-flex align-items-center justify-content-between mb-2">
                                                                    <div class="form-check d-flex align-items-center">
                                                                        <input class="form-check-input hover-scale" type="checkbox" name="service_names[]" value="weight_based" id="weightBased">
                                                                        <label class="form-check-label hover-text-primary cursor-pointer" for="weightBased">Weight Based Service</label>
                                                                    </div>
                                                                    <button type="button" class="btn btn-sm btn-link p-0" data-bs-toggle="tooltip" data-bs-placement="top" title="Price based on weight configuration">
                                                                        <iconify-icon icon="mdi:information-outline" class="text-primary"></iconify-icon>
                                                                    </button>
                                                                </div>
                                                                <input type="text" class="form-control form-control-sm" name="service_labels[weight_based]" placeholder="Custom Label" value="Weight Based Service">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="card h-100 border-0 shadow-sm hover-shadow">
                                                            <div class="card-body">
                                                                <div class="d-flex align-items-center justify-content-between mb-2">
                                                                    <div class="form-check d-flex align-items-center">
                                                                        <input class="form-check-input hover-scale" type="checkbox" name="service_names[]" value="non_bedded" id="nonBedded">
                                                                        <label class="form-check-label hover-text-primary cursor-pointer d-flex align-items-center" for="nonBedded">Non-Bedded</label>
                                                                    </div>
                                                                    <button type="button" class="btn btn-sm btn-link p-0" data-bs-toggle="tooltip" data-bs-placement="top" title="Price with non-bedded type">
                                                                        <iconify-icon icon="mdi:information-outline" class="text-primary"></iconify-icon>
                                                                    </button>
                                                                </div>
                                                                <input type="text" class="form-control form-control-sm" name="service_labels[non_bedded]" placeholder="Custom Label" value="Non-Bedded">
                                                                
                                                                <!-- Non-Bedded Types Selection -->
                                                                <div id="nonBeddedTypes" class="mt-3 d-none-important">
                                                                    <div class="card border-0 shadow-sm">
                                                                        <div class="card-header bg-white border-0 py-2">
                                                                            <div class="d-flex align-items-center justify-content-between">
                                                                                <h6 class="mb-0 fw-semibold">Non-Bedded Types</h6>
                                                                                
                                                                            </div>
                                                                        </div>
                                                                        <div class="card-body">
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" id="selectAllNonBedded">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="selectAllNonBedded">Select All</label>
                                                                            </div>
                                                                            <hr/>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="clinic" id="clinic">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="clinic">Clinic</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="multi_speciality_clinic" id="multiSpecialityClinic">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="multiSpecialityClinic">Multi speciality Clinic</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="super_speciality_clinic" id="superSpecialityClinic">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="superSpecialityClinic">Super Speciality Clinic</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="pathological_laboratory" id="pathologicalLaboratory">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="pathologicalLaboratory">Pathological Laboratory</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="super_speciality_pathological_laboratory" id="superSpecialityPathologicalLaboratory">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="superSpecialityPathologicalLaboratory">Super Speciality Pathological Laboratory</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="dental_clinic" id="dentalClinic">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="dentalClinic">Dental Clinic</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="multi_speciality_dental_clinic" id="multiSpecialityDentalClinic">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="multiSpecialityDentalClinic">Multi speciality Dental Clinic</label>
                                                                            </div>
                                                                            
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="super_speciality_dental_clinic" id="superSpecialityDentalClinic">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="superSpecialityDentalClinic">Super Speciality Dental Clinic</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="imaging_centre_and_laboratory" id="imagingCentreAndLaboratory">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="imagingCentreAndLaboratory">Imaging Centre and Laboratory</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="blood_centre" id="bloodCentre">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="bloodCentre">Blood Centre</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="bedded_hospital" id="beddedHospital">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="beddedHospital">Bedded Hospital</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="dispensaries" id="dispensaries">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="dispensaries">Dispensaries</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="urban_health_centre" id="urbanHealthCentre">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="urbanHealthCentre">Urban Health Centre</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="primary_health_centre" id="primaryHealthCentre">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="primaryHealthCentre">Primary Health Centre</label>
                                                                            </div>
                                                                            
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="community_health_center" id="communityHealthCenter">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="communityHealthCenter">Community Health Center</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="government_area_hospital" id="governmentAreaHospital">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="governmentAreaHospital">Government Area Hospital</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="government_hospital" id="governmentHospital">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="governmentHospital">Government Hospital</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="pharmacy_college" id="pharmacyCollege">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="pharmacyCollege">Pharmacy College</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="residential_school" id="residentialSchool">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="residentialSchool">Residential School</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="veterinary_hospital" id="veterinaryHospital">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="veterinaryHospital">Veterinary Hospital</label>
                                                                            </div>
                                                                            <div class="form-check d-flex align-items-center">
                                                                                <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="covid_19_test_centre" id="covid19TestCentre">
                                                                                <label class="form-check-label hover-text-primary cursor-pointer" for="covid19TestCentre">Covid-19 Test Centre</label>
                                                                            </div>
                                                                                
                                                                        </div>
                                                                        <div class="card-footer bg-white border-0 py-2">
                                                                            <button type="button" class="btn btn-sm btn-light d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#addNonBeddedTypeModal">
                                                                                <iconify-icon icon="mdi:plus"></iconify-icon> Add New Type
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Configuration Tab -->
                <div class="tab-pane fade" id="invoice-config" role="tabpanel" aria-labelledby="invoice-config-tab">
                    <div class="row gy-4">
                        <div class="col-md-12">
                            <div class="card shadow-sm border-0 rounded-3">
                                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                    <iconify-icon icon="mdi:file-document-edit" class="me-2 icon-size-20"></iconify-icon>
                                    Invoice Template Configuration
                                </div>
                                <div class="card-body bg-light">
                                    <form action="#" method="POST" enctype="multipart/form-data">
                                        @csrf
                                        <div class="row">
                                            <!-- Default Template Toggle -->
                                            <div class="col-md-12 mb-24">
                                                <div class="card border-0 shadow-sm hover-shadow">
                                                    <div class="card-body">
                                                        <div class="d-flex align-items-center justify-content-between">
                                                            <div class="d-flex align-items-center">
                                                                <iconify-icon icon="mdi:file-document-check" class="me-2 text-primary icon-size-20"></iconify-icon>
                                                                <h6 class="mb-0 fw-semibold">Use Default Template</h6>
                                                            </div>
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input hover-scale" type="checkbox" id="useDefaultTemplate" name="use_default_template" checked>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Header Configuration -->
                                            <div class="col-md-12 mb-24">
                                                <div class="card border-0 shadow-sm hover-shadow">
                                                    <div class="card-header bg-white border-0">
                                                        <h6 class="mb-0 fw-semibold d-flex align-items-center">
                                                            <iconify-icon icon="mdi:file-document-edit" class="me-2 text-primary"></iconify-icon>
                                                            Invoice Header Configuration
                                                        </h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="row g-3">
                                                            <div class="col-md-12">
                                                                <div class="d-flex gap-4 mb-3">
                                                                    <div class="form-check d-flex align-items-center">
                                                                        <input class="form-check-input hover-scale" type="radio" name="header_type" value="image" id="headerImage">
                                                                        <label class="form-check-label hover-text-primary cursor-pointer d-flex align-items-center" for="headerImage">
                                                                            <iconify-icon icon="mdi:image" class="me-1"></iconify-icon>
                                                                            Image
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check d-flex align-items-center">
                                                                        <input class="form-check-input hover-scale" type="radio" name="header_type" value="html" id="headerHTML">
                                                                        <label class="form-check-label hover-text-primary cursor-pointer d-flex align-items-center" for="headerHTML">
                                                                            <iconify-icon icon="mdi:code-tags" class="me-1"></iconify-icon>
                                                                            HTML
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <div id="headerImageUpload" class="mb-3 d-none-important">
                                                                    <label class="form-label d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:upload" class="me-2 text-primary"></iconify-icon>
                                                                        Upload Header Image
                                                                    </label>
                                                                    <input type="file" class="form-control" name="header_image" accept="image/*">
                                                                    <small class="text-muted mt-1 d-block">Recommended size: 800x200px, Max file size: 2MB</small>
                                                                </div>
                                                                <div id="headerHTMLInput" class="mb-3 d-none-important">
                                                                    <label class="form-label d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:code-tags" class="me-2 text-primary"></iconify-icon>
                                                                        HTML Code
                                                                    </label>
                                                                    <textarea class="form-control" name="header_html" rows="4" placeholder="Enter HTML code for header"></textarea>
                                                                    <small class="text-muted mt-1 d-block">Use HTML tags to customize your header</small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Footer Configuration -->
                                            <div class="col-md-12">
                                                <div class="card border-0 shadow-sm hover-shadow">
                                                    <div class="card-header bg-white border-0">
                                                        <h6 class="mb-0 fw-semibold d-flex align-items-center">
                                                            <iconify-icon icon="mdi:file-document-edit" class="me-2 text-primary"></iconify-icon>
                                                            Invoice Footer Configuration
                                                        </h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="row g-3">
                                                            <div class="col-md-12">
                                                                <div class="d-flex gap-4 mb-3">
                                                                    <div class="form-check d-flex align-items-center">
                                                                        <input class="form-check-input hover-scale" type="radio" name="footer_type" value="image" id="footerImage">
                                                                        <label class="form-check-label hover-text-primary cursor-pointer d-flex align-items-center" for="footerImage">
                                                                            <iconify-icon icon="mdi:image" class="me-1"></iconify-icon>
                                                                            Image
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check d-flex align-items-center">
                                                                        <input class="form-check-input hover-scale" type="radio" name="footer_type" value="html" id="footerHTML">
                                                                        <label class="form-check-label hover-text-primary cursor-pointer d-flex align-items-center" for="footerHTML">
                                                                            <iconify-icon icon="mdi:code-tags" class="me-1"></iconify-icon>
                                                                            HTML
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <div id="footerImageUpload" class="mb-3 d-none-important">
                                                                    <label class="form-label d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:upload" class="me-2 text-primary"></iconify-icon>
                                                                        Upload Footer Image
                                                                    </label>
                                                                    <input type="file" class="form-control" name="footer_image" accept="image/*">
                                                                    <small class="text-muted mt-1 d-block">Recommended size: 800x200px, Max file size: 2MB</small>
                                                                </div>
                                                                <div id="footerHTMLInput" class="mb-3 d-none-important">
                                                                <label class="form-label d-flex align-items-center d-flex align-items-center">
                                                                    <iconify-icon icon="mdi:code-tags" class="me-2 text-primary"></iconify-icon>
                                                                    HTML Code
                                                                </label>
                                                                <textarea class="form-control" name="footer_html" rows="4" placeholder="Enter HTML code for footer"></textarea>
                                                                <small class="text-muted mt-1 d-block">Use HTML tags to customize your footer</small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notification Center Tab -->
                <div class="tab-pane fade" id="notifications" role="tabpanel" aria-labelledby="notifications-tab">
                    <div class="row gy-4">
                        <div class="col-md-12">
                            <div class="card shadow-sm border-0 rounded-3">
                                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                    <iconify-icon icon="mdi:bell-outline" class="me-2 icon-size-20"></iconify-icon>
                                    Notification Preferences
                                </div>
                                <div class="card-body bg-light">
                                    <h6 class="mb-4 text-xl">Notification Settings</h6>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="card">
                                                <div class="card-body">
                                                    <form action="#" method="POST">
                                                        @csrf
                                                        <div class="row">
                                                            <div class="col-md-12 mb-4">
                                                                <h6 class="mb-3">Email Notifications</h6>
                                                                <div class="form-check form-switch mb-3">
                                                                    <input class="form-check-input" type="checkbox" id="invoiceNotifications" checked>
                                                                    <label class="form-check-label" for="invoiceNotifications">Invoice Notifications</label>
                                                                </div>
                                                                <div class="form-check form-switch mb-3">
                                                                    <input class="form-check-input" type="checkbox" id="paymentNotifications" checked>
                                                                    <label class="form-check-label" for="paymentNotifications">Payment Notifications</label>
                                                                </div>
                                                                <div class="form-check form-switch mb-3">
                                                                    <input class="form-check-input" type="checkbox" id="systemNotifications" checked>
                                                                    <label class="form-check-label" for="systemNotifications">System Notifications</label>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12 mb-4">
                                                                <h6 class="mb-3">Push Notifications</h6>
                                                                <div class="form-check form-switch mb-3">
                                                                    <input class="form-check-input" type="checkbox" id="mobileNotifications" checked>
                                                                    <label class="form-check-label" for="mobileNotifications">Mobile Notifications</label>
                                                                </div>
                                                                <div class="form-check form-switch mb-3">
                                                                    <input class="form-check-input" type="checkbox" id="desktopNotifications" checked>
                                                                    <label class="form-check-label" for="desktopNotifications">Desktop Notifications</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form-group d-flex align-items-center justify-content-end gap-8">
                                                            <button type="submit" class="btn btn-primary-600 px-32">Save Changes</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Service Type Modal -->
<div class="modal fade" id="addServiceTypeModal" tabindex="-1" aria-labelledby="addServiceTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addServiceTypeModalLabel">Add New Service Type</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addServiceTypeForm">
                    <div class="mb-3">
                        <label for="newServiceType" class="form-label">Service Type Name</label>
                        <input type="text" class="form-control" id="newServiceType" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="saveServiceType">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Non-Bedded Type Modal -->
<div class="modal fade" id="addNonBeddedTypeModal" tabindex="-1" aria-labelledby="addNonBeddedTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addNonBeddedTypeModalLabel">Add New Non-Bedded Type</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addNonBeddedTypeForm">
                    <div class="mb-3">
                        <label for="newNonBeddedType" class="form-label">Type Name</label>
                        <input type="text" class="form-control" id="newNonBeddedType" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="saveNonBeddedType">Save</button>
            </div>
        </div>
    </div>
</div>

@stop

@section('script')
<script>
    const fileInput = document.getElementById("upload-file");
    const imagePreview = document.getElementById("uploaded-img__preview");
    const uploadedImgContainer = document.querySelector(".uploaded-img");
    const removeButton = document.querySelector(".uploaded-img__remove");

    fileInput.addEventListener("change", (e) => {
        if (e.target.files.length) {
            const src = URL.createObjectURL(e.target.files[0]);
            imagePreview.src = src;
            uploadedImgContainer.classList.remove('d-none');
        }
    });
    removeButton.addEventListener("click", () => {
        imagePreview.src = "";
        uploadedImgContainer.classList.add('d-none');
        fileInput.value = "";
    });
    // =============================== Upload Single Image js End here ================================================

    const authSignInput = document.getElementById("upload-auth-sign");
    const authSignPreview = document.getElementById("uploaded-auth-sign__preview");
    const uploadedAuthSignContainer = document.querySelector(".uploaded-auth-sign");
    const removeAuthSignButton = document.querySelector(".uploaded-auth-sign__remove");

    authSignInput.addEventListener("change", (e) => {
        if (e.target.files.length) {
            const src = URL.createObjectURL(e.target.files[0]);
            authSignPreview.src = src;
            uploadedAuthSignContainer.classList.remove('d-none');
        }
    });

    removeAuthSignButton.addEventListener("click", () => {
        authSignPreview.src = "";
        uploadedAuthSignContainer.classList.add('d-none');
        authSignInput.value = "";
    });

    // Initialize Bootstrap tabs
    var settingsTabs = new bootstrap.Tab(document.querySelector('#profile-tab'));

    // Initialize Select2 for districts
    $('.select2').select2({
        placeholder: "Select Districts",
        allowClear: true,
        width: '100%'
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Invoice Template Configuration
    $('input[name="header_type"]').change(function() {
        if ($(this).val() === 'image') {
            $('#headerImageUpload').slideDown();
            $('#headerHTMLInput').slideUp();
        } else {
            $('#headerImageUpload').slideUp();
            $('#headerHTMLInput').slideDown();
        }
    });

    $('input[name="footer_type"]').change(function() {
        if ($(this).val() === 'image') {
            $('#footerImageUpload').slideDown();
            $('#footerHTMLInput').slideUp();
        } else {
            $('#footerImageUpload').slideUp();
            $('#footerHTMLInput').slideDown();
        }
    });

    $('#useDefaultTemplate').change(function() {
        if ($(this).is(':checked')) {
            $('input[name="header_type"], input[name="footer_type"]').prop('disabled', true);
            $('#headerImageUpload, #headerHTMLInput, #footerImageUpload, #footerHTMLInput').slideUp();
        } else {
            $('input[name="header_type"], input[name="footer_type"]').prop('disabled', false);
        }
    });

    // Add New Service Type
    $('#saveServiceType').click(function() {
        var newType = $('#newServiceType').val();
        if (newType) {
            var newCard = `
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm hover-shadow">
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="service_types[]" value="${newType.toLowerCase().replace(/\s+/g, '_')}" id="${newType.toLowerCase().replace(/\s+/g, '_')}">
                                <label class="form-check-label" for="${newType.toLowerCase().replace(/\s+/g, '_')}">${newType}</label>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('.row.g-3').first().append(newCard);
            $('#addServiceTypeModal').modal('hide');
            $('#newServiceType').val('');
        }
    });

    // Add hover effects for checkboxes
    $('.hover-scale').hover(
        function() {
            $(this).addClass('scale-110');
        },
        function() {
            $(this).removeClass('scale-110');
        }
    );

    // Non-Bedded Type Selection
    $('#nonBedded').change(function() {
        if ($(this).is(':checked')) {
            $('#nonBeddedTypes').slideDown();
        } else {
            $('#nonBeddedTypes').slideUp();
        }
    });

    // Select All Non-Bedded Types
    $('#selectAllNonBedded').change(function() {
        $('input[name="non_bedded_types[]"]').prop('checked', $(this).is(':checked'));
    });

    // Add New Non-Bedded Type
    $('#saveNonBeddedType').click(function() {
        var newType = $('#newNonBeddedType').val();
        if (newType) {
            var newCheckbox = `
                <div class="form-check">
                    <input class="form-check-input hover-scale" type="checkbox" name="non_bedded_types[]" value="${newType.toLowerCase().replace(/\s+/g, '_')}" id="${newType.toLowerCase().replace(/\s+/g, '_')}">
                    <label class="form-check-label hover-text-primary cursor-pointer" for="${newType.toLowerCase().replace(/\s+/g, '_')}">${newType}</label>
                </div>
            `;
            $('.col-md-4').first().append(newCheckbox);
            $('#addNonBeddedTypeModal').modal('hide');
            $('#newNonBeddedType').val('');
        }
    });
</script>
@stop
