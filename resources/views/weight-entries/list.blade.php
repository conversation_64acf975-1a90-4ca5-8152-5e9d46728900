@extends('layouts.master')
@section('title', 'Weight Entries')
@section('content')
<div class="dashboard-main-body">
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Weight Entries</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li class="fw-medium">/ Weight Entries</li>
        </ul>
    </div>

    <div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <!-- <div class="mb-2">
                    <small class="text-muted d-flex align-items-center gap-1">
                        <iconify-icon icon="mdi:information" width="14"></iconify-icon>
                        Use the month filter to view weight entries for a specific month. Filter by service start date range and view service status. Weight entry and CSV export are only allowed for active services.
                    </small>
                </div> -->
                <div class="d-flex gap-2 mb-2 mb-md-0">
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchkey" placeholder="Search...">
                        <span class="input-group-text">
                            <iconify-icon icon="tabler:search" width="20" height="20"></iconify-icon>
                        </span>
                    </div>
                    <div class="input-group">
                            <select id="client" class="form-select select2">
                                <option value="">All Clients</option>
                                @foreach ($clients as $client)
                                    <option value="{{ $client->id }}">{{ $client->name }} - {{ $client->client_code }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="input-group">
                        <input type="text" class="form-control" id="monthpicker" placeholder="Select Month Filter" readonly>
                        
                        <span class="input-group-text">
                            <iconify-icon icon="mdi:calendar-month" width="20" height="20"></iconify-icon>
                        </span>
                    </div>
                    
                    <div class="input-group">
                        <select id="serviceStatus" class="form-select">
                            <option value="">All Services</option>
                            <option value="active">Active Services Only</option>
                            <option value="inactive">Inactive Services Only</option>
                        </select>
                    </div>
                    
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <!-- <div class="mb-2">
                    <div id="serviceSummary" class="d-flex justify-content-end gap-2 mb-2">
                        Summary will be populated by JavaScript
                    </div>
                </div> -->
                <div class="btn-group">
                    <button type="button" class="btn btn-success btn-sm d-flex align-items-center" id="downloadTemplate">
                        <iconify-icon icon="mdi:download" width="18" height="18"></iconify-icon>
                        Download Template <span id="downloadCount" class="badge bg-light text-dark ms-1"></span>
                    </button>
                    <button type="button" class="btn btn-primary btn-sm d-flex align-items-center" id="bulkUploadBtn">
                        <iconify-icon icon="mdi:upload" width="18" height="18"></iconify-icon> Bulk Upload
                    </button>
                </div>
            </div>
        </div>

    </div>
    <div class="card-body">
        <!-- Table View for Desktop -->
        <div class="d-none d-md-block">
            <table id="weightEntriesTable" class="table table-sm table-striped table-hover table-bordered text-wrap" style="width:100%">
                <thead>
                    <tr>
                        <th>Client Name</th>
                        <th>Service Start Date</th>
                        <th>Service Status</th>
                        <th>Month & Year</th>
                        <th>Weight</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>

        <!-- Grid View for Mobile -->
        <div id="weightEntriesGrid" class="d-block d-md-none row g-3"></div>
    </div>
</div>
</div>

<!-- Weight Entry Modal -->
<div class="modal fade" id="weightEntryModal" tabindex="-1" aria-labelledby="weightEntryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="weightEntryModalLabel">Enter Weight</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="weightEntryForm">
                    <input type="hidden" id="client_id" name="client_id">
                    <input type="hidden" id="month" name="month">
                    <input type="hidden" id="year" name="year">

                    <div class="mb-3">
                        <label for="weight" class="form-label">Weight (kg)</label>
                        <input type="number" class="form-control" id="weight" name="weight" step="0.01" min="0.01" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm" id="saveWeight">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Upload Modal -->
<div class="modal fade" id="bulkUploadModal" tabindex="-1" aria-labelledby="bulkUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkUploadModalLabel">Bulk Upload Weight Entries</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="bulkUploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="csv_file" class="form-label">CSV File</label>
                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                        <div class="form-text">
                            Please upload the CSV file with updated weight entries.
                            <a href="#" id="downloadTemplateLink">Download template</a> if you don't have one.
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm" id="uploadCSV">Upload</button>
            </div>
        </div>
    </div>
</div>
@stop

@section('script')
<script>
    $(document).ready(function () {
        // Initialize Select2
        $('.select2').select2({
            dropdownParent: $('body'),
            width: '100%'
        });

        // Initialize month picker using daterangepicker
        $('#monthpicker').daterangepicker({
            singleDatePicker: true,
            showDropdowns: true,
            autoApply: true,
            locale: {
                format: 'MMMM YYYY',
                applyLabel: 'Select',
                cancelLabel: 'Clear'
            },
            startView: "months",
            minViewMode: "months",
            autoUpdateInput: true,
            showCustomRangeLabel: false,
            alwaysShowCalendars: true,
            opens: 'left'
        });

        // Set current month as default
        var currentDate = moment();
        $('#monthpicker').val(currentDate.format('MMMM YYYY'));
        $('#monthpicker').data('daterangepicker').setStartDate(currentDate);

        // Initialize start date range picker
        $('#startDateRange').daterangepicker({
            autoUpdateInput: false,
            showDropdowns: true,
            autoApply: true,
            locale: {
                format: 'DD/MM/YYYY',
                cancelLabel: 'Clear'
            }
        });

        $('#startDateRange').on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('DD/MM/YYYY') + ' - ' + picker.endDate.format('DD/MM/YYYY'));
            table.draw();
        });

        $('#startDateRange').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
            table.draw();
        });



        var table = $('#weightEntriesTable').DataTable({
            processing: true,
            serverSide: true,
            searching: false,
            pageLength: 25,
            dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6'f>>" +
                 "<'row'<'col-md-12'tr>>" +
                 "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>",
            ajax: {
                url: "{{ route('weight-entries.data') }}",
                data: function (d) {
                    d.searchkey = $('#searchkey').val();
                    d.client = $('#client').val();
                    d.month = $('#monthpicker').val();
                    d.start_date_range = $('#startDateRange').val();
                    d.service_status = $('#serviceStatus').val();
                    d.sort_by = $('#sortBy').val();
                }
            },
            columns: [
                { data: 'client_name', name: 'client_name' },
                { data: 'start_date', name: 'start_date', orderable: true, searchable: false },
                { data: 'service_status', name: 'service_status', orderable: true, searchable: false },
                { data: 'month_year', name: 'month_year' },
                { data: 'weight', name: 'weight' },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            drawCallback: function(settings) {
                updateView();
                updateServiceSummary();
            }
        });

        // Custom search event triggers
        $("#searchkey").on("keyup", function() {
            table.draw();
        });

        $("#client").on("change", function() {
            table.draw();
        });

        $('#monthpicker').on('apply.daterangepicker', function(ev, picker) {
            table.draw();
        });

        $('#monthpicker').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
            table.draw();
        });

        // Clear month filter button
        $('#clearMonthFilter').click(function() {
            $('#monthpicker').val('');
            table.draw();
        });

        $("#sortBy").on("change", function() {
            table.draw();
        });

        $("#serviceStatus").on("change", function() {
            table.draw();
        });

        // Add weight button click
        $(document).on('click', '.add-weight, .edit-weight', function(e) {
            e.preventDefault();

            // Check if the service is locked (inactive)
            var row = $(this).closest('tr');
            var serviceStatusCell = row.find('td:eq(2)'); // Service status is the 3rd column (index 2)
            var isLocked = serviceStatusCell.find('.badge').hasClass('bg-secondary');

            if (isLocked) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Service Inactive',
                    text: 'Cannot enter weight for inactive service. The service is not active for the selected month.',
                    confirmButtonColor: '#d33'
                });
                return;
            }

            var clientId = $(this).data('id');
            var month = $(this).data('month');
            var year = $(this).data('year');
            var weight = $(this).data('weight') || '';

            $('#client_id').val(clientId);
            $('#month').val(month);
            $('#year').val(year);
            $('#weight').val(weight);

            var modalTitle = $(this).hasClass('add-weight') ? 'Add Weight' : 'Edit Weight';
            $('#weightEntryModalLabel').text(modalTitle);

            $('#weightEntryModal').modal('show');
        });

        // Save weight
        $('#saveWeight').click(function() {
            if (!$('#weightEntryForm')[0].checkValidity()) {
                $('#weightEntryForm')[0].reportValidity();
                return;
            }

            $.ajax({
                url: "{{ route('weight-entries.store') }}",
                type: "POST",
                data: {
                    client_id: $('#client_id').val(),
                    month: $('#month').val(),
                    year: $('#year').val(),
                    weight: $('#weight').val(),
                    _token: "{{ csrf_token() }}"
                },
                success: function(response) {
                    if (response.success) {
                        $('#weightEntryModal').modal('hide');

                        Swal.fire({
                            icon: 'success',
                            title: 'Success!',
                            text: response.message,
                            confirmButtonColor: '#3085d6'
                        }).then((result) => {
                            table.draw();
                        });
                    }
                },
                error: function(xhr) {
                    var errorMessage = 'An error occurred while saving the weight.';

                    if (xhr.responseJSON) {
                        if (xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        } else if (xhr.responseJSON.errors) {
                            var errors = xhr.responseJSON.errors;
                            errorMessage = '';
                            $.each(errors, function(key, value) {
                                errorMessage += value + '<br>';
                            });
                        }
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        html: errorMessage,
                        confirmButtonColor: '#d33'
                    });
                }
            });
        });

        // Download template button click
        $('#downloadTemplate, #downloadTemplateLink').click(function(e) {
            e.preventDefault();

            var month = $('#monthpicker').val();
            var url = "{{ route('weight-entries.download-template') }}";

            if (month && month.trim() !== '') {
                url += "?month=" + encodeURIComponent(month);
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'Select Month Filter',
                    text: 'Please select a month filter to download the template.',
                    confirmButtonColor: '#3085d6'
                });
                return;
            }

            // Show info about active services only
            Swal.fire({
                icon: 'info',
                title: 'Downloading Template',
                text: 'The CSV template will include only clients with active services for the selected month (' + month + ').',
                timer: 2500,
                showConfirmButton: false
            });

            window.location.href = url;
        });

        // Bulk upload button click
        $('#bulkUploadBtn').click(function() {
            $('#bulkUploadForm')[0].reset();
            $('#bulkUploadModal').modal('show');
        });

        // Upload CSV button click
        $('#uploadCSV').click(function() {
            if (!$('#bulkUploadForm')[0].checkValidity()) {
                $('#bulkUploadForm')[0].reportValidity();
                return;
            }

            var formData = new FormData();
            formData.append('csv_file', $('#csv_file')[0].files[0]);
            formData.append('_token', "{{ csrf_token() }}");

            Swal.fire({
                title: 'Uploading...',
                text: 'Please wait while we process your file.',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: "{{ route('weight-entries.bulk-upload') }}",
                type: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $('#bulkUploadModal').modal('hide');

                    if (response.success) {
                        var errorHtml = '';
                        if (response.errors && response.errors.length > 0) {
                            errorHtml = '<ul class="text-left">';
                            response.errors.forEach(function(error) {
                                errorHtml += '<li>' + error + '</li>';
                            });
                            errorHtml += '</ul>';
                        }

                        Swal.fire({
                            icon: 'success',
                            title: 'Upload Complete',
                            html: response.message + (errorHtml ? '<br><br>Errors:<br>' + errorHtml : ''),
                            confirmButtonColor: '#3085d6'
                        }).then((result) => {
                            table.draw();
                        });
                    }
                },
                error: function(xhr) {
                    var errorMessage = 'An error occurred during upload.';

                    if (xhr.responseJSON && xhr.responseJSON.errors) {
                        var errors = xhr.responseJSON.errors;
                        errorMessage = '';

                        $.each(errors, function(key, value) {
                            errorMessage += value + '<br>';
                        });
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Upload Failed',
                        html: errorMessage,
                        confirmButtonColor: '#d33'
                    });
                }
            });
        });

        // Responsive view update
        function updateView() {
            if ($(window).width() < 768) {
                $('#weightEntriesTable_wrapper').addClass('d-none');

                // Create grid view for mobile
                var gridView = $('#weightEntriesGrid');
                gridView.empty();

                var data = table.rows().data();

                data.each(function(item) {
                    var card = `
                        <div class="col-12">
                            <div class="card shadow-sm border">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-2">${item.client_name}</h6>
                                    <div class="mb-2 d-flex align-items-center gap-2">
                                        <span class="text-muted"><strong><iconify-icon icon="mdi:calendar-start" width="16"></iconify-icon> Service Start:</strong></span>
                                        <span>${item.start_date}</span>
                                    </div>
                                    <div class="mb-2 d-flex align-items-center gap-2">
                                        <span class="text-muted"><strong><iconify-icon icon="mdi:toggle-switch" width="16"></iconify-icon> Status:</strong></span>
                                        <span>${item.service_status}</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="text-muted"><strong>Month & Year:</strong> ${item.month_year}</span>
                                        <span class="badge bg-info">${item.weight.replace(/<[^>]*>/g, '')}</span>
                                    </div>
                                    <div class="d-flex justify-content-end">
                                        ${item.action}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    gridView.append(card);
                });

                gridView.removeClass('d-none');
            } else {
                $('#weightEntriesTable_wrapper').removeClass('d-none');
                $('#weightEntriesGrid').addClass('d-none');
            }
        }

        // Update service summary
        function updateServiceSummary() {
            var data = table.rows().data();
            var activeCount = 0;
            var inactiveCount = 0;
            var totalCount = data.length;

            data.each(function(item) {
                if (item.service_status && item.service_status.includes('Active')) {
                    activeCount++;
                } else {
                    inactiveCount++;
                }
            });

            var summaryHtml = `
                <span class="badge bg-success-subtle text-success">
                    <iconify-icon icon="mdi:check-circle" width="12"></iconify-icon> ${activeCount} Active
                </span>
                <span class="badge bg-danger-subtle text-danger">
                    <iconify-icon icon="mdi:close-circle" width="12"></iconify-icon> ${inactiveCount} Inactive
                </span>
                <span class="badge bg-info-subtle text-info">
                    <iconify-icon icon="mdi:account-group" width="12"></iconify-icon> ${totalCount} Total
                </span>
            `;

            $('#serviceSummary').html(summaryHtml);

            // Update download count
            $('#downloadCount').text(activeCount > 0 ? `(${activeCount})` : '');
        }

        // Check on window resize
        $(window).resize(updateView);

        // Initial check on page load
        updateView();
    });
</script>
@stop
