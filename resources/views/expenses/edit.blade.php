@extends('layouts.master')

@section('title', 'Edit Expense')

@section('content')
<div class="dashboard-main-body">
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Edit Expense</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">
                <a href="{{ route('expenses.index') }}" class="d-flex align-items-center gap-1 hover-text-primary">
                    Expenses
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Edit</li>
        </ul>
    </div>

    <div class="card h-100 p-0 radius-12">
        <div class="card-header border-bottom bg-base py-16 px-24">
            <h6 class="text-lg fw-semibold mb-0">Edit Expense Information</h6>
        </div>
        <div class="card-body p-24">
            <form action="{{ route('expenses.update', $expense->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-20">
                            <label for="title" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                Expense Title <span class="text-danger-600">*</span>
                            </label>
                            <input type="text" class="form-control radius-8" id="title" name="title" 
                                   value="{{ old('title', $expense->title) }}" placeholder="Enter expense title" required>
                            @error('title')
                                <div class="text-danger-600 text-sm">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-20">
                            <label for="amount" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                Amount <span class="text-danger-600">*</span>
                            </label>
                            <input type="number" class="form-control radius-8" id="amount" name="amount" 
                                   value="{{ old('amount', $expense->amount) }}" placeholder="0.00" step="0.01" min="0" required>
                            @error('amount')
                                <div class="text-danger-600 text-sm">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-20">
                            <label for="category_id" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                Category <span class="text-danger-600">*</span>
                            </label>
                            <select class="form-select radius-8" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" 
                                            {{ old('category_id', $expense->category_id) == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('category_id')
                                <div class="text-danger-600 text-sm">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-20">
                            <label for="expense_date" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                Expense Date <span class="text-danger-600">*</span>
                            </label>
                            <input type="text" class="form-control radius-8" id="expense_date" name="expense_date"
                                   value="{{ old('expense_date', $expense->expense_date->format('d/m/Y')) }}"
                                   placeholder="dd/mm/yyyy" required>
                            @error('expense_date')
                                <div class="text-danger-600 text-sm">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-20">
                            <label for="vendor_name" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                Vendor Name
                            </label>
                            <input type="text" class="form-control radius-8" id="vendor_name" name="vendor_name" 
                                   value="{{ old('vendor_name', $expense->vendor_name) }}" placeholder="Enter vendor name">
                            @error('vendor_name')
                                <div class="text-danger-600 text-sm">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-20">
                            <label for="payment_method" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                Payment Method
                            </label>
                            <select class="form-select radius-8" id="payment_method" name="payment_method">
                                <option value="">Select Payment Method</option>
                                <option value="cash" {{ old('payment_method', $expense->payment_method) == 'cash' ? 'selected' : '' }}>Cash</option>
                                <option value="card" {{ old('payment_method', $expense->payment_method) == 'card' ? 'selected' : '' }}>Card</option>
                                <option value="bank_transfer" {{ old('payment_method', $expense->payment_method) == 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                                <option value="cheque" {{ old('payment_method', $expense->payment_method) == 'cheque' ? 'selected' : '' }}>Cheque</option>
                                <option value="online" {{ old('payment_method', $expense->payment_method) == 'online' ? 'selected' : '' }}>Online Payment</option>
                            </select>
                            @error('payment_method')
                                <div class="text-danger-600 text-sm">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-20">
                            <label for="reference_number" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                Reference Number
                            </label>
                            <input type="text" class="form-control radius-8" id="reference_number" name="reference_number" 
                                   value="{{ old('reference_number', $expense->reference_number) }}" placeholder="Invoice/Receipt number">
                            @error('reference_number')
                                <div class="text-danger-600 text-sm">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-20">
                            <label for="receipt_file" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                Receipt File
                            </label>
                            <input type="file" class="form-control radius-8" id="receipt_file" name="receipt_file" 
                                   accept=".jpg,.jpeg,.png,.pdf">
                            <small class="text-muted">Supported formats: JPG, PNG, PDF (Max: 5MB)</small>
                            @if($expense->receipt_file)
                                <div class="mt-2">
                                    <small class="text-success">Current file: 
                                        <a href="{{ Storage::url($expense->receipt_file) }}" target="_blank">View Receipt</a>
                                    </small>
                                </div>
                            @endif
                            @error('receipt_file')
                                <div class="text-danger-600 text-sm">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="mb-20">
                            <label for="description" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                Description
                            </label>
                            <textarea class="form-control radius-8" id="description" name="description" rows="3" 
                                      placeholder="Enter expense description">{{ old('description', $expense->description) }}</textarea>
                            @error('description')
                                <div class="text-danger-600 text-sm">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="mb-20">
                            <label for="notes" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                Notes
                            </label>
                            <textarea class="form-control radius-8" id="notes" name="notes" rows="2" 
                                      placeholder="Additional notes">{{ old('notes', $expense->notes) }}</textarea>
                            @error('notes')
                                <div class="text-danger-600 text-sm">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="mb-20">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_reimbursable" name="is_reimbursable" 
                                       value="1" {{ old('is_reimbursable', $expense->is_reimbursable) ? 'checked' : '' }}>
                                <label class="form-check-label fw-semibold text-primary-light text-sm" for="is_reimbursable">
                                    This expense is reimbursable
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex align-items-center justify-content-center gap-3">
                    <button type="button" onclick="window.history.back()" 
                            class="border border-danger-600 bg-hover-danger-200 text-danger-600 text-md px-56 py-11 radius-8">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="btn btn-primary border border-primary-600 text-md px-56 py-12 radius-8">
                        Update Expense
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
    $(document).ready(function() {
        // Initialize date picker using daterangepicker
        $('#expense_date').daterangepicker({
            singleDatePicker: true,
            showDropdowns: true,
            autoApply: true,
            maxDate: moment(), // Don't allow future dates
            locale: {
                format: 'DD/MM/YYYY'
            }
        }, function (chosen_date) {
            $('#expense_date').val(chosen_date.format('DD/MM/YYYY'));
        });
    });
</script>
@endsection
