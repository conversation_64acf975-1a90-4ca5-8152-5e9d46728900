@extends('layouts.master')

@section('title', 'Expense Categories')

@section('content')
<div class="dashboard-main-body">
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Expense Categories</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">
                <a href="{{ route('expenses.index') }}" class="d-flex align-items-center gap-1 hover-text-primary">
                    Expenses
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Categories</li>
        </ul>
    </div>

    <div class="card h-100 p-0 radius-12">
        <div class="card-header border-bottom bg-base py-16 px-24 d-flex align-items-center flex-wrap gap-3 justify-content-between">
            <div class="d-flex align-items-center flex-wrap gap-3">
                
                <form class="navbar-search">
                    <input type="text" class="bg-base h-40-px w-auto" name="search" id="searchkey" placeholder="Search categories">
                    <iconify-icon icon="ion:search-outline" class="icon"></iconify-icon>
                </form>
            </div>
            <div class="d-flex align-items-center flex-wrap gap-3">
                @can('expense-category-create')
                <button type="button" onclick="addCategory()" class="btn btn-primary text-sm btn-sm px-12 py-12 radius-8 d-flex align-items-center gap-2">
                    <iconify-icon icon="ic:baseline-plus" class="icon text-xl line-height-1"></iconify-icon>
                    Add New Category
                </button>
                @endcan
            </div>
        </div>
        <div class="card-body p-24">
            <!-- Table View for Desktop -->
            <div class="d-none d-md-block">
                <table id="categoriesTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Color</th>
                            <th>Status</th>
                            <th>Created By</th>
                            <th>Created At</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table>
            </div>

            <!-- Grid View for Mobile -->
            <div id="categoriesGrid" class="d-block d-md-none"></div>
        </div>
    </div>
</div>

<!-- Add/Edit Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1" aria-labelledby="categoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalLabel">Add New Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="categoryForm">
                <div class="modal-body">
                    <input type="hidden" id="categoryId" name="category_id">
                    <div class="mb-3">
                        <label for="name" class="form-label">Category Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="color" class="form-label">Color <span class="text-danger">*</span></label>
                        <input type="color" class="form-control form-control-color" id="color" name="color" value="#007bff" required>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                            <label class="form-check-label" for="is_active">
                                Active
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Category</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
    $(document).ready(function () {
        var table = $('#categoriesTable').DataTable({
            processing: true,
            serverSide: true,
            searching: false,
            pageLength: 25,
            ajax: {
                url: "{{ route('expense-categories.data') }}",
                data: function (d) {
                    d.searchkey = $('#searchkey').val();
                }
            },
            columns: [
                { data: 'name', name: 'name' },
                { data: 'description', name: 'description' },
                { data: 'color_preview', name: 'color', orderable: false, searchable: false },
                { data: 'status_badge', name: 'is_active', orderable: false, searchable: false },
                { data: 'created_by_name', name: 'createdBy.name' },
                { data: 'formatted_created_at', name: 'created_at' },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            drawCallback: function(settings) {
                var data = this.api().rows().data();
                var gridHtml = '';

                data.each(function (row) {
                    var categoryCard = `
                        <div class="card mb-4 shadow-none border">
                            <div class="card-body">
                                <h6 class="d-flex align-items-center fw-normal">
                                    <div style="width: 20px; height: 20px; background-color: ${row.color}; border-radius: 50%; border: 1px solid #ddd; margin-right: 8px;"></div>
                                    <strong>${row.name}</strong>
                                </h6>
                                <p class="mb-1">
                                    <small><strong>Description:</strong> ${row.description || 'N/A'}</small>
                                </p>
                                <p class="mb-1">
                                    <small><strong>Created By:</strong> ${row.created_by_name}</small>
                                </p>
                                <p class="mb-1">
                                    <small><strong>Created At:</strong> ${row.formatted_created_at}</small>
                                </p>
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div>${row.status_badge}</div>
                                    <div>${row.action}</div>
                                </div>
                            </div>
                        </div>
                    `;
                    gridHtml += categoryCard;
                });

                $('#categoriesGrid').html(gridHtml);
            }
        });

        // Search functionality
        $('#searchkey').on('keyup', function() {
            table.draw();
        });

        // Records per page
        $('#recordsPerPage').on('change', function() {
            table.page.len($(this).val()).draw();
        });

        // Category form submission
        $('#categoryForm').on('submit', function(e) {
            e.preventDefault();
            
            var categoryId = $('#categoryId').val();
            var url = categoryId ? `/expense-categories/${categoryId}` : "{{ route('expense-categories.store') }}";
            var method = categoryId ? 'PUT' : 'POST';
            
            $.ajax({
                url: url,
                method: method,
                data: $(this).serialize(),
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        $('#categoryModal').modal('hide');
                        table.draw();
                        showSnackbar(response.message, 'success');
                        resetForm();
                    }
                },
                error: function(xhr) {
                    var errors = xhr.responseJSON.errors;
                    if (errors) {
                        var errorMessage = Object.values(errors).flat().join('\n');
                        showSnackbar(errorMessage, 'error');
                    } else {
                        showSnackbar('Error saving category', 'error');
                    }
                }
            });
        });
    });

    function addCategory() {
        resetForm();
        $('#categoryModalLabel').text('Add New Category');
        $('#categoryModal').modal('show');
    }

    function editCategory(categoryId) {
        $.ajax({
            url: `/expense-categories/${categoryId}`,
            method: 'GET',
            success: function(category) {
                $('#categoryId').val(category.id);
                $('#name').val(category.name);
                $('#description').val(category.description);
                $('#color').val(category.color);
                $('#is_active').prop('checked', category.is_active);
                $('#categoryModalLabel').text('Edit Category');
                $('#categoryModal').modal('show');
            },
            error: function(xhr) {
                showSnackbar('Error loading category data', 'error');
            }
        });
    }

    function deleteCategory(categoryId) {
        if (confirm('Are you sure you want to delete this category?')) {
            $.ajax({
                url: `/expense-categories/${categoryId}`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        $('#categoriesTable').DataTable().draw();
                        showSnackbar(response.message, 'success');
                    } else {
                        showSnackbar(response.message, 'error');
                    }
                },
                error: function(xhr) {
                    showSnackbar('Error deleting category', 'error');
                }
            });
        }
    }

    function resetForm() {
        $('#categoryForm')[0].reset();
        $('#categoryId').val('');
        $('#color').val('#007bff');
        $('#is_active').prop('checked', true);
    }

    function showSnackbar(message, type) {
        // Implementation for showing snackbar notifications
        // This should match your existing snackbar implementation
        console.log(type + ': ' + message);
    }
</script>
@endsection
