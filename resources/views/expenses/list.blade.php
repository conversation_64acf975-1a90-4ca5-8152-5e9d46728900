@extends('layouts.master')
@section('title', 'Expenses List - Paidash')
@section('content')

<div class="dashboard-main-body">
@if(session('success'))
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('success') }}</span>
        </div>
        <button type="button" class="btn-close" aria-label="Close" onclick="hideSnackbar()"></button>
    </div>
@endif

@if(session('error'))
    <div id="snackbar" class="snackbar alert alert-danger bg-danger-100 text-danger-600 border-danger-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:circle-x" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('error') }}</span>
        </div>
        <button type="button" class="btn-close" aria-label="Close" onclick="hideSnackbar()"></button>
    </div>
@endif

    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Expenses</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Expenses</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">

            <div class="d-flex flex-wrap align-items-center gap-3 w-20 w-mb-100">
                

                <div class="icon-field w-100">
                    <input type="text" name="searchkey" class="form-control form-control-sm w-100" id="searchkey" placeholder="Search expenses...">
                    <span class="icon">
                        <iconify-icon icon="ion:search-outline"></iconify-icon>
                    </span>
                </div>
            </div>

            <div class="d-flex flex-no-wrap align-items-center gap-3">
                <select id="searchStatus" class="form-control form-select select2">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                    <option value="paid">Paid</option>
                </select>

                <select id="searchCategory" class="form-control form-select select2">
                    <option value="">All Categories</option>
                    @foreach($categories as $category)
                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                    @endforeach
                </select>

                <div class="input-group date_range " style="max-width: 250px;">
                    <input type="text" id="expense_daterange" class="form-control form-control-sm w-auto h-40-px" placeholder="Select Date Range" readonly>
                    <div class="input-group-append">
                        <span class="input-group-text" id="basic-addon2">
                            <iconify-icon icon="mdi:calendar" width="24" height="24"></iconify-icon>
                        </span>
                    </div>
                </div>

                
                @can('expense-create')
                <a href="{{ route('expenses.create') }}" class="btn btn-primary text-sm btn-sm px-12 py-12 radius-8 d-flex align-items-center gap-2 d-inline-flex">
                    <iconify-icon icon="ic:baseline-plus" class="icon text-xl line-height-1"></iconify-icon>
                    Add New Expense
                </a>
                @endcan
            </div>


        </div>
        <div class="card-body">
            <!-- Table View for Desktop -->
            <div class="d-none d-md-block">
                <table id="expensesTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Amount</th>
                            <th>Category</th>
                            <th>Date</th>
                            <th>Vendor</th>
                            <th>Status</th>
                            <th>Created By</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                    <tfoot>
                        <tr>
                            <th colspan="1" class="text-end">Total</th>
                            <th id="totalAmount"><strong>₹ 0.00</strong></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- Grid View for Mobile -->
            <div id="expensesGrid" class="d-block d-md-none"></div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1" aria-labelledby="statusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalLabel">Update Expense Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="statusForm">
                <div class="modal-body">
                    <input type="hidden" id="expenseId" name="expense_id">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                            <option value="paid">Paid</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
    $(document).ready(function () {

        $('#searchStatus').select2({
            width: '200px'
        });
        $('#searchCategory').select2({
            width: '200px'
        });
        // Initialize date range picker
        $('#expense_daterange').daterangepicker({
            autoUpdateInput: false,
            locale: {
                cancelLabel: 'Clear',
                format: 'DD/MM/YYYY'
            }
        });

        $('#expense_daterange').on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('DD/MM/YYYY') + ' - ' + picker.endDate.format('DD/MM/YYYY'));
        });

        $('#expense_daterange').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
        });

        var table = $('#expensesTable').DataTable({
            processing: true,
            serverSide: true,
            searching: false,
            pageLength: 25,
            ajax: {
                url: "{{ route('expenses.data') }}",
                data: function (d) {
                    d.searchkey = $('#searchkey').val();
                    d.searchStatus = $('#searchStatus').val();
                    d.searchCategory = $('#searchCategory').val();
                    d.daterange = $('#expense_daterange').val();
                }
            },
            columns: [
                { data: 'title', name: 'title' },
                { data: 'formatted_amount', name: 'amount',
                    render: function(data, type, row) {
                        return data;
                    }
                },
                { data: 'category_name', name: 'category.name' },
                { data: 'formatted_date', name: 'expense_date' },
                { data: 'vendor_name', name: 'vendor_name' },
                { data: 'status_badge', name: 'status', orderable: false, searchable: false },
                { data: 'created_by_name', name: 'createdBy.name' },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            drawCallback: function (settings) {
                var api = this.api();
                var data = api.rows().data();
                var gridContainer = $('#expensesGrid');
                gridContainer.empty();

                data.each(function (row) {
                    var expenseCard = `
                        <div class="card mb-4 shadow-none border">
                            <div class="card-body">
                                <h6 class="d-flex align-items-center fw-normal">
                                    <iconify-icon icon="hugeicons:money-send-square" class="me-2" width="20"></iconify-icon>
                                    <strong>${row.title}</strong>
                                </h6>
                                <p class="mb-1 d-flex align-items-center">
                                    <iconify-icon icon="hugeicons:money-bag-02" class="me-2" width="18"></iconify-icon>
                                    <small><strong>Amount:</strong> ${row.formatted_amount}</small>
                                </p>
                                <p class="mb-1 d-flex align-items-center">
                                    <iconify-icon icon="hugeicons:category" class="me-2" width="18"></iconify-icon>
                                    <small><strong>Category:</strong> ${row.category_name}</small>
                                </p>
                                <p class="mb-1 d-flex align-items-center">
                                    <iconify-icon icon="hugeicons:calendar-03" class="me-2" width="18"></iconify-icon>
                                    <small><strong>Date:</strong> ${row.formatted_date}</small>
                                </p>
                                <p class="mb-1 d-flex align-items-center">
                                    <iconify-icon icon="hugeicons:user-account" class="me-2" width="18"></iconify-icon>
                                    <small><strong>Vendor:</strong> ${row.vendor_name || 'N/A'}</small>
                                </p>
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div>${row.status_badge}</div>
                                    <div>${row.action}</div>
                                </div>
                            </div>
                        </div>
                    `;
                    gridContainer.append(expenseCard);
                });
            },
            dom:
            "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" + // Entries Dropdown & CSV Button
            "<'row'<'col-md-12'tr>>" +
            "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>", // Info at left, Pagination at right
            buttons: [
                {
                    text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export',
                    className: 'btn btn-success btn-sm bg-success-500',
                    action: function (e, dt, node, config) {
                        exportExpenses();
                    }
                }
            ],
            infoCallback: function(settings, start, end, max, total, pre) {
                return `Showing ${start} to ${end} of ${total} records`;
            },
            footerCallback: function(row, data, start, end, display) {
                var api = this.api();

                // Function to calculate the total of a column
                function calculateTotal(columnIndex) {
                    return api
                        .column(columnIndex, { page: 'current' })
                        .data()
                        .reduce(function(a, b) {
                            // Extract numeric value from formatted amount (remove ₹ and commas)
                            var numA = parseFloat(String(a).replace(/[₹,]/g, '')) || 0;
                            var numB = parseFloat(String(b).replace(/[₹,]/g, '')) || 0;
                            return numA + numB;
                        }, 0);
                }

                if (api.column(1).footer()) {
                    $(api.column(1).footer()).html(`<strong>₹ ${calculateTotal(1).toFixed(2)}</strong>`);
                }
            }
        });

        // Search functionality
        $('#searchkey, #searchStatus, #searchCategory, #expense_daterange').on('change keyup', function() {
            table.draw();
        });

        // Export function
        function exportExpenses() {
            var params = new URLSearchParams({
                searchkey: $('#searchkey').val(),
                searchStatus: $('#searchStatus').val(),
                searchCategory: $('#searchCategory').val(),
                daterange: $('#expense_daterange').val()
            });

            window.location.href = "{{ route('expenses.export') }}?" + params.toString();
        }

        // Make exportExpenses function global
        window.exportExpenses = exportExpenses;

        // Status update form
        $('#statusForm').on('submit', function(e) {
            e.preventDefault();

            $.ajax({
                url: "{{ route('expenses.update-status') }}",
                method: 'POST',
                data: $(this).serialize(),
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        $('#statusModal').modal('hide');
                        table.draw();
                        showSnackbar(response.message, 'success');
                    }
                },
                error: function(xhr) {
                    showSnackbar('Error updating status', 'error');
                }
            });
        });
    });

    function updateStatus(expenseId) {
        $('#expenseId').val(expenseId);
        $('#statusModal').modal('show');
    }

    function deleteExpense(expenseId) {
        if (confirm('Are you sure you want to delete this expense?')) {
            $.ajax({
                url: `/expenses/${expenseId}`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        $('#expensesTable').DataTable().draw();
                        showSnackbar(response.message, 'success');
                    }
                },
                error: function(xhr) {
                    showSnackbar('Error deleting expense', 'error');
                }
            });
        }
    }

    function showSnackbar(message, type) {
        const snackbar = document.getElementById('snackbar');
        const snackbarMessage = document.getElementById('snackbar-message');

        if (snackbar && snackbarMessage) {
            snackbarMessage.textContent = message;
            snackbar.className = `snackbar alert ${type === 'success' ? 'alert-success bg-success-100 text-success-600 border-success-100' : 'alert-danger bg-danger-100 text-danger-600 border-danger-100'} fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show`;

            setTimeout(() => {
                snackbar.classList.remove('show');
            }, 5000);
        }
    }

    function hideSnackbar() {
        const snackbar = document.getElementById('snackbar');
        if (snackbar) {
            snackbar.classList.remove('show');
        }
    }
</script>
@endsection
