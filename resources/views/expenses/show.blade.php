@extends('layouts.master')

@section('title', 'View Expense')

@section('content')
<div class="dashboard-main-body">
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Expense Details</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">
                <a href="{{ route('expenses.index') }}" class="d-flex align-items-center gap-1 hover-text-primary">
                    Expenses
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">View</li>
        </ul>
    </div>

    <div class="row gy-4">
        <!-- Expense Details Card -->
        <div class="col-lg-8">
            <div class="card h-100 p-0 radius-12">
                <div class="card-header border-bottom bg-base py-16 px-24 d-flex align-items-center justify-content-between">
                    <h6 class="text-lg fw-semibold mb-0">Expense Information</h6>
                    <div class="d-flex gap-2">
                        @can('expense-edit')
                        <a href="{{ route('expenses.edit', $expense->id) }}" class="btn btn-outline-primary btn-sm d-flex align-items-center gap-1">
                            <iconify-icon icon="lucide:edit" class="me-1"></iconify-icon>
                            Edit
                        </a>
                        @endcan
                        @can('expense-approve')
                        @if($expense->status === 'pending')
                        <button type="button" onclick="updateStatus({{ $expense->id }})" class="btn btn-success btn-sm d-flex align-items-center gap-1">
                            <iconify-icon icon="hugeicons:checkmark-circle-02" class="me-1"></iconify-icon>
                            Update Status
                        </button>
                        @endif
                        @endcan
                    </div>
                </div>
                <div class="card-body p-24">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-20">
                                <label class="form-label fw-semibold text-primary-light text-sm mb-8">Title</label>
                                <p class="text-secondary-light mb-0">{{ $expense->title }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-20">
                                <label class="form-label fw-semibold text-primary-light text-sm mb-8">Amount</label>
                                <p class="text-secondary-light mb-0 fw-semibold text-success-600">₹{{ number_format($expense->amount, 2) }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-20">
                                <label class="form-label fw-semibold text-primary-light text-sm mb-8">Category</label>
                                <p class="text-secondary-light mb-0">
                                    <span class="badge" style="background-color: {{ $expense->category->color }}; color: white;">
                                        {{ $expense->category->name }}
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-20">
                                <label class="form-label fw-semibold text-primary-light text-sm mb-8">Expense Date</label>
                                <p class="text-secondary-light mb-0">{{ $expense->expense_date->format('d/m/Y') }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-20">
                                <label class="form-label fw-semibold text-primary-light text-sm mb-8">Vendor</label>
                                <p class="text-secondary-light mb-0">{{ $expense->vendor_name ?: 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-20">
                                <label class="form-label fw-semibold text-primary-light text-sm mb-8">Payment Method</label>
                                <p class="text-secondary-light mb-0">{{ $expense->payment_method ? ucfirst(str_replace('_', ' ', $expense->payment_method)) : 'N/A' }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-20">
                                <label class="form-label fw-semibold text-primary-light text-sm mb-8">Reference Number</label>
                                <p class="text-secondary-light mb-0">{{ $expense->reference_number ?: 'N/A' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-20">
                                <label class="form-label fw-semibold text-primary-light text-sm mb-8">Status</label>
                                <p class="text-secondary-light mb-0">
                                    @php
                                        $badges = [
                                            'pending' => 'warning',
                                            'approved' => 'success',
                                            'rejected' => 'danger',
                                            'paid' => 'info'
                                        ];
                                        $class = $badges[$expense->status] ?? 'secondary';
                                    @endphp
                                    <span class="badge bg-{{ $class }}">{{ ucfirst($expense->status) }}</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    @if($expense->description)
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-20">
                                <label class="form-label fw-semibold text-primary-light text-sm mb-8">Description</label>
                                <p class="text-secondary-light mb-0">{{ $expense->description }}</p>
                            </div>
                        </div>
                    </div>
                    @endif

                    @if($expense->notes)
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-20">
                                <label class="form-label fw-semibold text-primary-light text-sm mb-8">Notes</label>
                                <p class="text-secondary-light mb-0">{{ $expense->notes }}</p>
                            </div>
                        </div>
                    </div>
                    @endif

                    <div class="row">
                        <div class="col-12">
                            <div class="mb-20">
                                <label class="form-label fw-semibold text-primary-light text-sm mb-8">Reimbursable</label>
                                <p class="text-secondary-light mb-0">
                                    <span class="badge bg-{{ $expense->is_reimbursable ? 'success' : 'secondary' }}">
                                        {{ $expense->is_reimbursable ? 'Yes' : 'No' }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>

                    @if($expense->receipt_file)
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-20">
                                <label class="form-label fw-semibold text-primary-light text-sm mb-8">Receipt</label>
                                <div>
                                    <a href="{{ Storage::url($expense->receipt_file) }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                        <iconify-icon icon="hugeicons:file-view" class="me-1"></iconify-icon>
                                        View Receipt
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Expense Metadata Card -->
        <div class="col-lg-4">
            <div class="card h-100 p-0 radius-12">
                <div class="card-header border-bottom bg-base py-16 px-24">
                    <h6 class="text-lg fw-semibold mb-0">Expense Metadata</h6>
                </div>
                <div class="card-body p-24">
                    <div class="mb-20">
                        <label class="form-label fw-semibold text-primary-light text-sm mb-8">Created By</label>
                        <p class="text-secondary-light mb-0">{{ $expense->createdBy->name }}</p>
                    </div>

                    <div class="mb-20">
                        <label class="form-label fw-semibold text-primary-light text-sm mb-8">Created At</label>
                        <p class="text-secondary-light mb-0">{{ $expense->created_at->format('d/m/Y H:i:s') }}</p>
                    </div>

                    @if($expense->approved_by)
                    <div class="mb-20">
                        <label class="form-label fw-semibold text-primary-light text-sm mb-8">Approved By</label>
                        <p class="text-secondary-light mb-0">{{ $expense->approvedBy->name }}</p>
                    </div>

                    <div class="mb-20">
                        <label class="form-label fw-semibold text-primary-light text-sm mb-8">Approved At</label>
                        <p class="text-secondary-light mb-0">{{ $expense->approved_at->format('d/m/Y H:i:s') }}</p>
                    </div>
                    @endif

                    <div class="mb-20">
                        <label class="form-label fw-semibold text-primary-light text-sm mb-8">Last Updated</label>
                        <p class="text-secondary-light mb-0">{{ $expense->updated_at->format('d/m/Y H:i:s') }}</p>
                    </div>

                    <div class="d-flex gap-2 mt-3">
                        <a href="{{ route('expenses.change-logs', $expense->id) }}" class="btn btn-outline-info btn-sm d-flex align-items-center gap-1">
                            <iconify-icon icon="hugeicons:time-management-circle" class="me-1"></iconify-icon>
                            Change Logs
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1" aria-labelledby="statusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalLabel">Update Expense Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="statusForm">
                <div class="modal-body">
                    <input type="hidden" id="expenseId" name="expense_id" value="{{ $expense->id }}">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                            <option value="paid">Paid</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
    function updateStatus(expenseId) {
        $('#expenseId').val(expenseId);
        $('#statusModal').modal('show');
    }

    $(document).ready(function() {
        // Status update form
        $('#statusForm').on('submit', function(e) {
            e.preventDefault();
            
            $.ajax({
                url: "{{ route('expenses.update-status') }}",
                method: 'POST',
                data: $(this).serialize(),
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        $('#statusModal').modal('hide');
                        location.reload(); // Reload to show updated status
                    }
                },
                error: function(xhr) {
                    alert('Error updating status');
                }
            });
        });
    });
</script>
@endsection
