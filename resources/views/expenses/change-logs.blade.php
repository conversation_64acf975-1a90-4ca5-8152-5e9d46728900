@extends('layouts.master')

@section('title', 'Expense Change Logs')

@section('content')
<div class="dashboard-main-body">
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Expense Change Logs</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">
                <a href="{{ route('expenses.index') }}" class="d-flex align-items-center gap-1 hover-text-primary">
                    Expenses
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">
                <a href="{{ route('expenses.show', $expense->id) }}" class="d-flex align-items-center gap-1 hover-text-primary">
                    {{ $expense->title }}
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Change Logs</li>
        </ul>
    </div>

    <div class="row gy-4">
        <!-- Expense Summary Card -->
        <div class="col-lg-4">
            <div class="card h-100 p-0 radius-12">
                <div class="card-header border-bottom bg-base py-16 px-24">
                    <h6 class="text-lg fw-semibold mb-0">Expense Summary</h6>
                </div>
                <div class="card-body p-24">
                    <div class="mb-20">
                        <label class="form-label fw-semibold text-primary-light text-sm mb-8">Title</label>
                        <p class="text-secondary-light mb-0">{{ $expense->title }}</p>
                    </div>
                    <div class="mb-20">
                        <label class="form-label fw-semibold text-primary-light text-sm mb-8">Amount</label>
                        <p class="text-secondary-light mb-0 fw-semibold text-success-600">₹{{ number_format($expense->amount, 2) }}</p>
                    </div>
                    <div class="mb-20">
                        <label class="form-label fw-semibold text-primary-light text-sm mb-8">Category</label>
                        <p class="text-secondary-light mb-0">
                            <span class="badge" style="background-color: {{ $expense->category->color }}; color: white;">
                                {{ $expense->category->name }}
                            </span>
                        </p>
                    </div>
                    <div class="mb-20">
                        <label class="form-label fw-semibold text-primary-light text-sm mb-8">Status</label>
                        <p class="text-secondary-light mb-0">
                            @php
                                $badges = [
                                    'pending' => 'warning',
                                    'approved' => 'success',
                                    'rejected' => 'danger',
                                    'paid' => 'info'
                                ];
                                $class = $badges[$expense->status] ?? 'secondary';
                            @endphp
                            <span class="badge bg-{{ $class }}">{{ ucfirst($expense->status) }}</span>
                        </p>
                    </div>
                    <div class="d-flex gap-2 mt-3">
                        <a href="{{ route('expenses.show', $expense->id) }}" class="btn btn-outline-primary btn-sm d-flex align-items-center gap-1">
                            <iconify-icon icon="majesticons:eye-line" class="me-1"></iconify-icon>
                            View Details
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Change Logs Card -->
        <div class="col-lg-8">
            <div class="card h-100 p-0 radius-12">
                <div class="card-header border-bottom bg-base py-16 px-24">
                    <h6 class="text-lg fw-semibold mb-0">Change History</h6>
                </div>
                <div class="card-body p-24">
                    @if($changeLogs->count() > 0)
                        <div class="timeline">
                            @foreach($changeLogs as $log)
                                <div class="timeline-item mb-4">
                                    <div class="d-flex align-items-start gap-3">
                                        <div class="timeline-marker">
                                            @php
                                                $actionIcons = [
                                                    'created' => 'hugeicons:add-circle',
                                                    'updated' => 'lucide:edit',
                                                    'deleted' => 'fluent:delete-24-regular',
                                                    'status_changed' => 'hugeicons:checkmark-circle-02'
                                                ];
                                                $actionColors = [
                                                    'created' => 'success',
                                                    'updated' => 'primary',
                                                    'deleted' => 'danger',
                                                    'status_changed' => 'warning'
                                                ];
                                                $icon = $actionIcons[$log->action] ?? 'hugeicons:time-management-circle';
                                                $color = $actionColors[$log->action] ?? 'secondary';
                                            @endphp
                                            <div class="w-40-px h-40-px bg-{{ $color }}-100 text-{{ $color }}-600 rounded-circle d-flex justify-content-center align-items-center">
                                                <iconify-icon icon="{{ $icon }}" class="text-lg"></iconify-icon>
                                            </div>
                                        </div>
                                        <div class="timeline-content flex-grow-1">
                                            <div class="card border-0 bg-light">
                                                <div class="card-body p-3">
                                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                                        <h6 class="mb-0 text-{{ $color }}-600">{{ ucfirst(str_replace('_', ' ', $log->action)) }}</h6>
                                                        <small class="text-muted">{{ $log->created_at->format('d/m/Y H:i:s') }}</small>
                                                    </div>
                                                    
                                                    @if($log->description)
                                                        <p class="mb-2 text-sm">{{ $log->description }}</p>
                                                    @endif
                                                    
                                                    @if($log->changed_fields && count($log->changed_fields) > 0)
                                                        <div class="mb-2">
                                                            <small class="text-muted fw-semibold">Changed Fields:</small>
                                                            <div class="mt-1">
                                                                @foreach($log->changed_fields as $field)
                                                                    <span class="badge bg-secondary me-1">{{ ucfirst(str_replace('_', ' ', $field)) }}</span>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                    @endif
                                                    
                                                    @if($log->old_values && $log->new_values)
                                                        <div class="row">
                                                            @foreach($log->changed_fields as $field)
                                                                @if(isset($log->old_values[$field]) || isset($log->new_values[$field]))
                                                                    <div class="col-12 mb-2">
                                                                        <small class="text-muted fw-semibold">{{ ucfirst(str_replace('_', ' ', $field)) }}:</small>
                                                                        <div class="d-flex gap-2 align-items-center">
                                                                            <span class="badge bg-danger-100 text-danger-600">
                                                                                {{ $log->old_values[$field] ?? 'N/A' }}
                                                                            </span>
                                                                            <iconify-icon icon="hugeicons:arrow-right-02" class="text-muted"></iconify-icon>
                                                                            <span class="badge bg-success-100 text-success-600">
                                                                                {{ $log->new_values[$field] ?? 'N/A' }}
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                @endif
                                                            @endforeach
                                                        </div>
                                                    @endif
                                                    
                                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                                        <small class="text-muted d-flex align-items-center">
                                                            <iconify-icon icon="hugeicons:user-account" class="me-1"></iconify-icon>
                                                            {{ $log->changed_by }}
                                                        </small>
                                                        @if($log->ip_address)
                                                            <small class="text-muted d-flex align-items-center">
                                                                <iconify-icon icon="hugeicons:location-01" class="me-1"></iconify-icon>
                                                                {{ $log->ip_address }}
                                                            </small>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <iconify-icon icon="hugeicons:time-management-circle" class="text-muted" style="font-size: 4rem;"></iconify-icon>
                            <h6 class="text-muted mt-3">No change logs found</h6>
                            <p class="text-muted">This expense has no recorded changes yet.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('css')
<style>
.timeline {
    position: relative;
}

.timeline-item {
    position: relative;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 40px;
    bottom: -16px;
    width: 2px;
    background-color: #e9ecef;
}

.timeline-marker {
    position: relative;
    z-index: 1;
}

.timeline-content {
    margin-left: 0;
}
</style>
@endsection
