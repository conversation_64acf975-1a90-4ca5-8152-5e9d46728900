<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="images/favicon.png" rel="icon" />
    <title>General Invoice - {{config('company.details.company_suffix')}}</title>
    <meta name="author" content="harnishdesign.net" />
    <!-- Web Fonts -->
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900"
      type="text/css"
    />
    <style>
      @media print {
        @page {
          size: A4;
          margin: 20mm 15mm 20mm 15mm;
        }
        .page-break {
          page-break-before: always;
        }
      }

      body {
        font-size: 14px;
        margin: 0;
        padding: 0;
        font-family: "Poppins", Arial, sans-serif;
        background-color: #fff;
        color: #333;
      }

      .container {
        max-width: 210mm;
        margin: 0 auto;
        position: relative;
      }

      .footer {
        border-top: 2px solid #e0e0e0;
        background: white;
      }

      .page-number:after {
        content: counter(page);
      }

      .watermark {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-45deg);
        font-size: 60px;
        color: rgba(0, 0, 0, 0.1);
        z-index: -1;
        font-weight: bold;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;
      }

      table,
      td,
      th {
        border-color: #e0e0e0;
      }

      td,
      th {
        text-align: left;
      }

      th {
        background-color: #f5f5f5;
        font-weight: 600;
      }

      h4 {
        font-size: 1.5rem;
        margin: 0;
        color: #2c3e50;
      }

      address,
      p {
        font-size: 14px;
        margin: 0;
        line-height: 1.5;
      }

      .text-end {
        text-align: right;
      }

      .text-center {
        text-align: center;
      }

      .qr-code {
        width: 100px;
        height: 100px;
        object-fit: contain;
      }

	  .qr-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .qr-label {
        font-size: 10px;
        margin-top: 2px;
        color: #555;
        text-transform: uppercase;
        letter-spacing: 0.4px;
      }

      .amount-in-words {
        background-color: #f8f9fa;
        padding: 4px;
        border-radius: 5px;
      }

      .signature-line {
        border-top: 1px solid #333;
        width: 200px;
        margin: 40px auto 0;
      }
       .invoice-page { page-break-after: always; } /* Default page break */
        .invoice-page:last-child { page-break-after: avoid; } /* Prevents blank page */
    </style>
  </head>

  <body
    data-new-gr-c-s-check-loaded="14.1222.0"
    data-gr-ext-installed=""
    data-new-gr-c-s-loaded="14.1222.0"
  >
    {{-- <div class="watermark">DRAFT</div> --}}
   @foreach ($invoices as $invoice)
        <div class="invoice-page">
    <div class="container">
      <!-- ========================= HEADER ========================= -->
      <div class="header">
        <table border="0" cellpadding="5" cellspacing="0" width="100%">
          <tbody>
            <tr>
              <!-- ====== Company Logo & Validation QR ====== -->
              <td style="width: 50%; text-align: left; vertical-align: top;">
                <table border="0" cellpadding="0" cellspacing="0">
                    <tr>
                    <td style="width:150px;">
                  @php
                    if(isset($print)){
                        $logoPath = asset('storage/'.$company_details['logo']);
                    }else{
                        $logoPath = public_path('storage/'.$company_details['logo']);
                    }
                    @endphp

                    <img src="{{ $logoPath }}"
                    alt="Company Logo"
                    style="width: 150px; height: auto;"
                  /></td>
                  @php
                       $controller = app()->make(App\Http\Controllers\InvoiceController::class);
                        $verificationUrl = $controller->verify_invoice($invoice->id);
                  @endphp
                  <!-- Validation QR (replace src with dynamic QR code) -->

                    <td style="width:110px; text-align:center;">
                        <table border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td>
                    <img
                      src="{{ $verificationUrl }}"
                      alt="Validate invoice online"
                      class="qr-code"
                    /></td>
                        </tr>
                        <tr>
                            <td class="qr-label">Validate Online</td>
                        </tr>
                        </table>
                    </td>
                    </tr>
                </table>
              </td>

              <!-- ====== Company Contact ====== -->
              <td
                style="
                  width: 50%;
                  text-align: right;
                  font-size: 14px;
                  line-height: 1.5;
                  vertical-align: top;
                "
              >
                <strong>{{$company_details['legal_name']}}</strong><br />
                        {!! (($company_details['address']) ? nl2br($company_details['address']) . '<br>' : '') !!}
                        {!! (($company_details['gst'])?'<strong> GSTIN:</strong>'.$company_details['gst'].' <br>':'') !!}
                        {!! (($company_details['website'])?'<strong> Website:</strong>'.$company_details['website'].' <br>':'') !!}
                        {!! (($company_details['email'])?'<strong> Email:</strong>'.$company_details['email'].' <br>':'') !!}
                        {!! (($company_details['phone'])?'<strong> Phone:</strong>'.$company_details['phone']:'') !!}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- ========================= TITLE ========================= -->
      <table border="0" style="margin-top: 10px;">
        <tbody>
          <tr>
            <td align="center">
              <h4 style="font-size: 24px; color: #2c3e50; text-align: center;">
                TAX INVOICE
              </h4>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- ========================= PARTY INFO ========================= -->
      <table border="1" cellpadding="0" cellspacing="0">
        <tbody>
          <tr>
            <td style="width: 50%">
              <table border="0" cellpadding="5" cellspacing="0">
                <tr>
                  <td>
                    <p><strong>GSTIN:</strong> 36**********2ZV</p>
                    <p><strong>PAN:</strong> **********</p>
                  </td>
                </tr>
                <tr>
                  <td>
                    <p><strong>Buyer (Bill to):</strong> <strong>{{$invoice->client->business_name}}</strong></p>
                    <p>
                      {{$invoice->client->address}}, {{$invoice->client->city}},
                    </p>
                    <p>{{$invoice->client->district->name}}, {{$invoice->client->state->name}}, {{$invoice->client->pincode}}</p>
                  </td>
                </tr>
              </table>
            </td>
            <td style="width: 50%" class="text-end">
              <table border="0" cellpadding="3" cellspacing="0">
                <tr>
                  <td><strong>Client Id:</strong> {{$invoice->client->client_code}}</td>
                </tr>
                <tr>
                  <td><strong>Invoice No:</strong>  {{$invoice->invoice_code}}</td>
                </tr>
                <tr>
                  <td><strong>Date:</strong> {{$invoice->invoice_date? date('d-m-Y', strtotime($invoice->invoice_date)):'NA' }}</td>
                </tr>
                @if($invoice->from_invoice_to)
                <tr>
                  <td><strong>Duration Period:</strong> {{$invoice->from_invoice_to}}</td>
                </tr>
                @endif
                <tr>
                  <td><strong>State :</strong> Telangana , <strong>State Code :</strong> 36</td>
                </tr>
              </table>
            </td>
          </tr>
        </tbody>
      </table>
        @php
            $service_type = $invoice->service_id;
            $show_bedded_details = ($service_type == 1);
            $show_weight_details = ($service_type == 3);
            $show_units_for_fixed = ($service_type == 4);
            $show_simple_format = ($service_type == 2 || $service_type == 4);
        @endphp
      <!-- ========================= INVOICE DETAILS ========================= -->
      <table border="1" cellpadding="5" cellspacing="0" style="margin-top: 10px;">
        <thead>
          <tr>
            <th>S.No</th>
            <th>Description</th>
            <th class="text-center">HSN/SAC Code</th>
             @if($show_bedded_details)
                <th>No of Days for Invoice</th>
                <th>No of Beds</th>
                <th class="text-center">Rate (Per Unit/Day)</th>
            @elseif($show_weight_details)
                <th>Weight (KG)</th>
                <th class="text-center">Rate (Per KG)</th>
            @elseif($show_units_for_fixed)
                <th>No of Units</th>
                <th class="text-center">Fixed Rate</th>
            @else
                <th>Qty</th>
                <th class="text-center">Rate</th>
            @endif
            <th class="text-end">Amount</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>1</td>
            <td>Service Charges for {{$invoice->service_type_data->name}}
              @if($service_type == 1)
                (Bedded Service)
              @elseif($service_type == 2)
                (Non-Bedded Service)
              @elseif($service_type == 3)
                (Weight-Based Service)
              @elseif($service_type == 4)
                (Bedded with Fixed Price)
              @endif
              <br>{{$invoice->comments}}
            </td>
            <td class="text-center">9993</td>
            @if($show_bedded_details)
                <td class="text-center">{{$invoice->days_for_invoice}}</td>
                <td>{{$invoice->beds_count}}</td>
                <td class="text-center" style="font-family: 'DejaVu Sans', sans-serif;">₹{{number_format($invoice->unit_price,2)}}</td>
            @elseif($show_weight_details)
                <td class="text-center">{{ number_format($invoice->weight_qty ?? 0, 2) }} KG</td>
                <td class="text-center" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($invoice->unit_price ?? 0, 2) }}</td>
            @elseif($show_units_for_fixed)
                <td class="text-center">{{ $invoice->beds_count ?? 1 }}</td>
                <td class="text-center" style="font-family: 'DejaVu Sans', sans-serif;">₹{{number_format($invoice->gross_amount,2)}}</td>
            @else
                <td>1</td>
                <td style="font-family: 'DejaVu Sans', sans-serif;">₹{{number_format($invoice->gross_amount,2)}}</td>
            @endif
            <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{$invoice->gross_amount}}</td>
        </tr>
        </tbody>
        <tfoot>
          @php
            $colspan = 5; // Default for simple format
            if ($show_bedded_details) $colspan = 6;
            elseif ($show_weight_details) $colspan = 5;
            elseif ($show_units_for_fixed) $colspan = 5;
          @endphp
          <tr>
                <td colspan="{{$colspan}}" class="text-end"><strong>Sub Total:</strong></td>
                <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{number_format($invoice->gross_amount,2)}}</td>
            </tr>
            @php
                $isWithoutGst = $invoice->cgst_amount == 0 && $invoice->sgst_amount == 0;
                $gstRate = $isWithoutGst ? '0' : '6';
            @endphp
            <tr>
                <td colspan="{{$colspan}}" class="text-end"><strong>CGST @ {{ $gstRate }}%:</strong></td>
                <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{number_format($invoice->cgst_amount,2)}}</td>
            </tr>
            <tr>
                <td colspan="{{$colspan}}" class="text-end"><strong>SGST @ {{ $gstRate }}%:</strong></td>
                <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{number_format($invoice->sgst_amount,2)}}</td>
            </tr>
            <tr>
                <td colspan="{{$colspan}}" class="text-end"><strong>Total:</strong></td>
                <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{number_format($invoice->total_amount_due,2)}}</td>
            </tr>
            {{-- <tr>
                <td colspan="{{$colspan + 1}}" class="text-end" style="font-family: 'DejaVu Sans', sans-serif;"><strong>Payment Outstanding as on Date Before Submission:</strong> ₹{{$invoice->client_pending_amount_before_invoice}}</td>
            </tr> --}}
            @if($invoice->paid_amount>0)
            <tr>
                <td colspan="{{$colspan + 1}}" class="text-end"><strong>Payment
                        Made:</strong>
                    ₹{{ number_format($invoice->paid_amount,2) }}</td>
            </tr>
            @endif
            <tr>
                <td colspan="{{$colspan + 1}}" class="text-end" style="font-family: 'DejaVu Sans', sans-serif;"><strong>Due Amount:</strong> ₹{{ number_format($invoice->unpaid_amount,2) }}</td>
            </tr>
            {{-- <tr>
                <td colspan="{{$colspan + 1}}" class="text-end" style="font-family: 'DejaVu Sans', sans-serif;"><strong>Grand Total:</strong> ₹{{$invoice->grand_total}}</td>
            </tr> --}}
            @php
                $formatter = new NumberFormatter('en', NumberFormatter::SPELLOUT);
                $amountInWords = ucfirst($formatter->format($invoice->unpaid_amount));
            @endphp
          <tr>
                    <td colspan="{{$colspan + 1}}" class="text-end border-bottom-0"><strong>Amount Chargable (in Words):</strong> INR {{ $amountInWords }} only</td>
                </tr>
        </tfoot>
      </table>

      <!-- ========================= FOOTER ========================= -->
      <div class="footer" style="margin-top: 10px;">
        <table>
          <tbody>
            <tr>
              <!-- ======= Company Bank Details + QR ======= -->
              <td style="width: 50%;">
                <table border="0" cellpadding="0" cellspacing="0">
            <tr>
              <td style="vertical-align:top;">
                 {!! (($company_details['bank_details']) ? '<p style="margin-bottom: 10px;"><strong>Company\'s Bank Details</strong></p>' . nl2br($company_details['bank_details']) . '<br>' : '') !!}<br>
              </td>
              <td style="width:110px;text-align:center;">
                <table border="0" cellpadding="0" cellspacing="0">
                    @php
                        $controller = app()->make(App\Http\Controllers\InvoiceController::class);
                        $qr_url = $controller->payment_qr($invoice->unpaid_amount);
                    @endphp
                    <tr><td>
                    <img src="{{ $qr_url }}"
                      class="qr-code" /></td></tr>
                    <tr><td class="qr-label">Scan to Pay Online</td></tr>
                </table>
              </td>
            </tr>
          </table>

              <!-- ======= Signature ======= -->
              <td style="width: 50%;" class="text-end">
                <p><strong>For {{$company_details['legal_name']}}</strong><br />
                  @if (!empty($company_details['auth_sign']))
                        @php
                    if(isset($print)){
                        $logoPath = asset('storage/'.$company_details['auth_sign']);
                    }else{
                        $logoPath = public_path('storage/'.$company_details['auth_sign']);
                    }
                    @endphp
                    <img src="{{ $logoPath }}" alt="Company" alt="Company" class="img-fluid" width="70">
                        @else
                        <br><br><br>
                        @endif
                </p>
                <p style="margin-top: 5px;">Authorized Signatory</p>
              </td>
            </tr>
          </tbody>
        </table>
        <hr style="margin: 5px 0; border-color: #e0e0e0;" />
        <table>
          <tbody>
            <tr>
              <td style="width: 50%">
                <p><strong>Declaration:</strong></p>
                <p style="font-size: 12px;">
                  We declare that this invoice shows the actual price of the
                  services described and that all particulars are true and
                  correct.
                </p>
              </td>
              <td style="width: 50%" class="text-end">
                <p><strong>NOTE:</strong></p>
                <p style="font-size: 12px;">
                  This is a computer-generated receipt and does not require a
                  physical signature.
                </p>
              </td>
            </tr>
            <tr>
                <td style="width:50%">
                    <p>Printed On: {{date('d-m-Y h:i A')}}.</p>
                </td>
                <td style="width: 50%" class="text-end">
                    <div class="page-number">Page </div>
                </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
</div>
    @endforeach
  </body>
</html>




