@php
    $title = 'Invoice';
    $moduleTitle = 'Invoices';
    $indexRoute = route('invoices.index');
    $viewRoute = route('invoices.show', $invoice->id);
    $entityInfo = [
        'Invoice ID' => $invoice->id,
        'Invoice Code' => $invoice->invoice_code ?? 'N/A',
        'Client' => $invoice->client->name . ' (' . $invoice->client->client_code . ')',
        'Invoice Date' => $invoice->invoice_date ? date('d-m-Y', strtotime($invoice->invoice_date)) : 'N/A',
        'Due Date' => $invoice->due_date ? date('d-m-Y', strtotime($invoice->due_date)) : 'N/A',
        'Total Amount' => '<span class="text-success fw-bold">₹' . number_format($invoice->total_amount_due ?? 0, 2) . '</span>',
        'Status' => $invoice->invoice_status ?? 'N/A',
        'Created At' => $invoice->created_at->format('d-m-Y h:i A')
    ];
    $changeLogs = $invoice->changeLogs()->orderBy('created_at', 'desc')->get();
@endphp

@include('shared.change-logs', [
    'title' => $title,
    'moduleTitle' => $moduleTitle,
    'indexRoute' => $indexRoute,
    'viewRoute' => $viewRoute,
    'entityInfo' => $entityInfo,
    'changeLogs' => $changeLogs
])
