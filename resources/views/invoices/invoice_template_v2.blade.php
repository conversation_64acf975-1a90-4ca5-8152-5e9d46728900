<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="images/favicon.png" rel="icon" />
    <title>General Invoice - {{ config('company.details.company_suffix') }}</title>

    <!-- Web Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900" />

    <style>
      /***********************  PRINT SETTINGS  ************************/
      @media print {
        @page {
          size: A4;
          margin: 20mm 15mm 20mm 15mm; /* top right bottom left */
        }
        .page-break {
          page-break-before: always;
        }
      }

      /***********************  BASE  ************************/
      body {
        margin: 0;
        padding: 0;
        font-family: "Poppins", Arial, sans-serif;
        font-size: 14px;
        background-color: #fff;
        color: #333;
      }
      p{
        margin: 0;
        padding: 0;
      }

      .container {
        max-width: 210mm; /* full printable width of A4 */
        margin: 0 auto;
        position: relative;
      }

      h4 {
        margin: 0;
        font-size: 24px;
        color: #2c3e50;
      }

      .text-end   { text-align: right; }
      .text-center{ text-align: center; }
      .fw-bold    { font-weight: 600; }
      .text-upper { text-transform: uppercase; }

      /***********************  TABLES  ************************/
      table {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;
      }

      /* Global 1‑px border so print/PDF never drops grid lines */
      table,
      th,
      td {
        border: 1px solid #b0b0b0;
      }

      /* Utility to suppress borders where we *don’t* want them */
      .no-border,
      .no-border > th,
      .no-border > td {
        border: none !important;
      }

      th {
        background-color: #f5f5f5;
        font-weight: 600;
      }

      td,
      th {
        padding: 6px 4px;
        vertical-align: top;
      }

      /* Avoid cutting rows across pages */
      tr { page-break-inside: avoid; }

      /***********************  QR / MISC.  ************************/
      .qr-code {
        width: 80px;
        height: 800x200px0px;
        object-fit: contain;
        border: none;
      }
      .qr-label {
        font-size: 10px;
        margin-top: 2px;
        color: #555;
        text-transform: uppercase;
        letter-spacing: 0.4px;
      }

      /***********************  AMOUNT IN WORDS & TOTALS  ************************/
      .amount-in-words {
        background-color: #f8f9fa;
        padding: 4px;
        border-radius: 4px;
      }

      /***********************  FOOTER  ************************/
      .footer {
        margin-top: 10px;
        border-top: 2px solid #e0e0e0;
        padding-top: 4px;
      }
      .footer-note { font-size: 12px; }
    </style>
  </head>
  <body>
    <div class="container">
      <!--========================= HEADER =========================-->
      <table class="no-border" cellpadding="5" cellspacing="0">
        <tr class="no-border">
          <!-- Logo + Validation QR (nested table keeps its own alignment) -->
          <td class="no-border" style="width: 50%;">
            <table class="no-border" cellpadding="0" cellspacing="0">
              <tr class="no-border">
                <td class="no-border" style="width: 150px;">
                  @php
                    $logoPath = isset($print)
                      ? asset('storage/'.$company_details['logo'])
                      : public_path('storage/'.$company_details['logo']);
                  @endphp
                  <img src="{{ $logoPath }}" alt="Company Logo" style="width: auto; height: 50px;" />
                </td>
                <td class="no-border" style="width: 110px; text-align: center;">
                  <table class="no-border" cellpadding="0" cellspacing="0">
                    <tr class="no-border">
                      <td class="no-border">
                        <img src="{{ $verificationUrl }}" alt="Validate invoice online" class="qr-code" />
                      </td>
                    </tr>
                    <tr class="no-border"><td class="no-border qr-label">Validate Online</td></tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>

          <!-- Company contact details -->
          <td class="no-border" style="width: 50%; text-align: right; line-height: 1.5;">
            <strong>{{ $company_details['legal_name'] }}</strong><br />
            {!! $company_details['address'] ? nl2br($company_details['address']).'<br>' : '' !!}
            {!! $company_details['gst']     ? '<strong>GSTIN:</strong> '.$company_details['gst'].'<br>' : '' !!}
            {!! $company_details['website'] ? '<strong>Website:</strong> '.$company_details['website'].'<br>' : '' !!}
            {!! $company_details['email']   ? '<strong>Email:</strong> '.$company_details['email'].'<br>' : '' !!}
            {!! $company_details['phone']   ? '<strong>Phone:</strong> '.$company_details['phone'] : '' !!}
          </td>
        </tr>
      </table>

      <!--========================= TITLE =========================-->
      <table class="no-border" style="margin-top: 10px;">
        <tr class="no-border">
          <td class="no-border text-center">
            <h4 class="text-upper">Tax Invoice</h4>
          </td>
        </tr>
      </table>

      <!--========================= PARTY INFO =========================-->
      <table cellpadding="0" cellspacing="0">
        <tr>
          <!-- Buyer / GST panel -->
          <td style="width: 50%;">
            <table class="no-border" cellpadding="5" cellspacing="0">
              @if(optional($invoice->client)->gst || optional($invoice->client)->pan)
                <tr class="no-border">
                    <td class="no-border">
                        @if(optional($invoice->client)->gst)
                            <p><strong>GSTIN:</strong> {{ $invoice->client->gst }}</p>
                        @endif

                        @if(optional($invoice->client)->pan)
                            <p><strong>PAN:</strong> {{ $invoice->client->pan }}</p>
                        @endif
                    </td>
                </tr>
            @endif

              <tr class="no-border">
                <td class="no-border">
                  <p><strong>Buyer (Bill to):</strong> <strong>{{ $invoice->client->business_name }}</strong></p>
                  <p>{{ $invoice->client->address }}, {{ $invoice->client->city }}</p>
                  <p>{{ $invoice->client->district->name }}, {{ $invoice->client->state->name }}, {{ $invoice->client->pincode }}</p>
                </td>
              </tr>
            </table>
          </td>

          <!-- Invoice meta panel -->
          <td style="width: 50%;">
            <table class="no-border" cellpadding="3" cellspacing="0" style="width: 100%;">
              <tr class="no-border"><td class="no-border"><strong>Client Id:</strong> {{ $invoice->client->client_code }}</td></tr>
              <tr class="no-border"><td class="no-border"><strong>Invoice No:</strong> {{ $invoice->invoice_code }}</td></tr>
              <tr class="no-border"><td class="no-border"><strong>Date:</strong> {{ $invoice->invoice_date ? date('d-m-Y', strtotime($invoice->invoice_date)) : 'NA' }}</td></tr>
              @if($invoice->from_invoice_to)
              <tr class="no-border"><td class="no-border"><strong>Duration Period:</strong> {{ $invoice->from_invoice_to }}</td></tr>
              @endif
              <tr class="no-border"><td class="no-border"><strong>State :</strong> Telangana , <strong>State Code :</strong> 36</td></tr>
            </table>
          </td>
        </tr>
      </table>

      <!--========================= INVOICE DETAILS =========================-->
      @php
        $has_invoice_items = $invoice->isInventoryInvoice();
        $service_type = $invoice->service_id;
        $is_true_inventory_invoice = $has_invoice_items && is_null($service_type);
        $is_weight_range_invoice = $has_invoice_items && ($service_type == 3);
        $show_bedded_details = !$has_invoice_items && ($service_type == 1);
        $show_weight_details = !$has_invoice_items && ($service_type == 3);
        $show_units_for_fixed = !$has_invoice_items && ($service_type == 4);
        $show_simple_format = !$has_invoice_items && ($service_type == 2 || $service_type == 4);
      @endphp

      @if($is_true_inventory_invoice)
      <!-- Inventory Invoice Indicator -->
      <table cellpadding="5" cellspacing="0" style="margin-top: 10px; background-color: #e3f2fd; border: 1px solid #2196f3;">
        <tr>
          <td style="text-align: center; font-weight: bold; color: #1976d2;">
            📦 INVENTORY INVOICE - {{ $invoice->invoiceItems->count() }} Item(s) | Total Qty: {{ $invoice->invoiceItems->sum('quantity') }}
          </td>
        </tr>
      </table>
      @elseif($is_weight_range_invoice)
      <!-- Weight Range Invoice Indicator -->
      <table cellpadding="5" cellspacing="0" style="margin-top: 10px; background-color: #e8f5e8; border: 1px solid #4caf50;">
        <tr>
          <td style="text-align: center; font-weight: bold; color: #2e7d32;">
            ⚖️ WEIGHT-BASED SERVICE INVOICE - {{ $invoice->invoiceItems->count() }} Range(s) | Total Weight: {{ number_format($invoice->invoiceItems->sum('quantity'), 2) }} KG
          </td>
        </tr>
      </table>
      @endif
      <table cellpadding="5" cellspacing="0" style="margin-top: 10px;">
        <thead>
          <tr>
            <th>S.No</th>
            <th>Description</th>
            <th class="text-center">HSN/SAC Code</th>
            @if($has_invoice_items)
              @if($is_true_inventory_invoice)
                <th>SKU</th>
                <th>Quantity</th>
                <th class="text-center">Unit Price</th>
                <th class="text-center">GST Rate</th>
              @else
                <th>Range</th>
                <th>Weight (KG)</th>
                <th class="text-center">Rate per KG</th>
                <th class="text-center">GST Rate</th>
              @endif
            @elseif($show_bedded_details)
              <th>No of Days for Invoice</th>
              <th>No of Beds</th>
              <th class="text-center">Rate (Per Unit/Day)</th>
            @elseif($show_weight_details)
              <th>Weight (KG)</th>
              <th class="text-center">Rate (Per KG)</th>
            @elseif($show_units_for_fixed)
              <th>No of Units</th>
              <th class="text-center">Fixed Rate</th>
            @else
              <th>Qty</th>
              <th class="text-center">Rate</th>
            @endif
            <th class="text-end">Amount</th>
          </tr>
        </thead>
        <tbody>
          @if($has_invoice_items)
            @foreach($invoice->invoiceItems as $index => $item)
              <tr>
                <td>{{ $index + 1 }}</td>
                <td>{{ $item->item_name }}
                  @if($item->item_description)
                    <br><small>{{ $item->item_description }}</small>
                  @endif
                </td>
                <td class="text-center">9993</td>
                @if($is_true_inventory_invoice)
                  <td class="text-center">{{ $item->item_sku }}</td>
                  <td class="text-center">{{ $item->quantity }} {{ $item->unit_of_measure }}</td>
                @else
                  <td class="text-center">{{ $item->item_sku }}</td>
                  <td class="text-center">{{ number_format($item->quantity, 2) }} {{ $item->unit_of_measure }}</td>
                @endif
                <td class="text-center" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($item->unit_price, 2) }}</td>
                <td class="text-center">
                  @if($item->is_gst_applicable)
                    {{ number_format($item->cgst_rate + $item->sgst_rate, 1) }}%
                    <br><small>(C:{{ number_format($item->cgst_rate, 1) }}% S:{{ number_format($item->sgst_rate, 1) }}%)</small>
                  @else
                    <small>Exempt</small>
                  @endif
                </td>
                <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($item->total_price, 2) }}</td>
              </tr>
            @endforeach
          @else
            <tr>
              <td>1</td>
              <td>Service Charges for {{ $invoice->service_type_data->name }}
                @if($service_type == 1)
                  (Bedded Service)
                @elseif($service_type == 2)
                  (Non-Bedded Service)
                @elseif($service_type == 3)
                  (Weight-Based Service)
                @elseif($service_type == 4)
                  (Bedded with Fixed Price)
                @endif
                <br>{{ $invoice->comments }}
              </td>
              <td class="text-center">9993</td>
              @if($show_bedded_details)
                <td class="text-center">{{ $invoice->days_for_invoice }}</td>
                <td>{{ $invoice->beds_count }}</td>
                <td class="text-center" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($invoice->unit_price,2) }}</td>
              @elseif($show_weight_details)
                <td class="text-center">{{ number_format($invoice->weight_qty ?? 0, 2) }} KG</td>
                <td class="text-center" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($invoice->unit_price ?? 0, 2) }}</td>
              @elseif($show_units_for_fixed)
                <td class="text-center">{{ $invoice->beds_count ?? 1 }}</td>
                <td class="text-center" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($invoice->gross_amount,2) }}</td>
              @else
                <td>1</td>
                <td style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($invoice->gross_amount,2) }}</td>
              @endif
              <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($invoice->gross_amount,2) }}</td>
            </tr>
          @endif
        </tbody>
        <tfoot>
          @php
            $colspan = 5; // Default for simple format
            if ($has_invoice_items) $colspan = 7; // S.No, Description, HSN/SAC, SKU, Quantity, Unit Price, GST Rate (7 columns before Amount)
            elseif ($show_bedded_details) $colspan = 6;
            elseif ($show_weight_details) $colspan = 5;
            elseif ($show_units_for_fixed) $colspan = 5;
          @endphp
          <tr>
            <td colspan="{{ $colspan }}" class="text-end"><strong>Sub Total:</strong></td>
            <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($invoice->gross_amount,2) }}</td>
          </tr>
          <tr>
            <td colspan="{{ $colspan }}" class="text-end"><strong>
              @if($has_invoice_items)
                Total CGST:
              @else
                @php
                  $isWithoutGst = $invoice->cgst_amount == 0 && $invoice->sgst_amount == 0;
                  $gstRate = $isWithoutGst ? '0' : '6';
                @endphp
                CGST @ {{ $gstRate }}%:
              @endif
            </strong></td>
            <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($invoice->cgst_amount,2) }}</td>
          </tr>
          <tr>
            <td colspan="{{ $colspan }}" class="text-end"><strong>
              @if($has_invoice_items)
                Total SGST:
              @else
                SGST @ {{ $gstRate }}%:
              @endif
            </strong></td>
            <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($invoice->sgst_amount,2) }}</td>
          </tr>
          <tr>
            <td colspan="{{ $colspan }}" class="text-end"><strong>Total:</strong></td>
            <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($invoice->total_amount_due,2) }}</td>
          </tr>
          @if($invoice->paid_amount>0)
          <tr>
            <td colspan="{{ $colspan + 1 }}" class="text-end" style="font-family: 'DejaVu Sans', sans-serif;"><strong>Payment Made:</strong> ₹{{ number_format($invoice->paid_amount,2) }}</td>
          </tr>
          @endif
          <tr>
            <td colspan="{{ $colspan + 1 }}" class="text-end" style="font-family: 'DejaVu Sans', sans-serif;"><strong>Due Amount:</strong> ₹{{ number_format($invoice->unpaid_amount,2) }}</td>
          </tr>
          @php
            $formatter = new NumberFormatter('en', NumberFormatter::SPELLOUT);
            $amountInWords = ucfirst($formatter->format($invoice->unpaid_amount));
          @endphp
          <tr>
            <td colspan="{{ $colspan + 1 }}" class="text-end"><strong>Amount Chargable (in Words):</strong> INR {{ $amountInWords }} only</td>
          </tr>
        </tfoot>
      </table>

      <!--========================= FOOTER =========================-->
      <div class="footer">
        <table class="no-border" cellpadding="0" cellspacing="0">
          <tr class="no-border">
            <!-- Bank details & Pay QR -->
            <td class="no-border" style="width: 50%;">
              <table class="no-border" cellpadding="0" cellspacing="0">
                <tr class="no-border">
                  <td class="no-border" style="vertical-align: top;">
                    {!! $company_details['bank_details'] ? '<p class="fw-bold" style="margin-bottom: 6px;">Company\'s Bank Details</p>'.nl2br($company_details['bank_details']) : '' !!}
                  </td>
                  <td class="no-border" style="width: 110px; text-align: center;">
                    <img src="{{ $qr_url }}" alt="Scan to Pay Online" class="qr-code" />
                    <div class="qr-label">Scan to Pay Online</div>
                  </td>
                </tr>
              </table>
            </td>

            <!-- Signature -->
            <td class="no-border" style="width: 50%; text-align: right;">
              <p><strong>For {{ $company_details['legal_name'] }}</strong></p>
              @if(!empty($company_details['auth_sign']))
                @php
                  $signPath = isset($print)
                    ? asset('storage/'.$company_details['auth_sign'])
                    : public_path('storage/'.$company_details['auth_sign']);
                @endphp
                <img src="{{ $signPath }}" alt="Authorized Signatory" style="width: 80px;" />
              @else
                <div style="height: 80px;"></div>
              @endif
              <p style="margin-top: 4px;">Authorized Signatory</p>
            </td>
          </tr>
        </table>
        <hr style="border-color: #e0e0e0; margin: 5px 0;" />
        <table class="no-border" cellpadding="0" cellspacing="0">
          <tr class="no-border">
            <td class="no-border text-center footer-note">
              This is a computer-generated invoice and does not require a physical signature.
            </td>
          </tr>
          <tr class="no-border">
            <td class="no-border footer-note">Printed On: {{ date('d-m-Y h:i A') }}</td>
          </tr>
        </table>
      </div>
    </div>
  </body>
</html>
