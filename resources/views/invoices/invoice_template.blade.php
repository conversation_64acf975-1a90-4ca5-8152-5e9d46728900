<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">
	<link href="images/favicon.png" rel="icon">
	<title>General Invoice - {{config('company.details.company_suffix')}}</title>
	<meta name="author" content="harnishdesign.net"><!-- Web Fonts -->
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900" type="text/css">
	<style>
		@media print {
			@page {
				size: A5;
				margin: 10mm
			}
		}

		body {
			font-size: 12px;
			margin: 0;
			padding: 0;
			font-family: Arial, Helvetica, sans-serif
		}

		table {
			width: 100%;
			border-collapse: collapse;
			font-size: 12px
		}

		table,
		td,
		th {
			border-color: #7e7e7e
		}

		td,
		th {
			padding: 5px;
			text-align: left
		}

		h4 {
			font-size: 1.2rem;
			margin: 0
		}

		address,
		p {
			font-size: 12px;
			margin: 0
		}

		.text-end {
			text-align: right
		}

		.text-center {
			text-align: center
		}

		.badge {
			display: inline-block;
			padding: .25em .4em;
			font-size: 75%;
			font-weight: 700;
			text-align: center;
			white-space: nowrap;
			vertical-align: baseline;
			border-radius: .375rem;
			background-color: #28a745;
			color: #fff
		}

		.footer p {
			font-size: 12px
		}
	</style>
</head>

<body data-new-gr-c-s-check-loaded="14.1222.0" data-gr-ext-installed="" data-new-gr-c-s-loaded="14.1222.0">
	<!-- Container -->
	<div class="container">
		<!-- Header -->
		<table border="0">
			<tbody>
				<tr>
					<td style="width:50%;text-align:left">
                    @php
                    if(isset($print)){
                        $logoPath = asset('storage/'.$company_details['logo']);
                    }else{
                        $logoPath = public_path('storage/'.$company_details['logo']);
                    }
                    @endphp

                    <img src="{{ $logoPath }}" alt="Company" style="height:34px">

                    </td>
					<td style="width:50%;text-align:right">
						<h4>Invoice: {{$invoice->invoice_code}}</h4>
					</td>
				</tr>
			</tbody>
		</table>
		<hr><!-- Main Content -->
		<table border="0">
			<tbody>
				<tr>
					<td><strong>Date:</strong> {{$invoice->invoice_date? date('d-m-Y', strtotime($invoice->invoice_date)):'NA' }}</td>
					<td class="text-end"><strong>Invoice Status:</strong>
                        @if ($invoice->invoice_status == 'Paid')
                            <span class="badge bg-success">Paid</span>
                        @elseif ($invoice->invoice_status == 'Pending')
                            <span class="badge bg-danger">Pending</span>
                        @elseif ($invoice->invoice_status == 'Partially Paid')
                            <span class="badge bg-warning">Partially Paid</span>
                        @endif
                    </td>
				</tr>
				<tr>
					<td><strong>Client Id:</strong>  {{$invoice->client->client_code}}</td>
					@if($invoice->from_invoice_to)
					<td class="text-end"><strong>Duration Period:</strong> {{$invoice->from_invoice_to}}</td>
					@else
					<td class="text-end"></td>
					@endif
				</tr>
			</tbody>
		</table>
		<hr>
		<table border="0">
			<tbody>
				<tr>
					<td style="width:50%"><strong>Invoiced To:</strong><strong>{{$invoice->client->business_name}}</strong>
						<address>{{$invoice->client->address}}, {{$invoice->client->city}}, {{$invoice->client->district->name}}, {{$invoice->client->state->name}}, {{$invoice->client->pincode}} </address>
					</td>
					<td style="width:50%" class="text-end"><strong>Pay To:</strong>
						<address>
                            {{$company_details['legal_name']}}<br>
                            {!! (($company_details['address']) ? nl2br($company_details['address']) . '<br>' : '') !!}
                            {!! (($company_details['gst'])?' GSTIN:'.$company_details['gst'].' <br>':'') !!}
                            {!! (($company_details['website'])?' Website:'.$company_details['website'].' <br>':'') !!}
                            {!! (($company_details['email'])?' Email:'.$company_details['email'].' <br>':'') !!}
                            {!! (($company_details['phone'])?' Phone:'.$company_details['phone']:'') !!}
                        </address>
					</td>
				</tr>
			</tbody>
		</table><!-- Invoice Details Table -->
        @php
            $service_type = $invoice->service_id;
            $has_invoice_items = $invoice->isInventoryInvoice();
            $is_true_inventory_invoice = $has_invoice_items && is_null($service_type);
            $is_weight_range_invoice = $has_invoice_items && ($service_type == 3);
            $show_bedded_details = !$has_invoice_items && ($service_type == 1);
            $show_weight_details = !$has_invoice_items && ($service_type == 3);
            $show_units_for_fixed = !$has_invoice_items && ($service_type == 4);
            $show_simple_format = !$has_invoice_items && ($service_type == 2 || $service_type == 4);
        @endphp
		<table border="1">
			<thead>
				<tr>
                    <th>S.No</th>
                    <th>Description</th>
                    <th class="text-center">HSN/SAC Code</th>
                    @if($has_invoice_items)
                        @if($is_true_inventory_invoice)
                            <th>SKU</th>
                            <th>Quantity</th>
                            <th class="text-center">Unit Price</th>
                        @else
                            <th>Range</th>
                            <th>Weight (KG)</th>
                            <th class="text-center">Rate (Per KG)</th>
                        @endif
                    @elseif($show_bedded_details)
                        <th>No of Days for Invoice</th>
                        <th>No of Beds</th>
                        <th class="text-center">Rate (Per Unit/Day)</th>
                    @elseif($show_weight_details)
                        <th>Weight (KG)</th>
                        <th class="text-center">Rate (Per KG)</th>
                    @elseif($show_units_for_fixed)
                        <th>No of Units</th>
                        <th class="text-center">Fixed Rate</th>
                    @else
                        <th>Qty</th>
                        <th class="text-center">Rate</th>
                    @endif
                    <th class="text-end">Amount</th>
				</tr>
			</thead>
			<tbody>
                @if($has_invoice_items)
                    @foreach($invoice->invoiceItems as $index => $item)
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>{{ $item->item_name }}
                                @if($item->item_description)
                                    <br><small>{{ $item->item_description }}</small>
                                @endif
                            </td>
                            <td class="text-center">9993</td>
                            @if($is_true_inventory_invoice)
                                <td class="text-center">{{ $item->item_sku }}</td>
                                <td class="text-center">{{ $item->quantity }} {{ $item->unit_of_measure }}</td>
                            @else
                                <td class="text-center">{{ $item->item_sku }}</td>
                                <td class="text-center">{{ number_format($item->quantity, 2) }} {{ $item->unit_of_measure }}</td>
                            @endif
                            <td class="text-center" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($item->unit_price, 2) }}</td>
                            <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($item->total_price, 2) }}</td>
                        </tr>
                    @endforeach
                    @if($invoice->comments)
                        <tr>
                            <td colspan="6" style="border-top: none; padding-top: 10px;">
                                <strong>Comments:</strong> {{ $invoice->comments }}
                            </td>
                        </tr>
                    @endif
                @else
                    <tr>
                        <td>1</td>
                        <td>Service Charges for {{$invoice->service_type_data->name}}
                          @if($service_type == 1)
                            (Bedded Service)
                          @elseif($service_type == 2)
                            (Non-Bedded Service)
                          @elseif($service_type == 3)
                            (Weight-Based Service)
                          @elseif($service_type == 4)
                            (Bedded with Fixed Price)
                          @endif
                          <br>{{$invoice->comments}}
                        </td>
                        <td class="text-center">9993</td>
                        @if($show_bedded_details)
                            <td class="text-center">{{$invoice->days_for_invoice}}</td>
                            <td>{{$invoice->beds_count}}</td>
                            <td class="text-center" style="font-family: 'DejaVu Sans', sans-serif;">₹{{number_format($invoice->unit_price,2)}}</td>
                        @elseif($show_weight_details)
                            <td class="text-center">{{ number_format($invoice->weight_qty ?? 0, 2) }} KG</td>
                            <td class="text-center" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($invoice->unit_price ?? 0, 2) }}</td>
                        @elseif($show_units_for_fixed)
                            <td class="text-center">{{ $invoice->beds_count ?? 1 }}</td>
                            <td class="text-center" style="font-family: 'DejaVu Sans', sans-serif;">₹{{number_format($invoice->gross_amount,2)}}</td>
                        @else
                            <td>1</td>
                            <td style="font-family: 'DejaVu Sans', sans-serif;">₹{{number_format($invoice->gross_amount,2)}}</td>
                        @endif
                        <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{$invoice->gross_amount}}</td>
                    </tr>
                @endif
			</tbody>
			<tfoot>
                @php
                    $colspan = 5; // Default for simple format
                    if ($has_invoice_items) $colspan = 5;
                    elseif ($show_bedded_details) $colspan = 6;
                    elseif ($show_weight_details) $colspan = 5;
                    elseif ($show_units_for_fixed) $colspan = 5;
                @endphp
				<tr>
                    <td colspan="{{$colspan}}" class="text-end"><strong>Sub Total:</strong></td>
                    <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{number_format($invoice->gross_amount,2)}}</td>
                </tr>
                @php
                    $isWithoutGst = $invoice->cgst_amount == 0 && $invoice->sgst_amount == 0;
                    $gstRate = $isWithoutGst ? '0' : '6';
                @endphp
                <tr>
                    <td colspan="{{$colspan}}" class="text-end"><strong>CGST @ {{$gstRate}}%:</strong></td>
                    <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{number_format($invoice->cgst_amount,2)}}</td>
                </tr>
                <tr>
                    <td colspan="{{$colspan}}" class="text-end"><strong>SGST @ {{$gstRate}}%:</strong></td>
                    <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{number_format($invoice->sgst_amount,2)}}</td>
                </tr>
                <tr>
                    <td colspan="{{$colspan}}" class="text-end"><strong>Total:</strong></td>
                    <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{number_format($invoice->total_amount_due,2)}}</td>
                </tr>
                <tr>
                    <td colspan="{{$colspan + 1}}" class="text-end" style="font-family: 'DejaVu Sans', sans-serif;"><strong>Payment Outstanding as on Date Before Submission:</strong> ₹{{number_format($invoice->client_pending_amount_before_invoice,2)}}</td>
                </tr><tr>
                    <td colspan="{{$colspan + 1}}" class="text-end" style="font-family: 'DejaVu Sans', sans-serif;"><strong>Grand Total:</strong> ₹{{number_format($invoice->grand_total,2)}}</td>
                </tr>
                @php
                    $formatter = new NumberFormatter('en', NumberFormatter::SPELLOUT);
                    $amountInWords = ucfirst($formatter->format($invoice->grand_total));
                @endphp
                <tr>
                    <td colspan="{{$colspan + 1}}" class="text-end border-bottom-0"><strong>Amount Chargable (in Words):</strong> INR {{ $amountInWords }} only</td>
                </tr>
			</tfoot>
		</table><!-- Footer -->
		<table>
			<tbody>
				<tr>
					<td style="width:50%">
                        {!! (($company_details['bank_details']) ? '<strong>Company\'s Bank Details</strong><br>' . nl2br($company_details['bank_details']) . '<br>' : '') !!}<br>
                        <img src="{{ $qr_url }}" alt="" width="100"><br>
                        <span>Scan to Pay </span>
                    </td>
					<td style="width:50%" class="text-end">For {{$company_details['legal_name']}}<br>
                        @if (!empty($company_details['auth_sign']))
                        @php
                    if(isset($print)){
                        $logoPath = asset('storage/'.$company_details['auth_sign']);
                    }else{
                        $logoPath = public_path('storage/'.$company_details['auth_sign']);
                    }
                    @endphp
                    <img src="{{ $logoPath }}" alt="Company" alt="Company" class="img-fluid" width="70">
                        @else
                        <br><br><br>
                        @endif
                    </td>
				</tr>
			</tbody>
		</table>
		<hr>
		<footer>
			<table>
				<tbody>
					<tr>
						<td style="width:50%">
							<p><strong>Declaration:</strong> We declare that this invoice shows the actual price of the services described and that all particulars are true and correct.</p>
						</td>
						<td style="width:50%" class="text-end">
							<p><strong>NOTE:</strong> This is a computer-generated receipt and does not require a physical signature.</p>
						</td>
					</tr>
                    <tr>
                        <td style="width:50%">
							<p>Printed On: {{date('d-m-Y h:i A')}}.</p>
						</td>
                    </tr>
				</tbody>
			</table>
		</footer>
	</div>
</body>
</html>
