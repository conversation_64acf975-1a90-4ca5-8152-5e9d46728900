@extends('layouts.master')
@section('title', 'Invoices Add - Paidash')
@section('content')
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">Add New Invoice</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="/invoices" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="hugeicons:invoice-03" class="icon text-lg"></iconify-icon>
                        Invoices
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">Add Invoice</li>
            </ul>
        </div>

        <div class="card">

            <div class="card-body py-40">
                <div class="row justify-content-center" id="invoice">
                    <div class="col-lg-12">
                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        <form action="{{ route('invoices.store') }}" method="POST">
                            @csrf
                            <div class="shadow-4 border radius-8">
                                <div class="p-20 border-bottom">
                                    <div class="row d-flex">
                                        <div class="col">
                                            <select id="client" name="client" class="form-control form-select select2 {{ $selectedClient ? 'border-success' : '' }}"
                                                title="select Client">
                                                <option value="" {{ !$selectedClient ? 'selected' : '' }}>Select Client</option>
                                                <option value="">All</option>
                                                @foreach ($clients as $client)
                                                    <option value="{{ $client->id }}"
                                                        {{ old('client', $selectedClient ? $selectedClient->id : '') == $client->id ? 'selected' : '' }}>
                                                        {{ $client->name.' - '. $client->client_code}}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @if($selectedClient)
                                                <small class="text-success mt-1  d-flex align-items-center">
                                                    <iconify-icon icon="solar:check-circle-bold" class="me-1"></iconify-icon>
                                                    Client "{{ $selectedClient->name }}" has been pre-selected for you
                                                </small>
                                            @endif
                                        </div>
                                        <div class="col">
                                            <input type="date" class="form-control" name="invoice_date" id="invoice_date"
                                                aria-describedby="helpId" placeholder="select Date" />
                                        </div>

                                    </div>
                                </div>

                                <div class="py-28 px-20">
                                    <div class="mt-24">
                                        <div class="table-responsive scroll-sm">
                                            <table class="table bordered-table text-sm" id="invoice-table">
                                                <thead>
                                                    <tr>
                                                        <th scope="col" class="text-sm">SL.</th>
                                                        <th scope="col" class="text-sm">Service Types</th>
                                                        <th scope="col" class="text-sm">Service Details </th>
                                                        <th scope="col" class="text-sm">Rate <span id="rate_label">(Per Unit)</span></th>
                                                        <th scope="col" class="text-sm">Qty <span id="qty_label">(Units)</span></th>
                                                        <th scope="col" class="text-sm">Price</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td class="align-top">01</td>
                                                        <td class="align-top">
                                                            <select class="form-control radius-8 form-select select2"
                                                                id="service_type" name="service_type">
                                                                <option selected disabled>Service Type </option>
                                                                @foreach ($service_types as $service_type)
                                                                    <option value="{{ $service_type->id }}">
                                                                        {{ $service_type->name }}
                                                                    </option>
                                                                @endforeach
                                                            </select>
                                                            <div class="mb-3">
                                                                <textarea class="form-control" name="comments" id="comments" rows="3" placeholder="Comments"></textarea>
                                                            </div>

                                                        </td>
                                                        <td class="align-top">

                                                            <select class="form-control radius-8 form-select select2"
                                                                id="service" name="service">
                                                                <option selected disabled>Service</option>
                                                                <option value="1">Bedded</option>
                                                                <option value="2">Non Bedded</option>
                                                                <option value="3">Weight Based</option>
                                                                <option value="4">Bedded with Fixed Price</option>
                                                            </select>
                                                            <div class="mt-2">
                                                                <small id="service_help" class="text-muted">
                                                                    Select a service type to configure rate and quantity fields
                                                                </small>
                                                            </div>
                                                        </td>
                                                        <td class="align-top"><input type="number"
                                                                class="form-control form-control-100" value="0"
                                                                 name="rate" id="rate"></td>
                                                        <td class="align-top"><input type="number"
                                                                class="form-control form-control-100" value="1"
                                                                name="qty" id="qty"></td>
                                                        <td class="align-top">₹<span id="item_amount"> 0</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="5"><span class="d-block text-right">Sub
                                                                Total:</span></td>
                                                        <td>₹ <span id="sub_total"> 0</span></td>
                                                    </tr>
                                                    {{-- <tr>
                                                <td colspan="5" ><span class="d-block text-right">Discount:</span></td>
                                                <td><input type="text" class="form-control form-control-150" placeholder="Discount"></td>
                                            </tr> --}}
                                                    <tr>
                                                        <td colspan="5"><span class="d-block text-right">CGST(6%):</span>
                                                        </td>
                                                        <td>₹<span id="cgst_amount"> 0</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="5"><span
                                                                class="d-block text-right">SGST(6%):</span></td>
                                                        <td>₹<span id="sgst_amount"> 0</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="5"><span class="d-block text-right">Total Amount
                                                                :</span></td>
                                                        <td>₹<span id="total_amount"> 0</span></td>
                                                    </tr>

                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- GST and Email Options -->
                                        <div class="row mt-3">
                                            <!-- GST Options -->
                                            <div class="col-md-6">
                                                <div class="card border border-warning-200 bg-warning-50">
                                                    <div class="card-body p-15">
                                                        <h6 class="fw-semibold text-warning-600 mb-2 d-flex align-items-center">
                                                            <iconify-icon icon="material-symbols:receipt-long" class="me-2"></iconify-icon>
                                                            GST Options
                                                        </h6>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="without_gst" id="without_gst">
                                                            <label class="form-check-label" for="without_gst">
                                                                <strong>Create invoice without GST</strong>
                                                            </label>
                                                        </div>
                                                        <small class="text-muted d-block mt-1" id="gst-info">
                                                            <iconify-icon icon="material-symbols:info" class="me-1"></iconify-icon>
                                                            When checked, CGST and SGST will be set to 0%.
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Email Options -->
                                            <div class="col-md-6">
                                                <div class="card border border-primary-200 bg-primary-50">
                                                    <div class="card-body p-15">
                                                        <h6 class="fw-semibold text-primary-600 mb-2 d-flex align-items-center">
                                                            <iconify-icon icon="material-symbols:email" class="me-2"></iconify-icon>
                                                            Email Options
                                                        </h6>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="send_email" id="send_email" checked>
                                                            <label class="form-check-label" for="send_email">
                                                                <strong>Send invoice email automatically</strong>
                                                            </label>
                                                        </div>
                                                        <small class="text-muted d-block mt-1" id="email-info">
                                                            <iconify-icon icon="material-symbols:info" class="me-1"></iconify-icon>
                                                            Email will be queued and sent based on environment settings.
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="d-flex flex-wrap align-items-center justify-content-end gap-2 mt-3">
                                            <a type="button" id="addRow" href="/invoices"
                                                class="btn btn-sm btn-neutral-500 radius-8 d-inline-flex align-items-center gap-1">
                                                Cancel
                                            </a>
                                            <button type="submit" id="addRow"
                                                class="btn btn-sm btn-primary-600 radius-8 d-inline-flex align-items-center gap-1 btn-submit">
                                                <iconify-icon icon="material-symbols:add" class="me-1"></iconify-icon>
                                                Create Invoice
                                            </button>
                                        </div>


                                    </div>

                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

    </div>
@stop

@section('script')
<script>
    $(document).ready(function () {
    $('.select2').select2();

    // Initialize form validation
    $("form").validate({
        rules: {
            client: { required: true },
            service_type: { required: true },
            invoice_date: { required: true },
            service: { required: true },
            rate: {
                required: true,
                min: function() {
                    return $("#service").val() ? 0.01 : 0;
                }
            },
            qty: {
                required: true,
                min: function() {
                    let serviceType = $("#service").val();
                    return (serviceType == "2" || serviceType == "4") ? 1 : 0.01;
                }
            },
        },
        messages: {
            client: "Please select a client",
            invoice_date: "Please select a date",
            service_type: "Please select a service type",
            service: "Please select a service",
            rate: "Please enter a valid rate greater than 0",
            qty: "Please enter a valid quantity greater than 0",
        },
        errorPlacement: function(error, element) {
                    // Handle Select2 validation error placement
                    if (element.hasClass("select2-hidden-accessible")) {
                        error.insertAfter(element.next('.select2-container'));
                    } else {
                        error.insertAfter(element);
                    }
                },
    submitHandler: function(form) {
        $(".btn-submit").prop("disabled", true).text("Processing..."); // Disable button and change text
        form.submit();
    }
    });
     // Ensure validation triggers on Select2 field change
     $('select[name="client"]').on('change', function () {
            $(this).valid();
        });

        // Trigger validation on blur for all fields
        $(document).on("focusout", "input, select, textarea", function () {
        var form = $(this).closest("form");
        if (form.data("validator")) {  // Check if the form has been initialized with validate()
            $(this).valid();
        }
    });

    function resetValues() {
        $("#rate").val(0);
        $("#qty").val(1);
        $("#item_amount, #sub_total, #cgst_amount, #sgst_amount, #total_amount").text("0.00");
        // Reset labels to default
        $("#rate_label").text("(Per Unit)");
        $("#qty_label").text("(Units)");
        // Reset placeholders
        $("#rate").attr("placeholder", "Enter rate");
        $("#qty").attr("placeholder", "Enter quantity");
    }

    $("#service").change(function () {
        let serviceType = $(this).val();
        resetValues();

        if (serviceType == "1") { // Bedded Service
            $("#rate, #qty").prop("readonly", false);
            $("#rate_label").text("(Per Unit/Day)");
            $("#qty_label").text("(No. of Units)");
            $("#rate").attr("placeholder", "Enter rate per unit per day");
            $("#qty").attr("placeholder", "Enter number of units");
            $("#service_help").text("Bedded Service: Enter rate per unit per day and number of units");

        } else if (serviceType == "2") { // Non Bedded Service
            $("#qty").val(1).prop("readonly", true);
            $("#rate").prop("readonly", false);
            $("#rate_label").text("(Total Amount)");
            $("#qty_label").text("(Fixed: 1)");
            $("#rate").attr("placeholder", "Enter total fixed amount");
            $("#qty").attr("placeholder", "1");
            $("#service_help").text("Non Bedded Service: Enter total fixed amount (quantity is fixed at 1)");

        } else if (serviceType == "3") { // Weight Based Service
            $("#rate, #qty").prop("readonly", false);
            $("#rate_label").text("(Per KG)");
            $("#qty_label").text("(Weight in KG)");
            $("#rate").attr("placeholder", "Enter rate per kg");
            $("#qty").attr("placeholder", "Enter weight in kg");
            $("#service_help").text("Weight Based Service: Enter rate per kg and total weight in kg");

        } else if (serviceType == "4") { // Bedded with Fixed Price
            $("#qty").val(1).prop("readonly", true);
            $("#rate").prop("readonly", false);
            $("#rate_label").text("(Fixed Amount)");
            $("#qty_label").text("(Fixed: 1)");
            $("#rate").attr("placeholder", "Enter fixed amount");
            $("#qty").attr("placeholder", "1");
            $("#service_help").text("Bedded with Fixed Price: Enter fixed amount (quantity is fixed at 1)");
        }

        // Trigger calculation after field setup
        calculateAmounts();
    });

    function calculateAmounts() {
        let rate = parseFloat($("#rate").val()) || 0;
        let qty = parseFloat($("#qty").val()) || 1;
        let serviceType = $("#service").val();
        let withoutGst = $("#without_gst").is(':checked');

        if (rate > 0 && qty > 0) {
            let itemAmount = 0;

            // Calculate based on service type
            switch(serviceType) {
                case "1": // Bedded Service
                    itemAmount = rate * qty; // rate per unit * number of units
                    break;
                case "2": // Non Bedded Service
                    itemAmount = rate; // fixed amount (qty is always 1)
                    break;
                case "3": // Weight Based Service
                    itemAmount = rate * qty; // rate per kg * weight in kg
                    break;
                case "4": // Bedded with Fixed Price
                    itemAmount = rate; // fixed amount (qty is always 1)
                    break;
                default:
                    itemAmount = rate * qty;
            }

            // Calculate GST based on checkbox
            let cgst = withoutGst ? 0 : itemAmount * 0.06;
            let sgst = withoutGst ? 0 : itemAmount * 0.06;
            let totalAmount = itemAmount + cgst + sgst;

            $("#item_amount").text(itemAmount.toFixed(2));
            $("#sub_total").text(itemAmount.toFixed(2));
            $("#cgst_amount").text(cgst.toFixed(2));
            $("#sgst_amount").text(sgst.toFixed(2));
            $("#total_amount").text(totalAmount.toFixed(2));

            // Update GST row labels and visibility
            updateGstDisplay(withoutGst);
        } else {
            $("#item_amount, #sub_total, #cgst_amount, #sgst_amount, #total_amount").text("0.00");
        }
    }

    // Function to update GST display based on checkbox
    function updateGstDisplay(withoutGst) {
        const cgstRow = $('td:contains("CGST")').closest('tr');
        const sgstRow = $('td:contains("SGST")').closest('tr');

        if (withoutGst) {
            // Update labels to show 0%
            $('td:contains("CGST(6%)")').html('<span class="d-block text-right text-muted">CGST(0%):</span>');
            $('td:contains("SGST(6%)")').html('<span class="d-block text-right text-muted">SGST(0%):</span>');

            // Add visual indication
            $("#cgst_amount, #sgst_amount").addClass('text-muted');
            cgstRow.addClass('table-secondary');
            sgstRow.addClass('table-secondary');
        } else {
            // Restore original labels
            $('td:contains("CGST(0%)")').html('<span class="d-block text-right">CGST(6%):</span>');
            $('td:contains("SGST(0%)")').html('<span class="d-block text-right">SGST(6%):</span>');

            // Remove visual indication
            $("#cgst_amount, #sgst_amount").removeClass('text-muted');
            cgstRow.removeClass('table-secondary');
            sgstRow.removeClass('table-secondary');
        }
    }

    $("#rate, #qty").on("change keyup focusout", function () {
        calculateAmounts();
    });

    // Handle GST checkbox change
    $("#without_gst").on("change", function () {
        calculateAmounts();
    });

    $("form").submit(function (e) {
        if (!$(this).valid()) {
            e.preventDefault();
        }
    });

    // Update email info when client changes
    $('#client').on('change', function() {
        updateEmailInfo();
    });

    // Trigger client change event if client is pre-selected (from URL parameter)
    @if($selectedClient)
        // Wait for Select2 to initialize, then trigger change event
        setTimeout(function() {
            $('#client').trigger('change');
        }, 1000);
    @endif

    function updateEmailInfo() {
        const clientId = $('#client').val();
        const emailInfo = $('#email-info');
        const sendEmailCheckbox = $('#send_email');

        if (!clientId) {
            emailInfo.html('<iconify-icon icon="material-symbols:info" class="me-1"></iconify-icon>Select a client to see email information.');
            return;
        }

        // Get client email info via AJAX
        $.get('/clients/' + clientId + '/email-info')
            .done(function(response) {
                let infoText = '';
                let iconClass = 'material-symbols:info';
                let textClass = 'text-muted';

                if (response.app_env === 'live') {
                    if (response.client_email) {
                        infoText = `Email will be sent to: ${response.client_email}`;
                        iconClass = 'material-symbols:check-circle';
                        textClass = 'text-success';
                    } else {
                        infoText = 'No email address found for this client. Email will not be sent.';
                        iconClass = 'material-symbols:warning';
                        textClass = 'text-warning';
                        sendEmailCheckbox.prop('checked', false);
                    }
                } else {
                    if (response.tester_email) {
                        infoText = `Test environment: Email will be sent to test address: ${response.tester_email}`;
                        iconClass = 'material-symbols:science';
                        textClass = 'text-info';
                    } else {
                        infoText = 'Test environment: No test email configured. Email will not be sent.';
                        iconClass = 'material-symbols:warning';
                        textClass = 'text-warning';
                        sendEmailCheckbox.prop('checked', false);
                    }
                }

                emailInfo.html(`<iconify-icon icon="${iconClass}" class="me-1"></iconify-icon>${infoText}`)
                         .removeClass('text-muted text-success text-warning text-info')
                         .addClass(textClass);
            })
            .fail(function() {
                emailInfo.html('<iconify-icon icon="material-symbols:error" class="me-1"></iconify-icon>Unable to check email settings.')
                         .removeClass('text-muted text-success text-warning text-info')
                         .addClass('text-danger');
            });
    }

    // Initialize email info on page load
    updateEmailInfo();
});

</script>
@stop

