@extends('layouts.master')
@section('title', 'Invoices List - Paidash')
@section('content')

<div class="dashboard-main-body">
@if(session('success'))
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('success') }}</span>

        </div>
        <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
            <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
        </button>
    </div>
@endif
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Invoice List</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Invoice List</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
            <div class="d-flex flex-wrap align-items-center gap-3 w-20 w-mb-100">
                <div class="icon-field w-100">
                    <input type="text" name="searchkey" class="form-control form-control-sm w-100" placeholder="Search Invoice Code" id="searchkey">
                    <span class="icon">
                        <iconify-icon icon="ion:search-outline"></iconify-icon>
                    </span>
                </div>
            </div>
            <div class="d-flex flex-no-wrap align-items-center gap-3">
                <select id="client" class="form-control form-select select2" title="select Client">
                    <option selected value="">Select Client</option>
                    <option value="">All</option>
                    @foreach ($clients as $client)
                         <option value="{{$client->id}}">{{$client->name}}</option>
                    @endforeach
                </select>
                <select id="invoice_status" class="form-control form-select" title="select Status">
                    <option selected value="">Select Status</option>
                    <option value="">All</option>
                    <option value="Paid">Paid</option>
                    <option value="Partially Paid">Partially Paid</option>
                    <option value="Pending">Pending</option>
                </select>
                <div class="input-group date_range " style="max-width: 250px;">
                    <input type="text" class="form-control" name="daterange" id="invoice_daterange">
                    <div class="input-group-append">
                        <span class="input-group-text" id="basic-addon2">
                            <iconify-icon icon="mdi:calendar" width="24" height="24"></iconify-icon>
                        </span>
                    </div>
                </div>
                @can('invoice-create')
                    <div class="dropdown">
                        <button class="btn btn-sm btn-primary-600 d-flex dropdown-toggle" type="button" id="createInvoiceDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="ri-add-line"></i>&nbsp;<span class="d-none d-md-block">Create&nbsp;</span>Invoice
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="createInvoiceDropdown">
                            <li>
                                <a class="dropdown-item d-flex align-items-center gap-2" href="/invoices/add">
                                    <iconify-icon icon="hugeicons:invoice-03" class="icon text-lg"></iconify-icon>
                                    <div>
                                        <div class="fw-semibold">Service Invoice</div>
                                        <small class="text-muted">Create invoice for services</small>
                                    </div>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center gap-2" href="/invoices/add-inventory">
                                    <iconify-icon icon="solar:box-bold" class="icon text-lg"></iconify-icon>
                                    <div>
                                        <div class="fw-semibold">Inventory Invoice</div>
                                        <small class="text-muted">Create invoice for inventory items</small>
                                    </div>
                                </a>
                            </li>
                        </ul>
                    </div>
                @endcan
                <button id="bulkDownload" class="btn btn-sm btn-primary-600 d-flex">Download Invoices</button>

            </div>
        </div>
        <div class="card-body">
            <!-- Table View for Desktop -->
            <div class="d-none d-md-block">
                <table id="invoicesTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                    <thead>
                        <tr>
                            <th>Invoice</th>
                            <th>Client</th>
                            <th>Invoice Date</th>
                            <th>Invoice Amount</th>
                            <th>Paid Amount</th>
                            <th>Due Amount</th>
                            <th>Status</th>
                            <th>Type</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                    <tfoot>
                        <tr>
                            <th colspan="3" class="text-end">Total</th>
                            <th id="totalInvoice" > <strong>₹ 0.00</strong></th>
                            <th id="totalPaid"> <strong>₹ 0.00</strong></th>
                            <th id="totalPending" > <strong>₹ 0.00</strong></th>
                            <th></th>
                            <th></th>
                            <th></th>

                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- Grid View for Mobile -->
            <div id="clientsGrid" class="d-block d-md-none"></div>
        </div>
    </div>

</div>
<div id="exportLoader" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999;">
    <span class="spinner-border text-primary" role="status"></span>
    <strong>Exporting...</strong>
</div>
@stop
@section('script')
<script>
    $(document).ready(function () {
        $('#client').select2({
            width: '200px'
        });
        $('#invoice_status').select2({
            width: '200px'
        });
        var table = $('#invoicesTable').DataTable({
            processing: true,
            serverSide: true,
            searching: false,
            pageLength: 25,
            ajax: {
                url: "{{ route('invoices.data') }}",
                data: function (d) {
                    d.searchkey = $('#searchkey').val();
                    d.invoice_status = $('#invoice_status').val();
                    d.client = $('#client').val();
                    d.daterange = $('#invoice_daterange').val();
                }
            },
            columns: [
                { data: 'invoice_code', name: 'invoice_code' },
                { data: 'client_name', name: 'client_name' },
                { data: 'invoice_date', name: 'invoice_date' },
                { data: 'total_amount_due', name: 'total_amount_due',
                    render: function(data, type, row) {
                        return `₹ ${parseFloat(data).toFixed(2)}`;
                    }
                },
                { data: 'paid_amount', name: 'paid_amount',
                    render: function(data, type, row) {
                        return `₹ ${parseFloat(data).toFixed(2)}`;
                    }
                },
                { data: 'unpaid_amount', name: 'unpaid_amount',
                    render: function(data, type, row) {
                        return `₹ ${parseFloat(data).toFixed(2)}`;
                    }
                },
                { data: 'invoice_status', name: 'invoice_status', orderable: false, searchable: false },
                { data: 'invoice_type', name: 'invoice_type', orderable: false, searchable: false },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            drawCallback: function (settings) {
                var api = this.api();
                var data = api.rows().data();
                var gridContainer = $('#clientsGrid');
                gridContainer.empty();

            },
            dom:
            "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" + // Entries Dropdown & CSV Button
            "<'row'<'col-md-12'tr>>" +
            "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>", // Info at left, Pagination at right
            buttons: [
                    {
                        text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export',
                        className: 'btn btn-success btn-sm bg-success-500',
                        action: function (e, dt, node, config) {
                            exportInvoices();
                        }
                    },
                    {
                        text: '<iconify-icon icon="mdi:cancel"></iconify-icon> Cancelled Invoices',
                        className: 'btn btn-danger btn-sm bg-danger-500',
                        action: function () {
                            window.location.href = '/cancelled-invoices';
                        }
                    }
                ],
            infoCallback: function(settings, start, end, max, total, pre) {
                return `Showing ${start} to ${end} of ${total} records`;
            },
            footerCallback: function(row, data, start, end, display) {
    var api = this.api();

    // Function to calculate the total of a column
    function calculateTotal(columnIndex) {
        return api
            .column(columnIndex, { page: 'current' }) // Ensure it's the current page data
            .data()
            .reduce(function(a, b) {
                return (parseFloat(a) || 0) + (parseFloat(b) || 0);
            }, 0);
    }

    if (api.column(3).footer()) {
        $(api.column(3).footer()).html(`<strong>₹ ${calculateTotal(3).toFixed(2)}</strong>`);
        $(api.column(4).footer()).html(`<strong>₹ ${calculateTotal(4).toFixed(2)}</strong>`);
        $(api.column(5).footer()).html(`<strong>₹ ${calculateTotal(5).toFixed(2)}</strong>`);
    }
}


        });

        // Custom search event triggers
        $("#searchkey").on("keyup", function () {
            table.draw();
        });

        $("#client, #invoice_status").on("change", function () {
            table.draw();
        });
        $('#invoice_daterange').on('apply.daterangepicker', function () {
            table.draw();
                });
    });
    $('#bulkDownload').on('click', function () {
    let filters = {
        date: $('#invoice_daterange').val(),
        client_id: $('#client').val(),
        _token: "{{ csrf_token() }}"
    };
    Swal.fire({
        title: 'Generating PDF...',
        text: 'Please wait while we process your request.',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    $.ajax({
        url: '/download-bulk-invoices',
        type: 'POST',
        data: filters,
        xhrFields: { responseType: 'blob' }, // Handle binary response
        success: function (response, status, xhr) {
            let filename = xhr.getResponseHeader('Content-Disposition').split('filename=')[1];
            let blob = new Blob([response], { type: 'application/zip' });
            let link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.download = filename;
            link.click();
            Swal.fire({
                icon: 'success',
                title: 'Download Successful!',
                text: 'Your invoices have been downloaded successfully.',
                confirmButtonColor: '#3085d6'
            });
        },
        error: function () {
            Swal.fire({
                icon: 'error',
                title: 'Download Failed!',
                text: 'An error occurred while downloading invoices. Please try again.',
                confirmButtonColor: '#d33'
            });
        },
        complete: function () {
            // Hide Loader
           // Swal.close();

        }
    });
});
var start = moment().subtract(29, 'days');  // Default start date
    var end = moment();  // Default end date
     function cb(start, end) {
    $('#invoice_daterange').val(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
}

$('#invoice_daterange').daterangepicker({
    startDate: start,
    endDate: end,
    autoApply: true,
    locale: { format: 'DD/MM/YYYY' }, // Set format
    ranges: {
       'Today': [moment(), moment()],
       'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
       'Last 7 Days': [moment().subtract(6, 'days'), moment()],
       'Last 30 Days': [moment().subtract(29, 'days'), moment()],
       'This Month': [moment().startOf('month'), moment().endOf('month')],
       'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    }
}, cb);

// Set initial date display
cb(start, end);
// Custom search event triggers
//invoice delete with reason capature
function confirmDelete(url, invoiceId) {
    // 1️⃣ First, fetch payment info
    $.ajax({
        url: `/invoice-payments/${invoiceId}`,
        type: "GET",
        success: function(response) {
            const hasDiscountPayment = response.payments.some(payment =>
                payment.payment_mode === 'Discount');

            if (hasDiscountPayment) {
                Swal.fire({
                    title: "Cannot Delete!",
                    text: "This invoice has discount payments and cannot be deleted.",
                    icon: "error"
                });
                return;
            }

            // 2️⃣ Build payment list HTML (if any)
            let paymentListHtml = '';
            if (response.payments.length > 0) {
                paymentListHtml = '<div class="mt-3"><strong>Existing Payments:</strong><ul class="text-left">';
                response.payments.forEach(payment => {
                    paymentListHtml += `<li>₹${parseFloat(payment.amount).toFixed(2)} -
                                        ${payment.payment_mode} -
                                        ${new Date(payment.paid_on).toLocaleDateString()}</li>`;
                });
                paymentListHtml += '</ul></div>';
            }

            // 3️⃣ Show main confirm dialog
            Swal.fire({
                title: 'Are you sure?',
                html: `You won't be able to revert this!${paymentListHtml}`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, continue'
            }).then((result) => {
                if (result.isConfirmed) {
                    // 4️⃣ Show input dialog for reason
                    Swal.fire({
                        title: 'Reason Required',
                        input: 'textarea',
                        inputLabel: 'Please enter the reason for deleting this invoice:',
                        inputPlaceholder: 'Type your reason here...',
                        inputAttributes: {
                            'aria-label': 'Type your reason here'
                        },
                        inputValidator: (value) => {
                            if (!value.trim()) {
                                return 'Reason is required!';
                            }
                        },
                        showCancelButton: true,
                        confirmButtonText: 'Submit',
                        cancelButtonText: 'Cancel'
                    }).then((reasonResult) => {
                        if (reasonResult.isConfirmed) {
                            const reason = reasonResult.value.trim();

                            // 5️⃣ Send AJAX delete with reason
                            $.ajax({
                                url: url,
                                type: "POST",
                                data: {
                                    _token: "{{ csrf_token() }}",
                                    reason: reason // Pass reason to backend
                                },
                                success: function(response) {
                                    if (response.success) {
                                        Swal.fire({
                                            title: "Deleted!",
                                            text: response.message,
                                            icon: "success",
                                            timer: 2000,
                                            showConfirmButton: false
                                        }).then(() => {
                                            location.reload();
                                        });
                                    } else {
                                        Swal.fire({
                                            title: "Error!",
                                            text: response.message,
                                            icon: "error",
                                        });
                                    }
                                },
                                error: function() {
                                    Swal.fire({
                                        title: "Error!",
                                        text: "An error occurred while deleting the invoice.",
                                        icon: "error",
                                    });
                                }
                            });
                        }
                    });
                }
            });
        },
        error: function() {
            Swal.fire({
                title: "Error!",
                text: "Failed to fetch payment information.",
                icon: "error",
            });
        }
    });
}

// Export function with all filters
function exportInvoices() {
    // Show loader
    $('#exportLoader').show();

    // Get current filter values
    var filters = {
        searchkey: $('#searchkey').val(),
        invoice_status: $('#invoice_status').val(),
        client: $('#client').val(),
        daterange: $('#invoice_daterange').val()
    };

    // Build export URL with filters
    var exportUrl = "{{ route('invoices.export') }}?" + $.param(filters);

    // Create temporary link and trigger download
    var link = document.createElement('a');
    link.href = exportUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Hide loader after a short delay
    setTimeout(() => {
        $('#exportLoader').hide();
    }, 1000);
}

</script>
@stop
