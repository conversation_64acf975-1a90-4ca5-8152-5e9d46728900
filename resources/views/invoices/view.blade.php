@extends('layouts.master')
@section('title', 'Invoices View - Paidash')
@section('content')
@if(session('success'))
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('success') }}</span>

        </div>
        <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
            <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
        </button>
    </div>
@endif
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">View Invoice</h6>
            <ul class="d-flex align-items-center gap-2">

                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="/invoices" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="hugeicons:invoice-03" class="icon text-lg"></iconify-icon>
                        Invoices
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">View Invoice</li>
            </ul>
        </div>

        <div class="card">

            <div class="card-header">
                <div class="d-flex flex-wrap align-items-center justify-content-end gap-2">
                    {{-- <a href="javascript:void(0)" class="btn btn-sm btn-primary-600 radius-8 d-inline-flex align-items-center gap-1">
                    <iconify-icon icon="pepicons-pencil:paper-plane" class="text-xl"></iconify-icon>
                    Send Invoice
                </a> --}}
                    <span class="badge bg-success text-white">
                        @php
                            $has_invoice_items = $invoice->isInventoryInvoice();
                            $service_type = $invoice->service_id;
                            $is_true_inventory_invoice = $has_invoice_items && is_null($service_type);
                            $is_weight_range_invoice = $has_invoice_items && ($service_type == 3);
                        @endphp

                        @if($is_true_inventory_invoice)
                            Inventory Invoice
                        @elseif($is_weight_range_invoice)
                            Weight-Based Service Invoice
                        @elseif($invoice->created_type == 1)
                            Manual Invoice
                        @else
                            Auto Invoice
                        @endif
                    </span>
                    <a href="{{ route('invoice.download', $invoice->id) }}"
                        class="btn btn-sm btn-warning radius-8 d-inline-flex align-items-center gap-1">
                        <iconify-icon icon="solar:download-linear" class="text-xl"></iconify-icon>
                        Download
                    </a>
                    <button type="button" data-bs-toggle="modal" data-bs-target="#sendEmailModal"
                        class="btn btn-sm btn-success radius-8 d-inline-flex align-items-center gap-1"
                        onclick="loadEmailModal({{ $invoice->id }})">
                        <iconify-icon icon="solar:letter-linear" class="text-xl"></iconify-icon>
                        Send Mail
                    </button>

                    {{-- <a href="javascript:void(0)" class="btn btn-sm btn-success radius-8 d-inline-flex align-items-center gap-1">
                    <iconify-icon icon="uil:edit" class="text-xl"></iconify-icon>
                    Edit
                </a> --}}
                    <button target="_blank" class="btn btn-sm btn-danger radius-8 d-inline-flex align-items-center gap-1"
                        onclick="printInvoice()">
                        <iconify-icon icon="basil:printer-outline" class="text-xl"></iconify-icon>
                        Print
                    </button>

                    <a href="{{ route('invoices.change-logs', $invoice->id) }}" class="btn btn-sm btn-outline-primary radius-8 d-inline-flex align-items-center gap-1">
                        <iconify-icon icon="mdi:history" class="text-xl"></iconify-icon>
                        Change Logs
                    </a>

                </div>
            </div>
            <div class="card-body py-40">
                <div class="row justify-content-center" id="invoice">
                    {{-- this invoice need to update as per present and this A5 size --}}
                    <div class="col-lg-9">
                        <div class="shadow-4 border radius-8 p-20">
                            <!-- Header -->
                            <div class="row mb-3">
                                <div class="col-md-6 d-flex align-items-center">
                                    <img src="{{ url('storage/'.$company_details['logo']) }}" alt="Company" class="img-fluid"
                                        width="70">
                                </div>
                                <div class="col-md-6 text-end">
                                    <div class="d-flex align-items-center justify-content-end gap-2">
                                        <h6>Invoice: {{ $invoice->invoice_code }}</h6>
                                        @if($invoice->cgst_amount == 0 && $invoice->sgst_amount == 0)
                                            <span class="badge bg-warning text-dark d-flex align-items-center gap-1">
                                                <iconify-icon icon="material-symbols:receipt-long" class="me-1"></iconify-icon>
                                                WITHOUT GST
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <hr>

                            <!-- Invoice Details -->
                            <div class="row mb-3">
                                <div class="col-md-6"><strong>Date:</strong>
                                    {{ $invoice->invoice_date ? date('d-m-Y', strtotime($invoice->invoice_date)) : 'NA' }}
                                </div>
                                <div class="col-md-6 text-end"><strong>Invoice Status:</strong>
                                    @if ($invoice->invoice_status == 'Paid')
                                        <span class="badge bg-success">Paid</span>
                                    @elseif ($invoice->invoice_status == 'Pending')
                                        <span class="badge bg-danger">Pending</span>
                                    @elseif ($invoice->invoice_status == 'Partially Paid')
                                        <span class="badge bg-warning">Partially Paid</span>
                                    @endif
                                </div>
                                <div class="col-md-6"><strong>Client Id:</strong> {{ $invoice->client->client_code }}</div>
                                @if($invoice->from_invoice_to)
                                <div class="col-md-6 text-end"><strong>Duration
                                        Period:</strong> {{ $invoice->from_invoice_to }}</div>
                                @endif
                            </div>
                            <hr>

                            <!-- Invoiced To & Pay To -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>Invoiced To:</strong><br>
                                    <strong>{{ $invoice->client->business_name }}</strong>
                                    <address>{{ $invoice->client->address }}, {{ $invoice->client->area }},
                                        {{ $invoice->client->city }}, {{ $invoice->client->district->name }},
                                        {{ $invoice->client->state->name }}, {{ $invoice->client->pincode }} </address>
                                </div>
                                <div class="col-md-6 text-end">
                                    <strong>Pay To:</strong>
                                    <address>
                                        {{$company_details['legal_name']}}<br>
                                        {!! (($company_details['address']) ? nl2br($company_details['address']) . '<br>' : '') !!}
                                        {!! (($company_details['gst'])?' GSTIN:'.$company_details['gst'].' <br>':'') !!}
                                        {!! (($company_details['website'])?' Website:'.$company_details['website'].' <br>':'') !!}
                                        {!! (($company_details['email'])?' Email:'.$company_details['email'].' <br>':'') !!}
                                        {!! (($company_details['phone'])?' Phone:'.$company_details['phone']:'') !!}

                                    </address>
                                </div>
                            </div>
                            @php
                                // Determine display format based on invoice type
                                $has_invoice_items = $invoice->isInventoryInvoice();
                                $service_type = $invoice->service_id;
                                $is_true_inventory_invoice = $has_invoice_items && is_null($service_type);
                                $is_weight_range_invoice = $has_invoice_items && ($service_type == 3);
                                $show_bedded_details = !$has_invoice_items && ($service_type == 1); // Bedded Service
                                $show_weight_details = !$has_invoice_items && ($service_type == 3); // Weight Based Service (single item)
                                $show_units_for_fixed = !$has_invoice_items && ($service_type == 4); // Bedded with Fixed Price
                                $show_simple_format = !$has_invoice_items && ($service_type == 2 || $service_type == 4); // Non-Bedded or Bedded with Fixed Price
                            @endphp

                            @if($is_true_inventory_invoice)
                                <!-- Inventory Invoice Header -->
                                <div class="alert alert-info d-flex align-items-center mb-3">
                                    <iconify-icon icon="solar:box-bold" class="me-2 text-xl"></iconify-icon>
                                    <div>
                                        <strong>Inventory Invoice</strong>
                                        <small class="d-block text-muted">This invoice contains {{ $invoice->invoiceItems->count() }} inventory item(s)</small>
                                    </div>
                                </div>
                            @elseif($is_weight_range_invoice)
                                <!-- Weight Range Invoice Header -->
                                <div class="alert alert-success d-flex align-items-center mb-3">
                                    <iconify-icon icon="mdi:weight-kilogram" class="me-2 text-xl"></iconify-icon>
                                    <div>
                                        <strong>Weight-Based Service Invoice (Range Pricing)</strong>
                                        <small class="d-block text-muted">This invoice shows {{ $invoice->invoiceItems->count() }} weight range(s) for {{ $invoice->service_type_data->name }}</small>
                                    </div>
                                </div>
                            @endif

                            <!-- Invoice Details Table -->
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>S.No</th>
                                        <th>Description</th>
                                        <th class="text-center">HSN/SAC Code</th>
                                        @if ($has_invoice_items)
                                            @if($is_true_inventory_invoice)
                                                <th>SKU</th>
                                                <th>Quantity</th>
                                                <th class="text-center">Unit Price</th>
                                                <th class="text-center">GST Rate</th>
                                            @else
                                                <th>Range</th>
                                                <th>Weight (KG)</th>
                                                <th class="text-center">Rate per KG</th>
                                                <th class="text-center">GST Rate</th>
                                            @endif
                                        @elseif ($show_bedded_details)
                                            <th>No of Days for Invoice</th>
                                            <th>No of Beds</th>
                                            <th class="text-center">Rate (Per Unit/Day)</th>
                                        @elseif ($show_weight_details)
                                            <th>Weight (KG)</th>
                                            <th class="text-center">Rate (Per KG)</th>
                                        @elseif ($show_units_for_fixed)
                                            <th>No of Units</th>
                                            <th class="text-center">Fixed Rate</th>
                                        @else
                                            <th>Qty</th>
                                            <th class="text-center">Rate</th>
                                        @endif
                                        <th class="text-end">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if ($has_invoice_items)
                                        @foreach($invoice->invoiceItems as $index => $item)
                                            <tr>
                                                <td>{{ $index + 1 }}</td>
                                                <td>
                                                    <strong>{{ $item->item_name }}</strong>
                                                    @if($item->item_description)
                                                        <br><small class="text-muted">{{ $item->item_description }}</small>
                                                    @endif
                                                </td>
                                                <td class="text-center">9993</td>
                                                @if($is_true_inventory_invoice)
                                                    <td class="text-center">{{ $item->item_sku }}</td>
                                                    <td class="text-center">{{ $item->quantity }} {{ $item->unit_of_measure }}</td>
                                                @else
                                                    <td class="text-center">{{ $item->item_sku }}</td>
                                                    <td class="text-center">{{ number_format($item->quantity, 2) }} {{ $item->unit_of_measure }}</td>
                                                @endif
                                                <td class="text-center">₹{{ number_format($item->unit_price, 2) }}</td>
                                                <td class="text-center">
                                                    @if($item->is_gst_applicable)
                                                        <span class="badge bg-success-100 text-success-600 px-2 py-1 rounded-pill fw-semibold text-xs">
                                                            {{ number_format($item->cgst_rate + $item->sgst_rate, 1) }}%
                                                        </span>
                                                        <br><small class="text-muted">C:{{ number_format($item->cgst_rate, 1) }}% S:{{ number_format($item->sgst_rate, 1) }}%</small>
                                                    @else
                                                        <span class="badge bg-neutral-200 text-neutral-600 px-2 py-1 rounded-pill fw-semibold text-xs">Exempt</span>
                                                    @endif
                                                </td>
                                                <td class="text-end">₹{{ number_format($item->total_price, 2) }}</td>
                                            </tr>
                                        @endforeach
                                        @if($invoice->comments)
                                            <tr>
                                                <td colspan="8" class="text-muted">
                                                    <strong>Comments:</strong> {{ $invoice->comments }}
                                                </td>
                                            </tr>
                                        @endif
                                    @else
                                        <tr>
                                            <td>1</td>
                                            <td>Service Charges for
                                                {{ $invoice->service_type_data->name }}
                                                @if($service_type == 1)
                                                    (Bedded Service)
                                                @elseif($service_type == 2)
                                                    (Non-Bedded Service)
                                                @elseif($service_type == 3)
                                                    (Weight-Based Service)
                                                @elseif($service_type == 4)
                                                    (Bedded with Fixed Price)
                                                @endif
                                                <br>{{ $invoice->comments }}
                                            </td>
                                            <td class="text-center">9993</td>
                                            @if ($show_bedded_details)
                                                <td class="text-center">{{ $invoice->days_for_invoice }}</td>
                                                <td>{{ $invoice->beds_count }}</td>
                                                <td class="text-center">₹{{ number_format($invoice->unit_price, 2) }}</td>
                                            @elseif ($show_weight_details)
                                                <td class="text-center">{{ number_format($invoice->weight_qty ?? 0, 2) }} KG</td>
                                                <td class="text-center">₹{{ number_format($invoice->unit_price ?? 0, 2) }}</td>
                                            @elseif ($show_units_for_fixed)
                                                <td class="text-center">{{ $invoice->beds_count ?? 1 }}</td>
                                                <td class="text-center">₹{{ number_format($invoice->gross_amount, 2) }}</td>
                                            @else
                                                <td>1</td>
                                                <td class="text-center">₹{{ number_format($invoice->gross_amount, 2) }}</td>
                                            @endif
                                            <td class="text-end">₹{{ number_format($invoice->gross_amount, 2) }}</td>
                                        </tr>
                                    @endif
                                </tbody>
                                <tfoot>
                                    <tr>
                                        @php
                                            $colspan = 5; // Default for simple format
                                            if ($has_invoice_items) $colspan = 7;
                                            elseif ($show_bedded_details) $colspan = 6;
                                            elseif ($show_weight_details) $colspan = 5;
                                            elseif ($show_units_for_fixed) $colspan = 5;
                                        @endphp
                                        <td colspan="{{ $colspan }}" class="text-end"><strong>Sub Total:</strong></td>
                                        <td class="text-end">₹{{ number_format($invoice->gross_amount, 2) }}</td>
                                    </tr>
                                    <tr>
                                        <td colspan="{{ $colspan }}" class="text-end"><strong>
                                            @if($has_invoice_items)
                                                Total CGST:
                                            @else
                                                @php
                                                    $isWithoutGst = $invoice->cgst_amount == 0 && $invoice->sgst_amount == 0;
                                                    $gstRate = $isWithoutGst ? '0' : '6';
                                                @endphp
                                                CGST @ {{ $gstRate }}%:
                                            @endif
                                        </strong></td>
                                        <td class="text-end">₹{{ number_format($invoice->cgst_amount, 2) }}</td>
                                    </tr>
                                    <tr>
                                        <td colspan="{{ $colspan }}" class="text-end"><strong>
                                            @if($has_invoice_items)
                                                Total SGST:
                                            @else
                                                SGST @ {{ $gstRate }}%:
                                            @endif
                                        </strong></td>
                                        <td class="text-end">₹{{ number_format($invoice->sgst_amount, 2) }}</td>
                                    </tr>
                                    <tr>
                                        <td colspan="{{ $colspan }}" class="text-end">
                                            <strong>Total:</strong></td>
                                        <td class="text-end">₹{{ $invoice->total_amount_due }}</td>
                                    </tr>
                                    @if($invoice->paid_amount>0)
                                    <tr>
                                        <td colspan="{{ $colspan + 1 }}" class="text-end"><strong>Payment
                                                Made:</strong>
                                            ₹{{ $invoice->paid_amount }}</td>
                                    </tr>
                                    @endif
                                    {{-- <tr>
                                        <td colspan="{{ $colspan + 1 }}" class="text-end"><strong>Payment
                                                Outstanding as on Date Before Submission:</strong>
                                            ₹{{ $invoice->client_pending_amount_before_invoice }}</td>
                                    </tr> --}}
                                    <tr>
                                        <td colspan="{{ $colspan + 1 }}" class="text-end"><strong>Due Amount:</strong> ₹{{ $invoice->unpaid_amount }}</td>
                                    </tr>
                                    @php
                                        $formatter = new NumberFormatter('en', NumberFormatter::SPELLOUT);
                                        $amountInWords = ucfirst($formatter->format($invoice->unpaid_amount));
                                    @endphp
                                    <tr>
                                        <td colspan="{{ $colspan + 1 }}" class="text-end border-bottom-0">
                                            <strong>Amount Chargable (in Words):</strong> INR {{ $amountInWords }} only
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>

                            @if($has_invoice_items)
                                <!-- Inventory Summary -->
                                <div class="row mt-4">
                                    <div class="col-md-12">
                                        <div class="card border border-info-200 bg-info-50">
                                            <div class="card-body p-3">
                                                @if($is_true_inventory_invoice)
                                                    <h6 class="fw-semibold text-info-600 mb-2 d-flex align-items-center">
                                                        <iconify-icon icon="solar:box-bold" class="me-2"></iconify-icon>
                                                        Inventory Summary
                                                    </h6>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <small class="text-muted">Total Items:</small>
                                                            <div class="fw-semibold">{{ $invoice->invoiceItems->count() }}</div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <small class="text-muted">Total Quantity:</small>
                                                            <div class="fw-semibold">{{ $invoice->invoiceItems->sum('quantity') }}</div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <small class="text-muted">Categories:</small>
                                                            <div class="fw-semibold">
                                                                @php
                                                                    $categories = $invoice->invoiceItems->map(function($item) {
                                                                        return $item->inventoryItem->category->name ?? 'N/A';
                                                                    })->unique()->values();
                                                                @endphp
                                                                {{ $categories->implode(', ') }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                @else
                                                    <h6 class="fw-semibold text-success-600 mb-2 d-flex align-items-center">
                                                        <iconify-icon icon="mdi:weight-kilogram" class="me-2"></iconify-icon>
                                                        Weight Range Summary
                                                    </h6>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <small class="text-muted">Total Weight Ranges:</small>
                                                            <div class="fw-semibold">{{ $invoice->invoiceItems->count() }}</div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <small class="text-muted">Total Weight:</small>
                                                            <div class="fw-semibold">{{ number_format($invoice->invoiceItems->sum('quantity'), 2) }} KG</div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <small class="text-muted">Service:</small>
                                                            <div class="fw-semibold">{{ $invoice->service_type_data->name }}</div>
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <!-- Bank Details -->
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    {!! (($company_details['bank_details']) ? '<strong>Company\'s Bank Details</strong><br>' . nl2br($company_details['bank_details']) . '<br>' : '') !!}

                                    <img src="{{ $qr_url }}" alt="" width="100"><br>
                                    <span>Scan to Pay </span>
                                    {{-- <img src="data:image/png;base64,{{ $qr_url }}" alt="UPI QR Code" /> --}}
                                </div>
                                <div class="col-md-6 text-end">
                                    <div><strong>For Kakatiya Mediclean Services</strong></div>
                                    @if (!empty($company_details['auth_sign']))
                                        <div class="mt-2">
                                            <img src="{{ url('storage/'.$company_details['auth_sign']) }}" alt="Company" class="img-fluid" width="70">
                                        </div>
                                    @endif
                                </div>


                            </div>
                            <hr>

                            <!-- Footer -->
                            <footer>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Declaration:</strong> We declare that this invoice shows the actual price
                                            of the services described and that all particulars are true and correct.</p>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <p><strong>NOTE:</strong> This is a computer-generated receipt and does not require
                                            a physical signature.</p>
                                    </div>
                                </div>
                            </footer>
                        </div>
                        <h6 class="mt-5">Payments</h6>
                        <table class="table table-bordered mt-3">
                            <thead class="table-light">
                                <tr>
                                    <th>Payment Date</th>
                                    <th>Payment Mode</th>
                                    <th class="text-end">Payment Amount</th>
                                </tr>
                            </thead><tbody>
                                @if ($invoice_payments->isEmpty())
                                    <tr>
                                        <td colspan="3" class="text-center text-muted">No payments made against this invoice.</td>
                                    </tr>
                                @else
                                    @foreach ($invoice_payments as $invoice_payment)
                                        <tr>
                                            <td>
                                                <a href="/payments/view/{{ $invoice_payment->id }}">
                                                    {{ date('d-m-Y', strtotime($invoice_payment->paid_on)) }}
                                                </a>
                                            </td>
                                            <td>{{ $invoice_payment->payment_mode }}</td>
                                            <td class="text-end">₹{{ number_format($invoice_payment->amount, 2) }}</td>
                                        </tr>
                                    @endforeach
                                @endif
                            </tbody>

                        </table>
                    </div>

                </div>
            </div>

        </div>

    </div>

<!-- Send Email Modal -->
<div class="modal fade" id="sendEmailModal" tabindex="-1" aria-labelledby="sendEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success-100">
                <h5 class="modal-title d-flex align-items-center gap-2" id="sendEmailModalLabel">
                    <iconify-icon icon="solar:letter-bold" class="text-success"></iconify-icon>
                    Send Invoice Email
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="sendEmailForm">
                    <input type="hidden" id="invoiceId" name="invoice_id">

                    <!-- Email Configuration Info -->
                    <div class="alert alert-info d-flex align-items-start gap-3 mb-4">
                        <iconify-icon icon="material-symbols:info" class="text-xl mt-1"></iconify-icon>
                        <div>
                            <h6 class="mb-2">Email Configuration</h6>
                            <div id="emailConfigInfo">
                                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                Loading email configuration...
                            </div>
                        </div>
                    </div>

                    <!-- Recipient Email -->
                    <div class="mb-4">
                        <label for="recipientEmail" class="form-label fw-semibold">
                            <iconify-icon icon="material-symbols:email" class="me-1"></iconify-icon>
                            Recipient Email Address
                        </label>
                        <input type="email" class="form-control" id="recipientEmail" name="recipient_email"
                               placeholder="Enter email address" required>
                        <div class="form-text">
                            <iconify-icon icon="material-symbols:edit" class="me-1"></iconify-icon>
                            You can modify the email address before sending
                        </div>
                    </div>

                    <!-- Sender Information -->
                    <div class="mb-4">
                        <label class="form-label fw-semibold">
                            <iconify-icon icon="material-symbols:person" class="me-1"></iconify-icon>
                            Sender Information
                        </label>
                        <div class="card border border-primary-200 bg-primary-50">
                            <div class="card-body p-3">
                                <div id="senderInfo">
                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                    Loading sender information...
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email Preview -->
                    <div class="mb-4">
                        <label class="form-label fw-semibold">
                            <iconify-icon icon="material-symbols:preview" class="me-1"></iconify-icon>
                            Email Preview
                        </label>
                        <div class="card border border-neutral-200">
                            <div class="card-body p-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">Subject:</small>
                                        <div class="fw-semibold" id="emailSubject">Invoice - {{ $invoice->invoice_code }} from {{ env('APP_NAME') }}</div>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">Attachment:</small>
                                        <div class="fw-semibold">
                                            <iconify-icon icon="material-symbols:attach-file" class="me-1"></iconify-icon>
                                            invoice_{{ str_replace(['/', '\\'], '-', $invoice->invoice_code) }}.pdf
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <iconify-icon icon="material-symbols:close" class="me-1"></iconify-icon>
                    Cancel
                </button>
                <button type="button" class="btn btn-success" id="confirmSendEmail">
                    <iconify-icon icon="solar:letter-bold" class="me-1"></iconify-icon>
                    Send Email
                </button>
            </div>
        </div>
    </div>
</div>

@stop
@section('script')
    <script>
        // Setup CSRF token for all AJAX requests
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        function printInvoice() {
            var url = "{{ route('invoice.print', $invoice->id) }}";
            var printWindow = window.open(url, '_blank');
            printWindow.onload = function() {
                printWindow.print();
            };
        }

        // Load email modal data
        function loadEmailModal(invoiceId) {
            $('#invoiceId').val(invoiceId);

            // Reset form
            $('#recipientEmail').val('');
            $('#emailConfigInfo').html('<div class="spinner-border spinner-border-sm me-2" role="status"></div>Loading email configuration...');
            $('#senderInfo').html('<div class="spinner-border spinner-border-sm me-2" role="status"></div>Loading sender information...');

            // Load email configuration
            $.get('/invoices/' + invoiceId + '/email-config')
                .done(function(response) {
                    updateEmailConfiguration(response);
                })
                .fail(function() {
                    $('#emailConfigInfo').html('<span class="text-danger"><iconify-icon icon="material-symbols:error" class="me-1"></iconify-icon>Failed to load email configuration</span>');
                });
        }

        function updateEmailConfiguration(config) {
            let configHtml = '';
            let iconClass = 'material-symbols:info';
            let alertClass = 'alert-info';



            if (config.app_env === 'live') {
                if (config.client_email) {
                    configHtml = `
                        <div class="d-flex align-items-center gap-2 mb-2">
                            <iconify-icon icon="material-symbols:check-circle" class="text-success"></iconify-icon>
                            <strong>Live Environment</strong>
                        </div>
                        <div>Email will be sent to the actual client email address</div>
                    `;
                    $('#recipientEmail').val(config.client_email);
                    alertClass = 'alert-success';
                } else {
                    configHtml = `
                        <div class="d-flex align-items-center gap-2 mb-2">
                            <iconify-icon icon="material-symbols:warning" class="text-warning"></iconify-icon>
                            <strong>Live Environment - No Client Email</strong>
                        </div>
                        <div>No email address found for this client. Please enter a valid email address.</div>
                    `;
                    alertClass = 'alert-warning';
                }
            } else {
                if (config.tester_email) {
                    configHtml = `
                        <div class="d-flex align-items-center gap-2 mb-2">
                            <iconify-icon icon="material-symbols:science" class="text-info"></iconify-icon>
                            <strong>Test Environment</strong>
                        </div>
                        <div>Email will be sent to the configured test address: <strong>${config.tester_email}</strong></div>
                        <small class="text-muted d-block mt-1">Client email will be ignored in test mode</small>
                    `;
                    $('#recipientEmail').val(config.tester_email);
                    alertClass = 'alert-info';
                } else {
                    configHtml = `
                        <div class="d-flex align-items-center gap-2 mb-2">
                            <iconify-icon icon="material-symbols:error" class="text-danger"></iconify-icon>
                            <strong>Test Environment - No Test Email</strong>
                        </div>
                        <div>No test email configured. Please contact administrator.</div>
                    `;
                    alertClass = 'alert-danger';
                }
            }

            $('#emailConfigInfo').html(configHtml);
            $('.alert').removeClass('alert-info alert-success alert-warning alert-danger').addClass(alertClass);

            // Update sender info
            let senderHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">From:</small>
                        <div class="fw-semibold">${config.sender_name}</div>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">Email:</small>
                        <div class="fw-semibold">${config.sender_email}</div>
                    </div>
                </div>
            `;
            $('#senderInfo').html(senderHtml);
        }

        // Send email confirmation
        $('#confirmSendEmail').click(function() {
            const invoiceId = $('#invoiceId').val();
            const recipientEmail = $('#recipientEmail').val();

            if (!recipientEmail) {
                alert('Please enter a recipient email address');
                return;
            }

            if (!isValidEmail(recipientEmail)) {
                alert('Please enter a valid email address');
                return;
            }

            // Disable button and show loading
            const btn = $(this);
            const originalHtml = btn.html();
            btn.prop('disabled', true).html('<div class="spinner-border spinner-border-sm me-2" role="status"></div>Sending...');

            // Send email
            $.post('/invoices/' + invoiceId + '/send-email', {
                recipient_email: recipientEmail
            })
            .done(function(response) {
                if (response.success) {
                    $('#sendEmailModal').modal('hide');
                    showSuccessMessage(response.message);
                } else {
                    alert('Error: ' + response.message);
                }
            })
            .fail(function(xhr) {
                let errorMessage = 'Failed to send email. Please try again.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                alert('Error: ' + errorMessage);
            })
            .always(function() {
                btn.prop('disabled', false).html(originalHtml);
            });
        });

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function showSuccessMessage(message) {
            // Create and show success snackbar
            const snackbar = $(`
                <div id="emailSnackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
                    <div class="d-flex align-items-center gap-2">
                        <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
                        <span>${message}</span>
                    </div>
                    <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeEmailSnackbar()">
                        <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
                    </button>
                </div>
            `);

            $('body').prepend(snackbar);

            // Auto hide after 5 seconds
            setTimeout(() => {
                closeEmailSnackbar();
            }, 5000);
        }

        function closeEmailSnackbar() {
            $('#emailSnackbar').removeClass('show').fadeOut(300, function() {
                $(this).remove();
            });
        }
    </script>
@stop
