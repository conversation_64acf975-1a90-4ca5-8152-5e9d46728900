@extends('layouts.master')
@section('title', 'Cancelled Invoices - Paidash')
@section('content')
<div class="dashboard-main-body">
@if(session('success'))
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('success') }}</span>
        </div>
        <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
            <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
        </button>
    </div>
@endif
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Cancelled Invoices</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Cancelled Invoices</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-body">
            <!-- Table View for Desktop -->
            <div class="d-none d-md-block">
                <table id="cancelledInvoicesTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                    <thead>
                        <tr>
                            <th>Invoice</th>
                            <th>Client</th>
                            <th>Invoice Date</th>
                            <th>Invoice Amount</th>
                            <th>Paid Amount</th>
                            <th>Due Amount</th>
                            <th>Payment Status</th>
                            <th>Cancelled By</th>
                            <th>Cancelled On</th>
                            <th>Cancelled Reason</th>
                            {{-- <th>Action</th> --}}
                        </tr>
                    </thead>
                    <tbody></tbody>
                    <tfoot>
                        <tr>
                            <th colspan="3" class="text-end">Total</th>
                            <th id="totalInvoice" > <strong>₹ 0.00</strong></th>
                            <th id="totalPaid"> <strong>₹ 0.00</strong></th>
                            <th id="totalPending" > <strong>₹ 0.00</strong></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>

                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>
@stop
@section('script')
<script>
    $(document).ready(function () {
        var table = $('#cancelledInvoicesTable').DataTable({
            // processing: true,
            serverSide: true,
            searching: false,
            pageLength: 25,
            ajax: {
                url: "{{ route('invoices.getCancelledInvoices') }}",
                data: function (d) {
                    d.daterange = $('#daterange').val();
                    d.client = $('#client').val();
                }
            },
            columns: [
                { data: 'invoice_code', name: 'invoice_code' },
                { data: 'client_name', name: 'client_name' },
                { data: 'invoice_date', name: 'invoice_date' },
                { data: 'total_amount_due', name: 'total_amount_due',
                    render: function(data, type, row) {
                        return `₹ ${parseFloat(data).toFixed(2)}`;
                    }
                },
                { data: 'paid_amount', name: 'paid_amount',
                    render: function(data, type, row) {
                        return `₹ ${parseFloat(data).toFixed(2)}`;
                    }
                },
                { data: 'unpaid_amount', name: 'unpaid_amount',
                    render: function(data, type, row) {
                        return `₹ ${parseFloat(data).toFixed(2)}`;
                    }
                },
                { data: 'invoice_status', name: 'invoice_status', orderable: false, searchable: false },
                { data: 'deleted_by', name: 'deleted_by' },
                { data: 'deleted_at', name: 'deleted_at' },
                { data: 'deleted_reason', name: 'deleted_reason' },
                // { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            drawCallback: function () {
                updateView();
            },
            dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" +
                "<'row'<'col-md-12'tr>>" +
                "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>", // Info at left, Pagination at right
            buttons: [
                    {
                        extend: 'csv',
                        text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
                        className: 'btn btn-success btn-sm bg-success-500',
                        filename: 'Deleted_Invoices_' + new Date().toISOString().slice(0, 10),
                        customize: function (csv) {
                            return '\uFEFF' + csv; // Add BOM to ensure proper UTF-8 encoding
                        },
                        action: function (e, dt, node, config) {
                            $('#exportLoader').show(); // Show loader

                            setTimeout(() => {
                                $.fn.dataTable.ext.buttons.csvHtml5.action.call(this, e, dt, node, config);
                                $('#exportLoader').hide(); // Hide loader after export
                            }, 500); // Small delay for better UX
                        }
                    }
                ],
            infoCallback: function(settings, start, end, max, total, pre) {
                return `Showing ${start} to ${end} of ${total} records`;
            },
            footerCallback: function(row, data, start, end, display) {
                var api = this.api();

                // Function to calculate the total of a column
                function calculateTotal(columnIndex) {
                    return api
                        .column(columnIndex, { page: 'current' }) // Ensure it's the current page data
                        .data()
                        .reduce(function(a, b) {
                            return (parseFloat(a) || 0) + (parseFloat(b) || 0);
                        }, 0);
                }

                // Calculate totals for column 3, 4, and 5
                var totalColumn3 = calculateTotal(3);
                var totalColumn4 = calculateTotal(4);
                var totalColumn5 = calculateTotal(5);

                // Update footer for column 3
                $(api.column(3).footer()).html(`<strong>₹ ${totalColumn3.toFixed(2)}</strong>`);

                // Update footer for column 4
                $(api.column(4).footer()).html(`<strong>₹ ${totalColumn4.toFixed(2)}</strong>`);

                // Update footer for column 5
                $(api.column(5).footer()).html(`<strong>₹ ${totalColumn5.toFixed(2)}</strong>`);
            }
        });
    });
</script>
@stop
