@extends('layouts.master')
@section('title', 'Create Inventory Invoice - Paidash')
@section('content')
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">Create Inventory Invoice</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="/invoices" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="hugeicons:invoice-03" class="icon text-lg"></iconify-icon>
                        Invoices
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">Create Inventory Invoice</li>
            </ul>
        </div>

        <div class="card">
            <div class="card-body py-40">
                <div class="row justify-content-center" id="invoice">
                    <div class="col-lg-12">
                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <h6 class="fw-semibold mb-2">Please fix the following errors:</h6>
                                <ul class="mb-0">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        @if (session('error'))
                            <div class="alert alert-danger">
                                <h6 class="fw-semibold mb-2">Error:</h6>
                                {{ session('error') }}
                            </div>
                        @endif
                        <form action="{{ route('invoices.store-inventory') }}" method="POST" id="inventory-invoice-form">
                            @csrf
                            <div class="shadow-4 border radius-8">
                                <div class="p-20 border-bottom">
                                    <div class="row d-flex">
                                        <div class="col">
                                            <select id="client" name="client" class="form-control form-select select2"
                                                title="select Client">
                                                <option selected value="">Select Client</option>
                                                @foreach ($clients as $client)
                                                    <option value="{{ $client->id }}">{{ $client->name.' - '. $client->client_code}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col">
                                            <input type="date" class="form-control" name="invoice_date" id="invoice_date"
                                                aria-describedby="helpId" placeholder="select Date" value="{{ date('Y-m-d') }}" />
                                        </div>
                                    </div>
                                </div>

                                <!-- Inventory Items Selection -->
                                <div class="py-28 px-20">
                                    <div class="alert alert-info d-flex align-items-center mb-3">
                                        <iconify-icon icon="material-symbols:info" class="me-2"></iconify-icon>
                                        <div>
                                            <strong>How to add items:</strong> Select a category to filter items, then choose items from the dropdown. You can add the same item multiple times with different quantities.
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="category_filter" class="form-label">Filter by Category</label>
                                            <select id="category_filter" class="form-control form-select select2">
                                                <option value="">All Categories</option>
                                                @foreach ($categories as $category)
                                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="inventory_item" class="form-label">Select Inventory Item</label>
                                            <select id="inventory_item" class="form-control form-select select2">
                                                <option value="">Select an item to add</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Selected Items Table -->
                                    <div class="mt-24">
                                        <div class="table-responsive scroll-sm">
                                            <table class="table bordered-table text-sm" id="invoice-items-table">
                                                <thead>
                                                    <tr>
                                                        <th scope="col" class="text-sm">Item</th>
                                                        <th scope="col" class="text-sm">SKU</th>
                                                        <th scope="col" class="text-sm">Description</th>
                                                        <th scope="col" class="text-sm">Unit Price</th>
                                                        <th scope="col" class="text-sm">Quantity</th>
                                                        <th scope="col" class="text-sm">GST Rate</th>
                                                        <th scope="col" class="text-sm">Total</th>
                                                        <th scope="col" class="text-sm">Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="invoice-items-body">
                                                    <tr id="no-items-row">
                                                        <td colspan="8" class="text-center text-muted">
                                                            <iconify-icon icon="solar:box-outline" class="icon text-2xl mb-2"></iconify-icon>
                                                            <br>No items selected. Use the dropdown above to add inventory items.
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- Invoice Totals -->
                                        <div class="row mt-4">
                                            <div class="col-md-8"></div>
                                            <div class="col-md-4">
                                                <table class="table table-sm">
                                                    <tr>
                                                        <td><strong>Subtotal:</strong></td>
                                                        <td class="text-end">₹<span id="subtotal">0.00</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Total CGST:</strong></td>
                                                        <td class="text-end">₹<span id="cgst">0.00</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Total SGST:</strong></td>
                                                        <td class="text-end">₹<span id="sgst">0.00</span></td>
                                                    </tr>
                                                    <tr class="table-primary">
                                                        <td><strong>Total Amount:</strong></td>
                                                        <td class="text-end"><strong>₹<span id="total">0.00</span></strong></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>

                                        <!-- Comments -->
                                        <div class="row mt-3">
                                            <div class="col-md-12">
                                                <label for="comments" class="form-label">Comments</label>
                                                <textarea class="form-control" name="comments" id="comments" rows="3" placeholder="Additional comments for this invoice"></textarea>
                                            </div>
                                        </div>

                                        <!-- GST and Email Options -->
                                        <div class="row mt-3">
                                            <!-- GST Options -->
                                            <div class="col-md-6">
                                                <div class="card border border-warning-200 bg-warning-50">
                                                    <div class="card-body p-15">
                                                        <h6 class="fw-semibold text-warning-600 mb-2 d-flex align-items-center">
                                                            <iconify-icon icon="material-symbols:receipt-long" class="me-2"></iconify-icon>
                                                            GST Options
                                                        </h6>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="without_gst" id="without_gst">
                                                            <label class="form-check-label" for="without_gst">
                                                                <strong>Create invoice without GST</strong>
                                                            </label>
                                                        </div>
                                                        <small class="text-muted d-block mt-1">
                                                            <iconify-icon icon="material-symbols:info" class="me-1"></iconify-icon>
                                                            When checked, CGST and SGST will be set to 0%.
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Email Options -->
                                            <div class="col-md-6">
                                                <div class="card border border-primary-200 bg-primary-50">
                                                    <div class="card-body p-15">
                                                        <h6 class="fw-semibold text-primary-600 mb-2 d-flex align-items-center">
                                                            <iconify-icon icon="material-symbols:email" class="me-2"></iconify-icon>
                                                            Email Options
                                                        </h6>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="send_email" id="send_email" checked>
                                                            <label class="form-check-label" for="send_email">
                                                                <strong>Send invoice email automatically</strong>
                                                            </label>
                                                        </div>
                                                        <small class="text-muted d-block mt-1" id="email-info">
                                                            <iconify-icon icon="material-symbols:info" class="me-1"></iconify-icon>
                                                            Email will be queued and sent based on environment settings.
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="d-flex flex-wrap align-items-center justify-content-end gap-2 mt-3">
                                            <a href="/invoices" class="btn btn-sm btn-neutral-500 radius-8 d-inline-flex align-items-center gap-1">
                                                Cancel
                                            </a>
                                            <button type="submit" class="btn btn-sm btn-primary-600 radius-8 d-inline-flex align-items-center gap-1 btn-submit" disabled>
                                                <iconify-icon icon="material-symbols:add" class="me-1"></iconify-icon>
                                                Create Inventory Invoice
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('script')
<script>
$(document).ready(function () {
    $('.select2').select2();
    
    let selectedItems = [];
    let itemCounter = 0;

    // Load inventory items when category changes
    $('#category_filter').on('change', function() {
        loadInventoryItems();
    });

    // Load all items initially
    loadInventoryItems();

    function loadInventoryItems() {
        const categoryId = $('#category_filter').val();

        $.get('/api/inventory-items', { category_id: categoryId })
            .done(function(items) {
                const $select = $('#inventory_item');
                $select.empty().append('<option value="">Select an item to add</option>');

                items.forEach(function(item) {
                    // Allow adding same item multiple times, but show remaining stock
                    const usedQuantity = getTotalUsedQuantity(item.id);
                    const availableStock = item.quantity_available - usedQuantity;

                    if (availableStock > 0) {
                        const gstInfo = item.is_gst_applicable ?
                            ` - GST: ${(parseFloat(item.cgst_rate) + parseFloat(item.sgst_rate)).toFixed(1)}%` :
                            ' - GST Exempt';
                        $select.append(`<option value="${item.id}">${item.name} (${item.sku_code}) - ₹${item.unit_price}${gstInfo} - Available: ${availableStock}</option>`);
                    }
                });

                $select.trigger('change');
            })
            .fail(function() {
                alert('Failed to load inventory items');
            });
    }

    function getTotalUsedQuantity(itemId) {
        let totalUsed = 0;
        selectedItems.forEach(function(item) {
            if (item.id == itemId) {
                const quantity = parseInt($(`input[name="items[${item.index}][quantity]"]`).val()) || 0;
                totalUsed += quantity;
            }
        });
        return totalUsed;
    }

    // Add item to invoice
    $('#inventory_item').on('change', function() {
        const itemId = $(this).val();
        if (!itemId) return;

        // Show loading state
        const $select = $(this);
        const originalHtml = $select.html();
        $select.html('<option value="">Loading item details...</option>').prop('disabled', true);

        $.get(`/api/inventory-items/${itemId}`)
            .done(function(item) {
                addItemToInvoice(item);
                $('#inventory_item').val('').trigger('change');
            })
            .fail(function(xhr, status, error) {
                console.error('Failed to load item details:', error);
                alert('Failed to load item details. Please try again.');
            })
            .always(function() {
                // Restore select and reload items
                $select.prop('disabled', false);
                loadInventoryItems();
            });
    });

    function addItemToInvoice(item) {
        const itemIndex = itemCounter++;
        const usedQuantity = getTotalUsedQuantity(item.id);
        const availableStock = item.quantity_available - usedQuantity;

        if (availableStock <= 0) {
            alert(`No more stock available for ${item.name}`);
            return;
        }

        selectedItems.push({
            id: item.id,
            index: itemIndex,
            ...item
        });

        $('#no-items-row').hide();

        const gstDisplay = item.is_gst_applicable ?
            `<span class="badge bg-success-100 text-success-600 px-2 py-1 rounded-pill fw-semibold text-xs">
                ${(parseFloat(item.cgst_rate) + parseFloat(item.sgst_rate)).toFixed(1)}%
            </span>
            <br><small class="text-muted">C:${parseFloat(item.cgst_rate).toFixed(1)}% S:${parseFloat(item.sgst_rate).toFixed(1)}%</small>` :
            `<span class="badge bg-neutral-200 text-neutral-600 px-2 py-1 rounded-pill fw-semibold text-xs">Exempt</span>`;

        const row = `
            <tr data-item-index="${itemIndex}" data-item-id="${item.id}">
                <td>
                    <strong>${item.name}</strong>
                    <input type="hidden" name="items[${itemIndex}][inventory_item_id]" value="${item.id}">
                </td>
                <td>${item.sku_code}</td>
                <td>${item.description || 'N/A'}</td>
                <td>₹${parseFloat(item.unit_price).toFixed(2)}</td>
                <td>
                    <input type="number" class="form-control quantity-input"
                           name="items[${itemIndex}][quantity]"
                           value="1" min="1" max="${availableStock}"
                           data-item-index="${itemIndex}" data-item-id="${item.id}"
                           data-unit-price="${item.unit_price}"
                           data-cgst-rate="${item.cgst_rate}"
                           data-sgst-rate="${item.sgst_rate}"
                           data-igst-rate="${item.igst_rate}"
                           data-is-gst-applicable="${item.is_gst_applicable}">
                    <small class="text-muted available-stock">Available: ${availableStock}</small>
                </td>
                <td class="gst-info">${gstDisplay}</td>
                <td class="item-total">₹${parseFloat(item.unit_price).toFixed(2)}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger remove-item" data-item-index="${itemIndex}">
                        <iconify-icon icon="material-symbols:delete-outline"></iconify-icon>
                    </button>
                </td>
            </tr>
        `;

        $('#invoice-items-body').append(row);
        calculateTotals();
        updateSubmitButton();
        updateAllStockLimits(); // Update stock limits for all items
        loadInventoryItems(); // Refresh dropdown to show updated available quantities
    }

    // Handle quantity changes
    $(document).on('input', '.quantity-input', function() {
        const itemIndex = $(this).data('item-index');
        const itemId = $(this).data('item-id');
        const unitPrice = parseFloat($(this).data('unit-price'));
        const quantity = parseInt($(this).val()) || 0;
        const total = unitPrice * quantity;

        $(this).closest('tr').find('.item-total').text(`₹${total.toFixed(2)}`);
        calculateTotals();
        updateAllStockLimits(); // Update stock limits when quantity changes
    });

    function updateAllStockLimits() {
        // Update stock limits for all items based on current quantities
        $('.quantity-input').each(function() {
            const $input = $(this);
            const itemId = $input.data('item-id');
            const currentIndex = $input.data('item-index');

            // Find the original item data
            const originalItem = selectedItems.find(item => item.index === currentIndex);
            if (!originalItem) return;

            // Calculate total used quantity for this item (excluding current input)
            let totalUsed = 0;
            $('.quantity-input').each(function() {
                const $otherInput = $(this);
                const otherItemId = $otherInput.data('item-id');
                const otherIndex = $otherInput.data('item-index');

                if (otherItemId == itemId && otherIndex !== currentIndex) {
                    totalUsed += parseInt($otherInput.val()) || 0;
                }
            });

            const availableForThis = originalItem.quantity_available - totalUsed;
            $input.attr('max', availableForThis);
            $input.closest('tr').find('.available-stock').text(`Available: ${availableForThis}`);

            // Validate current value
            const currentValue = parseInt($input.val()) || 0;
            if (currentValue > availableForThis) {
                $input.val(availableForThis);
                $input.trigger('input');
            }
        });
    }

    // Remove item
    $(document).on('click', '.remove-item', function() {
        const itemIndex = $(this).data('item-index');
        selectedItems = selectedItems.filter(item => item.index !== itemIndex);
        $(this).closest('tr').remove();

        if (selectedItems.length === 0) {
            $('#no-items-row').show();
        }

        calculateTotals();
        updateSubmitButton();
        updateAllStockLimits(); // Update stock limits after removal
        loadInventoryItems(); // Refresh available items
    });

    function calculateTotals() {
        let subtotal = 0;
        let totalCgst = 0;
        let totalSgst = 0;

        $('.quantity-input').each(function() {
            const unitPrice = parseFloat($(this).data('unit-price'));
            const quantity = parseInt($(this).val()) || 0;
            const itemTotal = unitPrice * quantity;
            subtotal += itemTotal;

            // Calculate GST for this item if applicable and not overridden by "without GST" checkbox
            const withoutGst = $('#without_gst').is(':checked');
            const isGstApplicable = $(this).data('is-gst-applicable');

            if (!withoutGst && isGstApplicable) {
                const cgstRate = parseFloat($(this).data('cgst-rate')) || 0;
                const sgstRate = parseFloat($(this).data('sgst-rate')) || 0;

                totalCgst += (itemTotal * cgstRate) / 100;
                totalSgst += (itemTotal * sgstRate) / 100;
            }
        });

        const total = subtotal + totalCgst + totalSgst;

        $('#subtotal').text(subtotal.toFixed(2));
        $('#cgst').text(totalCgst.toFixed(2));
        $('#sgst').text(totalSgst.toFixed(2));
        $('#total').text(total.toFixed(2));
    }

    function updateSubmitButton() {
        const hasItems = selectedItems.length > 0;
        const hasClient = $('#client').val() !== '';
        $('.btn-submit').prop('disabled', !(hasItems && hasClient));
    }

    // Handle GST checkbox
    $('#without_gst').on('change', function() {
        calculateTotals();
    });

    // Handle client selection
    $('#client').on('change', function() {
        updateSubmitButton();
        updateEmailInfo();
    });

    function updateEmailInfo() {
        const clientId = $('#client').val();
        const emailInfo = $('#email-info');
        const sendEmailCheckbox = $('#send_email');

        if (!clientId) {
            emailInfo.html('<iconify-icon icon="material-symbols:info" class="me-1"></iconify-icon>Select a client to see email information.');
            return;
        }

        $.get('/clients/' + clientId + '/email-info')
            .done(function(response) {
                let infoText = '';
                let iconClass = 'material-symbols:info';
                let textClass = 'text-muted';

                if (response.app_env === 'live') {
                    if (response.client_email) {
                        infoText = `Email will be sent to: ${response.client_email}`;
                        iconClass = 'material-symbols:check-circle';
                        textClass = 'text-success';
                    } else {
                        infoText = 'No email address found for this client. Email will not be sent.';
                        iconClass = 'material-symbols:warning';
                        textClass = 'text-warning';
                        sendEmailCheckbox.prop('checked', false);
                    }
                } else {
                    if (response.tester_email) {
                        infoText = `Test environment: Email will be sent to test address: ${response.tester_email}`;
                        iconClass = 'material-symbols:science';
                        textClass = 'text-info';
                    } else {
                        infoText = 'Test environment: No test email configured. Email will not be sent.';
                        iconClass = 'material-symbols:warning';
                        textClass = 'text-warning';
                        sendEmailCheckbox.prop('checked', false);
                    }
                }

                emailInfo.html(`<iconify-icon icon="${iconClass}" class="me-1"></iconify-icon>${infoText}`)
                         .removeClass('text-muted text-success text-warning text-info')
                         .addClass(textClass);
            })
            .fail(function() {
                emailInfo.html('<iconify-icon icon="material-symbols:error" class="me-1"></iconify-icon>Unable to check email settings.')
                         .removeClass('text-muted text-success text-warning text-info')
                         .addClass('text-danger');
            });
    }

    // Form validation
    $('#inventory-invoice-form').on('submit', function(e) {
        // Check if we have items
        if (selectedItems.length === 0) {
            e.preventDefault();
            alert('Please add at least one inventory item to the invoice.');
            return false;
        }

        // Check if client is selected
        if (!$('#client').val()) {
            e.preventDefault();
            alert('Please select a client.');
            return false;
        }

        // Check if invoice date is selected
        if (!$('#invoice_date').val()) {
            e.preventDefault();
            alert('Please select an invoice date.');
            return false;
        }

        // Validate all quantities
        let hasValidQuantities = true;
        $('.quantity-input').each(function() {
            const quantity = parseInt($(this).val()) || 0;
            const maxQuantity = parseInt($(this).attr('max')) || 0;

            if (quantity <= 0) {
                hasValidQuantities = false;
                alert('All quantities must be greater than 0.');
                return false;
            }

            if (quantity > maxQuantity) {
                hasValidQuantities = false;
                alert(`Quantity for some items exceeds available stock.`);
                return false;
            }
        });

        if (!hasValidQuantities) {
            e.preventDefault();
            return false;
        }

        // Log form data for debugging
        console.log('Form submission data:');
        console.log('Client:', $('#client').val());
        console.log('Invoice Date:', $('#invoice_date').val());
        console.log('Selected Items:', selectedItems.length);
        console.log('Without GST:', $('#without_gst').is(':checked'));

        // Log all form inputs
        const formData = new FormData(this);
        for (let [key, value] of formData.entries()) {
            console.log(key, value);
        }

        // Check if we have any items in the form
        const itemInputs = $('input[name^="items["]');
        console.log('Item inputs found:', itemInputs.length);

        if (itemInputs.length === 0) {
            e.preventDefault();
            alert('No item data found in form. Please add items and try again.');
            $('.btn-submit').prop('disabled', false).html('Create Inventory Invoice');
            return false;
        }

        // Disable submit button to prevent double submission
        $('.btn-submit').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Creating Invoice...');

        // Allow form submission
        return true;
    });

    // Initialize
    updateSubmitButton();
    updateEmailInfo();
});
</script>
@stop
