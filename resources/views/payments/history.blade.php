@php
    $title = 'Payment';
    $moduleTitle = 'Payments';
    $moduleIcon = 'solar:wallet-money-bold';
    $entityIcon = 'solar:wallet-money-bold';
    $indexRoute = route('payments.index');
    $viewRoute = route('payments.show', $payment->id);
    $editRoute = auth()->user()->can('payment-edit') ? route('payments.edit', $payment->id) : null;
    $entityInfo = [
        'Payment ID' => $payment->id,
        'Client' => $payment->client->name . ' (' . $payment->client->client_code . ')',
        'Amount' => '<span class="text-success fw-bold">₹' . number_format($payment->amount, 2) . '</span>',
        'Payment Mode' => $payment->payment_mode,
        'Payment Date' => $payment->paid_on ? date('d-m-Y', strtotime($payment->paid_on)) : 'N/A',
        'Reference' => $payment->ref_number ?: 'N/A',
        'Created By' => $payment->created_by,
        'Created At' => $payment->created_at->format('d-m-Y h:i A')
    ];
    $changeLogs = $history; // Use the existing $history variable
@endphp

@include('shared.change-logs', [
    'title' => $title,
    'moduleTitle' => $moduleTitle,
    'moduleIcon' => $moduleIcon,
    'entityIcon' => $entityIcon,
    'indexRoute' => $indexRoute,
    'viewRoute' => $viewRoute,
    'editRoute' => $editRoute,
    'entityInfo' => $entityInfo,
    'changeLogs' => $changeLogs
])
