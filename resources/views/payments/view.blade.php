@extends('layouts.master')
@section('title', 'Payments View - Paidash')
@section('content')
@if(session('success'))
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('success') }}</span>

        </div>
        <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
            <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
        </button>
    </div>
@else
    <!-- Hidden snackbar for JavaScript use -->
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between" style="display: none;">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"></span>
        </div>
        <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
            <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
        </button>
    </div>
@endif
<div class="dashboard-main-body">

    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">View Payment</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">
                <a href="/payments" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="hugeicons:money-send-square" class="icon text-lg"></iconify-icon>
                    Payments
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">View Payment</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-header">
            <div class="d-flex flex-wrap align-items-center justify-content-end gap-2">
                <a href="{{ route('payments.download', $payment->id) }}"  class="btn btn-sm btn-warning radius-8 d-inline-flex align-items-center gap-1">
                    <iconify-icon icon="solar:download-linear" class="text-xl"></iconify-icon>
                    Download
                </a>
                <button type="button" data-bs-toggle="modal" data-bs-target="#sendEmailModal"
                    class="btn btn-sm btn-success radius-8 d-inline-flex align-items-center gap-1"
                    onclick="loadEmailModal({{ $payment->id }})">
                    <iconify-icon icon="solar:letter-linear" class="text-xl"></iconify-icon>
                    Send Mail
                </button>
                <button target="_blank" class="btn btn-sm btn-danger radius-8 d-inline-flex align-items-center gap-1" onclick="printPaymentReceipt()">
                    <iconify-icon icon="basil:printer-outline" class="text-xl"></iconify-icon>
                    Print
                </button>
                <a href="{{ route('payments.history', $payment->id) }}" class="btn btn-sm btn-outline-primary radius-8 d-inline-flex align-items-center gap-1">
                    <iconify-icon icon="mdi:history" class="text-xl"></iconify-icon>
                    Change Logs
                </a>
            </div>
        </div>
        <div class="card-body py-40">
            <div class="row justify-content-center" id="invoice">
                <div class="col-lg-9">
                    <div class="shadow-4 border radius-8 p-20">
                        <header class="d-flex justify-content-between align-items-center border-bottom pb-3">
                            <img src="{{ url('storage/'.$company_details['logo']) }}" alt="Company" class="img-fluid" width="70">
                            <div>
                                <address>
                                    {{$company_details['legal_name']}}<br>
                                    {!! (($company_details['address']) ? nl2br($company_details['address']) . '<br>' : '') !!}
                                    {!! (($company_details['gst'])?' GSTIN:'.$company_details['gst'].' <br>':'') !!}
                                    {!! (($company_details['website'])?' Website:'.$company_details['website'].' <br>':'') !!}
                                    {!! (($company_details['email'])?' Email:'.$company_details['email'].' <br>':'') !!}
                                    {!! (($company_details['phone'])?' Phone:'.$company_details['phone']:'') !!}
                                </address>
                            </div>
                        </header>

                        <h5 class="text-center py-4 border-bottom">PAYMENT RECEIPT</h5>

                        <div class="row mb-4">
                            <div class="col-md-8">
                                <p><strong>Payment Date:</strong> {{date('d-m-Y',strtotime($payment->paid_on))}}</p>
                                <p><strong>Reference Number:</strong> {{$payment->ref_number?$payment->ref_number:'NA'}}</p>
                                <p><strong>Payment Mode:</strong> {{$payment->payment_mode}}</p>
                                @php
                                    $formatter = new NumberFormatter('en', NumberFormatter::SPELLOUT);
                                    $amountInWords = ucfirst($formatter->format($payment->amount));
                                @endphp
                                <p><strong>Amount Received In Words:</strong> {{$amountInWords}} only</p>
                            </div>
                            <div class="col-md-4 text-center bg-success text-white py-4 rounded">
                                <div class="d-flex flex-column align-items-center justify-content-center h-100 align-items-stretch ">
                                    <h7>Amount Received</h7>
                                    <h6>₹{{$payment->amount}}</h6>
                                </div>

                            </div>
                        </div>

                        <div class="border-top pt-3">
                            <div class="row">
                                <div class="col-md-8">
                                    <p class="fw-bold">Received From</p>
                                    <p><strong>{{$payment->client->business_name}}</strong><br>{{$payment->client->address}}, {{$payment->client->area}}, <br>{{$payment->client->city}}, {{$payment->client->district->name}}, {{$payment->client->state->name}}, {{$payment->client->pincode}}</p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <p class="fw-bold">Authorized Signature</p>
                                    <div class="border-top mt-3" style="width: 200px; margin-left: auto;"></div>
                                    @if (!empty($company_details['auth_sign']))
                                    <div class="mt-2">
                                        <img src="{{ url('storage/'.$company_details['auth_sign']) }}" alt="Company" class="img-fluid" width="70">
                                    </div>
                                @endif
                                </div>
                            </div>
                        </div>

                        <h6 class="mt-5">Payment for</h6>
                        <table class="table table-bordered mt-3">
                            <thead class="table-light">
                                <tr>
                                    <th>Invoice Number</th>
                                    <th>Invoice Date</th>
                                    <th class="text-end">Invoice Amount</th>
                                    <th class="text-end">Payment Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><a href="/invoices/view/{{$payment->invoice->id}}">{{$payment->invoice->invoice_code}}</a></td>
                                    <td>{{date('d-m-Y',strtotime($payment->invoice->invoice_date))}}</td>
                                    <td class="text-end">₹{{$payment->invoice->grand_total}}</td>
                                    <td class="text-end">₹{{$payment->amount}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Send Email Modal -->
<div class="modal fade" id="sendEmailModal" tabindex="-1" aria-labelledby="sendEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success-100">
                <h5 class="modal-title d-flex align-items-center gap-2" id="sendEmailModalLabel">
                    <iconify-icon icon="solar:letter-bold" class="text-success"></iconify-icon>
                    Send Payment Receipt Email
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="sendEmailForm">
                    <input type="hidden" id="paymentId" name="payment_id">

                    <!-- Email Configuration Info -->
                    <div class="alert alert-info d-flex align-items-start gap-3 mb-4">
                        <iconify-icon icon="material-symbols:info" class="text-xl mt-1"></iconify-icon>
                        <div>
                            <h6 class="mb-2">Email Configuration</h6>
                            <div id="emailConfigInfo">
                                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                Loading email configuration...
                            </div>
                        </div>
                    </div>

                    <!-- Recipient Email -->
                    <div class="mb-4">
                        <label for="recipientEmail" class="form-label fw-semibold">
                            <iconify-icon icon="material-symbols:email" class="me-1"></iconify-icon>
                            Recipient Email Address
                        </label>
                        <input type="email" class="form-control" id="recipientEmail" name="recipient_email"
                               placeholder="Enter email address" required>
                        <div class="form-text">
                            <iconify-icon icon="material-symbols:edit" class="me-1"></iconify-icon>
                            You can modify the email address before sending
                        </div>
                    </div>

                    <!-- Sender Information -->
                    <div class="mb-4">
                        <label class="form-label fw-semibold">
                            <iconify-icon icon="material-symbols:person" class="me-1"></iconify-icon>
                            Sender Information
                        </label>
                        <div class="card border border-primary-200 bg-primary-50">
                            <div class="card-body p-3">
                                <div id="senderInfo">
                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                    Loading sender information...
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email Preview -->
                    <div class="mb-4">
                        <label class="form-label fw-semibold">
                            <iconify-icon icon="material-symbols:preview" class="me-1"></iconify-icon>
                            Email Preview
                        </label>
                        <div class="card border border-neutral-200">
                            <div class="card-body p-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">Subject:</small>
                                        <div class="fw-semibold" id="emailSubject">Payment Receipt - {{ $payment->ref_number ?: 'Payment ID ' . $payment->id }} from {{ config('app.name') }}</div>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">Attachment:</small>
                                        <div class="fw-semibold">
                                            <iconify-icon icon="material-symbols:attach-file" class="me-1"></iconify-icon>
                                            payment_receipt_{{ $payment->id }}.pdf
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <iconify-icon icon="material-symbols:close" class="me-1"></iconify-icon>
                    Cancel
                </button>
                <button type="button" class="btn btn-success" id="confirmSendEmail">
                    <iconify-icon icon="solar:letter-bold" class="me-1"></iconify-icon>
                    Send Email
                </button>
            </div>
        </div>
    </div>
</div>

@stop
@section('script')
<script>
    function printPaymentReceipt() {
        var url = "{{ route('payments.print', $payment->id) }}";
        var printWindow = window.open(url, '_blank');
        printWindow.onload = function() {
            printWindow.print();
        };
    }

    // Load email modal data
    function loadEmailModal(paymentId) {
        $('#paymentId').val(paymentId);

        // Reset form
        $('#recipientEmail').val('');
        $('#emailConfigInfo').html('<div class="spinner-border spinner-border-sm me-2" role="status"></div>Loading email configuration...');
        $('#senderInfo').html('<div class="spinner-border spinner-border-sm me-2" role="status"></div>Loading sender information...');

        // Load email configuration
        $.get('/payments/' + paymentId + '/email-config')
            .done(function(response) {
                updateEmailConfiguration(response);
            })
            .fail(function() {
                $('#emailConfigInfo').html('<span class="text-danger"><iconify-icon icon="material-symbols:error" class="me-1"></iconify-icon>Failed to load email configuration</span>');
            });
    }

    // Update email configuration display
    function updateEmailConfiguration(config) {
        let configHtml = '';
        let alertClass = 'alert-info';

        if (config.app_env === 'live') {
            configHtml = `
                <div class="d-flex align-items-center gap-2 mb-2">
                    <iconify-icon icon="material-symbols:check-circle" class="text-success"></iconify-icon>
                    <strong>Live Environment</strong>
                </div>
                <div>Emails are sent to actual client email addresses.</div>
            `;
            alertClass = 'alert-success';

            // Set client email as default
            $('#recipientEmail').val(config.client_email || '');
        } else {
            configHtml = `
                <div class="d-flex align-items-center gap-2 mb-2">
                    <iconify-icon icon="material-symbols:science" class="text-info"></iconify-icon>
                    <strong>Test Environment</strong>
                </div>
                <div>All emails are sent to: <strong>${config.tester_email || 'Not configured'}</strong></div>
            `;
            alertClass = 'alert-info';

            // Set tester email as default
            $('#recipientEmail').val(config.tester_email || '');
        }

        $('#emailConfigInfo').html(configHtml);
        $('.alert').removeClass('alert-info alert-success alert-warning').addClass(alertClass);

        // Update sender information
        let senderHtml = `
            <div class="d-flex align-items-center gap-2">
                <iconify-icon icon="material-symbols:person" class="text-primary"></iconify-icon>
                <strong>From:</strong> ${config.sender_name} &lt;${config.sender_email}&gt;
            </div>
        `;
        $('#senderInfo').html(senderHtml);
    }

    // Handle send email button click
    $('#confirmSendEmail').click(function() {
        const paymentId = $('#paymentId').val();
        const recipientEmail = $('#recipientEmail').val();

        if (!recipientEmail) {
            alert('Please enter a recipient email address.');
            return;
        }

        // Disable button and show loading
        $(this).prop('disabled', true).html('<div class="spinner-border spinner-border-sm me-2" role="status"></div>Sending...');

        // Send email
        $.post('/payments/' + paymentId + '/send-email', {
            recipient_email: recipientEmail,
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                // Close modal
                $('#sendEmailModal').modal('hide');

                // Show success message
                showSnackbar('Payment receipt email sent successfully to: ' + recipientEmail, 'success');
            } else {
                alert('Failed to send email: ' + (response.message || 'Unknown error'));
            }
        })
        .fail(function(xhr) {
            let errorMessage = 'Failed to send email. Please try again.';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            alert(errorMessage);
        })
        .always(function() {
            // Re-enable button
            $('#confirmSendEmail').prop('disabled', false).html('<iconify-icon icon="solar:letter-bold" class="me-1"></iconify-icon>Send Email');
        });
    });

    // Show snackbar function
    function showSnackbar(message, type = 'success') {
        const snackbar = $('#snackbar');
        const messageElement = $('#snackbar-message');

        // Update message
        messageElement.text(message);

        // Update classes based on type
        snackbar.removeClass('alert-success alert-danger alert-warning alert-info bg-success-100 bg-danger-100 bg-warning-100 bg-info-100 text-success-600 text-danger-600 text-warning-600 text-info-600 border-success-100 border-danger-100 border-warning-100 border-info-100');

        if (type === 'success') {
            snackbar.addClass('alert-success bg-success-100 text-success-600 border-success-100');
        } else if (type === 'error') {
            snackbar.addClass('alert-danger bg-danger-100 text-danger-600 border-danger-100');
        } else if (type === 'warning') {
            snackbar.addClass('alert-warning bg-warning-100 text-warning-600 border-warning-100');
        } else {
            snackbar.addClass('alert-info bg-info-100 text-info-600 border-info-100');
        }

        // Show snackbar
        snackbar.show().addClass('show');

        // Auto hide after 5 seconds
        setTimeout(function() {
            snackbar.removeClass('show');
        }, 5000);
    }

    // Close snackbar function
    function closeSnackbar() {
        $('#snackbar').removeClass('show');
    }
</script>
@stop
