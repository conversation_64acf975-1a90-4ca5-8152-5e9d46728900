@extends('layouts.master')
@section('title', 'Payments Add - Paidash')

@section('content')
<div class="dashboard-main-body">

    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Add New Payment</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">
                <a href="/payments" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="hugeicons:money-send-square" class="icon text-lg"></iconify-icon>
                    Payments
                </a>
            </li>
            <li>-</li>

            <li class="fw-medium">Add Payment</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-body">
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            <h6 class="mb-4 text-xl">Fill the required details to add a new payment</h6>
            <!-- <p class="text-neutral-500">Fill up your details and proceed next steps.</p> -->

            <form  action="{{ route('payments.store') }}" method="POST"  id="paymentForm">
                @csrf
                <div class="row gy-3">
                    <!-- Client Selection -->
                    <div class="col-sm-12">
                        <label for="client_id" class="form-label fw-semibold text-primary-light text-sm mb-8">
                            Select Client <span class="text-danger-600">*</span>
                        </label>
                        <select class="form-control radius-8 form-select select2 w-100 {{ $selectedClient ? 'border-success' : '' }}" id="client_id" name="client_id">
                            <option value="" {{ !$selectedClient ? 'selected' : '' }} disabled>Select Client</option>
                            @foreach ($clients as $client)
                                <option value="{{ $client->id }}"
                                    {{ old('client_id', $selectedClient ? $selectedClient->id : '') == $client->id ? 'selected' : '' }}>
                                    {{ $client->name.' - '. $client->client_code}}
                                </option>
                            @endforeach
                        </select>
                        @if($selectedClient)
                            <small class="text-success mt-1  d-flex align-items-center">
                                <iconify-icon icon="solar:check-circle-bold" class="me-1"></iconify-icon>
                                Client "{{ $selectedClient->name }}" has been pre-selected for you
                            </small>
                        @endif
                    </div>


                    <!-- Invoice Table -->
                    <div class="col-sm-7">
                        <table class="table bordered-table mb-0">
                            <thead>
                                <tr>
                                    <th scope="col">Select</th>
                                    <th scope="col">Invoice No</th>
                                    <th scope="col">Amount</th>
                                </tr>
                            </thead>
                            <tbody id="invoiceTableBody">
                                <tr>
                                    <td colspan="3" class="text-center">No invoices found</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Hidden Field for Invoice ID -->
                    <input type="hidden" name="invoice_id" id="invoice_id">

                    <!-- Has TDS Checkbox -->


                    <!-- TDS Amount Field (Hidden Initially) -->
                    {{-- <div class="col-sm-6 d-none" id="tdsAmountDiv">
                        <label class="form-label">TDS Amount</label>
                        <input type="number" class="form-control" id="tds_amount" name="tds_amount" placeholder="Enter TDS Amount">

                    </div> --}}

                    <!-- Amount Field -->
                    <div class="col-sm-6">
                        <label class="form-label">Enter Amount<span class="text-danger-600">*</span></label>
                        <div class="position-relative">
                            <input type="number" class="form-control wizard-required" id="amount" name="amount" placeholder="Enter Amount"  >
                            <div class="wizard-form-error"></div>
                        </div>
                    </div>
                    <div class="col-sm-6">

                        <label class="form-label">Has TDS
                            <button type="button" class="tooltip-button text-primary-600" data-bs-toggle="tooltip" data-bs-custom-class="tooltip-primary" data-bs-placement="right">
                            <iconify-icon icon="jam:alert" class="text-primary-light text-lg mt-4"></iconify-icon>
                        </button>
                        <div class="my-tooltip tip-content hidden text-start shadow">
                            <h6 class="text-white">Tax Deducted at Source (TDS)</h6>
                            <p class="text-white">Select this option if TDS is applicable on this payment. The deducted TDS amount will be recorded separately and reflected in the transaction details.</p>
                        </div>
                        </label>
                        <div class="input-group">
                            <div class="input-group-text flex-grow-0">
                                <input type="checkbox" class="form-check-input " id="has_tds" name="has_tds">
                            </div>
                            <input type="number" class="form-control flex-grow-1" id="tds_amount" name="tds_amount" placeholder="Enter TDS Amount">
                        </div>
                        <small id="tds-error" class="error"></small>

                    </div>
                    <!-- Payment Mode -->
                    <div class="col-sm-6">
                        <label class="form-label">Payment Mode<span class="text-danger-600">*</span></label>
                        <select class="form-control radius-8 form-select" id="payment_mode" name="payment_mode">
                            <option selected disabled>Payment Mode</option>
                            <option>Cash</option>
                            <option>Cheque</option>
                            <option>UPI</option>
                            <option>Net Banking</option>
                        </select>
                    </div>

                    <!-- Payment Reference -->
                    <div class="col-sm-6">
                        <label class="form-label">Payment Reference Number</label>
                        <input type="text" class="form-control wizard-required" name="payment_reference" placeholder="Payment Reference Number" >
                    </div>

                    <!-- Payment Date -->
                    <div class="col-sm-6">
                        <label class="form-label">Payment Date<span class="text-danger-600">*</span></label>
                        <input type="text" name="payment_date" id="payment_date" class="form-control" readonly>
                    </div>

                    <!-- Description -->
                    <div class="col-12">
                        <label class="form-label">Description / Comments<span class="text-danger-600">*</span></label>
                        <textarea name="description" class="form-control" rows="4" placeholder="Enter Description..."></textarea>
                    </div>

                    <!-- Email Options -->
                    <div class="form-group">
                        <div class="card border border-primary-200 bg-primary-50">
                            <div class="card-body p-15">
                                <h6 class="fw-semibold text-primary-600 mb-2 d-flex align-items-center">
                                    <iconify-icon icon="material-symbols:email" class="me-2"></iconify-icon>
                                    Email Options
                                </h6>
                                <div class="form-check d-flex align-items-center gap-2">
                                    <input class="form-check-input" type="checkbox" name="send_email" id="send_email" checked>
                                    <label class="form-check-label" for="send_email">
                                        <strong>Send payment receipt email automatically</strong>
                                    </label>
                                </div>
                                <small class="text-muted d-flex align-items-center mt-1" id="email-info">
                                    <iconify-icon icon="material-symbols:info" class="me-1"></iconify-icon>
                                    Email will be queued and sent based on environment settings.
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="form-group d-flex align-items-center justify-content-end gap-8">
                        <a type="button" href="/payments" class="btn btn-neutral-500 border-neutral-100 px-32">Cancel</a>
                        <button type="submit" class="btn btn-primary-600 px-32 btn-submit">Submit</button>
                    </div>
                </div>
            </form>

        </div>
    </div>

</div>
@stop
@section('script')
<script>
$(function () {
    $('#payment_date').daterangepicker({
        singleDatePicker: true,
        showDropdowns: true,
        autoApply: true,
        maxDate: moment(), // Restrict future dates
        locale: {
            format: 'DD/MM/YYYY' // Required format
        }
    }, function (chosen_date) {
        $('#payment_date').val(chosen_date.format('DD/MM/YYYY')); // Set selected date
    });
});

$(document).ready(function () {
    $('.select2').select2({
        width: 'resolve'
    });
    $('#tds_amount').prop('disabled', true).val('');
        // Custom validation rule for Amount + TDS
        $.validator.addMethod("amountCheck", function (value, element) {
        var invoiceAmount = parseFloat($('#invoiceTableBody input:checked').data('amount') || 0);
        var enteredAmount = parseFloat($('#amount').val()) || 0;
        var tdsAmount = ($('#has_tds').is(':checked')) ? (parseFloat($('#tds_amount').val()) || 0) : 0;
        return (enteredAmount + tdsAmount) <= invoiceAmount;
    }, "Total amount (Amount + TDS) cannot exceed invoice amount.");

    // Show/Hide TDS Amount Field
    $('#has_tds').change(function () {
        if ($(this).is(':checked')) {
            $('#tds_amount').prop('disabled', false); // Enable TDS field
        } else {
            $('#tds_amount').prop('disabled', true).val(''); // Disable and reset TDS amount
        }
        validateTotalAmount();
    });


    // Load pending invoices when client changes
    $('#client_id').change(function () {
        $("input[name='amount']").val(""); // Reset amount field
        var clientId = $(this).val();
        if (clientId) {
            $.ajax({
                url: '/get-pending-invoices', // Replace with your route
                type: 'GET',
                data: { client_id: clientId },
                success: function (response) {
                    var tableBody = $('#invoiceTableBody');
                    tableBody.empty(); // Clear table
                    if (response.length > 0) {
                        $.each(response, function (index, invoice) {
                            tableBody.append(`
                                <tr>
                                    <td>
                                        <input type="checkbox" class="invoice-checkbox form-check-input border border-neutral-500"
                                               data-amount="${invoice.unpaid_amount}"
                                               value="${invoice.id}">
                                    </td>
                                    <td>${invoice.invoice_code}</td>
                                    <td>₹${invoice.unpaid_amount}</td>
                                </tr>
                            `);
                        });
                    } else {
                        tableBody.append('<tr><td colspan="3" class="text-center">No invoices found</td></tr>');
                    }
                }
            });
        }
    });

       // When an invoice checkbox is checked, update the amount field
       $(document).on('change', '.invoice-checkbox', function () {
        $('.invoice-checkbox').not(this).prop('checked', false); // Uncheck other checkboxes

        if ($(this).is(':checked')) {
            var selectedInvoiceId = $(this).val();
            var selectedAmount = parseFloat($(this).data('amount'));

            $('#amount').val(selectedAmount).attr('max', selectedAmount); // Set max value
            $('#invoice_id').val(selectedInvoiceId);
        } else {
            $('#amount').val('');
            $('#invoice_id').val('');
        }
        validateTotalAmount();
    });

   // Validate Amount and TDS Amount on Change
   $('#amount, #tds_amount').on('input', function () {
        validateTotalAmount();
        $('#amount').valid();
        $('#tds_amount').valid(); // Re-check validation for the field being changed
    });

    // Function to Validate Amount + TDS
    function validateTotalAmount() {
        var invoiceAmount = parseFloat($('#invoiceTableBody input:checked').data('amount') || 0);
        var enteredAmount = parseFloat($('#amount').val()) || 0;
        var tdsAmount = ($('#has_tds').is(':checked')) ? (parseFloat($('#tds_amount').val()) || 0) : 0;
        var totalAmount = enteredAmount + tdsAmount;

        if (totalAmount > invoiceAmount) {
            $('#tds-error').text('Total amount (Amount + TDS) cannot exceed invoice amount.');
            return false;
        } else {
            $('#tds-error').text('');
            return true;
        }
    }

    // Restrict payment date selection to today or earlier
    var today = new Date().toISOString().split("T")[0];
    $("input[name='payment_date']").attr("max", today);

     // Form Validation
     $("#paymentForm").validate({
        rules: {
            client_id: { required: true },
            amount: { required: true, number: true, min: 1, amountCheck: true },
            payment_mode: { required: true },
            payment_date: { required: true },
            description: { required: true },
            tds_amount: {
                number: true,
                min: 0,
                required: function () {
                    return $('#has_tds').is(':checked'); // Required only if TDS is checked
                }
            }
        },
        messages: {
            client_id: "Please select a client",
            amount: "Please enter a valid amount",
            payment_mode: "Please select a payment mode",
            payment_date: "Please select a valid date",
            description: "Please enter a description",
            tds_amount: "Please enter a valid TDS amount"
        },
        errorPlacement: function (error, element) {
            if (element.hasClass("select2-hidden-accessible")) {
                error.insertAfter(element.next('.select2-container'));
            } else {
                error.insertAfter(element);
            }
        },
        submitHandler: function (form) {
            if (validateTotalAmount()) {
                $(".btn-submit").prop("disabled", true).text("Processing..."); // Disable button and change text
                form.submit();
            } else {
                $('#tds-error').text('Total amount (Amount + TDS) cannot exceed invoice amount.');
            }
        }
    });

    // Update email info when client changes
    $('#client_id').on('change', function() {
        updateEmailInfo();
    });

    // Trigger client change event if client is pre-selected (from URL parameter)
    @if($selectedClient)
        // Wait for Select2 to initialize, then trigger change event
        setTimeout(function() {
            $('#client_id').trigger('change');
        }, 1000);
    @endif

    // Function to update email information
    function updateEmailInfo() {
        var clientId = $('#client_id').val();
        var emailInfo = $('#email-info');

        if (clientId) {
            // Get email configuration from server
            $.get('/clients/' + clientId + '/email-info')
                .done(function(response) {
                    let infoText = '';
                    let iconClass = 'material-symbols:info';
                    let textClass = 'text-muted';

                    if (response.app_env === 'live') {
                        if (response.client_email) {
                            infoText = `Email will be sent to: ${response.client_email}`;
                            iconClass = 'material-symbols:check-circle';
                            textClass = 'text-success';
                        } else {
                            infoText = 'No email address found for this client. Email will not be sent.';
                            iconClass = 'material-symbols:warning';
                            textClass = 'text-warning';
                            $('#send_email').prop('checked', false);
                        }
                    } else {
                        if (response.tester_email) {
                            infoText = `Email will be sent to test address: ${response.tester_email}`;
                            iconClass = 'material-symbols:science';
                            textClass = 'text-info';
                        } else {
                            infoText = 'No test email configured. Email will not be sent.';
                            iconClass = 'material-symbols:warning';
                            textClass = 'text-warning';
                            $('#send_email').prop('checked', false);
                        }
                    }

                    emailInfo.html(`<iconify-icon icon="${iconClass}" class="me-1"></iconify-icon>${infoText}`)
                             .removeClass('text-muted text-success text-warning text-info')
                             .addClass(textClass);
                })
                .fail(function() {
                    emailInfo.html('<iconify-icon icon="material-symbols:error" class="me-1"></iconify-icon>Unable to check email settings.')
                             .removeClass('text-muted text-success text-warning text-info')
                             .addClass('text-danger');
                });
        } else {
            emailInfo.html('<iconify-icon icon="material-symbols:info" class="me-1"></iconify-icon>Please select a client to see email information.')
                     .removeClass('text-success text-warning text-info text-danger')
                     .addClass('text-muted');
        }
    }

    // Initialize email info on page load
    updateEmailInfo();

});
const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));

    // Boxed Tooltip
    $(document).ready(function() {
        $('.tooltip-button').each(function () {
            var tooltipButton = $(this);
            var tooltipContent = $(this).siblings('.my-tooltip').html();

            // Initialize the tooltip
            tooltipButton.tooltip({
                title: tooltipContent,
                trigger: 'hover',
                html: true
            });

            // Optionally, reinitialize the tooltip if the content might change dynamically
            tooltipButton.on('mouseenter', function() {
                tooltipButton.tooltip('dispose').tooltip({
                    title: tooltipContent,
                    trigger: 'hover',
                    html: true
                }).tooltip('show');
            });
        });
    });

</script>
@stop
