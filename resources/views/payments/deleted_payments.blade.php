@extends('layouts.master')
@section('title', 'Deleted Payments - Paidash')
@section('content')
<div class="dashboard-main-body">
@if(session('success'))
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('success') }}</span>
        </div>
        <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
            <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
        </button>
    </div>
@endif
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Cancelled Payments</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Cancelled Payments</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-body">
            <!-- Table View for Desktop -->
            <div class="d-none d-md-block">
                <table id="deletedPaymentsTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                    <thead>
                        <tr>
                            <th>Client Name</th>
                            <th>Client Code</th>
                            <th>Invoice Code</th>
                            <th>Amount</th>
                            <th>Payment Mode</th>
                            <th>Payment Date</th>
                            <th>Cancelled By</th>
                            <th>Cancelled On</th>
                            <th>Cancelled Reason</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                    <tfoot>
                        <tr>
                            <th colspan="3" class="text-end">Total</th>
                            <th id="totalInvoice" colspan="6"><strong>₹ 0.00</strong></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>
@stop
@section('script')
<script>
    $(document).ready(function () {
        var table = $('#deletedPaymentsTable').DataTable({
            // processing: true,
            serverSide: true,
            searching: false,
            pageLength: 25,
            ajax: {
                url: "{{ route('payments.getDeletedPayments') }}",
                data: function (d) {
                    d.daterange = $('#daterange').val();
                    d.client = $('#client').val();
                }
            },
            columns: [
                { data: 'client_name', name: 'client_name' },
                { data: 'client_code', name: 'client_code' },
                { data: 'invoice_code', name: 'invoice_code' },
                { data: 'amount', name: 'amount',
                    render: function(data, type, row) {
                        return `₹ ${parseFloat(data).toFixed(2)}`;
                    }
                },
                { data: 'payment_mode', name: 'payment_mode' },
                { data: 'paid_on', name: 'paid_on' },
                { data: 'deleted_by', name: 'deleted_by' },
                { data: 'deleted_at', name: 'deleted_at' },
                { data: 'deleted_reason', name: 'deleted_reason' }
            ],
            drawCallback: function () {
                updateView();
            },
            footerCallback: function(row, data, start, end, display) {
                var api = this.api();
                var total = api.column(3).data().reduce(function(a, b) {
                    return parseFloat(a) + parseFloat(b);
                }, 0);
                $(api.column(3).footer()).html(`<strong>₹ ${total.toFixed(2)}</strong>`);
            }
        });
    });
</script>
@stop
