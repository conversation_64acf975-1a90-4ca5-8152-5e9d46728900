@extends('layouts.master')
@section('title', 'Payments List - Paidash')
@section('content')
<div class="dashboard-main-body">
@if(session('success'))
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('success') }}</span>

        </div>
        <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
            <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
        </button>
    </div>
@endif
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Payments List</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Payments List</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
            <div class="d-flex flex-wrap align-items-center gap-3 w-25 w-mb-100">
                <div class="icon-field w-100">
                    <select id="client" class="form-control form-select select2" title="select Client">
                        <option selected value="">Select Client</option>
                        <option value="">All</option>
                        @foreach ($clients as $client)
                             <option value="{{$client->id}}">{{$client->name}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="d-flex flex-wrap flex-md-nowrap align-items-center gap-3">
                <div class="input-group date_range mw-300">
                    <input type="text" class="form-control" name="daterange" id="daterange">
                    <div class="input-group-append">
                        <span class="input-group-text" id="basic-addon2">
                            <iconify-icon icon="mdi:calendar" width="24"
                                height="24"></iconify-icon>
                        </span>
                    </div>
                </div>
                @can('payment-create')
                    <a href="/payments/add" class="btn btn-sm btn-primary-600 d-flex"><i class="ri-add-line"></i>&nbsp;<span class="d-none d-md-block">Create&nbsp;</span>Payment</a>
                @endcan
            </div>
        </div>
        <div class="card-body">
            <!-- Table View for Desktop -->
            <div class="d-none d-md-block">
                <table id="paymentsTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                    <thead>
                        <tr>
                            <th>Client Name</th>
                            <th>Client Code</th>
                            <th>Invoice Code</th>
                            <th>Paid Amount</th>
                            <th>Payment Mode</th>
                            <th>Payment Date</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                    <tfoot>
                        <tr>
                            <th colspan="3" class="text-end">Total</th>
                            <th id="totalInvoice"  colspan="4"><strong>₹ 0.00</strong></th>
                        </tr>
                    </tfoot>
                </table>
            </div>

             <!-- Mobile Grid View -->
            <div id="paymentgridView" class="d-none row g-3 mt-4"></div>
        </div>
    </div>

</div>
@stop
@section('script')
<script>
    $(document).ready(function () {
        $('.select2').select2({
            width: '300px'
        });
        var table = $('#paymentsTable').DataTable({
            processing: true,
            serverSide: true,
            searching: false,
            pageLength: 25,
            ajax: {
                url: "{{ route('payments.data') }}",
                data: function (d) {
                    d.daterange = $('#daterange').val();
                    d.client = $('#client').val();
                }
            },
            columns: [
                { data: 'client_name', name: 'client_name' },
                { data: 'client_code', name: 'client_code' },
                { data: 'invoice_code', name: 'invoice_code' },
                { data: 'amount', name: 'amount',
                                    render: function(data, type, row) {
                                        return `₹ ${parseFloat(data).toFixed(2)}`;
                                    } },
                { data: 'payment_mode', name: 'payment_mode' },
                { data: 'paid_on', name: 'paid_on' },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
             dom:
            "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" + // Entries Dropdown & CSV Button
            "<'row'<'col-md-12'tr>>" +
            "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>", // Info at left, Pagination at right
             buttons: [
                    {
                        text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export',
                        className: 'btn btn-success btn-sm bg-success-500',
                        action: function (e, dt, node, config) {
                            exportPayments();
                        }
                    },
                    {
                        text: '<iconify-icon icon="mdi:cancel"></iconify-icon> Cancelled Payments',
                        className: 'btn btn-danger btn-sm bg-danger-500',
                        action: function () {
                            window.location.href = '/deleted-payments';
                        }
                    }
                ],
            drawCallback: function () {
                updateView();
            },
            footerCallback: function(row, data, start, end, display) {
                console.log('Footer callback executed'); // Debugging

                var api = this.api();

                var total = api
                    .column(3, { page: 'current' }) // Column index 1 (discount_amount)
                    .data()
                    .reduce(function(a, b) {
                        return (parseFloat(a) || 0) + (parseFloat(b) || 0);
                    }, 0);

                console.log('Total discount:', total); // Debugging

                $(api.column(3).footer()).html(`<strong>₹ ${total.toFixed(2)}</strong>`);
            }
        });

        function updateView() {
            if (window.innerWidth <= 768) {
                $('#tableView').addClass('d-none');
                $('#paymentgridView').removeClass('d-none');

                var data = table.rows().data();
                var gridContainer = $('#paymentgridView');
                gridContainer.empty();

                data.each(function (row) {
                    var card = `
                        <div class="col-12">
                            <div class="card shadow-none border">
                                <div class="card-body">
                                    <h6 class="card-title mb-1">${row.client_name} (#${row.client_code})</h6>
                                    <p class="mb-1"><strong>Amount:</strong> ${row.amount}</p>
                                    <p class="mb-1"><strong>Payment Mode:</strong> ${row.payment_mode}</p>
                                    <p class="mb-1"><strong>Payment Date:</strong> ${row.paid_on}</p>
                                    <div class="text-end">${row.action}</div>
                                </div>
                            </div>
                        </div>`;
                    gridContainer.append(card);
                });
            } else {
                $('#tableView').removeClass('d-none');
                $('#paymentgridView').addClass('d-none');
            }
        }

        // Check on window resize
        $(window).resize(updateView);

        // Initial check on page load
        updateView();

        // Custom search event triggers
        $('#daterange').on('apply.daterangepicker', function () {
            table.draw();
        });

        $("#client, #invoice_status").on("change", function () {
            table.draw();
        });
    });
    $(function() {

var start = moment().subtract(29, 'days');  // Default start date
var end = moment();  // Default end date

function cb(start, end) {
    $('#daterange').val(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
}

$('#daterange').daterangepicker({
    startDate: start,
    endDate: end,
    autoApply: true,
    locale: { format: 'DD/MM/YYYY' }, // Set format
    ranges: {
       'Today': [moment(), moment()],
       'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
       'Last 7 Days': [moment().subtract(6, 'days'), moment()],
       'Last 30 Days': [moment().subtract(29, 'days'), moment()],
       'This Month': [moment().startOf('month'), moment().endOf('month')],
       'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    }
}, cb);

// Set initial date display
cb(start, end);

});

// Function to confirm and delete payment
function confirmDeletePayment(url, paymentId) {
    Swal.fire({
        title: 'Are you sure?',
        text: "You won't be able to revert this! This will update invoice and client balances.",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, continue'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show input dialog for reason
            Swal.fire({
                title: 'Reason Required',
                input: 'textarea',
                inputLabel: 'Please enter the reason for deleting this payment:',
                inputPlaceholder: 'Type your reason here...',
                inputAttributes: {
                    'aria-label': 'Type your reason here'
                },
                inputValidator: (value) => {
                    if (!value.trim()) {
                        return 'Reason is required!';
                    }
                },
                showCancelButton: true,
                confirmButtonText: 'Submit',
                cancelButtonText: 'Cancel'
            }).then((reasonResult) => {
                if (reasonResult.isConfirmed) {
                    const reason = reasonResult.value.trim();

                    // Send AJAX request to delete with reason
                    $.ajax({
                        url: url,
                        type: "POST",
                        data: {
                            _token: "{{ csrf_token() }}",
                            reason: reason // Pass reason to backend
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    title: "Deleted!",
                                    text: response.message,
                                    icon: "success",
                                    timer: 2000,
                                    showConfirmButton: false
                                }).then(() => {
                                    table.draw(); // Refresh the table
                                });
                            } else {
                                Swal.fire({
                                    title: "Error!",
                                    text: response.message,
                                    icon: "error",
                                });
                            }
                        },
                        error: function() {
                            Swal.fire({
                                title: "Error!",
                                text: "An error occurred while deleting the payment.",
                                icon: "error",
                            });
                        }
                    });
                }
            });
        }
    });
}

// Export function with all filters
function exportPayments() {
    // Show loader
    $('#exportLoader').show();

    // Get current filter values
    var filters = {
        client: $('#client').val(),
        daterange: $('#daterange').val()
    };

    // Build export URL with filters
    var exportUrl = "{{ route('payments.export') }}?" + $.param(filters);

    // Create temporary link and trigger download
    var link = document.createElement('a');
    link.href = exportUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Hide loader after a short delay
    setTimeout(() => {
        $('#exportLoader').hide();
    }, 1000);
}

</script>
@stop
