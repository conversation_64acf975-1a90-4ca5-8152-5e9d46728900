<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="images/favicon.png" rel="icon" />
    <title>Payment Receipt - {{config('company.details.company_suffix')}}</title>
    <!-- Web Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900" type="text/css" />
    <style>
      /* -------- PRINT SETTINGS -------- */
      @media print {
        @page {
          size: A4;
          margin: 20mm 15mm 20mm 15mm;
        }
        .page-break {
          page-break-before: always;
        }
      }

      /* -------- BASE -------- */
      body {
        font-size: 14px;
        margin: 0;
        padding: 0;
        font-family: "Poppins", Aria<PERSON>, sans-serif;
        background-color: #fff;
        color: #333;
      }

      .container {
        max-width: 210mm;
        margin: 0 auto;
        position: relative;
      }

      /* -------- TABLES -------- */
      table {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;
      }
      /* Ensure every cell and table has a visible border in PDF */
      table,
      th,
      td {
        border: 1px solid #b0b0b0; /* slightly darker for print clarity */
      }
      /* Remove the outer border when we explicitly don’t want any (we’ll override inline) */
      .no-border,
      .no-border > th,
      .no-border > td {
        border: none;
      }

      th {
        background-color: #f5f5f5;
        font-weight: 600;
      }

      td,
      th {
        padding: 6px 4px;
      }

      .text-end {
        text-align: right;
      }
      .text-center {
        text-align: center;
      }
      .text-uppercase {
        text-transform: uppercase;
      }
      .fw-bold {
        font-weight: 600;
      }

      /* -------- QR & AMOUNT -------- */
      .qr-code {
        width: 100px;
        height: 100px;
        object-fit: contain;
        border: none; /* keep QR crisp */
      }
      .qr-label {
        font-size: 10px;
        margin-top: 2px;
        color: #555;
        text-transform: uppercase;
        letter-spacing: 0.4px;
      }
      .amount-box {
        background-color: #198754;
        padding: 12px 16px;
        border-radius: 5px;
        color: #fff;
        font-size: 18px;
        display: inline-block;
      }

      /* -------- FOOTER -------- */
      .footer-note {
        font-size: 12px;
      }
    </style>
  </head>

  <body>
    <div class="container">
      <!-- ========================= HEADER ========================= -->
      <table class="no-border" cellpadding="5" cellspacing="0">
        <tr class="no-border">
          <!-- ====== Company Logo & Validation QR ====== -->
          <td class="no-border" style="width: 50%; text-align: left; vertical-align: top;">
            <table class="no-border" cellpadding="0" cellspacing="0">
              <tr class="no-border">
                <td class="no-border" style="width: 150px;">
                  @php
                    if(isset($print)){
                      $logoPath = asset('storage/'.$company_details['logo']);
                    }else{
                      $logoPath = public_path('storage/'.$company_details['logo']);
                    }
                  @endphp
                  <img src="{{ $logoPath }}" alt="Company Logo" style="width: 150px; height: auto;" />
                </td>
                <td class="no-border" style="width: 110px; text-align: center;">
                  @php
                    $controller = app()->make(App\Http\Controllers\PaymentController::class);
                    $verificationUrl = $controller->verify_payment($payment->id);
                  @endphp
                  <img src="{{ $verificationUrl }}" alt="Validate payment online" class="qr-code" />
                  <div class="qr-label">Validate Online</div>
                </td>
              </tr>
            </table>
          </td>

          <!-- ====== Company Contact ====== -->
          <td class="no-border" style="width: 50%; text-align: right; line-height: 1.5; vertical-align: top;">
            <strong>{{ $company_details['legal_name'] }}</strong><br />
            {!! ($company_details['address'] ? nl2br($company_details['address']).'<br>' : '') !!}
            {!! ($company_details['gst'] ? '<strong>GSTIN:</strong> '.$company_details['gst'].'<br>' : '') !!}
            {!! ($company_details['website'] ? '<strong>Website:</strong> '.$company_details['website'].'<br>' : '') !!}
            {!! ($company_details['email'] ? '<strong>Email:</strong> '.$company_details['email'].'<br>' : '') !!}
            {!! ($company_details['phone'] ? '<strong>Phone:</strong> '.$company_details['phone'] : '') !!}
          </td>
        </tr>
      </table>

      <!-- ========================= TITLE ========================= -->
      <table class="no-border" style="margin-top: 10px;">
        <tr class="no-border">
          <td class="no-border" align="center">
            <h4 style="font-size: 24px; color: #2c3e50;" class="text-uppercase">Payment Receipt</h4>
          </td>
        </tr>
      </table>

      <!-- ========================= PARTY & RECEIPT INFO ========================= -->
      <table cellpadding="0" cellspacing="0" style="margin-top: 10px;">
        <tr>
          <!-- ======== Received From ======== -->
          <td style="width: 50%; border-right: 0;">
            <p class="fw-bold" style="margin-bottom: 6px;">Received From:</p>
            <p><strong>{{ $payment->client->business_name }}</strong></p>
            <p>
              {{ $payment->client->address }}, {{ $payment->client->city }},<br />
              {{ $payment->client->district->name }}, {{ $payment->client->state->name }},
              {{ $payment->client->pincode }}
            </p>
          </td>

          <!-- ======== Receipt Details ======== -->
          <td style="width: 50%; border-left: 0;">
            <table class="no-border" style="width: 100%;">
              <tr class="no-border">
                <td class="no-border"><strong>Receipt No:</strong> {{ $payment->ref_number ?: 'NA' }}</td>
              </tr>
              <tr class="no-border">
                <td class="no-border"><strong>Date:</strong> {{ date('d-m-Y', strtotime($payment->paid_on)) }}</td>
              </tr>
              <tr class="no-border">
                <td class="no-border"><strong>Payment Mode:</strong> {{ $payment->payment_mode }}</td>
              </tr>
              <tr class="no-border">
                @php
                  $formatter = new NumberFormatter('en', NumberFormatter::SPELLOUT);
                  $amountInWords = ucfirst($formatter->format($payment->amount));
                @endphp
                <td class="no-border"><strong>Amount (in words):</strong> INR {{ $amountInWords }} only</td>
              </tr>
              <tr class="no-border">
                <td class="no-border" style="padding-top: 6px;">
                  <span class="amount-box">₹{{ number_format($payment->amount,2) }}</span>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>

      <!-- ========================= PAYMENT FOR ========================= -->
      <h4 style="margin: 14px 0 4px 0; font-size: 18px;" class="text-uppercase">Payment For</h4>
      <table cellpadding="5" cellspacing="0">
        <thead>
          <tr>
            <th>Invoice Number</th>
            <th>Invoice Date</th>
            <th class="text-end">Invoice Amount</th>
            <th class="text-end">Payment Amount</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>{{ $payment->invoice->invoice_code }}</td>
            <td>{{ date('d-m-Y', strtotime($payment->invoice->invoice_date)) }}</td>
            <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($payment->invoice->grand_total,2) }}</td>
            <td class="text-end" style="font-family: 'DejaVu Sans', sans-serif;">₹{{ number_format($payment->amount,2) }}</td>
          </tr>
        </tbody>
      </table>

      <!-- ========================= SIGNATURE ========================= -->
      <table class="no-border" cellpadding="0" cellspacing="0" style="margin-top: 20px;">
        <tr class="no-border">
          <td class="no-border" style="width: 50%;"></td>
          <td class="no-border" style="width: 50%; text-align: right;">
            <p class="fw-bold" style="margin-bottom: 40px;">Authorized Signature</p>
            @if (!empty($company_details['auth_sign']))
              @php
                if(isset($print)){
                  $signPath = asset('storage/'.$company_details['auth_sign']);
                }else{
                  $signPath = public_path('storage/'.$company_details['auth_sign']);
                }
              @endphp
              <img src="{{ $signPath }}" alt="Company Signature" style="width: 80px;" />
            @else
              <div style="height: 80px;"></div>
            @endif
          </td>
        </tr>
      </table>

      <!-- ========================= FOOTER ========================= -->
      <div style="margin-top: 10px; border-top: 2px solid #e0e0e0; padding-top: 4px;">
        <table class="no-border">
          <tr class="no-border">
            <td class="no-border footer-note">Printed On: {{ date('d-m-Y h:i A') }}</td>
            <td class="no-border text-end footer-note">
              <strong>NOTE:</strong> This is a computer-generated receipt and does not require a physical signature.
            </td>
          </tr>
        </table>
      </div>
    </div>
  </body>
</html>