@extends('layouts.master')

@section('content')
<div class="dashboard-main-body">
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Edit Payment</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="{{ route('dashboard') }}" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">
                <a href="{{ route('payments.index') }}" class="d-flex align-items-center gap-1 hover-text-primary">
                    Payments
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Edit Payment</li>
        </ul>
    </div>

    <div class="card h-100 p-0 radius-12">
        <div class="card-header border-bottom bg-base py-16 px-24">
            <h6 class="text-lg fw-semibold mb-0">Edit Payment - ID: {{ $payment->id }}</h6>
        </div>

        <div class="card-body py-40">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul>
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <!-- Sensitive Data Warning -->
                    <div class="alert alert-warning d-flex align-items-center mb-4">
                        <iconify-icon icon="material-symbols:warning" class="text-warning me-2 text-xl"></iconify-icon>
                        <div>
                            <strong>Important:</strong> For security reasons, sensitive data like client, invoice, amount, and TDS cannot be modified.
                            Only payment details can be updated.
                        </div>
                    </div>

                    <form action="{{ route('payments.update', $payment->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Read-only Sensitive Information -->
                        <div class="card border border-secondary-200 bg-secondary-50 mb-4">
                            <div class="card-body p-15">
                                <h6 class="fw-semibold text-secondary-600 mb-3 d-flex align-items-center">
                                    <iconify-icon icon="material-symbols:lock" class="me-2"></iconify-icon>
                                    Protected Information (Read-only)
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label class="form-label fw-semibold text-primary-light text-sm mb-8">Client</label>
                                            <input type="text" class="form-control radius-8 bg-light"
                                                   value="{{ $payment->client->name }} ({{ $payment->client->client_code }})"
                                                   readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label class="form-label fw-semibold text-primary-light text-sm mb-8">Invoice</label>
                                            <input type="text" class="form-control radius-8 bg-light"
                                                   value="{{ $payment->invoice ? $payment->invoice->invoice_code : 'No Invoice' }}"
                                                   readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label class="form-label fw-semibold text-primary-light text-sm mb-8">Payment Amount</label>
                                            <input type="text" class="form-control radius-8 bg-light"
                                                   value="₹{{ number_format($payment->amount, 2) }}"
                                                   readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label class="form-label fw-semibold text-primary-light text-sm mb-8">Created By</label>
                                            <input type="text" class="form-control radius-8 bg-light"
                                                   value="{{ $payment->created_by }}"
                                                   readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Editable Fields -->
                        <div class="card border border-primary-200 bg-primary-50 mb-4">
                            <div class="card-body p-15">
                                <h6 class="fw-semibold text-primary-600 mb-3 d-flex align-items-center">
                                    <iconify-icon icon="material-symbols:edit" class="me-2"></iconify-icon>
                                    Editable Payment Details
                                </h6>

                                <div class="row">
                                    <!-- Payment Mode -->
                                    <div class="col-md-6">
                                        <div class="form-group mb-20">
                                            <label for="payment_mode" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                                Payment Mode <span class="text-danger-600">*</span>
                                            </label>
                                            <select class="form-control radius-8 form-select" id="payment_mode" name="payment_mode" required>
                                                <option value="">Select Payment Mode</option>
                                                <option value="Cash" {{ $payment->payment_mode == 'Cash' ? 'selected' : '' }}>Cash</option>
                                                <option value="Cheque" {{ $payment->payment_mode == 'Cheque' ? 'selected' : '' }}>Cheque</option>
                                                <option value="Online Transfer" {{ $payment->payment_mode == 'Online Transfer' ? 'selected' : '' }}>Online Transfer</option>
                                                <option value="UPI" {{ $payment->payment_mode == 'UPI' ? 'selected' : '' }}>UPI</option>
                                                <option value="Card" {{ $payment->payment_mode == 'Card' ? 'selected' : '' }}>Card</option>
                                                <option value="Other" {{ $payment->payment_mode == 'Other' ? 'selected' : '' }}>Other</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Payment Date -->
                                    <div class="col-md-6">
                                        <div class="form-group mb-20">
                                            <label for="payment_date" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                                Payment Date <span class="text-danger-600">*</span>
                                            </label>
                                            <input type="text" class="form-control radius-8" id="payment_date" name="payment_date"
                                                   value="{{ $payment->paid_on ? date('d/m/Y', strtotime($payment->paid_on)) : '' }}"
                                                   placeholder="Select Date" required>
                                        </div>
                                    </div>

                                    <!-- Payment Reference -->
                                    <div class="col-md-6">
                                        <div class="form-group mb-20">
                                            <label for="payment_reference" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                                Payment Reference
                                            </label>
                                            <input type="text" class="form-control radius-8" id="payment_reference" name="payment_reference"
                                                   value="{{ $payment->ref_number }}"
                                                   placeholder="Enter reference number">
                                        </div>
                                    </div>

                                    <!-- Description -->
                                    <div class="col-md-6">
                                        <div class="form-group mb-20">
                                            <label for="description" class="form-label fw-semibold text-primary-light text-sm mb-8">
                                                Description <span class="text-danger-600">*</span>
                                            </label>
                                            <textarea class="form-control radius-8" id="description" name="description"
                                                      rows="3" placeholder="Enter payment description" required>{{ $payment->comments }}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="form-group d-flex align-items-center justify-content-end gap-8">
                            <a href="{{ route('payments.index') }}" class="btn btn-neutral-500 border-neutral-100 px-32">Cancel</a>
                            <button type="submit" class="btn btn-primary-600 px-32 btn-submit">Update Payment</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('script')
<script>
$(function () {
    // Initialize date picker
    $('#payment_date').daterangepicker({
        singleDatePicker: true,
        showDropdowns: true,
        autoApply: true,
        maxDate: moment(), // Restrict future dates
        locale: {
            format: 'DD/MM/YYYY'
        }
    }, function (chosen_date) {
        $('#payment_date').val(chosen_date.format('DD/MM/YYYY'));
    });

    // Form validation
    $("form").validate({
        rules: {
            payment_mode: { required: true },
            payment_date: { required: true },
            description: { required: true }
        },
        messages: {
            payment_mode: "Please select a payment mode",
            payment_date: "Please select a payment date",
            description: "Please enter a description"
        },
        errorPlacement: function (error, element) {
            error.insertAfter(element);
        },
        submitHandler: function (form) {
            $(".btn-submit").prop("disabled", true).text("Updating...");
            form.submit();
        }
    });
});
</script>
@stop
