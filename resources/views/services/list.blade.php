@extends('layouts.master')

@section('title', 'Services List-Paidash')
@section('content')
<div class="dashboard-main-body">
@if(session('success'))
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('success') }}</span>

        </div>
        <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
            <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
        </button>
    </div>
@endif
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Assign Service to Client</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Assign Service to Client</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
            <div class="d-flex flex-nowrap align-items-center gap-3 w-25 w-mb-100">
                <div class="icon-field w-100">
                    <select id="client" class="form-control form-select select2" title="select Client">
                        <option selected value="">Select Client</option>
                        <option value="">All</option>
                        @foreach ($clients as $client)
                             <option value="{{$client->id}}">{{$client->name}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="d-flex flex-no-wrap flex-mb-wrap align-items-center gap-3">

                <select id="searchService" class="form-control form-select" title="select Service">
                    <option selected value="">Select Service</option>
                    <option value="">All</option>
                    <option value="1">Bedded</option>
                    <option value="2">Non Bedded</option>
                </select>
                <select id="payment_cycle" class="form-control form-select" title="select Payment Cycle">
                    <option selected value="">Select Payment Cycle</option>
                    <option value="">All</option>
                    <option value="Monthly">Monthly</option>
                    <option value="Yearly">Yearly</option>
                </select>
                @can('service-assign')
                    <a href="/services/assign" class="btn btn-sm btn-primary-600 w-mb-100 text-nowrap"><i class="ri-add-line"></i> Assgin Service to Client</a>
                @endcan
            </div>
        </div>
        <div class="card-body">
            <div class="d-none d-md-block">
                <table id="clientServiceTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                    <thead>
                        <tr>
                            <th>Client</th>
                            <th>Service</th>
                            <th>Payment Cycle</th>
                            <th>Start Date</th>
                            <th>Next Inovice Date</th>
                            <th>Aprox Bill Amount</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table>
            </div>



            <!-- Grid View for Mobile -->
            <div id="servicesGrid" class="d-block d-md-none"></div>
        </div>
    </div>

</div>
<div id="exportLoader" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999;">
    <span class="spinner-border text-primary" role="status"></span>
    <strong>Exporting...</strong>
</div>
@stop
@section('script')
<script>
    $(document).ready(function () {
        $('.select2').select2();

        var table = $('#clientServiceTable').DataTable({
            processing: true,
            serverSide: true,
            searching: false,
            pageLength: 25,
            ajax: {
                url: "{{ route('services.data') }}",
                data: function (d) {
                    d.searchService = $('#searchService').val();
                    d.payment_cycle = $('#payment_cycle').val();
                    d.client = $('#client').val();
                }
            },
            columns: [
                { data: 'client', name: 'client' },
                { data: 'service', name: 'service' },
                { data: 'payment_cycle', name: 'payment_cycle' },
                { data: 'start_date', name: 'start_date' },
                { data: 'next_invoice_date', name: 'next_invoice_date' },
                { data: 'total_price', name: 'total_price' },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            drawCallback: function (settings) {
                var api = this.api();
                var data = api.rows().data();
                var gridContainer = $('#servicesGrid');
                gridContainer.empty();

                data.each(function (row) {
                    var serviceCard = `
                        <div class="card mb-3 shadow-sm border-start border-4 border-success">
                            <div class="card-body">
                                <h6 class="d-flex align-items-center">
                                    <iconify-icon icon="mdi:account-tie" class="me-2" width="20"></iconify-icon>
                                    <strong>${row.client}</strong>
                                </h6>
                                <p class="mb-1 d-flex align-items-center">
                                    <iconify-icon icon="mdi:briefcase-outline" class="me-2" width="18"></iconify-icon>
                                    <small><strong>Service:</strong> ${row.service}</small>
                                </p>
                                <p class="mb-1 d-flex align-items-center">
                                    <iconify-icon icon="mdi:calendar-repeat-outline" class="me-2" width="18"></iconify-icon>
                                    <small><strong>Payment Cycle:</strong> ${row.payment_cycle}</small>
                                </p>
                                <p class="mb-1 d-flex align-items-center">
                                    <iconify-icon icon="mdi:clock-outline" class="me-2" width="18"></iconify-icon>
                                    <small><strong>Start Date:</strong> ${row.start_date}</small>
                                </p>
                                <p class="mb-1 d-flex align-items-center">
                                    <iconify-icon icon="mdi:file-document-outline" class="me-2" width="18"></iconify-icon>
                                    <small><strong>Next Invoice:</strong> ${row.next_invoice_date}</small>
                                </p>
                                <p class="mb-2 d-flex align-items-center">
                                    <iconify-icon icon="mdi:currency-usd" class="me-2" width="18"></iconify-icon>
                                    <small><strong>Total Price:</strong> ${row.total_price}</small>
                                </p>
                                <div class="mt-2">${row.action}</div>
                            </div>
                        </div>
                    `;
                    gridContainer.append(serviceCard);
                });
            },
            dom:
                "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" + // Entries Dropdown & CSV Button
                "<'row'<'col-md-12'tr>>" +
                "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>", // Info at left, Pagination at right
                buttons: [
                    {
                        text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export',
                        className: 'btn btn-success btn-sm bg-success-500',
                        action: function (e, dt, node, config) {
                            exportServices();
                        }
                    }
                ],

            infoCallback: function(settings, start, end, max, total, pre) {
                return `Showing ${start} to ${end} of ${total} records`;
            }
        });

        // Custom search event triggers
        $("#searchService, #payment_cycle, #client").on("change", function () {
            table.draw();
        });
    });

    // Export function with all filters
    function exportServices() {
        // Show loader
        $('#exportLoader').show();

        // Get current filter values
        var filters = {
            searchService: $('#searchService').val(),
            payment_cycle: $('#payment_cycle').val(),
            client: $('#client').val()
        };

        // Build export URL with filters
        var exportUrl = "{{ route('services.export') }}?" + $.param(filters);

        // Create temporary link and trigger download
        var link = document.createElement('a');
        link.href = exportUrl;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Hide loader after a short delay
        setTimeout(() => {
            $('#exportLoader').hide();
        }, 1000);
    }

    function confirmCancelService(url) {
        Swal.fire({
            title: "Are you sure?",
            text: "Do you really want to cancel this service?",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#d33",
            cancelButtonColor: "#3085d6",
            confirmButtonText: "Yes, cancel it!"
        }).then((result) => {
            if (result.isConfirmed) {
                // Send AJAX request
                $.ajax({
                    url: url,
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: "Cancelled!",
                                text: response.message,
                                icon: "success",
                                timer: 2000,
                                showConfirmButton: false
                            }).then(() => {
                                location.reload(); // Reload page to reflect changes
                            });
                        } else {
                            Swal.fire({
                                title: "Error!",
                                text: response.message,
                                icon: "error",
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: "Error!",
                            text: "Something went wrong. Please try again!",
                            icon: "error",
                        });
                    }
                });
            }
        });
    }
</script>


@stop
