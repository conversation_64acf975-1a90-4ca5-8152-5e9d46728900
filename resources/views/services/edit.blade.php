@extends('layouts.master')
@section('title', 'Edit Service - Paidash')

@section('content')
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">Edit Service</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="/services" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="hugeicons:user-settings-01" class="icon text-lg"></iconify-icon>
                        Services
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">Edit Service</li>
            </ul>
        </div>

        <div class="card">
            <div class="card-header bg-primary-50 border-bottom">
                <div class="d-flex align-items-center gap-3">
                    <div class="w-40-px h-40-px bg-primary rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="lucide:edit" class="text-white text-lg"></iconify-icon>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1 fw-bold text-primary">Edit Service Details</h6>
                        <p class="mb-0 text-muted">Update service information for {{ $client_service->client->name }}</p>
                    </div>
                </div>

                <!-- Additional Information Alert -->
                <div class="alert alert-warning mt-3 mb-0" role="alert">
                    <div class="d-flex align-items-start gap-2">
                        <iconify-icon icon="solar:info-circle-bold" class="text-warning mt-1"></iconify-icon>
                        <div>
                            <h6 class="alert-heading mb-1 fw-semibold">Important Notice</h6>
                            <p class="mb-2 small">Changes to service details will impact future invoices and billing calculations.</p>
                            <ul class="mb-0 small">
                                <li><strong>Pricing Changes:</strong> Will apply to invoices generated after the update</li>
                                <li><strong>Service Type Changes:</strong> May affect billing frequency and calculation methods</li>
                                <li><strong>Start Date:</strong> Cannot be modified as it impacts existing billing cycles</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                @if ($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif

                @if(session('error'))
                <div class="alert alert-danger">
                    {{ session('error') }}
                </div>
                @endif

                <!-- Form Start -->
                <form action="{{ route('service.update', $client_service->id) }}" method="POST" id="editServiceForm">
                    @csrf
                    @method('PUT')
                    <div class="row gy-3">
                        <div class="col-sm-6">
                            <label for="client_id" class="form-label fw-semibold text-primary-light text-sm mb-8">Select
                                Client <span class="text-danger-600">*</span> </label>
                            <select class="form-control radius-8 form-select select2" id="client_id" name="client_id">
                                <option selected disabled>Select Client </option>
                                @foreach ($clients as $client)
                                    <option value="{{ $client->id }}" {{ $client_service->client_id == $client->id ? 'selected' : '' }}>
                                        {{ $client->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-sm-6">
                            <label for="payment_mode" class="form-label fw-semibold text-primary-light text-sm mb-8">Payment
                                Mode <span class="text-danger-600">*</span> </label>
                            <select class="form-control radius-8 form-select" id="payment_mode" name="payment_mode">
                                <option selected disabled>Payment Mode </option>
                                <option value="Monthly" {{ $client_service->payment_cycle == 'Monthly' ? 'selected' : '' }}>Monthly</option>
                                <option value="Yearly" {{ $client_service->payment_cycle == 'Yearly' ? 'selected' : '' }}>Yearly</option>
                            </select>
                        </div>

                        <div class="col-sm-6">
                            <label for="invoice_generation_type" class="form-label fw-semibold text-primary-light text-sm mb-8">Invoice Generation Type <span class="text-danger-600">*</span></label>
                            <select class="form-control radius-8 form-select" id="invoice_generation_type" name="invoice_generation_type" data-original-value="{{ $client_service->invoice_generation_type ?? 1 }}">
                                <option selected disabled>Select Invoice Generation Type</option>
                                <option value="1" {{ ($client_service->invoice_generation_type ?? 1) == 1 ? 'selected' : '' }}>End of Month</option>
                                <option value="2" {{ ($client_service->invoice_generation_type ?? 1) == 2 ? 'selected' : '' }}>Based on Start Date</option>
                                <option value="3" {{ ($client_service->invoice_generation_type ?? 1) == 3 ? 'selected' : '' }}>Custom Day</option>
                            </select>
                            <small class="text-muted" id="invoice_type_help">Select payment mode first to see detailed descriptions</small>

                            <!-- Warning Alert for Invoice Generation Type Changes -->
                            <div class="alert alert-warning mt-2" id="invoice_type_warning" style="display: none;">
                                <div class="d-flex align-items-start">
                                    <iconify-icon icon="mdi:alert" width="20" class="me-2 mt-1 text-warning"></iconify-icon>
                                    <div>
                                        <strong>Important:</strong> Changing the invoice generation type will recalculate the next invoice date. This may affect when the next invoice is generated for this service.
                                        <br><small class="text-muted">Current next invoice date: {{ $client_service->next_invoice_date ? date('d-m-Y', strtotime($client_service->next_invoice_date)) : 'Not set' }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-6" id="custom_day_section" style="display: {{ ($client_service->invoice_generation_type ?? 1) == 3 ? 'block' : 'none' }};">
                            <label for="custom_invoice_day" class="form-label fw-semibold text-primary-light text-sm mb-8">Custom Invoice Day <span class="text-danger-600">*</span></label>
                            <select class="form-control radius-8 form-select" id="custom_invoice_day" name="custom_invoice_day">
                                <option selected disabled>Select Day of Month</option>
                                @for($i = 1; $i <= 31; $i++)
                                    <option value="{{ $i }}" {{ ($client_service->custom_invoice_day ?? '') == $i ? 'selected' : '' }}>{{ $i }}</option>
                                @endfor
                            </select>
                            <small class="text-muted" id="custom_day_help">Day of the month when invoice should be generated (1-31)</small>
                        </div>
                        <div class="col-sm-6">
                            <label for="service_type" class="form-label fw-semibold text-primary-light text-sm mb-8">Service
                                Type<span class="text-danger-600">*</span> </label>
                            <select class="form-control radius-8 form-select" id="service_type" name="service_type">
                                <option selected disabled>Service Type </option>
                                @foreach ($service_types as $service_type)
                                    <option value="{{ $service_type->id }}" {{ $client_service->service_type == $service_type->id ? 'selected' : '' }}>
                                        {{ $service_type->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-sm-6">
                            <label for="service" class="form-label fw-semibold text-primary-light text-sm mb-8">Service<span
                                    class="text-danger-600">*</span> </label>
                            <select class="form-control radius-8 form-select" id="service" name="service">
                                <option selected disabled>Service </option>
                                @foreach ($services as $service)
                                    <option value="{{ $service->id }}" {{ $client_service->service_id == $service->id ? 'selected' : '' }}>
                                        {{ $service->user_label }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-sm-6">
                            <label class="form-label">No of Units<span class="text-danger-600">*</span></label>
                            <div class="position-relative">
                                <input type="number" class="form-control wizard-required" placeholder="Enter Units"
                                    id="no_of_units" name="no_of_units" value="{{ $client_service->beds_count }}">
                                <div class="wizard-form-error"></div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label">Unit Price<span class="text-danger-600">*</span></label>
                            <div class="position-relative">
                                <input type="number" class="form-control wizard-required"
                                    placeholder="Enter Unit Price" id="unit_price" name="unit_price" value="{{ $client_service->unit_price }}">
                                <div class="wizard-form-error"></div>
                            </div>
                        </div>
                        <div class="col-sm-6 weight_section">
                            <label class="form-label">Weight Cost Type<span class="text-danger-600">*</span></label>
                            <div class="position-relative">
                                <select class="form-control radius-8 form-select" id="weight_cost_type" name="weight_cost_type">
                                    <option selected disabled>Weight Cost Type</option>
                                    <option value="1" {{ $client_service->weight_cost_type == 1 ? 'selected' : '' }}>Fixed</option>
                                    <option value="2" {{ $client_service->weight_cost_type == 2 ? 'selected' : '' }}>Fixed Range Based</option>
                                    <option value="3" {{ $client_service->weight_cost_type == 3 ? 'selected' : '' }}>Floating Range Based</option>
                                    <option value="4" {{ $client_service->weight_cost_type == 4 ? 'selected' : '' }}>Fixed with Minimum Qty</option>
                                </select>
                                <div class="wizard-form-error"></div>
                            </div>
                        </div>
                        <!-- Fixed Cost Input -->
                        <div class="col-sm-6 weight_section" id="fixed_cost_section" style="display: none;">
                            <label class="form-label">Cost for KG <span class="text-danger-600">*</span></label>
                            <div class="position-relative">
                                <input type="number" name="weight_cost" id="weight_cost" class="form-control" value="{{ $client_service->weight_cost }}">
                                <div class="wizard-form-error"></div>
                            </div>
                        </div>

                        <div class="col-sm-6 fixed_with_min_cost_section" style="display: none;">
                            <label class="form-label">Minimum KG <span class="text-danger-600">*</span></label>
                            <div class="position-relative">
                                <input type="number" name="minimum_kgs" id="minimum_kgs" class="form-control" value="{{ $client_service->minimum_kgs }}">
                                <div class="wizard-form-error"></div>
                            </div>
                        </div>

                        <div class="col-sm-6 fixed_with_min_cost_section" style="display: none;">
                            <label class="form-label">Minimum Cost <span class="text-danger-600">*</span></label>
                            <div class="position-relative">
                                <input type="number" name="minimum_cost" id="minimum_cost" class="form-control" value="{{ $client_service->minimum_cost }}">
                                <div class="wizard-form-error"></div>
                            </div>
                        </div>

                        <!-- Range Based Section -->
                        <div class="col-sm-12" id="range_cost_section" style="display: none;">
                            <label class="form-label">Range Based Cost <span class="text-danger-600">*</span></label>

                            <!-- Weight Range Requirements Info -->
                            <div class="alert alert-info mb-3" id="range_requirements_info" style="display: none;">
                                <h6 class="mb-2"><i class="fas fa-info-circle"></i> Weight Range Requirements:</h6>
                                <div id="fixed_range_info" style="display: none;">
                                    <strong>Fixed Range Based:</strong>
                                    <ul class="mb-0 mt-1">
                                        <li>Ranges must be <strong>continuous</strong> without gaps</li>
                                        <li>First range should start from <strong>1 KG</strong> (or 0 KG)</li>
                                        <li>From KG must be <strong>less than</strong> To KG</li>
                                        <li><span class="text-success">✅ Valid:</span> 1-100, 101-200, 201-300</li>
                                        <li><span class="text-danger">❌ Invalid:</span> 1-100, 200-300 (missing 101-199)</li>
                                        <li><span class="text-danger">❌ Invalid:</span> 800-600 (From > To)</li>
                                    </ul>
                                </div>
                                <div id="floating_range_info" style="display: none;">
                                    <strong>Floating Range Based:</strong>
                                    <ul class="mb-0 mt-1">
                                        <li>Ranges <strong>can have gaps</strong> between them</li>
                                        <li>From KG must be <strong>less than</strong> To KG</li>
                                        <li><span class="text-success">✅ Valid:</span> 1-100, 150-200, 300-400</li>
                                        <li><span class="text-danger">❌ Invalid:</span> 800-600 (From > To)</li>
                                    </ul>
                                </div>
                            </div>

                            <div id="range_inputs">
                                @if($client_service->weightCostRanges && $client_service->weightCostRanges->count() > 0)
                                    @foreach($client_service->weightCostRanges as $range)
                                    <div class="row mb-2 range-row">
                                        <div class="col-sm-3">
                                            <input type="number" name="from_kg[]" class="form-control" placeholder="From KG" value="{{ $range->from_kg }}" step="0.01">
                                        </div>
                                        <div class="col-sm-3">
                                            <input type="number" name="to_kg[]" class="form-control" placeholder="To KG" value="{{ $range->to_kg }}" step="0.01">
                                        </div>
                                        <div class="col-sm-3">
                                            <input type="number" name="cost[]" class="form-control" placeholder="Cost" value="{{ $range->cost }}" step="0.01">
                                        </div>
                                        <div class="col-sm-3">
                                            <button type="button" class="btn btn-danger remove-range">Remove</button>
                                        </div>
                                    </div>
                                    @endforeach
                                @else
                                    <div class="row mb-2 range-row">
                                        <div class="col-sm-3">
                                            <input type="number" name="from_kg[]" class="form-control" placeholder="From KG" step="0.01">
                                        </div>
                                        <div class="col-sm-3">
                                            <input type="number" name="to_kg[]" class="form-control" placeholder="To KG" step="0.01">
                                        </div>
                                        <div class="col-sm-3">
                                            <input type="number" name="cost[]" class="form-control" placeholder="Cost" step="0.01">
                                        </div>
                                        <div class="col-sm-3">
                                            <button type="button" class="btn btn-danger remove-range">Remove</button>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <button type="button" class="btn btn-primary mt-2" id="add_range">Add More Range</button>
                        </div>

                        <div class="col-sm-6">
                            <label class="form-label">Start date</label>
                            <input type="date" name="start_date" id="start_date" class="form-control bg-light" value="{{ $client_service->start_date }}" readonly>
                            <small class="text-muted d-flex align-items-center">
                                <iconify-icon icon="mdi:lock" width="14" class="me-1"></iconify-icon>
                                <span>Start date cannot be modified to maintain data integrity</span>
                            </small>
                        </div>
                        <div class="col-sm-6">
                            <label for="non_bedded_type" class="form-label fw-semibold text-primary-light text-sm mb-8">Non
                                Bedded Type <span class="text-danger-600">*</span> </label>
                            <select class="form-control radius-8 form-select select2" id="non_bedded_type" name="non_bedded_type">
                                <option selected disabled>Non Beded Type</option>
                               @foreach ($non_bedded_types as $non_bedded_type)
                                   <option value="{{$non_bedded_type->name}}" {{ $client_service->non_bedded_type == $non_bedded_type->name ? 'selected' : '' }}>{{$non_bedded_type->name}}</option>
                               @endforeach
                            </select>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label">Total Cost<span class="text-danger-600">*</span></label>
                            <div class="position-relative">
                                <input type="number" class="form-control wizard-required" placeholder="Total Cost"
                                    name="total_cost" id="total_cost" value="{{ $client_service->total_price }}">
                                <div class="wizard-form-error"></div>
                            </div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Description / Comments<span
                                    class="text-danger-600">*</span></label>
                            <textarea name="description" class="form-control" rows="4" cols="50" placeholder="Enter Description...">{{ $client_service->description }}</textarea>
                        </div>
                        <div class="form-group d-flex align-items-center justify-content-end gap-8">
                            <a href="{{ route('services.index') }}" class="btn btn-neutral-500 border-neutral-100 px-32">Cancel</a>
                            <button type="submit" class="btn btn-primary-600 px-32 btn-submit">Update Service</button>
                        </div>
                    </div>
                </form>
                <!-- Form End -->
            </div>
        </div>

    </div>
@stop
@section('script')
<script>
    $(document).ready(function() {
        $('#client_id').select2({
            width: '100%',
            placeholder: "Select a Client",
            allowClear: true
        });
        $('#service_type').select2({
            width: '100%',
            placeholder: "Select a Service Type",
            allowClear: true
        });
        $('#service').select2({
            width: '100%',
            placeholder: "Select a Service",
            allowClear: true
        });
        $('#non_bedded_type').select2({
            width: '100%',
            placeholder: "Select Non Bedded Type",
            allowClear: true
        });
    });

    // Function to update the blue info widget with all validation results
    function updateRangeInfoWidget() {
        var service = $("#service").val();
        var weightCostType = $("#weight_cost_type").val();

        // Only proceed for weight-based services with range cost types
        if (service != "3" || (weightCostType != "2" && weightCostType != "3")) {
            return;
        }

        // Get all range inputs - only include COMPLETE ranges (all 3 fields filled)
        var ranges = [];
        var hasIncompleteRanges = false;

        $('.range-row').each(function(index) {
            var fromKg = $(this).find('input[name="from_kg[]"]').val();
            var toKg = $(this).find('input[name="to_kg[]"]').val();
            var cost = $(this).find('input[name="cost[]"]').val();

            // Check if this row has any data but is incomplete
            if ((fromKg || toKg || cost) && !(fromKg && toKg && cost)) {
                hasIncompleteRanges = true;
            }

            // Only add to ranges if ALL three fields are filled
            if (fromKg && toKg && cost) {
                ranges.push({
                    index: index + 1,
                    from: parseFloat(fromKg),
                    to: parseFloat(toKg),
                    cost: parseFloat(cost),
                    display: fromKg + '-' + toKg
                });
            }
        });

        // If there are incomplete ranges and no complete ranges, don't show validation errors yet
        if (hasIncompleteRanges && ranges.length === 0) {
            return;
        }

        // Build the widget content
        var content = '<h6 class="mb-2"><i class="fas fa-info-circle"></i> Weight Range Requirements:</h6>';

        // Add cost type info
        if (weightCostType == "2") {
            content += '<strong>Fixed Range Based:</strong><br>';
            content += 'Ranges must be <strong>continuous</strong> without gaps<br>';
            content += 'First range should start from <strong>1 KG</strong> (or 0 KG)<br>';
            content += 'From KG must be <strong>less than</strong> To KG<br>';
        } else {
            content += '<strong>Floating Range Based:</strong><br>';
            content += 'Ranges can have gaps<br>';
            content += 'From KG must be <strong>less than</strong> To KG<br>';
        }

        var validRanges = [];
        var invalidRanges = [];
        var errors = [];

        // Validate each range
        ranges.forEach(function(range) {
            if (range.from >= range.to) {
                invalidRanges.push(range.display + ' (From > To)');
                errors.push('Range ' + range.index + ': From KG (' + range.from + ') must be less than To KG (' + range.to + ')');
            } else {
                validRanges.push(range);
            }
        });

        // Check for gaps and overlaps (only for Fixed Range Based)
        if (weightCostType == "2" && validRanges.length >= 2) {
            validRanges.sort(function(a, b) { return a.from - b.from; });

            // Check if first range starts from 1
            if (validRanges[0].from > 1) {
                errors.push('First range should start from 1 KG (current: ' + validRanges[0].from + ' KG)');
            }

            // Check for gaps and overlaps
            for (var i = 0; i < validRanges.length - 1; i++) {
                var current = validRanges[i];
                var next = validRanges[i + 1];

                if (current.to >= next.from) {
                    errors.push('Ranges overlap: ' + current.display + ' overlaps with ' + next.display);
                } else if (current.to + 1 != next.from) {
                    var gapStart = current.to + 1;
                    var gapEnd = next.from - 1;
                    errors.push('Gap detected: Missing range ' + gapStart + '-' + gapEnd + ' KG');
                    invalidRanges.push(current.display + ', ' + next.display + ' (missing ' + gapStart + '-' + gapEnd + ')');
                }
            }
        }

        // Show valid ranges
        if (validRanges.length > 0) {
            var validDisplays = validRanges.map(function(r) { return r.display; });
            content += '<span class="text-success"><i class="fas fa-check"></i> Valid: ' + validDisplays.join(', ') + '</span><br>';
        }

        // Show invalid ranges
        if (invalidRanges.length > 0) {
            content += '<span class="text-danger"><i class="fas fa-times"></i> Invalid: ' + invalidRanges.join(', ') + '</span><br>';
        }

        // Show error messages
        if (errors.length > 0) {
            errors.forEach(function(error) {
                content += '<span class="text-danger"><i class="fas fa-times"></i> ' + error + '</span><br>';
            });
        }

        // Update the widget
        $('#range_requirements_info').html(content);
    }

    $(document).ready(function () {
        // Make function globally accessible for testing
        window.updateRangeInfoWidget = updateRangeInfoWidget;

        // Initialize the range info widget on page load
        setTimeout(function() {
            updateRangeInfoWidget();
        }, 1000);

        // Custom validation for range inputs
        $.validator.addMethod("rangeRequired", function(value, element) {
            var service = $("#service").val();
            var weightCostType = $("#weight_cost_type").val();

            if (service == "3" && (weightCostType == "2" || weightCostType == "3")) {
                var hasValidRange = false;
                $('.range-row').each(function() {
                    var fromKg = $(this).find('input[name="from_kg[]"]').val();
                    var toKg = $(this).find('input[name="to_kg[]"]').val();
                    var cost = $(this).find('input[name="cost[]"]').val();

                    if (fromKg && toKg && cost) {
                        hasValidRange = true;
                        return false; // break loop
                    }
                });
                return hasValidRange;
            }
            return true;
        }, "At least one complete range (From KG, To KG, Cost) is required for range-based weight cost types.");

        // jQuery Validation Setup
        $("#editServiceForm").validate({
            rules: {
                client_id: { required: true },
                payment_mode: { required: true },
                invoice_generation_type: { required: true },
                custom_invoice_day: {
                    required: function() {
                        return $("#invoice_generation_type").val() == "3";
                    }
                },
                service_type: { required: true },
                service: { required: true },
                no_of_units: {
                    required: function () { return $("#service").val() == "1" || $("#service").val() == "4"; },
                    number: true,
                    min: 1,
                    step:0.01
                },
                unit_price: {
                    required: function () { return $("#service").val() == "1"; },
                    number: true,
                    min: 1,
                    step:0.01
                },
                total_cost: {
                    required: function () { return $("#service").val() != "3"; }, // Not required for weight-based services
                    number: true
                },
                non_bedded_type: { required: function () { return $("#service").val() == "2"; } },
                weight_cost_type: {
                    required: function () { return $("#service").val() == "3"; }
                },
                weight_cost: {
                    required: function () {
                        return $("#service").val() == "3" && ($("#weight_cost_type").val() == "1" || $("#weight_cost_type").val() == "4");
                    },
                    number: true,
                    min: 0
                },
                minimum_kgs: {
                    required: function () {
                        return $("#service").val() == "3" && $("#weight_cost_type").val() == "4";
                    },
                    number: true,
                    min: 0
                },
                minimum_cost: {
                    required: function () {
                        return $("#service").val() == "3" && $("#weight_cost_type").val() == "4";
                    },
                    number: true,
                    min: 0
                },
                "from_kg[]": {
                    rangeRequired: true,
                    number: true,
                    min: 0
                },
                "to_kg[]": {
                    number: true,
                    min: 0
                },
                "cost[]": {
                    number: true,
                    min: 0
                },
                description: { required: true }
            },
            messages: {
                client_id: "Please select a client.",
                payment_mode: "Please select a payment mode.",
                invoice_generation_type: "Please select an invoice generation type.",
                custom_invoice_day: "Please select a custom invoice day.",
                service_type: "Please select a service type.",
                service: "Please select a service.",
                no_of_units: "Please enter a valid number of units.",
                unit_price: "Please enter a valid unit price.",
                total_cost: "Total cost is required.",
                non_bedded_type: "Please select a non-bedded type.",
                weight_cost_type: "Please select a weight cost type.",
                weight_cost: "Please enter a valid cost per KG.",
                minimum_kgs: "Please enter minimum KGs (required for Fixed with Minimum Qty).",
                minimum_cost: "Please enter minimum cost (required for Fixed with Minimum Qty).",
                description: "Please enter a description."
            },
            errorPlacement: function(error, element) {
                if (element.hasClass("select2-hidden-accessible")) {
                    error.insertAfter(element.next('.select2-container'));
                } else {
                    error.insertAfter(element);
                }
            },
            submitHandler: function(form) {
                // Show SweetAlert confirmation before submitting
                Swal.fire({
                    title: 'Confirm Service Update',
                    html: `
                        <div class="text-start">
                            <p class="mb-3"><strong>Are you sure you want to update this service?</strong></p>
                            <div class="alert alert-warning mb-0">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-exclamation-triangle text-warning me-2 mt-1"></i>
                                    <div>
                                        <p class="mb-2"><strong>Important:</strong> Changes will impact future invoices:</p>
                                        <ul class="mb-0 small">
                                            <li>Pricing changes will apply to next invoices</li>
                                            <li>Service modifications affect billing calculations</li>
                                            <li>Invoice generation settings will be updated</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, Update Service',
                    cancelButtonText: 'Cancel',
                    reverseButtons: true,
                    customClass: {
                        popup: 'swal-wide'
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        // User confirmed, proceed with form submission
                        $(".btn-submit").prop("disabled", true).text("Updating...");
                        form.submit();
                    }
                    // If cancelled, do nothing (form won't submit)
                });

                // Prevent default form submission until user confirms
                return false;
            }
        });

        // Hide fields initially based on current service type
        var currentService = $("#service").val();
        toggleFields();

        // Initialize invoice generation type help text
        updateInvoiceGenerationTypeHelp();

        // Initialize payment mode restrictions
        restrictPaymentModeForWeightBased();

        // Handle Payment Mode Change
        $("#payment_mode").change(function () {
            updateInvoiceGenerationTypeHelp();
        });

        // Handle Invoice Generation Type Change
        $("#invoice_generation_type").change(function () {
            var generationType = $(this).val();
            var originalValue = $(this).data('original-value');

            // Show/hide custom day section
            if (generationType == "3") { // Custom day
                $("#custom_day_section").show();
            } else {
                $("#custom_day_section").hide();
                $("#custom_invoice_day").val(''); // Clear the value
            }

            // Show/hide warning if value changed from original
            if (generationType != originalValue) {
                $("#invoice_type_warning").show();
            } else {
                $("#invoice_type_warning").hide();
            }

            updateInvoiceGenerationTypeHelp();
        });

        // Handle Custom Invoice Day Change
        $("#custom_invoice_day").change(function () {
            var originalInvoiceType = $("#invoice_generation_type").data('original-value');
            var currentInvoiceType = $("#invoice_generation_type").val();
            var originalCustomDay = "{{ $client_service->custom_invoice_day ?? '' }}";
            var currentCustomDay = $(this).val();

            // Show warning if invoice type is 3 and custom day changed, or if invoice type changed
            if ((currentInvoiceType == "3" && currentCustomDay != originalCustomDay) || currentInvoiceType != originalInvoiceType) {
                $("#invoice_type_warning").show();
            } else {
                $("#invoice_type_warning").hide();
            }
        });

        // Handle Service Type Change
        $("#service").change(function () {
            toggleFields();
            restrictPaymentModeForWeightBased();
        });

        // Function to restrict payment mode for weight-based services
        function restrictPaymentModeForWeightBased() {
            var service = $("#service").val();
            var paymentModeSelect = $("#payment_mode");

            if (service == "3") { // Weight-based service
                // Clear current selection if it's not Monthly
                if (paymentModeSelect.val() !== "Monthly") {
                    paymentModeSelect.val("").trigger('change');
                }

                // Hide all options except Monthly
                paymentModeSelect.find('option').each(function() {
                    var optionValue = $(this).val();
                    if (optionValue === "Monthly" || optionValue === "" || $(this).is(':disabled')) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });

                // Add help text
                var helpHtml = '<small class="text-info mt-1 d-block" id="weight_payment_help">' +
                              '<i class="fas fa-info-circle me-1"></i>' +
                              'Weight-based services only support Monthly payment cycle' +
                              '</small>';

                // Remove existing help and add new one
                $("#weight_payment_help").remove();
                paymentModeSelect.after(helpHtml);

            } else {
                // Show all payment mode options for non-weight-based services
                paymentModeSelect.find('option').show();

                // Remove weight-based help text
                $("#weight_payment_help").remove();
            }
        }

        // Handle Payment Mode, Units, and Unit Price Change for Recalculation (only for bedded services)
        $("#payment_mode, #no_of_units, #unit_price").on("input change", function () {
            var service = $("#service").val();
            if (service == "1") { // Only calculate for bedded services
                calculateTotalCost();
            }
        });

        function toggleFields() {
            var service = $("#service").val();

            // First, hide all weight-related conditional fields
            $('#fixed_cost_section').hide();
            $('#range_cost_section').hide();
            $('.fixed_with_min_cost_section').hide();

            if (service == "1") { // Bedded Service
                $("#no_of_units, #unit_price, #total_cost").closest(".col-sm-6").show();
                $("#non_bedded_type, #weight_cost_type").closest(".col-sm-6").hide().find("select").val("").removeClass("error");
                $("#no_of_units, #unit_price, #total_cost").prop("required", true);
                $("#non_bedded_type, #weight_cost_type").prop("required", false);
                calculateTotalCost();
                $("#total_cost").prop("readonly", true);
                $(".weight_section").hide();
            } else if (service == "2") { // Non-Bedded Service
                $("#no_of_units, #unit_price, #weight_cost_type").closest(".col-sm-6").hide().find("input").val("").removeClass("error");
                $("#non_bedded_type, #total_cost").closest(".col-sm-6").show();
                $("#no_of_units, #unit_price, #weight_cost_type").prop("required", false);
                $("#non_bedded_type, #total_cost").prop("required", true);
                $("#total_cost").prop("readonly", false);
                $(".weight_section").hide();
            } else if (service == "3") { // Weight based
                $(".weight_section").show();
                $("#no_of_units, #unit_price, #non_bedded_type, #total_cost").closest(".col-sm-6").hide().find("input").val("").removeClass("error");
                $("#weight_cost_type").closest(".col-sm-6").show();
                $("#no_of_units, #unit_price, #non_bedded_type, #total_cost").prop("required", false);
                $("#weight_cost_type").prop("required", true);

                // Always hide all weight cost sections first, then show appropriate one
                $('#fixed_cost_section').hide();
                $('#range_cost_section').hide();
                $('.fixed_with_min_cost_section').hide();

                // Show appropriate weight cost fields based on current selection
                var weightCostType = $("#weight_cost_type").val();
                if (weightCostType == '1') {
                    $('#fixed_cost_section').show();
                    $('#range_requirements_info').hide();
                } else if (weightCostType == '2' || weightCostType == '3') {
                    $('#range_cost_section').show();
                    $('#range_requirements_info').show();

                    // Show appropriate info based on cost type
                    if (weightCostType == '2') { // Fixed Range Based
                        $('#fixed_range_info').show();
                        $('#floating_range_info').hide();
                    } else { // Floating Range Based
                        $('#fixed_range_info').hide();
                        $('#floating_range_info').show();
                    }

                    // Update validation widget when weight service is selected
                    setTimeout(function() {
                        updateRangeInfoWidget();
                    }, 200);
                } else if (weightCostType == '4') {
                    $('#fixed_cost_section').show();
                    $('.fixed_with_min_cost_section').show();
                    $('#range_requirements_info').hide();
                }
            } else if (service == "4") { // Bedded with Fixed Price
                $("#no_of_units, #total_cost").closest(".col-sm-6").show();
                $("#unit_price, #non_bedded_type, #weight_cost_type").closest(".col-sm-6").hide().find("input, select").val("").removeClass("error");
                $("#no_of_units, #total_cost").prop("required", true);
                $("#unit_price, #non_bedded_type, #weight_cost_type").prop("required", false);
                $("#total_cost").prop("readonly", false);
                $(".weight_section").hide();
            } else {
                $("#no_of_units, #unit_price, #non_bedded_type, #weight_cost_type").closest(".col-sm-6").hide();
                $("#total_cost").val("");
                $(".weight_section").hide();
            }
        }

        function updateInvoiceGenerationTypeHelp() {
            var paymentMode = $("#payment_mode").val();
            var generationType = $("#invoice_generation_type").val();
            var helpText = "";
            var customDayHelpText = "";

            if (!paymentMode) {
                helpText = "Select payment mode first to see detailed descriptions";
                customDayHelpText = "Day of the month when invoice should be generated (1-31)";
            } else if (paymentMode === "Monthly") {
                switch (generationType) {
                    case "1":
                        helpText = "Invoice will be generated at the end of each month";
                        break;
                    case "2":
                        helpText = "Invoice will be generated on the same date each month as the start date";
                        break;
                    case "3":
                        helpText = "Invoice will be generated on the selected custom day each month";
                        customDayHelpText = "Day of the month for monthly invoice generation (e.g., 5th = 5th of every month)";
                        break;
                    default:
                        helpText = "Monthly: Choose when to generate invoices each month";
                        customDayHelpText = "Day of the month when invoice should be generated (1-31)";
                }
            } else if (paymentMode === "Yearly") {
                switch (generationType) {
                    case "1":
                        helpText = "Invoice will be generated at the end of the start month, then annually";
                        break;
                    case "2":
                        helpText = "Invoice will be generated on the anniversary of the start date each year";
                        break;
                    case "3":
                        helpText = "Invoice will be generated on the selected custom day of the start month, then annually";
                        customDayHelpText = "Day of the start month for yearly invoice generation (e.g., 5th = 5th of start month each year)";
                        break;
                    default:
                        helpText = "Yearly: Choose when to generate the annual invoice";
                        customDayHelpText = "Day of the month when invoice should be generated (1-31)";
                }
            }

            $("#invoice_type_help").text(helpText);
            $("#custom_day_help").text(customDayHelpText);
        }

        function calculateTotalCost() {
            var service = $("#service").val();
            var paymentMode = $("#payment_mode").val();
            var units = parseFloat($("#no_of_units").val()) || 0;
            var unitPrice = parseFloat($("#unit_price").val()) || 0;

            if (service == "1") { // Bedded Service
                var multiplier = (paymentMode === "Monthly") ? 30 : 365;
                var totalCost = units * unitPrice * multiplier;
                // Apply minimum total cost condition
                if (paymentMode === "Monthly" && totalCost < 2000) {
                    totalCost = 2000;
                } else if (paymentMode === "Yearly" && totalCost < 24000) {
                    totalCost = 24000;
                }
                $("#total_cost").val(totalCost.toFixed(2));
            }
        }

        // Handle weight cost type changes
        $('#weight_cost_type').on('change', function () {
            var selected = $(this).val();
            if (selected == '1') {
                $('#fixed_cost_section').show();
                $('#range_cost_section').hide();
                $('.fixed_with_min_cost_section').hide();
                $('#range_requirements_info').hide();
            } else if (selected == '2' || selected == '3') {
                $('#fixed_cost_section').hide();
                $('#range_cost_section').show();
                $('.fixed_with_min_cost_section').hide();
                $('#range_requirements_info').show();

                // Show appropriate info based on cost type
                if (selected == '2') { // Fixed Range Based
                    $('#fixed_range_info').show();
                    $('#floating_range_info').hide();
                } else { // Floating Range Based
                    $('#fixed_range_info').hide();
                    $('#floating_range_info').show();
                }

                // Update the info widget
                setTimeout(function() {
                    updateRangeInfoWidget();
                }, 100);
            } else if (selected == '4') {
                $('#fixed_cost_section').show();
                $('#range_cost_section').hide();
                $('.fixed_with_min_cost_section').show();
                $('#range_requirements_info').hide();
            } else {
                $('#fixed_cost_section').hide();
                $('#range_cost_section').hide();
                $('.fixed_with_min_cost_section').hide();
                $('#range_requirements_info').hide();
            }
        });

        // Add new range
        $('#add_range').click(function () {
            var html = `<div class="row mb-2 range-row">
                <div class="col-sm-3">
                    <input type="number" name="from_kg[]" class="form-control" placeholder="From KG" step="0.01">
                </div>
                <div class="col-sm-3">
                    <input type="number" name="to_kg[]" class="form-control" placeholder="To KG" step="0.01">
                </div>
                <div class="col-sm-3">
                    <input type="number" name="cost[]" class="form-control" placeholder="Cost" step="0.01">
                </div>
                <div class="col-sm-3">
                    <button type="button" class="btn btn-danger remove-range">Remove</button>
                </div>
            </div>`;
            $('#range_inputs').append(html);

            // Update the info widget after adding a range
            setTimeout(function() {
                updateRangeInfoWidget();
            }, 100);
        });

        // Remove range row
        $(document).on('click', '.remove-range', function () {
            if ($('.range-row').length > 1) {
                $(this).closest('.range-row').remove();

                // Update the info widget after removing a range
                setTimeout(function() {
                    updateRangeInfoWidget();
                }, 100);
            } else {
                Swal.fire({
                    title: 'Cannot Remove',
                    text: 'At least one range is required for range-based cost types.',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
            }
        });

        // Custom validation for range inputs
        $.validator.addMethod("rangeRequired", function(value, element) {
            var service = $("#service").val();
            var weightCostType = $("#weight_cost_type").val();

            if (service == "3" && (weightCostType == "2" || weightCostType == "3")) {
                var hasValidRange = false;
                $('.range-row').each(function() {
                    var fromKg = $(this).find('input[name="from_kg[]"]').val();
                    var toKg = $(this).find('input[name="to_kg[]"]').val();
                    var cost = $(this).find('input[name="cost[]"]').val();

                    if (fromKg && toKg && cost) {
                        hasValidRange = true;
                        return false; // break loop
                    }
                });
                return hasValidRange;
            }
            return true;
        }, "At least one complete range (From KG, To KG, Cost) is required.");

        // Add range validation to form and real-time widget updates
        $(document).on('input change', 'input[name="from_kg[]"], input[name="to_kg[]"], input[name="cost[]"]', function() {
            $("#editServiceForm").valid();

            // Update the info widget in real-time as user types
            clearTimeout(window.rangeValidationTimeout);
            window.rangeValidationTimeout = setTimeout(function() {
                updateRangeInfoWidget();
            }, 300); // Debounce for 300ms
        });
    });
</script>

<style>
    /* Custom SweetAlert styling */
    .swal-wide {
        width: 600px !important;
    }

    .swal2-html-container {
        text-align: left !important;
    }

    .swal2-html-container .alert {
        border-radius: 8px;
        border: 1px solid #ffeaa7;
        background-color: #fff3cd;
    }

    .swal2-html-container .alert ul {
        padding-left: 1.2rem;
        margin-bottom: 0;
    }

    .swal2-html-container .alert li {
        margin-bottom: 0.25rem;
    }

    /* Additional Information Alert styling */
    .alert-warning {
        background-color: #fff3cd;
        border-color: #ffeaa7;
        color: #856404;
    }

    .alert-warning .alert-heading {
        color: #856404;
    }

    .alert-warning iconify-icon {
        color: #f39c12 !important;
    }
</style>
@stop
