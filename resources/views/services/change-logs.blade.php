@php
    $title = 'Service';
    $moduleTitle = 'Services';
    $moduleIcon = 'solar:settings-bold';
    $entityIcon = 'solar:settings-bold';
    $indexRoute = route('services.index');
    $viewRoute = route('service.show', $client_service->id);
    $editRoute = auth()->user()->can('service-edit') ? route('service.edit', $client_service->id) : null;
    $entityInfo = [
        'Service ID' => $client_service->id,
        'Client' => $client_service->client->name ?? 'N/A',
        'Service' => $client_service->service->user_label ?? 'N/A',
        'Start Date' => $client_service->start_date ? date('d-m-Y', strtotime($client_service->start_date)) : 'N/A',
        'Status' => $client_service->status == 1 ? '<span class="badge bg-success">Active</span>' : '<span class="badge bg-danger">Inactive</span>',
        'Created At' => $client_service->created_at->format('d-m-Y h:i A')
    ];
    $changeLogs = $client_service->changeLogs()->orderBy('created_at', 'desc')->get();
@endphp

@include('shared.change-logs', [
    'title' => $title,
    'moduleTitle' => $moduleTitle,
    'moduleIcon' => $moduleIcon,
    'entityIcon' => $entityIcon,
    'indexRoute' => $indexRoute,
    'viewRoute' => $viewRoute,
    'editRoute' => $editRoute,
    'entityInfo' => $entityInfo,
    'changeLogs' => $changeLogs
])