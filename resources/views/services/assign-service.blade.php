@extends('layouts.master')
@section('title', 'Service Assign - Paidash')

@section('content')
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">Assign Service to client</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="/services" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="hugeicons:user-settings-01" class="icon text-lg"></iconify-icon>
                        Services
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">Assign Service to Client</li>
            </ul>
        </div>

        <div class="card">
            <div class="card-body">
                <h6 class="mb-4 text-xl">Fill the required Details to Assign service to client</h6>
                <!-- <p class="text-neutral-500">Fill up your details and proceed next steps.</p> -->
                @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
                <!-- Form Wizard Start -->
                <div class="form-wizard mt-4">
                    <form action="{{ route('serive.clientService') }}" method="POST"  id="form" enctype="multipart/form-data">
                        @csrf
                        <div class="row gy-3">
                            <div class="col-sm-6">
                                <label for="client_id" class="form-label fw-semibold text-primary-light text-sm mb-8">Select
                                    Client <span class="text-danger-600">*</span> </label>
                                <select class="form-control radius-8 form-select select2 {{ $selectedClient ? 'border-success' : '' }}" id="client_id" name="client_id">
                                    <option value="" {{ !$selectedClient ? 'selected' : '' }} disabled>Select Client </option>
                                    @foreach ($clients as $client)
                                        <option value="{{ $client->id }}"
                                            {{ old('client_id', $selectedClient ? $selectedClient->id : '') == $client->id ? 'selected' : '' }}>
                                            {{ $client->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @if($selectedClient)
                                    <small class="text-success mt-1 d-flex align-items-center">
                                        <iconify-icon icon="solar:check-circle-bold" class="me-1"></iconify-icon>
                                        Client "{{ $selectedClient->name }}" has been pre-selected for you
                                    </small>
                                @endif
                            </div>
                            <div class="col-sm-6">
                                <label for="payment_mode" class="form-label fw-semibold text-primary-light text-sm mb-8">Payment
                                    Mode <span class="text-danger-600">*</span> </label>
                                <select class="form-control radius-8 form-select" id="payment_mode" name="payment_mode">
                                    <option selected disabled>Payment Mode </option>
                                    <option value="Monthly">Monthly</option>
                                    <option value="Yearly">Yearly</option>
                                </select>
                            </div>

                            <div class="col-sm-6">
                                <label for="invoice_generation_type" class="form-label fw-semibold text-primary-light text-sm mb-8">Invoice Generation Type <span class="text-danger-600">*</span></label>
                                <select class="form-control radius-8 form-select" id="invoice_generation_type" name="invoice_generation_type">
                                    <option selected disabled>Select Invoice Generation Type</option>
                                    <option value="1">End of Month</option>
                                    <option value="2">Based on Start Date</option>
                                    <option value="3">Custom Day</option>
                                </select>
                                <small class="text-muted" id="invoice_type_help">Select payment mode first to see detailed descriptions</small>
                            </div>

                            <div class="col-sm-6" id="custom_day_section" style="display: none;">
                                <label for="custom_invoice_day" class="form-label fw-semibold text-primary-light text-sm mb-8">Custom Invoice Day <span class="text-danger-600">*</span></label>
                                <select class="form-control radius-8 form-select" id="custom_invoice_day" name="custom_invoice_day">
                                    <option selected disabled>Select Day of Month</option>
                                    @for($i = 1; $i <= 31; $i++)
                                        <option value="{{ $i }}">{{ $i }}</option>
                                    @endfor
                                </select>
                                <small class="text-muted" id="custom_day_help">Day of the month when invoice should be generated (1-31)</small>
                            </div>
                            <div class="col-sm-6">
                                <label for="service_type" class="form-label fw-semibold text-primary-light text-sm mb-8">Service
                                    Type<span class="text-danger-600">*</span> </label>
                                <select class="form-control radius-8 form-select" id="service_type"
                                    name="service_type">
                                    <option selected disabled>Service Type </option>
                                    @foreach ($service_types as $service_type)
                                        <option value="{{ $service_type->id }}">
                                            {{ $service_type->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-sm-6">
                                <label for="service"
                                    class="form-label fw-semibold text-primary-light text-sm mb-8">Service<span
                                        class="text-danger-600">*</span> </label>
                                <select class="form-control radius-8 form-select" id="service" name="service">
                                    <option selected disabled>Service </option>
                                    @foreach ($services as $service)
                                        <option value="{{ $service->id }}">
                                            {{ $service->user_label }}
                                        </option>
                                    @endforeach

                                </select>
                            </div>

                            <div class="col-sm-6">
                                <label class="form-label">No of Units<span class="text-danger-600">*</span></label>
                                <div class="position-relative">
                                    <input type="number" class="form-control wizard-required" placeholder="Enter Units"
                                        id="no_of_units" name="no_of_units">
                                    <div class="wizard-form-error"></div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <label class="form-label">Unit Price<span class="text-danger-600">*</span></label>
                                <div class="position-relative">
                                    <input type="number" class="form-control wizard-required"
                                        placeholder="Enter Unit Price" id="unit_price" name="unit_price">
                                    <div class="wizard-form-error"></div>
                                </div>
                            </div>
                            <div class="col-sm-6 weight_section">
                                <label class="form-label">Weight Cost Type<span class="text-danger-600">*</span></label>
                                <div class="position-relative">
                                    <select class="form-control radius-8 form-select" id="weight_cost_type" name="weight_cost_type">
                                        <option selected disabled>Weight Cost Type</option>
                                        <option value="1">Fixed</option>
                                        <option value="2">Fixed Range Based</option>
                                        <option value="3">Floating Range Based</option>
                                        <option value="4">Fixed with Minimum Qty</option>
                                    </select>
                                    <div class="wizard-form-error"></div>
                                </div>
                            </div>
                            <!-- Fixed Cost Input -->
                            <div class="col-sm-6 weight_section" id="fixed_cost_section" style="display: none;">
                                <label class="form-label">Cost for KG <span class="text-danger-600">*</span></label>
                                <div class="position-relative">
                                    <input type="number" name="weight_cost" id="weight_cost" class="form-control">
                                    <div class="wizard-form-error"></div>
                                </div>
                            </div>

                            <div class="col-sm-6 fixed_with_min_cost_section" style="display: none;">
                                <label class="form-label">Minimum KG <span class="text-danger-600">*</span></label>
                                <div class="position-relative">
                                    <input type="number" name="minimum_kgs" id="minimum_kgs" class="form-control">
                                    <div class="wizard-form-error"></div>
                                </div>
                            </div>

                            <div class="col-sm-6 fixed_with_min_cost_section" style="display: none;">
                                <label class="form-label">Minimum Cost <span class="text-danger-600">*</span></label>
                                <div class="position-relative">
                                    <input type="number" name="minimum_cost" id="minimum_cost" class="form-control">
                                    <div class="wizard-form-error"></div>
                                </div>
                            </div>


                            <!-- Range Based Section -->
                            <div class="col-sm-12 " id="range_cost_section" style="display: none;">
                                <label class="form-label">Range Based Cost <span class="text-danger-600">*</span></label>

                                <!-- Weight Range Requirements Info -->
                                <div class="alert alert-info mb-3" id="range_requirements_info" style="display: none;">
                                    <h6 class="mb-2"><i class="fas fa-info-circle"></i> Weight Range Requirements:</h6>
                                    <div id="fixed_range_info" style="display: none;">
                                        <strong>Fixed Range Based:</strong>
                                        <ul class="mb-0 mt-1">
                                            <li>Ranges must be <strong>continuous</strong> without gaps</li>
                                            <li>First range should start from <strong>1 KG</strong> (or 0 KG)</li>
                                            <li>From KG must be <strong>less than</strong> To KG</li>
                                            <li><span class="text-success">✅ Valid:</span> 1-100, 101-200, 201-300</li>
                                            <li><span class="text-danger">❌ Invalid:</span> 1-100, 200-300 (missing 101-199)</li>
                                            <li><span class="text-danger">❌ Invalid:</span> 800-600 (From > To)</li>
                                        </ul>
                                    </div>
                                    <div id="floating_range_info" style="display: none;">
                                        <strong>Floating Range Based:</strong>
                                        <ul class="mb-0 mt-1">
                                            <li>Ranges can have <strong>gaps</strong> (allowed)</li>
                                            <li>Ranges should not <strong>overlap</strong></li>
                                            <li>From KG must be <strong>less than</strong> To KG</li>
                                            <li><span class="text-success">✅ Valid:</span> 1-50, 100-200, 500-1000</li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- Validation Error Display -->
                                <div class="alert alert-danger" id="range_validation_errors" style="display: none;">
                                    <h6 class="mb-2"><i class="fas fa-exclamation-triangle"></i> Validation Errors:</h6>
                                    <ul id="range_error_list" class="mb-0"></ul>
                                </div>

                                <div id="range_inputs">
                                    <div class="row mb-2 range-row">
                                        <div class="col-sm-3">
                                            <input type="number" name="from_kg[]" class="form-control" placeholder="From KG" step="0.01">
                                        </div>
                                        <div class="col-sm-3">
                                            <input type="number" name="to_kg[]" class="form-control" placeholder="To KG" step="0.01">
                                        </div>
                                        <div class="col-sm-3">
                                            <input type="number" name="cost[]" class="form-control" placeholder="Cost" step="0.01">
                                        </div>
                                        <div class="col-sm-3">
                                            <button type="button" class="btn btn-danger remove-range">Remove</button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary mt-2" id="add_range">Add More Range</button>
                            </div>


                            <div class="col-sm-6">
                                <label class="form-label">Start date<span class="text-danger-600">*</span></label>
                                <input type="date" name="start_date" id="start_date" class="form-control" >
                            </div>
                            <div class="col-sm-6">
                                <label for="non_bedded_type" class="form-label fw-semibold text-primary-light text-sm mb-8">Non
                                    Bedded Type <span class="text-danger-600">*</span> </label>
                                <select class="form-control radius-8 form-select select2" id="non_bedded_type" name="non_bedded_type">
                                    <option selected disabled>Non Beded Type</option>
                                   @foreach ($non_bedded_types as $non_bedded_type)
                                       <option value="{{$non_bedded_type->name}}">{{$non_bedded_type->name}}</option>
                                   @endforeach
                                </select>
                            </div>
                            <div class="col-sm-6">
                                <label class="form-label">Total Cost<span class="text-danger-600">*</span></label>
                                <div class="position-relative">
                                    <input type="number" class="form-control wizard-required" placeholder="Total Cost"
                                        name="total_cost" id="total_cost">
                                    <div class="wizard-form-error"></div>
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Discription / Comments<span
                                        class="text-danger-600">*</span></label>
                                <textarea name="description" class="form-control" rows="4" cols="50" placeholder="Enter Discription..."></textarea>
                            </div>
                            <div class="form-group d-flex align-items-center justify-content-end gap-8">
                                <a type="button" href="/services"
                                    class="form-wizard-previous-btn btn btn-neutral-500 border-neutral-100 px-32">Cancel</a>
                                <button type="submit"
                                    class="form-wizard-next-btn btn btn-primary-600 px-32 btn-submit">Submit</button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Bulk Upload Section -->
                <div class="card mt-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0 text-lg fw-semibold text-primary-600 d-flex align-items-center">
                            <iconify-icon icon="material-symbols:upload-file" class="icon text-xl me-2"></iconify-icon>
                            Bulk Upload Services
                        </h6>
                        <p class="text-neutral-500 mb-0 mt-1">Upload multiple client services at once using CSV file</p>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center gap-3">
                                    <div class="bg-primary-50 p-3 rounded-circle">
                                        <iconify-icon icon="material-symbols:info" class="text-primary-600 text-xl"></iconify-icon>
                                    </div>
                                    <div>
                                        <h6 class="mb-1 fw-medium">How to use bulk upload?</h6>
                                        <p class="text-neutral-500 mb-0 text-sm">
                                            1. Download the template file<br>
                                            2. Fill in your data following the format<br>
                                            3. Upload the completed CSV file
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex flex-column gap-2">
                                    <button type="button" class="btn btn-success d-flex align-items-center justify-content-center gap-2" id="downloadTemplate">
                                        <iconify-icon icon="material-symbols:download" width="20" height="20"></iconify-icon>
                                        Download Template
                                    </button>
                                    <button type="button" class="btn btn-primary d-flex align-items-center justify-content-center gap-2" id="bulkUploadBtn">
                                        <iconify-icon icon="material-symbols:upload" width="20" height="20"></iconify-icon>
                                        Upload CSV File
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
                <!-- Form Wizard End -->
            </div>
        </div>

    </div>

    <!-- Bulk Upload Modal -->
    <div class="modal fade" id="bulkUploadModal" tabindex="-1" aria-labelledby="bulkUploadModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkUploadModalLabel">Bulk Upload Client Services</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="bulkUploadForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="csv_file" class="form-label">CSV File <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                            <div class="form-text">Please upload a CSV file with the required format. <a href="/download-bulk-upload-template" target="_blank">Download template</a></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="uploadCSV">Upload</button>
                </div>
            </div>
        </div>
    </div>
@stop
@section('script')
    <script>
        // $(function () {
        //     $('.select2').select2();
        //     $('#start_date').daterangepicker({
        //         singleDatePicker: true,  // Enable single date selection
        //         showDropdowns: true,     // Allow month & year selection
        //         autoApply: true,         // Apply selection immediately
        //         // minDate: moment().add(1, 'days').startOf('day'), // Disable today, allow from tomorrow
        //         locale: {
        //             format: 'MM/DD/YYYY' // Your required format
        //         }
        //     }, function (chosen_date) {
        //         $('#start_date').val(chosen_date.format('MM/DD/YYYY')); // Set selected date
        //     });

        //     // Clear the input field initially
        //     $('#start_date').val('');
        // });

        $(document).ready(function() {
            $('#client_id').select2({
                width: '100%', // Ensure it fits the container
                placeholder: "Select an Client",
                allowClear: true
            });
            $('#service_type').select2({
                width: '100%', // Ensure it fits the container
                placeholder: "Select an Service Type",
                allowClear: true
            });
            $('#service').select2({
                width: '100%', // Ensure it fits the container
                placeholder: "Select an Service",
                allowClear: true
            });
        });
        // Function to update the blue info widget with all validation results
        function updateRangeInfoWidget() {
            var service = $("#service").val();
            var weightCostType = $("#weight_cost_type").val();

            // Only proceed for weight-based services with range cost types
            if (service != "3" || (weightCostType != "2" && weightCostType != "3")) {
                return;
            }

            // Get all range inputs - only include COMPLETE ranges (all 3 fields filled)
            var ranges = [];
            var hasIncompleteRanges = false;

            $('.range-row').each(function(index) {
                var fromKg = $(this).find('input[name="from_kg[]"]').val();
                var toKg = $(this).find('input[name="to_kg[]"]').val();
                var cost = $(this).find('input[name="cost[]"]').val();



                // Check if this row has any data but is incomplete
                if ((fromKg || toKg || cost) && !(fromKg && toKg && cost)) {
                    hasIncompleteRanges = true;
                }

                // Only add to ranges if ALL three fields are filled
                if (fromKg && toKg && cost) {
                    ranges.push({
                        index: index + 1,
                        from: parseFloat(fromKg),
                        to: parseFloat(toKg),
                        cost: parseFloat(cost),
                        display: fromKg + '-' + toKg
                    });
                }
            });

            // If there are incomplete ranges and no complete ranges, don't show validation errors yet
            if (hasIncompleteRanges && ranges.length === 0) {
                return;
            }

            // Build the widget content
            var content = '<h6 class="mb-2"><i class="fas fa-info-circle"></i> Weight Range Requirements:</h6>';

            // Add cost type info
            if (weightCostType == "2") {
                content += '<strong>Fixed Range Based:</strong><br>';
                content += 'Ranges must be <strong>continuous</strong> without gaps<br>';
                content += 'First range should start from <strong>1 KG</strong> (or 0 KG)<br>';
                content += 'From KG must be <strong>less than</strong> To KG<br>';
            } else {
                content += '<strong>Floating Range Based:</strong><br>';
                content += 'Ranges can have gaps<br>';
                content += 'From KG must be <strong>less than</strong> To KG<br>';
            }

            var validRanges = [];
            var invalidRanges = [];
            var errors = [];

            // Validate each range
            ranges.forEach(function(range) {
                if (range.from >= range.to) {
                    invalidRanges.push(range.display + ' (From > To)');
                    errors.push('Range ' + range.index + ': From KG (' + range.from + ') must be less than To KG (' + range.to + ')');
                } else {
                    validRanges.push(range);
                }
            });

            // Check for gaps and overlaps (only for Fixed Range Based)
            if (weightCostType == "2" && validRanges.length >= 2) {
                validRanges.sort(function(a, b) { return a.from - b.from; });

                // Check if first range starts from 1
                if (validRanges[0].from > 1) {
                    errors.push('First range should start from 1 KG (current: ' + validRanges[0].from + ' KG)');
                }

                // Check for gaps and overlaps
                for (var i = 0; i < validRanges.length - 1; i++) {
                    var current = validRanges[i];
                    var next = validRanges[i + 1];

                    if (current.to >= next.from) {
                        errors.push('Ranges overlap: ' + current.display + ' overlaps with ' + next.display);
                    } else if (current.to + 1 != next.from) {
                        var gapStart = current.to + 1;
                        var gapEnd = next.from - 1;
                        errors.push('Gap detected: Missing range ' + gapStart + '-' + gapEnd + ' KG');
                        invalidRanges.push(current.display + ', ' + next.display + ' (missing ' + gapStart + '-' + gapEnd + ')');
                    }
                }
            }

            // Show valid ranges
            if (validRanges.length > 0) {
                var validDisplays = validRanges.map(function(r) { return r.display; });
                content += '<span class="text-success"><i class="fas fa-check"></i> Valid: ' + validDisplays.join(', ') + '</span><br>';
            }

            // Show invalid ranges
            if (invalidRanges.length > 0) {
                content += '<span class="text-danger"><i class="fas fa-times"></i> Invalid: ' + invalidRanges.join(', ') + '</span><br>';
            }

            // Show error messages
            if (errors.length > 0) {
                errors.forEach(function(error) {
                    content += '<span class="text-danger"><i class="fas fa-times"></i> ' + error + '</span><br>';
                });
            }

            // Update the widget
            $('#range_requirements_info').html(content);
        }

        $(document).ready(function () {
            // Make function globally accessible for testing
            window.updateRangeInfoWidget = updateRangeInfoWidget;

            // Initialize the range info widget on page load
            setTimeout(function() {
                updateRangeInfoWidget();
            }, 1000);

            // jQuery Validation Setup
            $("form").validate({
                rules: {
                    client_id: { required: true },
                    payment_mode: { required: true },
                    invoice_generation_type: { required: true },
                    custom_invoice_day: {
                        required: function() {
                            return $("#invoice_generation_type").val() == "3";
                        }
                    },
                    service_type: { required: true },
                    service: { required: true },
                    no_of_units: {
                        required: function () { return $("#service").val() == "1" || $("#service").val() == "4"; },
                        number: true,
                        min: 1,
                        step:0.01
                    },
                    unit_price: {
                        required: function () { return $("#service").val() == "1"; },
                        number: true,
                        min: 1,
                        step:0.01
                    },
                    total_cost: {
                        required: function () { return $("#service").val() != "3"; }, // Not required for weight-based services
                        number: true
                    },
                    non_bedded_type: { required: function () { return $("#service").val() == "2"; } },
                    weight_cost_type: {
                        required: function () { return $("#service").val() == "3"; }
                    },
                    weight_cost: {
                        required: function () {
                            return $("#service").val() == "3" && ($("#weight_cost_type").val() == "1" || $("#weight_cost_type").val() == "4");
                        },
                        number: true,
                        min: 0
                    },
                    minimum_kgs: {
                        required: function () {
                            return $("#service").val() == "3" && $("#weight_cost_type").val() == "4";
                        },
                        number: true,
                        min: 0
                    },
                    minimum_cost: {
                        required: function () {
                            return $("#service").val() == "3" && $("#weight_cost_type").val() == "4";
                        },
                        number: true,
                        min: 0
                    },
                    "from_kg[]": {
                        rangeRequired: true,
                        number: true,
                        min: 0
                    },
                    "to_kg[]": {
                        number: true,
                        min: 0
                    },
                    "cost[]": {
                        number: true,
                        min: 0
                    },
                    start_date: { required: true },
                    description: { required: true }
                },
                messages: {
                    client_id: "Please select a client.",
                    payment_mode: "Please select a payment mode.",
                    invoice_generation_type: "Please select an invoice generation type.",
                    custom_invoice_day: "Please select a custom invoice day.",
                    service_type: "Please select a service type.",
                    service: "Please select a service.",
                    no_of_units: "Please enter a valid number of units (no leading zeros).",
                    unit_price: "Please enter a valid unit price (no leading zeros).",
                    total_cost: "Total cost is required.",
                    non_bedded_type: "Please select a non-bedded type.",
                    weight_cost_type: "Please select a weight cost type.",
                    weight_cost: "Please enter a valid cost per KG.",
                    minimum_kgs: "Please enter minimum KGs (required for Fixed with Minimum Qty).",
                    minimum_cost: "Please enter minimum cost (required for Fixed with Minimum Qty).",
                    start_date: "Please select a start date.",
                    description: "Please enter a description."
                },
                errorPlacement: function(error, element) {
                    var elementName = element.attr('name') || '';
                    var errorText = error.text();

                    // Handle ALL weight range validation errors - show in the blue info widget
                    if ((elementName.indexOf('from_kg') !== -1 || elementName.indexOf('to_kg') !== -1) &&
                        (errorText.indexOf('Gap detected') !== -1 ||
                         errorText.indexOf('First range should start') !== -1 ||
                         errorText.indexOf('Ranges overlap') !== -1 ||
                         errorText.indexOf('continuous') !== -1 ||
                         errorText.indexOf('From KG') !== -1 && errorText.indexOf('must be less than') !== -1)) {

                        // Add error to the blue info widget
                        updateRangeInfoWidget();
                        error.hide(); // Hide the default error placement
                    }
                    // Handle Select2 validation error placement
                    else if (element.hasClass("select2-hidden-accessible")) {
                        error.insertAfter(element.next('.select2-container'));
                    }
                    // Handle other inputs
                    else {
                        error.insertAfter(element);
                    }
                },
                submitHandler: function(form) {
                    // Check for weight range validation errors before submitting
                    var service = $("#service").val();
                    var weightCostType = $("#weight_cost_type").val();

                    if (service == "3" && (weightCostType == "2" || weightCostType == "3")) {
                        // Update the widget to get latest validation status
                        updateRangeInfoWidget();

                        // Check if there are any error messages in the widget
                        var widgetContent = $('#range_requirements_info').html();
                        if (widgetContent && widgetContent.indexOf('text-danger') !== -1) {
                            alert('Please fix the weight range validation errors before submitting.');
                            $(".btn-submit").prop("disabled", false).text("Submit"); // Re-enable button
                            return false;
                        }
                    }

                    $(".btn-submit").prop("disabled", true).text("Processing..."); // Disable button and change text
                    form.submit();
                }
            });

            // Focusout validation
            $(document).on("focusout", "input, select, textarea", function () {
                var form = $(this).closest("form");
                if (form.data("validator")) {
                    $(this).valid();
                }
            });

            // Hide fields initially on page load
            $("#no_of_units, #unit_price, #non_bedded_type, #weight_cost_type").closest(".col-sm-6").hide();
            $(".weight_section").hide();

            // Initialize payment mode restrictions
            restrictPaymentModeForWeightBased();

            // Handle Payment Mode Change
            $("#payment_mode").change(function () {
                updateInvoiceGenerationTypeHelp();
            });

            // Handle Invoice Generation Type Change
            $("#invoice_generation_type").change(function () {
                var generationType = $(this).val();
                if (generationType == "3") { // Custom day
                    $("#custom_day_section").show();
                } else {
                    $("#custom_day_section").hide();
                    $("#custom_invoice_day").val(''); // Clear the value
                }
                updateInvoiceGenerationTypeHelp();
            });

            function updateInvoiceGenerationTypeHelp() {
                var paymentMode = $("#payment_mode").val();
                var generationType = $("#invoice_generation_type").val();
                var helpText = "";
                var customDayHelpText = "";

                if (!paymentMode) {
                    helpText = "Select payment mode first to see detailed descriptions";
                    customDayHelpText = "Day of the month when invoice should be generated (1-31)";
                } else if (paymentMode === "Monthly") {
                    switch (generationType) {
                        case "1":
                            helpText = "Invoice will be generated at the end of each month";
                            break;
                        case "2":
                            helpText = "Invoice will be generated on the same date each month as the start date (after completion of first month)";
                            break;
                        case "3":
                            helpText = "Invoice will be generated on the selected custom day each month (after completion of first month)";
                            customDayHelpText = "Day of the month for monthly invoice generation (e.g., 5th = 5th of every month after first month)";
                            break;
                        default:
                            helpText = "Monthly: Choose when to generate invoices each month";
                            customDayHelpText = "Day of the month when invoice should be generated (1-31)";
                    }
                } else if (paymentMode === "Yearly") {
                    switch (generationType) {
                        case "1":
                            helpText = "Invoice will be generated at the end of the start month, then annually";
                            break;
                        case "2":
                            helpText = "Invoice will be generated on the anniversary of the start date each year";
                            break;
                        case "3":
                            helpText = "Invoice will be generated on the selected custom day of the start month, then annually";
                            customDayHelpText = "Day of the start month for yearly invoice generation (e.g., 5th = 5th of start month each year)";
                            break;
                        default:
                            helpText = "Yearly: Choose when to generate the annual invoice";
                            customDayHelpText = "Day of the month when invoice should be generated (1-31)";
                    }
                }

                $("#invoice_type_help").text(helpText);
                $("#custom_day_help").text(customDayHelpText);
            }

            // Handle Service Type Change
            $("#service").change(function () {
                toggleFields();
                restrictPaymentModeForWeightBased();
            });

            // Function to restrict payment mode for weight-based services
            function restrictPaymentModeForWeightBased() {
                var service = $("#service").val();
                var paymentModeSelect = $("#payment_mode");

                if (service == "3") { // Weight-based service
                    // Clear current selection if it's not Monthly
                    if (paymentModeSelect.val() !== "Monthly") {
                        paymentModeSelect.val("").trigger('change');
                    }

                    // Hide all options except Monthly
                    paymentModeSelect.find('option').each(function() {
                        var optionValue = $(this).val();
                        if (optionValue === "Monthly" || optionValue === "" || $(this).is(':disabled')) {
                            $(this).show();
                        } else {
                            $(this).hide();
                        }
                    });

                    // Add help text
                    var helpHtml = '<small class="text-info mt-1 d-block" id="weight_payment_help">' +
                                  '<i class="fas fa-info-circle me-1"></i>' +
                                  'Weight-based services only support Monthly payment cycle' +
                                  '</small>';

                    // Remove existing help and add new one
                    $("#weight_payment_help").remove();
                    paymentModeSelect.after(helpHtml);

                } else {
                    // Show all payment mode options for non-weight-based services
                    paymentModeSelect.find('option').show();

                    // Remove weight-based help text
                    $("#weight_payment_help").remove();
                }
            }

            // Handle Payment Mode, Units, and Unit Price Change for Recalculation (only for bedded services)
            $("#payment_mode, #no_of_units, #unit_price").on("input change", function () {
                var service = $("#service").val();
                if (service == "1") { // Only calculate for bedded services
                    calculateTotalCost();
                }
            });

            function toggleFields() {
                var service = $("#service").val();

                // First, hide all weight-related conditional fields
                $('#fixed_cost_section').hide();
                $('#range_cost_section').hide();
                $('.fixed_with_min_cost_section').hide();

                if (service == "1") { // Bedded Service
                    $("#no_of_units, #unit_price, #total_cost").closest(".col-sm-6").show();
                    $("#non_bedded_type, #weight_cost_type").closest(".col-sm-6").hide().find("select").val("").removeClass("error");
                    $("#no_of_units, #unit_price, #total_cost").prop("required", true);
                    $("#non_bedded_type, #weight_cost_type").prop("required", false);
                    calculateTotalCost(); // Recalculate if switching services
                    $("#total_cost").prop("readonly", true);
                    $(".weight_section").hide();
                } else if (service == "2") { // Non-Bedded Service
                    $("#no_of_units, #unit_price, #weight_cost_type").closest(".col-sm-6").hide().find("input").val("").removeClass("error");
                    $("#non_bedded_type, #total_cost").closest(".col-sm-6").show();
                    $("#no_of_units, #unit_price, #weight_cost_type").prop("required", false);
                    $("#non_bedded_type, #total_cost").prop("required", true);
                    $("#total_cost").prop("readonly", false).val(""); // Reset cost for manual entry
                    $(".weight_section").hide();

                } else if (service == "3") { // Weight based
                    $(".weight_section").show();
                    $("#no_of_units, #unit_price, #non_bedded_type, #total_cost").closest(".col-sm-6").hide().find("input").val("").removeClass("error");
                    $("#weight_cost_type").closest(".col-sm-6").show();
                    $("#no_of_units, #unit_price, #non_bedded_type, #total_cost").prop("required", false);
                    $("#weight_cost_type").prop("required", true);

                    // Update validation widget when weight service is selected
                    setTimeout(function() {
                        updateRangeInfoWidget();
                    }, 200);

                } else if (service == "4") { // Bedded with Fixed Price
                    $("#no_of_units, #total_cost").closest(".col-sm-6").show();
                    $("#unit_price, #non_bedded_type, #weight_cost_type").closest(".col-sm-6").hide().find("input, select").val("").removeClass("error");
                    $("#no_of_units, #total_cost").prop("required", true);
                    $("#unit_price, #non_bedded_type, #weight_cost_type").prop("required", false);
                    $("#total_cost").prop("readonly", false).val(""); // Reset cost for manual entry
                    $(".weight_section").hide();

                } else { // Hide everything if no service is selected
                    $("#no_of_units, #unit_price, #non_bedded_type, #weight_cost_type").closest(".col-sm-6").hide();
                    $("#total_cost").val(""); // Reset total cost
                    $(".weight_section").hide();
                }
            }

            function calculateTotalCost() {
                var service = $("#service").val();
                var paymentMode = $("#payment_mode").val();
                var units = parseFloat($("#no_of_units").val()) || 0;
                var unitPrice = parseFloat($("#unit_price").val()) || 0;

                if (service == "1") { // Bedded Service
                    var multiplier = (paymentMode === "Monthly") ? 30 : 365;
                    var totalCost = units * unitPrice * multiplier;
                    // Apply minimum total cost condition
                    if (paymentMode === "Monthly" && totalCost < 2000) {
                        totalCost = 2000;
                    } else if (paymentMode === "Yearly" && totalCost < 24000) {
                        totalCost = 24000;
                    }
                    $("#total_cost").val(totalCost.toFixed(2));
                }
            }

            // Initial Setup
            toggleFields();
        });
        $.validator.addMethod("nowithleadingzero", function(value, element) {
            return this.optional(element) || /^[1-9][0-9]*$/.test(value);
        }, "Leading zeros are not allowed.");

        // Custom validation for range inputs
        $.validator.addMethod("rangeRequired", function(value, element) {
            var service = $("#service").val();
            var weightCostType = $("#weight_cost_type").val();

            if (service == "3" && (weightCostType == "2" || weightCostType == "3")) {
                var hasValidRange = false;
                $('.range-row').each(function() {
                    var fromKg = $(this).find('input[name="from_kg[]"]').val();
                    var toKg = $(this).find('input[name="to_kg[]"]').val();
                    var cost = $(this).find('input[name="cost[]"]').val();

                    if (fromKg && toKg && cost) {
                        hasValidRange = true;
                        return false; // break loop
                    }
                });
                return hasValidRange;
            }
            return true;
        }, "At least one complete range (From KG, To KG, Cost) is required for range-based weight cost types.");

        // Custom validation for individual range validity - only applied to to_kg[] fields
        $.validator.addMethod("validRange", function(value, element) {
            // Only validate to_kg fields
            if ($(element).attr('name').indexOf('to_kg') === -1) {
                return true;
            }

            var $row = $(element).closest('.range-row');
            var fromKgValue = $row.find('input[name="from_kg[]"]').val();
            var toKgValue = value;

            // Only validate if both fields have values (not empty)
            if (!fromKgValue || !toKgValue) {
                return true; // Don't validate if either field is empty
            }

            var fromKg = parseFloat(fromKgValue);
            var toKg = parseFloat(toKgValue);

            // Only validate if both values are valid numbers
            if (!isNaN(fromKg) && !isNaN(toKg)) {
                if (fromKg >= toKg) {
                    return false;
                }
            }
            return true;
        }, function(params, element) {
            var $row = $(element).closest('.range-row');
            var fromKg = parseFloat($row.find('input[name="from_kg[]"]').val());
            var toKg = parseFloat($(element).val());
            var rowIndex = $('.range-row').index($row) + 1;

            if (!isNaN(fromKg) && !isNaN(toKg) && fromKg >= toKg) {
                return "Range " + rowIndex + ": From KG (" + fromKg + ") must be less than To KG (" + toKg + ")";
            }
            return "From KG must be less than To KG";
        });

        // Custom validation for weight range gaps (Fixed Range Based only)
        $.validator.addMethod("noRangeGaps", function(value, element) {
            var service = $("#service").val();
            var weightCostType = $("#weight_cost_type").val();

            if (service == "3" && weightCostType == "2") { // Fixed Range Based
                var ranges = [];
                var hasValidRanges = false;

                // Collect all valid ranges
                $('.range-row').each(function(index) {
                    var fromKg = parseFloat($(this).find('input[name="from_kg[]"]').val());
                    var toKg = parseFloat($(this).find('input[name="to_kg[]"]').val());
                    var cost = parseFloat($(this).find('input[name="cost[]"]').val());

                    // Only consider ranges where all three fields have valid values
                    if (!isNaN(fromKg) && !isNaN(toKg) && !isNaN(cost) && fromKg < toKg) {
                        ranges.push({from: fromKg, to: toKg, index: index + 1});
                        hasValidRanges = true;
                    }
                });

                // Only validate gaps if there are at least 2 valid ranges
                if (ranges.length < 2) {
                    return true; // No gap validation needed for single range
                }

                // Sort ranges by 'from' value
                ranges.sort(function(a, b) { return a.from - b.from; });

                // Check if first range starts from 1 or 0
                if (ranges.length > 0 && ranges[0].from > 1) {
                    return false;
                }

                // Check for gaps and overlaps between ranges
                for (var i = 0; i < ranges.length - 1; i++) {
                    var current = ranges[i];
                    var next = ranges[i + 1];

                    // Check for overlaps
                    if (current.to >= next.from) {
                        return false;
                    }

                    // Check for gaps (Fixed Range Based requires continuous ranges)
                    if (current.to + 1 != next.from) {
                        return false;
                    }
                }
            }
            return true;
        }, function(params, element) {
            var service = $("#service").val();
            var weightCostType = $("#weight_cost_type").val();

            if (service == "3" && weightCostType == "2") {
                var ranges = [];

                // Collect all valid ranges
                $('.range-row').each(function() {
                    var fromKg = parseFloat($(this).find('input[name="from_kg[]"]').val());
                    var toKg = parseFloat($(this).find('input[name="to_kg[]"]').val());
                    var cost = parseFloat($(this).find('input[name="cost[]"]').val());

                    if (!isNaN(fromKg) && !isNaN(toKg) && !isNaN(cost) && fromKg < toKg) {
                        ranges.push({from: fromKg, to: toKg});
                    }
                });

                // Only show gap errors if there are multiple ranges
                if (ranges.length < 2) {
                    return ""; // No error for single range
                }

                ranges.sort(function(a, b) { return a.from - b.from; });

                // Check specific error conditions
                if (ranges.length > 0 && ranges[0].from > 1) {
                    return "First range should start from 1 KG (current: " + ranges[0].from + " KG)";
                }

                for (var i = 0; i < ranges.length - 1; i++) {
                    var current = ranges[i];
                    var next = ranges[i + 1];

                    if (current.to >= next.from) {
                        return "Ranges overlap: " + current.from + "-" + current.to + " overlaps with " + next.from + "-" + next.to;
                    }

                    if (current.to + 1 != next.from) {
                        var gapStart = current.to + 1;
                        var gapEnd = next.from - 1;
                        return "Gap detected: Missing range " + gapStart + "-" + gapEnd + " KG. Ranges must be continuous.";
                    }
                }
            }
            return "";
        });
        $(document).ready(function () {
            $('#weight_cost_type').on('change', function () {
                var selected = $(this).val();

                // Clear any existing validation errors
                clearRangeValidationErrors();

                if (selected == '1') {
                    $('#fixed_cost_section').show();
                    $('#range_cost_section').hide();
                    $('.fixed_with_min_cost_section').hide();
                    $('#range_requirements_info').hide();
                } else if (selected == '2' || selected == '3') {
                    $('#fixed_cost_section').hide();
                    $('#range_cost_section').show();
                    $('.fixed_with_min_cost_section').hide();
                    $('#range_requirements_info').show();

                    // Show appropriate info based on cost type
                    if (selected == '2') { // Fixed Range Based
                        $('#fixed_range_info').show();
                        $('#floating_range_info').hide();
                    } else { // Floating Range Based
                        $('#fixed_range_info').hide();
                        $('#floating_range_info').show();
                    }

                    // Update the info widget
                    setTimeout(function() {
                        updateRangeInfoWidget();
                    }, 100);
                } else if (selected == '4') {
                    $('#fixed_cost_section').show();
                    $('#range_cost_section').hide();
                    $('.fixed_with_min_cost_section').show();
                    $('#range_requirements_info').hide();
                } else {
                    $('#fixed_cost_section').hide();
                    $('#range_cost_section').hide();
                    $('.fixed_with_min_cost_section').hide();
                    $('#range_requirements_info').hide();
                }
            });

            // Function to clear range validation errors
            function clearRangeValidationErrors() {
                $('#range_validation_errors').hide();
                $('#range_error_list').empty();
            }



            // Add new range
            $('#add_range').click(function () {
                var html = `<div class="row mb-2 range-row">
                    <div class="col-sm-3">
                        <input type="number" name="from_kg[]" class="form-control" placeholder="From KG" step="0.01">
                    </div>
                    <div class="col-sm-3">
                        <input type="number" name="to_kg[]" class="form-control" placeholder="To KG" step="0.01">
                    </div>
                    <div class="col-sm-3">
                        <input type="number" name="cost[]" class="form-control" placeholder="Cost" step="0.01">
                    </div>
                    <div class="col-sm-3">
                        <button type="button" class="btn btn-danger remove-range">Remove</button>
                    </div>
                </div>`;
                $('#range_inputs').append(html);

                // Update the info widget after adding a range
                setTimeout(function() {
                    updateRangeInfoWidget();
                }, 100);
            });

            // Remove range row
            $(document).on('click', '.remove-range', function () {
                $(this).closest('.range-row').remove();

                // Update the info widget after removing a range
                setTimeout(function() {
                    updateRangeInfoWidget();
                }, 100);
            });

            // Real-time validation for weight ranges
            $(document).on('input change', 'input[name="from_kg[]"], input[name="to_kg[]"], input[name="cost[]"]', function() {
                // Update the info widget in real-time
                setTimeout(function() {
                    updateRangeInfoWidget();
                }, 100);
            });
        });
        //Implementing bulk upload client services
        $(document).ready(function () {
            $('#bulkUploadBtn').click(function () {
                $('#bulkUploadModal').modal('show');
            });

            $('#downloadTemplate').click(function () {
                window.location.href = '/download-bulk-upload-template';
            });

            $('#uploadCSV').click(function () {
                if (!$('#bulkUploadForm')[0].checkValidity()) {
                    $('#bulkUploadForm')[0].reportValidity();
                    return;
                }

                var formData = new FormData();
                formData.append('csv_file', $('#csv_file')[0].files[0]);
                formData.append('_token', "{{ csrf_token() }}");

                Swal.fire({
                    title: 'Uploading...',
                    text: 'Please wait while we process your file.',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: "{{ route('serive.bulkUpload') }}",
                    type: "POST",
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#bulkUploadModal').modal('hide');

                        if (response.success) {
                            var errorHtml = '';
                            if (response.errors && response.errors.length > 0) {
                                errorHtml = '<ul class="text-left">';
                                response.errors.forEach(function(error) {
                                    errorHtml += '<li>' + error + '</li>';
                                });
                                errorHtml += '</ul>';
                            }

                            Swal.fire({
                                icon: 'success',
                                title: 'Upload Complete',
                                html: response.message + (errorHtml ? '<br><br>Errors:<br>' + errorHtml : ''),
                                confirmButtonColor: '#3085d6'
                            }).then((result) => {
                                location.reload();
                            });
                        }
                    },
                    error: function(xhr) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: xhr.responseJSON.message || 'Something went wrong!',
                            confirmButtonColor: '#3085d6'
                        });
                    }
                });
            });
        });
</script>
@stop
