@extends('layouts.master')
@section('title', 'Services View-Paidash')
@section('content')
<meta name="csrf-token" content="{{ csrf_token() }}">
<div class="dashboard-main-body">

    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">View Client Service </h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">
                <a href="/services" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="hugeicons:user-settings-01" class="icon text-lg"></iconify-icon>
                    Cient Service
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">View Service</li>
        </ul>
    </div>

    <div class="row gy-4">
        <div class="col-xxl-12 mb-4">
            <div class="card">
                <div class="card-body">
                    <div class="card shadow-none border">
                        <div class="card-header bg-light fw-bold d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <iconify-icon icon="mdi:cog-outline" width="22"></iconify-icon>
                                {{$client_service->service_type_data->name}}
                                <small class="text-muted">({{$client_service->service->user_label}})</small>
                            </div>
                            <div class="d-flex align-items-center">
                                @if($client_service->status == 1)
                                    <span class="badge bg-success text-white">Active</span>
                                    <!-- End Service Button -->
                                    <button class="btn btn-danger btn-sm ms-2 d-inline-flex align-items-center gap-1" onclick="openEndServiceModal()">
                                        <iconify-icon icon="hugeicons:logout-square-02" width="16"></iconify-icon> End Service
                                    </button>
                                    <a href="javascript:void(0);"
                                        class="btn btn-sm ms-2 d-inline-flex align-items-center gap-1 btn-primary generate-invoice-btn"
                                        data-id="{{ $client_service->id }}"
                                        data-next-date="{{ $client_service->next_invoice_date }}"
                                        data-bs-toggle="tooltip"
                                        title="Next invoice in {{ \Carbon\Carbon::parse($client_service->next_invoice_date)->diffInDays(\Carbon\Carbon::now()) }} days">
                                        Generate Invoice
                                    </a>

                                @else
                                    <span class="badge bg-primary text-white">Completed</span>
                                @endif

                                <!-- Change Logs Button (Always visible) -->
                                <a href="{{ route('service.change-logs', $client_service->id) }}"
                                   class="btn btn-info btn-sm ms-2 d-inline-flex align-items-center gap-1"
                                   data-bs-toggle="tooltip"
                                   title="View change history">
                                    <iconify-icon icon="mdi:history" width="16"></iconify-icon> Change Logs
                                </a>
                            </div>
                        </div>

                        <!-- Card Body with Grid Layout -->
                        <div class="card-body">
                            <div class="row">
                                <!-- Basic Information -->
                                <div class="col-12 mb-3">
                                    <h6 class="fw-bold text-primary mb-2 d-flex align-items-center">
                                        <iconify-icon icon="mdi:information-outline" width="20" class="me-2"></iconify-icon>
                                        Basic Information
                                    </h6>
                                </div>

                                <div class="col-4 d-flex align-items-center mb-2">
                                    <iconify-icon icon="mdi:account" width="20" class="me-2"></iconify-icon>
                                    <span><strong>Client:</strong> {{ $client_service->client ? $client_service->client->name : 'NA' }}</span>
                                </div>

                                <div class="col-4 d-flex align-items-center mb-2">
                                    <iconify-icon icon="mdi:calendar-start" width="20" class="me-2"></iconify-icon>
                                    <span><strong>Start Date:</strong> {{ $client_service->start_date ? date('d-m-Y', strtotime($client_service->start_date)) : 'NA' }}</span>
                                </div>

                                <div class="col-4 d-flex align-items-center mb-2">
                                    <iconify-icon icon="mdi:calendar-end" width="20" class="me-2"></iconify-icon>
                                    <span><strong>End Date:</strong> {{ $client_service->end_date ? date('d-m-Y', strtotime($client_service->end_date)) : 'NA' }}</span>
                                </div>

                                <div class="col-4 d-flex align-items-center mb-2">
                                    <iconify-icon icon="mdi:credit-card-outline" width="20" class="me-2"></iconify-icon>
                                    <span><strong>Payment Cycle:</strong> {{$client_service->payment_cycle}}
                                        @if($client_service->service_id == 3)
                                            <small class="text-info d-block">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Weight-based services support Monthly cycle only
                                            </small>
                                        @endif
                                    </span>
                                </div>

                                <div class="col-4 d-flex align-items-center mb-2">
                                    <iconify-icon icon="mdi:calendar-clock" width="20" class="me-2"></iconify-icon>
                                    <span><strong>Next Invoice Date:</strong> {{ $client_service->next_invoice_date ? date('d-m-Y', strtotime($client_service->next_invoice_date)) : 'NA' }}</span>
                                </div>

                                <div class="col-4 d-flex align-items-center mb-2">
                                    <iconify-icon icon="mdi:cog-outline" width="20" class="me-2"></iconify-icon>
                                    <span><strong>Invoice Generation Type:</strong>
                                        @php
                                            $invoiceTypes = [
                                                1 => 'End of Month',
                                                2 => 'Based on Start Date',
                                                3 => 'Custom Day'
                                            ];
                                        @endphp
                                        {{ $invoiceTypes[$client_service->invoice_generation_type ?? 1] ?? 'End of Month' }}
                                        @if(($client_service->invoice_generation_type ?? 1) == 3 && $client_service->custom_invoice_day)
                                            ({{ $client_service->custom_invoice_day }}{{
                                                $client_service->custom_invoice_day == 1 ? 'st' :
                                                ($client_service->custom_invoice_day == 2 ? 'nd' :
                                                ($client_service->custom_invoice_day == 3 ? 'rd' : 'th'))
                                            }})
                                        @endif
                                    </span>
                                </div>

                                <div class="col-4 d-flex align-items-center mb-2">
                                    <iconify-icon icon="mdi:cash" width="20" class="me-2"></iconify-icon>
                                    <span><strong>Total Price:</strong> ₹{{ number_format($client_service->total_price, 2) }}</span>
                                </div>

                                <!-- Service Type Specific Information -->
                                @if($client_service->service_id == 1) {{-- Bedded Service --}}
                                    <div class="col-12 mb-3 mt-3">
                                        <h6 class="fw-bold text-success mb-2 d-flex align-items-center">
                                            <iconify-icon icon="mdi:bed-outline" width="20" class="me-2"></iconify-icon>
                                            Bedded Service Details
                                        </h6>
                                    </div>

                                    @if($client_service->beds_count)
                                        <div class="col-4 d-flex align-items-center mb-2">
                                            <iconify-icon icon="mdi:bed" width="20" class="me-2"></iconify-icon>
                                            <span><strong>Number of Units:</strong> {{ $client_service->beds_count }}</span>
                                        </div>
                                    @endif

                                    @if($client_service->unit_price)
                                        <div class="col-4 d-flex align-items-center mb-2">
                                            <iconify-icon icon="mdi:currency-inr" width="20" class="me-2"></iconify-icon>
                                            <span><strong>Unit Price:</strong> ₹{{ number_format($client_service->unit_price, 2) }}</span>
                                        </div>
                                    @endif

                                @elseif($client_service->service_id == 2) {{-- Non-Bedded Service --}}
                                    <div class="col-12 mb-3 mt-3">
                                        <h6 class="fw-bold text-info mb-2 d-flex align-items-center">
                                            <iconify-icon icon="mdi:home-outline" width="20" class="me-2"></iconify-icon>
                                            Non-Bedded Service Details
                                        </h6>
                                    </div>

                                    @if($client_service->non_bedded_type)
                                        <div class="col-4 d-flex align-items-center mb-2">
                                            <iconify-icon icon="mdi:format-list-bulleted-type" width="20" class="me-2"></iconify-icon>
                                            <span><strong>Non-Bedded Type:</strong> {{ $client_service->non_bedded_type }}</span>
                                        </div>
                                    @endif

                                @elseif($client_service->service_id == 3) {{-- Weight-Based Service --}}
                                    <div class="col-12 mb-3 mt-3">
                                        <h6 class="fw-bold text-warning mb-2 d-flex align-items-center">
                                            <iconify-icon icon="mdi:scale-balance" width="20" class="me-2"></iconify-icon>
                                            Weight-Based Service Details
                                        </h6>
                                    </div>

                                    @if($client_service->weight_cost_type)
                                        <div class="col-4 d-flex align-items-center mb-2">
                                            <iconify-icon icon="mdi:format-list-numbered" width="20" class="me-2"></iconify-icon>
                                            <span><strong>Weight Cost Type:</strong>
                                            @switch($client_service->weight_cost_type)
                                                @case(1)
                                                    Fixed Cost
                                                    @break
                                                @case(2)
                                                    Fixed Range Based
                                                    @break
                                                @case(3)
                                                    Floating Range Based
                                                    @break
                                                @case(4)
                                                    Fixed with Minimum Qty
                                                    @break
                                                @default
                                                    Unknown
                                            @endswitch
                                            </span>
                                        </div>
                                    @endif

                                    @if($client_service->weight_cost_type == 1 || $client_service->weight_cost_type == 4)
                                        @if($client_service->weight_cost)
                                            <div class="col-4 d-flex align-items-center mb-2">
                                                <iconify-icon icon="mdi:currency-inr" width="20" class="me-2"></iconify-icon>
                                                <span><strong>Cost per KG:</strong> ₹{{ number_format($client_service->weight_cost, 2) }}</span>
                                            </div>
                                        @endif
                                    @endif

                                    @if($client_service->weight_cost_type == 4)
                                        @if($client_service->minimum_kgs)
                                            <div class="col-4 d-flex align-items-center mb-2">
                                                <iconify-icon icon="mdi:weight-kilogram" width="20" class="me-2"></iconify-icon>
                                                <span><strong>Minimum KGs:</strong> {{ $client_service->minimum_kgs }} KG</span>
                                            </div>
                                        @endif

                                        @if($client_service->minimum_cost)
                                            <div class="col-4 d-flex align-items-center mb-2">
                                                <iconify-icon icon="mdi:currency-inr" width="20" class="me-2"></iconify-icon>
                                                <span><strong>Minimum Cost:</strong> ₹{{ number_format($client_service->minimum_cost, 2) }}</span>
                                            </div>
                                        @endif
                                    @endif

                                    @if(($client_service->weight_cost_type == 2 || $client_service->weight_cost_type == 3) && $client_service->weightCostRanges && $client_service->weightCostRanges->count() > 0)
                                        <div class="col-12 mb-3 mt-3">
                                            <h6 class="fw-bold text-secondary mb-2 d-flex align-items-center">
                                                <iconify-icon icon="mdi:chart-line" width="20" class="me-2"></iconify-icon>
                                                Weight Cost Ranges
                                            </h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-bordered">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th>From KG</th>
                                                            <th>To KG</th>
                                                            <th>Cost per KG</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($client_service->weightCostRanges as $range)
                                                            <tr>
                                                                <td>{{ $range->from_kg }} KG</td>
                                                                <td>{{ $range->to_kg }} KG</td>
                                                                <td>₹{{ number_format($range->cost, 2) }}</td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    @endif

                                @elseif($client_service->service_id == 4) {{-- Bedded with Fixed Price --}}
                                    <div class="col-12 mb-3 mt-3">
                                        <h6 class="fw-bold text-purple mb-2 d-flex align-items-center">
                                            <iconify-icon icon="mdi:bed-king-outline" width="20" class="me-2"></iconify-icon>
                                            Bedded with Fixed Price Details
                                        </h6>
                                    </div>

                                    @if($client_service->beds_count)
                                        <div class="col-4 d-flex align-items-center mb-2">
                                            <iconify-icon icon="mdi:bed" width="20" class="me-2"></iconify-icon>
                                            <span><strong>Number of Units:</strong> {{ $client_service->beds_count }}</span>
                                        </div>
                                    @endif

                                    @if($client_service->non_bedded_type)
                                        <div class="col-4 d-flex align-items-center mb-2">
                                            <iconify-icon icon="mdi:format-list-bulleted-type" width="20" class="me-2"></iconify-icon>
                                            <span><strong>Non-Bedded Type:</strong> {{ $client_service->non_bedded_type }}</span>
                                        </div>
                                    @endif
                                @endif

                                <!-- Description -->
                                @if($client_service->description)
                                    <div class="col-12 mt-3">
                                        <h6 class="fw-bold text-muted mb-2 d-flex align-items-center">
                                            <iconify-icon icon="mdi:comment-text-outline" width="20" class="me-2"></iconify-icon>
                                            Description
                                        </h6>
                                        <p class="text-muted">{{ $client_service->description }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Ending Service -->
    <!-- Modal for Ending Service -->
<div class="modal fade" id="endServiceModal" tabindex="-1" aria-labelledby="endServiceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h7 class="modal-title" id="endServiceModalLabel"><b>End Service</b></h7>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="endServiceForm">
                    @csrf
                    <input type="hidden" id="service_id" value="{{ $client_service->id }}">

                    <!-- Start Date Display -->
                    <div class="mb-3">
                        <label class="form-label"><b>Start Date:</b> {{ date('m/d/Y',strtotime($client_service->start_date)) }}</label>
                        <input type="hidden" id="start_date" value="{{ $client_service->start_date }}">
                    </div>

                    <!-- End Date Selection -->
                    <div class="mb-3">
                        <label for="end_date" class="form-label">Enter End Date</label>
                        <input type="date" class="form-control" id="end_date" value="{{ $client_service->end_date }}" required>
                    </div>

                    <button type="submit" class="btn btn-danger w-100">Submit</button>
                </form>
            </div>
        </div>
    </div>
</div>



</div>
@stop
@section('script')
<script>
   function openEndServiceModal() {
    $('#endServiceModal').modal('show');
    }

    $(document).ready(function () {
        $('[data-bs-toggle="tooltip"]').tooltip();

        let today = new Date();
        let firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

        let startDate = new Date($("#start_date").val());

        // Set the min attribute for end_date (must be at least the first day of the current month)
        let minDate = firstDayOfMonth.toISOString().split("T")[0];
        $("#end_date").attr("min", minDate);

        $("#end_date").on("change", function () {
            let selectedEndDate = new Date($(this).val());

            if (selectedEndDate < firstDayOfMonth) {
                Swal.fire("Error", "End date cannot be from a previous month.", "error");
                $(this).val(""); // Reset invalid date
                return;
            }

            if (selectedEndDate < startDate) {
                Swal.fire("Error", "End date cannot be before the start date.", "error");
                $(this).val(""); // Reset invalid date
            }
        });

        $("#endServiceForm").on("submit", function (event) {
            event.preventDefault();

            let serviceId = $("#service_id").val();
            let endDate = $("#end_date").val();

            if (!endDate) {
                Swal.fire("Error", "Please enter an end date", "error");
                return;
            }

            if (new Date(endDate) < firstDayOfMonth) {
                Swal.fire("Error", "End date cannot be from a previous month.", "error");
                return;
            }

            if (new Date(endDate) < startDate) {
                Swal.fire("Error", "End date cannot be before the start date.", "error");
                return;
            }

            Swal.fire({
                title: "Are you sure?",
                text: "You are about to end this service.",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "Yes, end it!"
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: "{{ route('service.end_service') }}",
                        type: "POST",
                        data: {
                            service_id: serviceId,
                            end_date: endDate,
                            _token: $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function (response) {
                            if (response.success) {
                                Swal.fire("Success!", "The service has been ended.", "success").then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire("Error!", response.message || "Something went wrong.", "error");
                            }
                        },
                        error: function (xhr, status, error) {
                            Swal.fire("Error!", "Something went wrong.", "error");
                            console.error("Error:", error);
                        }
                    });
                }
            });
        });
    });
    $(document).on('click', '.generate-invoice-btn', function(e) {
        e.preventDefault(); // Prevent any default action

        var clientServiceId = $(this).data('id');
        var nextInvoiceDate = $(this).data('next-date');

        console.log('Generate Invoice clicked for service ID:', clientServiceId);
        console.log('Next invoice date:', nextInvoiceDate);
        console.log('Button element:', this);

        // Validate required data
        if (!clientServiceId) {
            console.error('No client service ID found');
            Swal.fire({
                title: 'Error!',
                text: 'Service ID not found. Please refresh the page and try again.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
            return;
        }

        Swal.fire({
            title: 'Are you sure?',
            text: 'We are generating an invoice for ' + nextInvoiceDate + '. Do you want to continue?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, generate it!'
        }).then((result) => {
            if (result.isConfirmed) {
                console.log('User confirmed, making AJAX request...');

                // Show loading state
                Swal.fire({
                    title: 'Generating Invoice...',
                    text: 'Please wait while we generate your invoice.',
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: '/generate-invoice/' + clientServiceId,
                    type: 'GET',
                    dataType: 'json',
                    timeout: 30000, // 30 second timeout
                    success: function(response) {
                        console.log('AJAX Success Response:', response);
                        console.log('Response type:', typeof response);

                        // Handle different response types
                        if (typeof response === 'string') {
                            try {
                                response = JSON.parse(response);
                            } catch (e) {
                                console.log('Failed to parse response as JSON:', e);
                                Swal.fire({
                                    title: 'Error!',
                                    text: 'Received invalid response from server.',
                                    icon: 'error',
                                    confirmButtonText: 'OK'
                                });
                                return;
                            }
                        }

                        if (response && response.success === true) {
                            Swal.fire({
                                title: 'Success!',
                                text: response.message || 'Invoice generated successfully!',
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then(() => {
                                location.reload(); // Reload page after confirmation
                            });
                        } else if (response && response.success === false) {
                            Swal.fire({
                                title: 'Notice',
                                text: response.message || 'No invoice was generated.',
                                icon: 'warning',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.message || response.error || 'Failed to generate invoice.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('AJAX Error:', xhr, status, error);
                        console.log('Response Text:', xhr.responseText);
                        console.log('Status Code:', xhr.status);

                        let errorMessage = 'Something went wrong. Please try again.';

                        if (xhr.status === 403) {
                            errorMessage = 'You do not have permission to generate invoices.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Invoice generation endpoint not found.';
                        } else if (xhr.status === 500) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                errorMessage = response.error || 'Server error occurred.';
                            } catch (e) {
                                errorMessage = 'Server error occurred.';
                            }
                        } else if (status === 'timeout') {
                            errorMessage = 'Request timed out. Please try again.';
                        }

                        Swal.fire({
                            title: 'Error!',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    });
</script>
@stop

