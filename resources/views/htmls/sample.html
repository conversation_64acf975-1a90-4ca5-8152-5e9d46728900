<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Lexend%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div
      class="relative flex size-full min-h-screen flex-col bg-slate-50 justify-between group/design-root overflow-x-hidden"
      style='font-family: Lexend, "Noto Sans", sans-serif;'
    >
      <div>
        <div class="flex items-center bg-slate-50 p-4 pb-2 justify-between">
          <div class="text-[#0e161b] flex size-12 shrink-0 items-center" data-icon="ArrowLeft" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z"></path>
            </svg>
          </div>
          <h2 class="text-[#0e161b] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center">Find a doctor</h2>
          <div class="flex w-12 items-center justify-end">
            <button
              class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 bg-transparent text-[#0e161b] gap-2 text-base font-bold leading-normal tracking-[0.015em] min-w-0 p-0"
            >
              <div class="text-[#0e161b]" data-icon="SlidersHorizontal" data-size="24px" data-weight="regular">
                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                  <path
                    d="M40,88H73a32,32,0,0,0,62,0h81a8,8,0,0,0,0-16H135a32,32,0,0,0-62,0H40a8,8,0,0,0,0,16Zm64-24A16,16,0,1,1,88,80,16,16,0,0,1,104,64ZM216,168H199a32,32,0,0,0-62,0H40a8,8,0,0,0,0,16h97a32,32,0,0,0,62,0h17a8,8,0,0,0,0-16Zm-48,24a16,16,0,1,1,16-16A16,16,0,0,1,168,192Z"
                  ></path>
                </svg>
              </div>
            </button>
          </div>
        </div>
        <div class="px-4 py-3">
          <label class="flex flex-col min-w-40 h-12 w-full">
            <div class="flex w-full flex-1 items-stretch rounded-lg h-full">
              <div
                class="text-[#4e7a97] flex border-none bg-[#e7eef3] items-center justify-center pl-4 rounded-l-lg border-r-0"
                data-icon="MagnifyingGlass"
                data-size="24px"
                data-weight="regular"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                  <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
                </svg>
              </div>
              <input
                placeholder="Search for doctors"
                class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#0e161b] focus:outline-0 focus:ring-0 border-none bg-[#e7eef3] focus:border-none h-full placeholder:text-[#4e7a97] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                value=""
              />
            </div>
          </label>
        </div>
        <h2 class="text-[#0e161b] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Categories</h2>
        <div class="flex overflow-y-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&amp;::-webkit-scrollbar]:hidden">
          <div class="flex items-stretch p-4 gap-8">
            <div class="flex h-full flex-1 flex-col gap-4 text-center rounded-lg min-w-32 pt-4">
              <div
                class="bg-center bg-no-repeat aspect-square bg-cover rounded-full flex flex-col self-center w-full"
                style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDh0U7bLgEMSTH1IVLk93RRkVhpeTyq8034snToVAbCvD7PjKTpPvSCkW-nZLvxviA38Rg5EHffh5kzWq5l5Ju2i72LxxFsQ_CQ4mNcfUlnmLSPKzAFWvOWLUBJEsWBWPrXphfi0eHB3ltYxg04nmhwGP31pTv3Lrbcw5sx6p-zQoi_QOt-u2-ubcO5qQroD5SE_nFmOo_-987wsahHDhBBkkGUQYUztobgSqXQR91_6MiQ0jsHXwH-RrteclxSZ0mMJ8LVm-OBhmep");'
              ></div>
              <p class="text-[#0e161b] text-base font-medium leading-normal">General Physician</p>
            </div>
            <div class="flex h-full flex-1 flex-col gap-4 text-center rounded-lg min-w-32 pt-4">
              <div
                class="bg-center bg-no-repeat aspect-square bg-cover rounded-full flex flex-col self-center w-full"
                style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDlQLZ2LKpn6gUWAOo_irGMaRw4Ao1xCuv8fO02tyBX_C7Kne77zjQToWzlWiFVa1VF10U0of73kPHDFeezt_HaAWIyAknEFdo9TBxkpaC8hD3F7qQYoxpmU1_wUiyeE5ybe8T-QXbQB8obnj5u0wGDwOQrsnivs0hElhvuTZ4qUmx2xD2fQKSaWBgnrKk-as7sgw8J38BwB95pvphA34Ij2WvdWjyF0gqVfHd9i5vNQU6wbWRaiIOiqELcDdUNQVlZ_u2zXzKIack9");'
              ></div>
              <p class="text-[#0e161b] text-base font-medium leading-normal">Dermatologist</p>
            </div>
            <div class="flex h-full flex-1 flex-col gap-4 text-center rounded-lg min-w-32 pt-4">
              <div
                class="bg-center bg-no-repeat aspect-square bg-cover rounded-full flex flex-col self-center w-full"
                style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBoIQmFnrpOQBzGC1EAP9ZcHwvXjobY6JMAxTZ58jN0f4CnkCfsMMcvU2bm8-hP5s3I61BVbt9E4e0KQFsjRdseBpJc1778kKBb4HvvoAmUkjUrN1gqJGfJht35XVqZ-9owIVlQzKY93XDBwwNB70i2OeV49K9s0SMHwD0gb2zSNTRdANbKGTS0Qc7KV1CzImDdoreeomX8GUA_q85C8BkxDc5Me-CJO3EKdqG-z53zd4HI2rJvOk9n886R9IE2hg95Wsjb0yBTBPXO");'
              ></div>
              <p class="text-[#0e161b] text-base font-medium leading-normal">Pediatrician</p>
            </div>
            <div class="flex h-full flex-1 flex-col gap-4 text-center rounded-lg min-w-32 pt-4">
              <div
                class="bg-center bg-no-repeat aspect-square bg-cover rounded-full flex flex-col self-center w-full"
                style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuD8WPfU7UITK9_cx7IvcSItivXEUWn8om0teUGcyUmDCh94SiABkvkXuRxGV4VhkdzbuBALZp3twZIxoR09O2iTWveQAfvTWMLUZl4okKDe8-r7JjvWWD7m3g_-k4zdBn8GaGUsD_X2LgkLwPdlFRTI1oNDu0u-9wO2VnVQ1qw-cJL-w6jOA3KGrVTqSIUaKrLG2gW7fqUa56KS5EsKYp7fdzexhMt7JpiG98HGbw7p0dGFUg2EWbI80yP8Dwu60MaGo_tVIEfqIL_v");'
              ></div>
              <p class="text-[#0e161b] text-base font-medium leading-normal">Gynecologist</p>
            </div>
          </div>
        </div>
        <h2 class="text-[#0e161b] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Top Doctors</h2>
        <div class="flex items-center gap-4 bg-slate-50 px-4 min-h-[72px] py-2">
          <div
            class="bg-center bg-no-repeat aspect-square bg-cover rounded-full h-14 w-fit"
            style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAq9QOkLhgngKGJ-AaBxLMVd5TAi0O7hDYFDluqcZh_aC2RTnjZFiOmQGXzfjp2GbKKKB2vpHZCJdlvJCQr_l0crYexhrp_hiqn9SYTnSS8sMOd0DJCDxRbS1kcI9VDt2k-srZRyHcnuei6qc90eR_XyeEEJKM-gX3AjMMJQ1NeTE89kcVesmazVakj2Fpi0rdIRx3As0ru9J4VlDJ69pJClS1iDskWsZVPoeu-_UClFFv_gKDII-WoQk4hCYV6nfRv1pdzpnJ6wCZO");'
          ></div>
          <div class="flex flex-col justify-center">
            <p class="text-[#0e161b] text-base font-medium leading-normal line-clamp-1">Dr. Emily Carter</p>
            <p class="text-[#4e7a97] text-sm font-normal leading-normal line-clamp-2">General Physician</p>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-slate-50 px-4 min-h-[72px] py-2">
          <div
            class="bg-center bg-no-repeat aspect-square bg-cover rounded-full h-14 w-fit"
            style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDZCEFDmgZKKqtN_5T95uP_7a1mSew6GOVvXg_hw88TZ_2nhpiYp-RFUpPxfarIbJcGc5_TMK4ozQkDLnN87rL-q02M6mACIECqh7K8PIEyAPX64YvfjN20rWf0SYJ6fhUvi4dx-YhSBYIcDzForQDk7WFIdxZEDy3WEqDuVbu6xCeMn-RFLtPq_Iwv_pE_7po_1KsngEanj7Rxyt53C_iwMdDo2YVAuYdAVzdZfibZaNkK1U_jz5mbST30tD3P8OFbd0735byuUKAa");'
          ></div>
          <div class="flex flex-col justify-center">
            <p class="text-[#0e161b] text-base font-medium leading-normal line-clamp-1">Dr. David Lee</p>
            <p class="text-[#4e7a97] text-sm font-normal leading-normal line-clamp-2">Dermatologist</p>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-slate-50 px-4 min-h-[72px] py-2">
          <div
            class="bg-center bg-no-repeat aspect-square bg-cover rounded-full h-14 w-fit"
            style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBzl7TfKwbQTMIICiPK_1hSlV4SBsn-VyE7hw_jaBecO5sloAzJUIVvfH16sIp_6v-6Ft0B7_-DK0MdMY3ZLxGj7eaw8JHYdnUxxjfQIuAUzH7GaZ-E8Ll1JCyKOkhfP3M92TCgUjWNEkbW_MCIPvkzi3obyxnMKrMrOOYB6Bbv0HqpJ-U43mmIytWPUY7pj-p537L3JsbShKyR2LWdj3ugrsha1nNA3a0FoTiXu0cJtmSTjOfg-mQkJYq5VuMDQIhVsKIMm17W2sEg");'
          ></div>
          <div class="flex flex-col justify-center">
            <p class="text-[#0e161b] text-base font-medium leading-normal line-clamp-1">Dr. Sophia Clark</p>
            <p class="text-[#4e7a97] text-sm font-normal leading-normal line-clamp-2">Pediatrician</p>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-slate-50 px-4 min-h-[72px] py-2">
          <div
            class="bg-center bg-no-repeat aspect-square bg-cover rounded-full h-14 w-fit"
            style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuB9TilnlA_P5D7R1Yo8beHW47PlPNYquGVbRjD4Jn8nPFhr9baDyfBSQT6KbAUwzdz890nATY3MiE_HCgQvcIxj5imNth_C4eVgelIUpofljD8RhqJQWbzf_DpTqCJbUiE8kgZrhnYSGJiMOgNbK7xOn-tSgLua6nVQMzbsUI5_4NdZZk3Smw2fA3rFEbT8qIKix_iRaYfk2JvxM9Y9bLbycUi3K0lrFg5crTOqLvWfmQ8VYKcAY6nrUjkSvtQspwxo6MafcspuKLbq");'
          ></div>
          <div class="flex flex-col justify-center">
            <p class="text-[#0e161b] text-base font-medium leading-normal line-clamp-1">Dr. Ethan Brown</p>
            <p class="text-[#4e7a97] text-sm font-normal leading-normal line-clamp-2">Gynecologist</p>
          </div>
        </div>
      </div>
      <div>
        <div class="flex gap-2 border-t border-[#e7eef3] bg-slate-50 px-4 pb-3 pt-2">
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#4e7a97]" href="#">
            <div class="text-[#4e7a97] flex h-8 items-center justify-center" data-icon="House" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M218.83,103.77l-80-75.48a1.14,1.14,0,0,1-.11-.11,16,16,0,0,0-21.53,0l-.11.11L37.17,103.77A16,16,0,0,0,32,115.55V208a16,16,0,0,0,16,16H96a16,16,0,0,0,16-16V160h32v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V115.55A16,16,0,0,0,218.83,103.77ZM208,208H160V160a16,16,0,0,0-16-16H112a16,16,0,0,0-16,16v48H48V115.55l.11-.1L128,40l79.9,75.43.11.1Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#4e7a97] text-xs font-medium leading-normal tracking-[0.015em]">Home</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#0e161b]" href="#">
            <div class="text-[#0e161b] flex h-8 items-center justify-center" data-icon="User" data-size="24px" data-weight="fill">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M230.93,220a8,8,0,0,1-6.93,4H32a8,8,0,0,1-6.92-12c15.23-26.33,38.7-45.21,66.09-54.16a72,72,0,1,1,73.66,0c27.39,8.95,50.86,27.83,66.09,54.16A8,8,0,0,1,230.93,220Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#0e161b] text-xs font-medium leading-normal tracking-[0.015em]">Doctors</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#4e7a97]" href="#">
            <div class="text-[#4e7a97] flex h-8 items-center justify-center" data-icon="Pill" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M216.42,39.6a53.26,53.26,0,0,0-75.32,0L39.6,141.09a53.26,53.26,0,0,0,75.32,75.31h0L216.43,114.91A53.31,53.31,0,0,0,216.42,39.6ZM103.61,205.09h0a37.26,37.26,0,0,1-52.7-52.69L96,107.31,148.7,160ZM205.11,103.6,160,148.69,107.32,96l45.1-45.09a37.26,37.26,0,0,1,52.69,52.69ZM189.68,82.34a8,8,0,0,1,0,11.32l-24,24a8,8,0,1,1-11.31-11.32l24-24A8,8,0,0,1,189.68,82.34Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#4e7a97] text-xs font-medium leading-normal tracking-[0.015em]">Pharmacy</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#4e7a97]" href="#">
            <div class="text-[#4e7a97] flex h-8 items-center justify-center" data-icon="TestTube" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M237.66,86.34l-60-60a8,8,0,0,0-11.32,0L37.11,155.57a44.77,44.77,0,0,0,63.32,63.32L212.32,107l22.21-7.4a8,8,0,0,0,3.13-13.25ZM89.11,207.57a28.77,28.77,0,0,1-40.68-40.68l28.8-28.8c8.47-2.9,21.75-4,39.07,5,10.6,5.54,20.18,8,28.56,8.73ZM205.47,92.41a8,8,0,0,0-3.13,1.93l-39.57,39.57c-8.47,2.9-21.75,4-39.07-5-10.6-5.54-20.18-8-28.56-8.73L172,43.31,217.19,88.5Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#4e7a97] text-xs font-medium leading-normal tracking-[0.015em]">Tests</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#4e7a97]" href="#">
            <div class="text-[#4e7a97] flex h-8 items-center justify-center" data-icon="UserCircle" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24ZM74.08,197.5a64,64,0,0,1,107.84,0,87.83,87.83,0,0,1-107.84,0ZM96,120a32,32,0,1,1,32,32A32,32,0,0,1,96,120Zm97.76,66.41a79.66,79.66,0,0,0-36.06-28.75,48,48,0,1,0-59.4,0,79.66,79.66,0,0,0-36.06,28.75,88,88,0,1,1,131.52,0Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#4e7a97] text-xs font-medium leading-normal tracking-[0.015em]">Profile</p>
          </a>
        </div>
        <div class="h-5 bg-slate-50"></div>
      </div>
    </div>
  </body>
</html>
