@extends('layouts.master')
@section('title', 'Dashboard - Paidash')

@push('styles')
<link rel="stylesheet" href="{{ asset('assets/css/dashboard.css') }}">
@endpush

@section('content')

<div class="dashboard-main-body">
    <!-- Ultra Professional Dashboard Header -->
    <div class="dashboard-header mb-3">
        <!-- Background Pattern -->
        <div class="dashboard-header-pattern"></div>
        <div class="d-flex flex-wrap align-items-center justify-content-between gap-4 position-relative">
            <div class="d-flex align-items-center gap-4">
                <div class="dashboard-icon">
                    <iconify-icon icon="solar:chart-2-bold" class="text-white"></iconify-icon>
                </div>
                <div>
                    <h1 class="text-white fw-bold mb-2 dashboard-title">Business Dashboard</h1>
                    <p class="text-white mb-0 dashboard-subtitle">Real-time insights and analytics for {{ Auth::user()->name }}'s business</p>
                </div>
            </div>
            <div class="d-flex align-items-center gap-4">
                {{-- <button type="button" class="btn btn-light btn-lg d-flex align-items-center gap-3 dashboard-refresh-btn" onclick="refreshDashboard()">
                    <iconify-icon icon="solar:refresh-bold"></iconify-icon>
                    <span>Refresh Data</span>
                </button> --}}
                <div class="text-center dashboard-date-widget">
                    <div class="d-flex align-items-center justify-content-center gap-2 mb-1">
                        <iconify-icon icon="solar:calendar-bold" class="text-warning"></iconify-icon>
                        <span class="text-white fw-bold dashboard-date-day">{{ now()->format('l') }} / {{ now()->format('F j, Y') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Quick Actions Panel -->
    @if(Auth::user()->can('client-create') || Auth::user()->can('invoice-create') || Auth::user()->can('payment-create'))
    <div class="card border-0 mb-3 quick-actions-panel">
        <div class="card-body p-4">
            <div class="d-flex align-items-center justify-content-between flex-wrap gap-4">
                <div class="d-flex align-items-center gap-3">
                    <div class="quick-actions-icon">
                        <iconify-icon icon="solar:widget-add-bold" class="text-white"></iconify-icon>
                    </div>
                    <h5 class="fw-bold mb-0 quick-actions-title">Quick Actions</h5>
                </div>
                <div class="d-flex align-items-center gap-3 flex-wrap quick-actions">
                    @can('client-create')
                    <a href="{{ route('clients.add') }}" class="btn btn-primary d-flex align-items-center gap-2 action-btn action-btn-primary" title="Add new client">
                        <iconify-icon icon="hugeicons:user-add-01"></iconify-icon>
                        <span class="d-none d-sm-inline">Add Client</span>
                    </a>
                    @endcan
                    @can('invoice-create')
                    <a href="{{ route('invoices.add') }}" class="btn btn-success d-flex align-items-center gap-2 action-btn action-btn-success" title="Create new invoice">
                        <iconify-icon icon="hugeicons:invoice-03"></iconify-icon>
                        <span class="d-none d-sm-inline">Create Invoice</span>
                    </a>
                    @endcan
                    @can('payment-create')
                    <a href="{{ route('payments.add') }}" class="btn btn-info d-flex align-items-center gap-2 action-btn action-btn-info" title="Record new payment">
                        <iconify-icon icon="hugeicons:money-receive-circle"></iconify-icon>
                        <span class="d-none d-sm-inline">Record Payment</span>
                    </a>
                    @endcan
                    @can('employee-create')
                    <a href="{{ route('employees.add') }}" class="btn btn-warning d-flex align-items-center gap-2 action-btn action-btn-warning" title="Add new employee">
                        <iconify-icon icon="hugeicons:user-account"></iconify-icon>
                        <span class="d-none d-sm-inline">Add Employee</span>
                    </a>
                    @endcan
                    @can('report-list')
                    <a href="/reports" class="btn btn-secondary d-flex align-items-center gap-2 action-btn action-btn-secondary" title="View reports">
                        <iconify-icon icon="hugeicons:analytics-02"></iconify-icon>
                        <span class="d-none d-sm-inline">Reports</span>
                    </a>
                    @endcan
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Main Statistics Cards -->
    <div class="row g-4">
        <!-- Total Revenue Card - Enhanced Professional Design -->
        <div class="col-xl-4 col-lg-4 col-md-6">
            <div class="card stats-card stats-card-revenue h-100 stats-card-pattern mb-3">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between position-relative">
                        <div class="flex-grow-1 stats-card-content">
                            <div class="d-flex align-items-center gap-2 mb-3">
                                <div class="stats-card-indicator"></div>
                                <p class=" mb-0 fw-semibold stats-card-label">Total Revenue</p>
                            </div>
                            <h2 class="fw-bold mb-2 stats-card-value-lg">₹{{ App\Http\Controllers\Controller::IND_money_format($data['total_revenue'], 0) }}</h2>
                            <div class="d-flex align-items-center gap-2 flex-wrap">
                                <span class="badge d-flex align-items-center gap-1 stats-card-badge {{ $data['revenue_difference'] >= 0 ? 'stats-card-badge-positive' : 'stats-card-badge-negative' }}">
                                    <iconify-icon icon="{{ $data['revenue_difference'] >= 0 ? 'solar:arrow-up-outline' : 'solar:arrow-down-outline' }}"></iconify-icon>
                                    {{ $data['revenue_difference'] >= 0 ? '+' : '' }}₹{{ App\Http\Controllers\Controller::IND_money_format(abs($data['revenue_difference']), 0) }}
                                </span>
                                <span class="stats-card-comparison">vs last month</span>
                            </div>
                        </div>
                        <div class="stats-card-icon">
                            <iconify-icon icon="hugeicons:money-bag-02" class="text-white"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Clients Card - Enhanced Professional Design -->
        <div class="col-xl-4 col-lg-4 col-md-6">
            <div class="card stats-card stats-card-clients h-100 stats-card-pattern">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between position-relative">
                        <div class="flex-grow-1 stats-card-content">
                            <div class="d-flex align-items-center gap-2 mb-3">
                                <div class="stats-card-indicator"></div>
                                <p class=" mb-0 fw-semibold stats-card-label">Active Clients</p>
                            </div>
                            <h2 class="fw-bold mb-2 stats-card-value-lg">{{ App\Http\Controllers\Controller::IND_money_format($data['active_clients'], 0) }}</h2>
                            <div class="d-flex align-items-center gap-2 flex-wrap">
                                <span class="badge d-flex align-items-center gap-1 stats-card-badge stats-card-badge-neutral">
                                    <iconify-icon icon="solar:users-group-rounded-outline"></iconify-icon>
                                    {{ App\Http\Controllers\Controller::IND_money_format($data['total_clients'], 0) }} Total
                                </span>
                                <span class="stats-card-comparison">clients registered</span>
                            </div>
                        </div>
                        <div class="stats-card-icon">
                            <iconify-icon icon="hugeicons:user-group" class="text-white"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Payments Card - Enhanced Professional Design -->
        <div class="col-xl-4 col-lg-4 col-md-6">
            <div class="card stats-card stats-card-payments h-100 stats-card-patterns">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between position-relative">
                        <div class="flex-grow-1 stats-card-content">
                            <div class="d-flex align-items-center gap-2 mb-3">
                                <div class="stats-card-indicator"></div>
                                <p class="mb-0 fw-semibold stats-card-label">Pending Payments</p>
                            </div>
                            <h2 class="fw-bold mb-2 stats-card-value-lg">₹{{ App\Http\Controllers\Controller::IND_money_format($data['unpaid_amount'], 0) }}</h2>
                            <div class="d-flex align-items-center gap-2 flex-wrap">
                                <span class="badge d-flex align-items-center gap-1 stats-card-badge {{ $data['unpaid_difference'] >= 0 ? 'stats-card-badge-negative' : 'stats-card-badge-positive' }}">
                                    <iconify-icon icon="{{ $data['unpaid_difference'] >= 0 ? 'solar:arrow-up-outline' : 'solar:arrow-down-outline' }}"></iconify-icon>
                                    {{ $data['unpaid_difference'] >= 0 ? '+' : '' }}₹{{ App\Http\Controllers\Controller::IND_money_format(abs($data['unpaid_difference']), 0) }}
                                </span>
                                <span class="stats-card-comparison">vs last month</span>
                            </div>
                        </div>
                        <div class="stats-card-icon">
                            <iconify-icon icon="hugeicons:money-receive-circle" class="text-white"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Services Card - Enhanced Professional Design -->
        <div class="col-xl-4 col-lg-4 col-md-6">
            <div class="card stats-card stats-card-services h-100 stats-card-pattern">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between position-relative">
                        <div class="flex-grow-1 stats-card-content">
                            <div class="d-flex align-items-center gap-2 mb-3">
                                <div class="stats-card-indicator"></div>
                                <p class=" mb-0 fw-semibold stats-card-label">Active Services</p>
                            </div>
                            <h2 class="fw-bold mb-2 stats-card-value-lg">{{ App\Http\Controllers\Controller::IND_money_format($data['active_services'], 0) }}</h2>
                            <div class="d-flex align-items-center gap-2 flex-wrap">
                                <span class="badge d-flex align-items-center gap-1 stats-card-badge stats-card-badge-neutral">
                                    <iconify-icon icon="solar:document-text-outline"></iconify-icon>
                                    {{ App\Http\Controllers\Controller::IND_money_format($data['no_of_invoices'], 0) }} Invoices
                                </span>
                                <span class="stats-card-comparison">currently running</span>
                            </div>
                        </div>
                        <div class="stats-card-icon">
                            <iconify-icon icon="mdi:cog" class="text-white"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Card - Enhanced Professional Design -->
        @can('inventory-list')
        <div class="col-xl-4 col-lg-4 col-md-6">
            <div class="card stats-card stats-card-inventory h-100 stats-card-pattern">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between position-relative">
                        <div class="flex-grow-1 stats-card-content">
                            <div class="d-flex align-items-center gap-2 mb-3">
                                <div class="stats-card-indicator"></div>
                                <p class=" mb-0 fw-semibold stats-card-label">Inventory Items</p>
                            </div>
                            <h2 class="fw-bold mb-2 stats-card-value-lg">{{ $inventoryMetrics['total_items'] ?? 0 }}</h2>
                            <div class="d-flex align-items-center gap-2 flex-wrap">
                                <span class="badge d-flex align-items-center gap-1 stats-card-badge stats-card-badge-neutral">
                                    <iconify-icon icon="solar:dollar-minimalistic-outline"></iconify-icon>
                                    ₹{{ App\Http\Controllers\Controller::IND_money_format($inventoryMetrics['total_value'] ?? 0) }} Value
                                </span>
                                <span class="stats-card-comparison">in stock</span>
                            </div>
                        </div>
                        <div class="stats-card-icon">
                            <iconify-icon icon="hugeicons:package" class="text-white"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endcan

        <!-- Monthly Expenses Card - Enhanced Professional Design -->
        <div class="col-xl-4 col-lg-4 col-md-6">
            <div class="card stats-card stats-card-expenses h-100 stats-card-pattern">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between position-relative">
                        <div class="flex-grow-1 stats-card-content">
                            <div class="d-flex align-items-center gap-2 mb-3">
                                <div class="stats-card-indicator"></div>
                                <p class=" mb-0 fw-semibold stats-card-label">Monthly Expenses</p>
                            </div>
                            <h2 class="fw-bold mb-2 stats-card-value-lg">₹{{ App\Http\Controllers\Controller::IND_money_format($expenseMetrics['monthly_total'] ?? 0) }}</h2>
                            <div class="d-flex align-items-center gap-2 flex-wrap">
                                <span class="badge d-flex align-items-center gap-1 stats-card-badge stats-card-badge-neutral">
                                    <iconify-icon icon="solar:receipt-outline"></iconify-icon>
                                    {{ $expenseMetrics['monthly_count'] ?? 0 }} Transactions
                                </span>
                                <span class="stats-card-comparison">this month</span>
                            </div>
                        </div>
                        <div class="stats-card-icon">
                            <iconify-icon icon="hugeicons:money-send-circle" class="text-white"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Performance Metrics Row -->
    @if(isset($performanceMetrics))
    <div class="row g-4">
        <div class="col-12">
            <div class="card border-0 performance-metrics-panel mb-4">
                <div class="card-header bg-transparent border-0 pb-0 performance-metrics-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center gap-3">
                            <div class="performance-metrics-icon">
                                <iconify-icon icon="solar:chart-2-bold" class="text-white"></iconify-icon>
                            </div>
                            <h4 class="fw-bold mb-0 performance-metrics-title">Performance Metrics</h4>
                        </div>
                        <span class="badge performance-metrics-badge">This Month</span>
                    </div>
                </div>
                <div class="card-body performance-metrics-body">
                    <div class="d-flex no-wrap gap-4 performance-metrics m-0">
                        <!-- Collection Efficiency - Enhanced -->
                        <div class="metric-card metric-card-success w-100">
                            <div class="metric-card-icon metric-card-icon-success">
                                <iconify-icon icon="hugeicons:analytics-02" class="text-white"></iconify-icon>
                            </div>
                            <h3 class="fw-bold mb-2 metric-card-value">{{ $performanceMetrics['collection_efficiency'] }}%</h3>
                            <p class="mb-3 flex-grow-1 metric-card-label">Collection Efficiency</p>
                            <div class="mt-auto">
                                @if($performanceMetrics['collection_efficiency'] >= 80)
                                    <span class="badge metric-card-badge metric-card-badge-excellent">Excellent</span>
                                @elseif($performanceMetrics['collection_efficiency'] >= 60)
                                    <span class="badge metric-card-badge metric-card-badge-good">Good</span>
                                @else
                                    <span class="badge metric-card-badge metric-card-badge-attention">Needs Attention</span>
                                @endif
                            </div>
                        </div>

                        <!-- Overdue Amount - Enhanced -->
                        <div class="metric-card metric-card-danger w-100">
                            <div class="metric-card-icon metric-card-icon-danger">
                                <iconify-icon icon="hugeicons:clock-02" class="text-white"></iconify-icon>
                            </div>
                            <h3 class="fw-bold mb-2 metric-card-value-danger">₹{{ App\Http\Controllers\Controller::IND_money_format($performanceMetrics['overdue_amount'], 0) }}</h3>
                            <p class="mb-0 flex-grow-1 metric-card-label">Overdue Amount</p>
                        </div>

                        <!-- Average Invoice Value - Enhanced -->
                        <div class="metric-card metric-card-info w-100">
                            <div class="metric-card-icon metric-card-icon-info">
                                <iconify-icon icon="hugeicons:invoice-03" class="text-white"></iconify-icon>
                            </div>
                            <h3 class="fw-bold mb-2 metric-card-value-info">₹{{ App\Http\Controllers\Controller::IND_money_format($performanceMetrics['avg_invoice_value'], 0) }}</h3>
                            <p class="mb-0 flex-grow-1 metric-card-label">Avg Invoice Value</p>
                        </div>

                        <!-- Client Growth - Enhanced -->
                        <div class="metric-card metric-card-warning w-100">
                            <div class="metric-card-icon {{ $performanceMetrics['client_growth'] >= 0 ? 'metric-card-icon-warning-positive' : 'metric-card-icon-warning-negative' }}">
                                <iconify-icon icon="{{ $performanceMetrics['client_growth'] >= 0 ? 'hugeicons:trending-up' : 'hugeicons:trending-down' }}" class="text-white"></iconify-icon>
                            </div>
                            <h3 class="fw-bold mb-2 {{ $performanceMetrics['client_growth'] >= 0 ? 'metric-card-value-warning-positive' : 'metric-card-value-warning-negative' }}">{{ $performanceMetrics['client_growth'] >= 0 ? '+' : '' }}{{ $performanceMetrics['client_growth'] }}%</h3>
                            <p class="mb-3 flex-grow-1 metric-card-label">Client Growth</p>
                            <div class="mt-auto">
                                @if($performanceMetrics['client_growth'] > 10)
                                    <span class="badge metric-card-badge metric-card-badge-strong">Strong Growth</span>
                                @elseif($performanceMetrics['client_growth'] > 0)
                                    <span class="badge metric-card-badge metric-card-badge-positive">Positive</span>
                                @elseif($performanceMetrics['client_growth'] == 0)
                                    <span class="badge metric-card-badge metric-card-badge-stable">Stable</span>
                                @else
                                    <span class="badge metric-card-badge metric-card-badge-declining">Declining</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Charts and Analytics Row -->
    <div class="row g-4 mb-4 charts-row">
        <!-- Enhanced Revenue Analytics Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card border-0 h-100 charts-panel">
                <div class="card-header bg-transparent border-0 charts-header">
                    <div class="d-flex align-items-center justify-content-between flex-wrap gap-4">
                        <div class="d-flex align-items-center gap-3">
                            <div class="charts-icon">
                                <iconify-icon icon="solar:chart-2-bold" class="text-white"></iconify-icon>
                            </div>
                            <div>
                                <h4 class="fw-bold mb-1 charts-title">Revenue Analytics</h4>
                                <p class="mb-0 charts-subtitle">Monthly revenue trends and invoice metrics</p>
                            </div>
                        </div>
                        <select id="yearSelect" class="form-select w-auto charts-select">
                            @for ($y = date('Y'); $y >= date('Y') - 5; $y--)
                                <option value="{{ $y }}" {{ $y == date('Y') ? 'selected' : '' }}>{{ $y }}</option>
                            @endfor
                        </select>
                    </div>
                </div>
                <div class="card-body charts-body">
                    <div id="paymentStatusChart1" class="chart-container">
                        <!-- Loading state -->
                        <div id="barChartLoading" class="chart-loading">
                            <div class="chart-loading-content">
                                <div class="spinner-border text-primary mb-3" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="text-muted">Loading revenue data...</p>
                            </div>
                        </div>

                        <!-- CSS-based chart fallback -->
                        <div id="cssChartFallback" class="css-chart-container" style="display: none;">
                            <div class="css-chart-header mb-4">
                                <h6 class="fw-bold text-primary mb-2">Monthly Revenue Trend</h6>
                                <p class="text-muted small">Sample data visualization</p>
                            </div>
                            <div class="css-chart-bars d-flex align-items-end justify-content-between" style="height: 280px; gap: 8px;">
                                <div class="css-bar" style="height: 45%; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px 4px 0 0; flex: 1; position: relative;">
                                    <span class="bar-label">Jan</span>
                                    <span class="bar-value">₹1.8L</span>
                                </div>
                                <div class="css-bar" style="height: 55%; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px 4px 0 0; flex: 1; position: relative;">
                                    <span class="bar-label">Feb</span>
                                    <span class="bar-value">₹2.2L</span>
                                </div>
                                <div class="css-bar" style="height: 40%; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px 4px 0 0; flex: 1; position: relative;">
                                    <span class="bar-label">Mar</span>
                                    <span class="bar-value">₹1.6L</span>
                                </div>
                                <div class="css-bar" style="height: 70%; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px 4px 0 0; flex: 1; position: relative;">
                                    <span class="bar-label">Apr</span>
                                    <span class="bar-value">₹2.8L</span>
                                </div>
                                <div class="css-bar" style="height: 80%; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px 4px 0 0; flex: 1; position: relative;">
                                    <span class="bar-label">May</span>
                                    <span class="bar-value">₹3.2L</span>
                                </div>
                                <div class="css-bar" style="height: 95%; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px 4px 0 0; flex: 1; position: relative;">
                                    <span class="bar-label">Jun</span>
                                    <span class="bar-value">₹3.8L</span>
                                </div>
                                <div class="css-bar" style="height: 75%; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px 4px 0 0; flex: 1; position: relative;">
                                    <span class="bar-label">Jul</span>
                                    <span class="bar-value">₹2.9L</span>
                                </div>
                                <div class="css-bar" style="height: 85%; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px 4px 0 0; flex: 1; position: relative;">
                                    <span class="bar-label">Aug</span>
                                    <span class="bar-value">₹3.4L</span>
                                </div>
                                <div class="css-bar" style="height: 90%; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px 4px 0 0; flex: 1; position: relative;">
                                    <span class="bar-label">Sep</span>
                                    <span class="bar-value">₹3.6L</span>
                                </div>
                                <div class="css-bar" style="height: 100%; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px 4px 0 0; flex: 1; position: relative;">
                                    <span class="bar-label">Oct</span>
                                    <span class="bar-value">₹4.2L</span>
                                </div>
                                <div class="css-bar" style="height: 88%; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px 4px 0 0; flex: 1; position: relative;">
                                    <span class="bar-label">Nov</span>
                                    <span class="bar-value">₹3.5L</span>
                                </div>
                                <div class="css-bar" style="height: 95%; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px 4px 0 0; flex: 1; position: relative;">
                                    <span class="bar-label">Dec</span>
                                    <span class="bar-value">₹3.9L</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Payment Analysis Chart -->
        <div class="col-xl-4 col-lg-5">
            <div class="card border-0 h-100" style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); box-shadow: 0 15px 40px rgba(0,0,0,0.1); border-radius: 25px; overflow: hidden;">
                <div class="card-header bg-transparent border-0" style="padding: 2rem 2rem 0 2rem;">
                    <div class="d-flex align-items-center justify-content-between flex-wrap gap-3">
                        <div class="d-flex align-items-center gap-3">
                            <div style="background: linear-gradient(135deg, #22c55e, #16a34a); padding: 15px; border-radius: 15px; box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);">
                                <iconify-icon icon="solar:pie-chart-2-bold" class="text-white" style="font-size: 1.5rem;"></iconify-icon>
                            </div>
                            <div>
                                <h4 class="fw-bold mb-1" style="color: #2d3748; font-size: 1.4rem;">Payment Analysis</h4>
                                <p class="mb-0" style="color: #6b7280; font-size: 0.9rem; font-weight: 500;">Payment mode distribution</p>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="input-group" style="max-width: 100%;">
                            <input type="text" class="form-control" id="daterange" placeholder="Select Date Range" style="border-radius: 12px 0 0 12px; padding: 10px 15px; font-weight: 500; border: 2px solid #e5e7eb; background: white;" />
                            <div class="input-group-append">
                                <span class="input-group-text" style="border-radius: 0 12px 12px 0; border: 2px solid #e5e7eb; border-left: none; background: white; padding: 10px 15px;">
                                    <iconify-icon icon="solar:calendar-bold" style="color: #667eea; font-size: 1.2rem;"></iconify-icon>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body" style="padding: 1.5rem 2rem 2rem 2rem;" id="payments_chart_section">
                    <div class="d-flex align-items-center justify-content-center" style="background: rgba(255,255,255,0.5); border-radius: 15px; padding: 20px; min-height: 300px;">
                        <div id="userOverviewDonutChart" class="apexcharts-tooltip-z-none">
                            <!-- Loading state -->
                            <div id="donutChartLoading" class="d-flex align-items-center justify-content-center" style="height: 250px;">
                                <div class="text-center">
                                    <div class="spinner-border text-success mb-3" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="text-muted">Loading payment data...</p>
                                </div>
                            </div>

                            <!-- CSS-based donut chart fallback -->
                            <div id="cssDonutFallback" class="css-donut-container" style="display: none;">
                                <div class="css-donut">
                                    <div class="css-donut-center">
                                        <p class="css-donut-total">₹1,40,66,501</p>
                                        <p class="css-donut-label">Total</p>
                                    </div>
                                </div>
                                <div class="css-legend">
                                    <div class="css-legend-item">
                                        <div class="css-legend-color" style="background: #3b82f6;"></div>
                                        <span style="color: #374151; font-weight: 500;">Cash ₹45,36,200</span>
                                    </div>
                                    <div class="css-legend-item">
                                        <div class="css-legend-color" style="background: #10b981;"></div>
                                        <span style="color: #374151; font-weight: 500;">Cheque ₹30,45,160</span>
                                    </div>
                                    <div class="css-legend-item">
                                        <div class="css-legend-color" style="background: #f59e0b;"></div>
                                        <span style="color: #374151; font-weight: 500;">Discount ₹12</span>
                                    </div>
                                    <div class="css-legend-item">
                                        <div class="css-legend-color" style="background: #ef4444;"></div>
                                        <span style="color: #374151; font-weight: 500;">Net Banking ₹64,32,547</span>
                                    </div>
                                    <div class="css-legend-item">
                                        <div class="css-legend-color" style="background: #8b5cf6;"></div>
                                        <span style="color: #374151; font-weight: 500;">Online Transfer ₹400</span>
                                    </div>
                                    <div class="css-legend-item">
                                        <div class="css-legend-color" style="background: #f97316;"></div>
                                        <span style="color: #374151; font-weight: 500;">TDS ₹21,454</span>
                                    </div>
                                    <div class="css-legend-item">
                                        <div class="css-legend-color" style="background: #06b6d4;"></div>
                                        <span style="color: #374151; font-weight: 500;">UPI ₹30,728</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <ul id="paymentSummary" class="list-unstyled mb-0">
                            <!-- Payment summary will be dynamically inserted here -->
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Service Breakdown and Recent Activities Row -->
    <div class="row g-4 mb-4">
        <!-- Enhanced Service Type Breakdown -->
        <div class="col-xl-8 col-lg-7">
            <div class="card border-0 h-100" style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); box-shadow: 0 15px 40px rgba(0,0,0,0.1); border-radius: 25px; overflow: hidden; min-height: 500px;">
                <div class="card-header bg-transparent border-0" style="padding: 2rem 2rem 0 2rem;">
                    <div class="d-flex align-items-center gap-3">
                        <div style="background: linear-gradient(135deg, #06b6d4, #0891b2); padding: 15px; border-radius: 15px; box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);">
                            <iconify-icon icon="solar:chart-square-bold" class="text-white" style="font-size: 1.5rem;"></iconify-icon>
                        </div>
                        <div>
                            <h4 class="fw-bold mb-1" style="color: #2d3748; font-size: 1.2rem;">Service Distribution</h4>
                            <p class="mb-0" style="color: #6b7280; font-size: 0.85rem; font-weight: 500;">Active services by type</p>
                        </div>
                    </div>
                </div>
                <div class="card-body" style="padding: 1.5rem 2rem 2rem 2rem;">
                    <div class="row g-4">
                        <!-- Enhanced Bedded Service -->
                        <div class="col-md-6">
                            <div class="d-flex align-items-center justify-content-between position-relative" style="background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); padding: 1.5rem; border-radius: 18px; box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15); border: 1px solid rgba(59, 130, 246, 0.1);">
                                <div class="d-flex align-items-center gap-3">
                                    <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #3b82f6, #2563eb); border-radius: 15px; display: flex; justify-content: center; align-items: center; box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);">
                                        <iconify-icon icon="solar:hospital-bold" class="text-white" style="font-size: 1.3rem;"></iconify-icon>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold mb-1" style="color: #1e40af; font-size: 1.1rem;">Bedded Service</h5>
                                        <p class="mb-0" style="color: #6b7280; font-size: 0.85rem; font-weight: 500;">{{ App\Http\Controllers\Controller::IND_money_format($data['total_beds'], 0) }} Beds</p>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <h4 class="fw-bold mb-1" style="color: #1e40af; font-size: 1.6rem;">{{ App\Http\Controllers\Controller::IND_money_format($data['beded_clients'], 0) }}</h4>
                                    <p class="mb-0" style="color: #6b7280; font-size: 0.85rem; font-weight: 600;">Clients</p>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Non-Bedded Service -->
                        <div class="col-md-6">
                            <div class="d-flex align-items-center justify-content-between position-relative" style="background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%); padding: 1.5rem; border-radius: 18px; box-shadow: 0 8px 25px rgba(34, 197, 94, 0.15); border: 1px solid rgba(34, 197, 94, 0.1);">
                                <div class="d-flex align-items-center gap-3">
                                    <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #22c55e, #16a34a); border-radius: 15px; display: flex; justify-content: center; align-items: center; box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);">
                                        <iconify-icon icon="solar:users-group-rounded-bold" class="text-white" style="font-size: 1.3rem;"></iconify-icon>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold mb-1" style="color: #15803d; font-size: 1.1rem;">Non-Bedded Service</h5>
                                        <p class="mb-0" style="color: #6b7280; font-size: 0.85rem; font-weight: 500;">{{ $data['non_beded_data']->count() }} Categories</p>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <h4 class="fw-bold mb-1" style="color: #15803d; font-size: 1.6rem;">{{ App\Http\Controllers\Controller::IND_money_format($data['non_beded_clients'], 0) }}</h4>
                                    <p class="mb-0" style="color: #6b7280; font-size: 0.85rem; font-weight: 600;">Clients</p>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Weight-Based Service -->
                        <div class="col-md-6">
                            <div class="d-flex align-items-center justify-content-between position-relative" style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); padding: 1.5rem; border-radius: 18px; box-shadow: 0 8px 25px rgba(245, 158, 11, 0.15); border: 1px solid rgba(245, 158, 11, 0.1);">
                                <div class="d-flex align-items-center gap-3">
                                    <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 15px; display: flex; justify-content: center; align-items: center; box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);">
                                        <iconify-icon icon="solar:scale-bold" class="text-white" style="font-size: 1.3rem;"></iconify-icon>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold mb-1" style="color: #b45309; font-size: 1.1rem;">Weight-Based Service</h5>
                                        <p class="mb-0" style="color: #6b7280; font-size: 0.85rem; font-weight: 500;">Per Kg Pricing</p>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <h4 class="fw-bold mb-1" style="color: #b45309; font-size: 1.6rem;">{{ App\Http\Controllers\Controller::IND_money_format($data['weight_based_clients'], 0) }}</h4>
                                    <p class="mb-0" style="color: #6b7280; font-size: 0.85rem; font-weight: 600;">Clients</p>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Bedded Fixed Price -->
                        <div class="col-md-6">
                            <div class="d-flex align-items-center justify-content-between position-relative" style="background: linear-gradient(135deg, #f0f9ff 0%, #bae6fd 100%); padding: 1.5rem; border-radius: 18px; box-shadow: 0 8px 25px rgba(6, 182, 212, 0.15); border: 1px solid rgba(6, 182, 212, 0.1);">
                                <div class="d-flex align-items-center gap-3">
                                    <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #06b6d4, #0891b2); border-radius: 15px; display: flex; justify-content: center; align-items: center; box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);">
                                        <iconify-icon icon="solar:bed-bold" class="text-white" style="font-size: 1.3rem;"></iconify-icon>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold mb-1" style="color: #0e7490; font-size: 1.1rem;">Bedded Fixed Price</h5>
                                        <p class="mb-0" style="color: #6b7280; font-size: 0.85rem; font-weight: 500;">{{ App\Http\Controllers\Controller::IND_money_format($data['total_beds_fixed'], 0) }} Beds</p>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <h4 class="fw-bold mb-1" style="color: #0e7490; font-size: 1.6rem;">{{ App\Http\Controllers\Controller::IND_money_format($data['bedded_fixed_clients'], 0) }}</h4>
                                    <p class="mb-0" style="color: #6b7280; font-size: 0.85rem; font-weight: 600;">Clients</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Recent Activities -->
        <div class="col-xl-4 col-lg-5">
            <div class="card border-0 h-100" style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); box-shadow: 0 15px 40px rgba(0,0,0,0.1); border-radius: 25px; overflow: hidden; min-height: 500px;">
                <div class="card-header bg-transparent border-0" style="padding: 2rem 2rem 0 2rem;">
                    <div class="d-flex align-items-center gap-3">
                        <div style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); padding: 15px; border-radius: 15px; box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);">
                            <iconify-icon icon="mdi:history" class="text-white" style="font-size: 1.5rem;"></iconify-icon>
                        </div>
                        <div class="flex-grow-1">
                            <h4 class="fw-bold mb-1" style="color: #2d3748; font-size: 1.2rem;">Recent Activities</h4>
                            <p class="mb-0" style="color: #6b7280; font-size: 0.85rem; font-weight: 500;">Latest system activities</p>
                        </div>
                    </div>
                </div>
                <div class="card-body pt-3" style="padding: 1.5rem 2rem 2rem 2rem;">
                    @if(isset($recentActivities) && $recentActivities->count() > 0)
                        <div class="activity-timeline" style="max-height: 350px; overflow-y: auto; padding-right: 10px;">
                            @foreach($recentActivities->take(8) as $activity)
                            <div class="d-flex align-items-start gap-3 mb-3 p-3" style="background: rgba(255,255,255,0.7); border-radius: 12px; border: 1px solid rgba(0,0,0,0.05);">
                                <div style="width: 40px; height: 40px; background: linear-gradient(135deg, {{ $activity['color'] === 'primary' ? '#3b82f6, #2563eb' : ($activity['color'] === 'success' ? '#22c55e, #16a34a' : ($activity['color'] === 'warning' ? '#f59e0b, #d97706' : '#ef4444, #dc2626')) }}); border-radius: 12px; display: flex; justify-content: center; align-items: center; flex-shrink-0; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                                    <iconify-icon icon="{{ $activity['icon'] }}" class="text-white" style="font-size: 1.1rem;"></iconify-icon>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="fw-bold mb-1" style="color: #2d3748; font-size: 0.9rem;">{{ $activity['title'] }}</h6>
                                    <p class="mb-1" style="color: #6b7280; font-size: 0.8rem; line-height: 1.4;">{{ $activity['description'] }}</p>
                                    @if($activity['amount'])
                                    <span class="badge" style="background: linear-gradient(135deg, {{ $activity['color'] === 'primary' ? '#3b82f6, #2563eb' : ($activity['color'] === 'success' ? '#22c55e, #16a34a' : ($activity['color'] === 'warning' ? '#f59e0b, #d97706' : '#ef4444, #dc2626')) }}); color: white; padding: 4px 8px; border-radius: 8px; font-size: 0.7rem; font-weight: 600;">₹{{ App\Http\Controllers\Controller::IND_money_format($activity['amount'], 0) }}</span>
                                    @endif
                                    <p class="mb-0 mt-1" style="color: #9ca3af; font-size: 0.7rem; font-weight: 500;">{{ $activity['date']->diffForHumans() }}</p>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5 d-flex flex-column align-items-center justify-content-center" style="height: 350px;">
                            <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #f3f4f6, #e5e7eb); border-radius: 20px; display: flex; justify-content: center; align-items: center; margin-bottom: 1rem;">
                                <iconify-icon icon="hugeicons:file-not-found" style="font-size: 2rem; color: #9ca3af;"></iconify-icon>
                            </div>
                            <p style="color: #6b7280; font-size: 0.9rem; font-weight: 500; margin: 0;">No recent activities found</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Business Intelligence Analytics Row -->
    <div class="row g-4 mb-4">
        <!-- Monthly Invoice Analytics -->
        <div class="col-xl-6 col-lg-6">
            <div class="card border-0 h-100" style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); box-shadow: 0 15px 40px rgba(0,0,0,0.1); border-radius: 25px; overflow: hidden; min-height: 450px;">
                <div class="card-header bg-transparent border-0" style="padding: 2rem 2rem 0 2rem;">
                    <div class="d-flex align-items-center justify-content-between flex-wrap gap-3">
                        <div class="d-flex align-items-center gap-3">
                            <div style="background: linear-gradient(135deg, #f59e0b, #d97706); padding: 15px; border-radius: 15px; box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);">
                                <iconify-icon icon="solar:document-text-bold" class="text-white" style="font-size: 1.5rem;"></iconify-icon>
                            </div>
                            <div>
                                <h4 class="fw-bold mb-1" style="color: #2d3748; font-size: 1.2rem;">Monthly Invoice Analytics</h4>
                                <p class="mb-0" style="color: #6b7280; font-size: 0.85rem; font-weight: 500;">Invoice count and amount trends</p>
                            </div>
                        </div>
                        <select id="invoiceAnalyticsYear" class="form-select w-auto" style="border-radius: 12px; padding: 8px 12px; font-weight: 600; border: 2px solid #e5e7eb; background: white; color: #374151; box-shadow: 0 4px 15px rgba(0,0,0,0.1); font-size: 0.85rem;">
                            @for ($y = date('Y'); $y >= date('Y') - 3; $y--)
                                <option value="{{ $y }}" {{ $y == date('Y') ? 'selected' : '' }}>{{ $y }}</option>
                            @endfor
                        </select>
                    </div>
                </div>
                <div class="card-body" style="padding: 1.5rem 2rem 2rem 2rem;">
                    <div id="monthlyInvoiceChart" style="min-height: 320px; border-radius: 15px; background: rgba(255,255,255,0.5); padding: 20px;">
                        <div class="d-flex align-items-center justify-content-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-warning mb-3" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="text-muted">Loading invoice analytics...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Trends Analytics -->
        <div class="col-xl-6 col-lg-6">
            <div class="card border-0 h-100" style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); box-shadow: 0 15px 40px rgba(0,0,0,0.1); border-radius: 25px; overflow: hidden; min-height: 450px;">
                <div class="card-header bg-transparent border-0" style="padding: 2rem 2rem 0 2rem;">
                    <div class="d-flex align-items-center justify-content-between flex-wrap gap-3">
                        <div class="d-flex align-items-center gap-3">
                            <div style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); padding: 15px; border-radius: 15px; box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);">
                                <iconify-icon icon="solar:card-2-bold" class="text-white" style="font-size: 1.5rem;"></iconify-icon>
                            </div>
                            <div>
                                <h4 class="fw-bold mb-1" style="color: #2d3748; font-size: 1.2rem;">Payment Trends</h4>
                                <p class="mb-0" style="color: #6b7280; font-size: 0.85rem; font-weight: 500;">Monthly payment collection trends</p>
                            </div>
                        </div>
                        <select id="paymentTrendsYear" class="form-select w-auto" style="border-radius: 12px; padding: 8px 12px; font-weight: 600; border: 2px solid #e5e7eb; background: white; color: #374151; box-shadow: 0 4px 15px rgba(0,0,0,0.1); font-size: 0.85rem;">
                            @for ($y = date('Y'); $y >= date('Y') - 3; $y--)
                                <option value="{{ $y }}" {{ $y == date('Y') ? 'selected' : '' }}>{{ $y }}</option>
                            @endfor
                        </select>
                    </div>
                </div>
                <div class="card-body" style="padding: 1.5rem 2rem 2rem 2rem;">
                    <div id="paymentTrendsChart" style="min-height: 320px; border-radius: 15px; background: rgba(255,255,255,0.5); padding: 20px;">
                        <div class="d-flex align-items-center justify-content-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-purple mb-3" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="text-muted">Loading payment trends...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Pending Amount Analytics Row -->
    <div class="row g-4 mb-4">
        <!-- Pending Amount Breakdown -->
        <div class="col-xl-8 col-lg-7">
            <div class="card border-0 h-100" style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); box-shadow: 0 15px 40px rgba(0,0,0,0.1); border-radius: 25px; overflow: hidden; min-height: 450px;">
                <div class="card-header bg-transparent border-0" style="padding: 2rem 2rem 0 2rem;">
                    <div class="d-flex align-items-center justify-content-between flex-wrap gap-3">
                        <div class="d-flex align-items-center gap-3">
                            <div style="background: linear-gradient(135deg, #ef4444, #dc2626); padding: 15px; border-radius: 15px; box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);">
                                <iconify-icon icon="solar:clock-circle-bold" class="text-white" style="font-size: 1.5rem;"></iconify-icon>
                            </div>
                            <div>
                                <h4 class="fw-bold mb-1" style="color: #2d3748; font-size: 1.2rem;">Pending Amount Analytics</h4>
                                <p class="mb-0" style="color: #6b7280; font-size: 0.85rem; font-weight: 500;">Outstanding payments by aging</p>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm" id="refreshPendingData" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white; border: none; border-radius: 10px; padding: 8px 12px; font-weight: 600; box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);">
                                <iconify-icon icon="solar:refresh-bold" style="font-size: 0.9rem;"></iconify-icon>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body" style="padding: 1.5rem 2rem 2rem 2rem;">
                    <div id="pendingAmountChart" style="min-height: 320px; border-radius: 15px; background: rgba(255,255,255,0.5); padding: 20px;">
                        <div class="d-flex align-items-center justify-content-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-danger mb-3" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="text-muted">Loading pending amount analytics...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Client Growth Analytics -->
        <div class="col-xl-4 col-lg-5">
            <div class="card border-0 h-100" style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); box-shadow: 0 15px 40px rgba(0,0,0,0.1); border-radius: 25px; overflow: hidden; min-height: 450px;">
                <div class="card-header bg-transparent border-0" style="padding: 2rem 2rem 0 2rem;">
                    <div class="d-flex align-items-center gap-3">
                        <div style="background: linear-gradient(135deg, #22c55e, #16a34a); padding: 15px; border-radius: 15px; box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);">
                            <iconify-icon icon="mdi:chart-line" class="text-white" style="font-size: 1.5rem;"></iconify-icon>
                        </div>
                        <div>
                            <h4 class="fw-bold mb-1" style="color: #2d3748; font-size: 1.2rem;">Client Growth</h4>
                            <p class="mb-0" style="color: #6b7280; font-size: 0.85rem; font-weight: 500;">Monthly client acquisition</p>
                        </div>
                    </div>
                </div>
                <div class="card-body" style="padding: 1.5rem 2rem 2rem 2rem;">
                    <div id="clientGrowthChart" style="min-height: 320px; border-radius: 15px; background: rgba(255,255,255,0.5); padding: 20px;">
                        <div class="d-flex align-items-center justify-content-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-success mb-3" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="text-muted">Loading client growth data...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Due Clients Section -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0 pb-0">
                    <div class="d-flex align-items-center justify-content-between flex-wrap gap-3">
                        <div class="d-flex align-items-center gap-3">
                            <div class="w-40-px h-40-px bg-danger-100 rounded-circle d-flex justify-content-center align-items-center">
                                <iconify-icon icon="hugeicons:money-receive-circle" class="text-danger-600 text-lg"></iconify-icon>
                            </div>
                            <div>
                                <h5 class="fw-bold text-primary-light mb-0">Top Due Clients <span id="clientCountBadge" class="badge bg-primary-600 text-white ms-2">{{ $top_due_clients->count() }}</span></h5>
                                <p class="text-sm text-secondary-light mb-0">Clients with highest pending payments</p>
                            </div>
                        </div>
                        <select id="clientTypeFilter" class="form-select form-select-sm w-auto bg-base border text-secondary-light">
                            <option value="all" selected>All Clients</option>
                            <option value="1">Private Clients</option>
                            <option value="2">Government Clients</option>
                        </select>
                    </div>
                </div>
                <div class="card-body pt-3">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0" id="pendingPaymentsTable">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col" class="border-0 text-secondary-light fw-semibold">#</th>
                                    <th scope="col" class="border-0 text-secondary-light fw-semibold client-name-col">Client Name</th>
                                    <th scope="col" class="border-0 text-secondary-light fw-semibold">Employee</th>
                                    <th scope="col" class="border-0 text-secondary-light fw-semibold text-end">Invoice Amount</th>
                                    <th scope="col" class="border-0 text-secondary-light fw-semibold text-end">Paid Amount</th>
                                    <th scope="col" class="border-0 text-secondary-light fw-semibold text-end">Pending Amount</th>
                                </tr>
                            </thead>
                            <tbody id="pendingPaymentsTableBody">
                            @foreach ($top_due_clients as $index => $client)
                            <tr data-client-type="{{ $client->client_type }}" class="border-bottom">
                                <td class="py-3">
                                    <div class="w-24-px h-24-px bg-primary-100 rounded-circle d-flex justify-content-center align-items-center">
                                        <span class="text-xs fw-semibold text-primary-600">{{ $index + 1 }}</span>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="w-32-px h-32-px bg-{{ $client->client_type == 1 ? 'success' : 'info' }}-100 rounded-circle d-flex justify-content-center align-items-center">
                                            <iconify-icon icon="hugeicons:user-circle" class="text-{{ $client->client_type == 1 ? 'success' : 'info' }}-600"></iconify-icon>
                                        </div>
                                        <div>
                                            @if(auth()->user()->can('client-view'))
                                                <a href="{{ route('clients.show', $client->id) }}" class="fw-semibold text-primary-light hover-text-primary text-decoration-none client-name" title="{{ $client->client_name }}">
                                                    {{ $client->client_name }}
                                                </a>
                                            @else
                                                <span class="fw-semibold text-primary-light client-name">{{ $client->client_name }}</span>
                                            @endif
                                            <p class="text-xs text-secondary-light mb-0">{{ $client->client_type == 1 ? 'Private' : 'Government' }}</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    @if($client->employee_id)
                                        <div class="d-flex align-items-center gap-2">
                                            <div class="w-24-px h-24-px bg-warning-100 rounded-circle d-flex justify-content-center align-items-center">
                                                <iconify-icon icon="hugeicons:user-account" class="text-warning-600 text-xs"></iconify-icon>
                                            </div>
                                            @if(auth()->user()->can('employee-view'))
                                                <a href="{{ route('employees.show', ['id' => $client->employee_id]) }}" class="text-primary-light hover-text-primary text-decoration-none fw-medium">
                                                    {{ $client->employee_name }}
                                                </a>
                                            @else
                                                <span class="text-primary-light fw-medium">{{ $client->employee_name }}</span>
                                            @endif
                                        </div>
                                    @else
                                        <span class="text-secondary-light">-</span>
                                    @endif
                                </td>
                                <td class="py-3 text-end">
                                    <span class="fw-semibold text-primary-light">₹{{ App\Http\Controllers\Controller::IND_money_format($client->total_invoice_amount, 0) }}</span>
                                </td>
                                <td class="py-3 text-end">
                                    <span class="fw-semibold text-success-600">₹{{ App\Http\Controllers\Controller::IND_money_format($client->total_paid_amount, 0) }}</span>
                                </td>
                                <td class="py-3 text-end">
                                    <span class="fw-bold text-danger-600">₹{{ App\Http\Controllers\Controller::IND_money_format($client->total_pending_amount, 0) }}</span>
                                </td>
                            </tr>
                            @endforeach
                            @if($top_due_clients->isEmpty())
                            <tr>
                                <td colspan="6" class="text-center py-5">
                                    <div class="d-flex flex-column align-items-center">
                                        <iconify-icon icon="hugeicons:file-not-found" class="text-secondary-light text-4xl mb-2"></iconify-icon>
                                        <p class="text-secondary-light mb-0">No pending payments found</p>
                                    </div>
                                </td>
                            </tr>
                            @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('style')
<style>
/* CSS Chart Fallback Styles */
.css-chart-container {
    font-family: 'Inter', sans-serif;
}

.css-chart-bars {
    padding: 20px 10px 30px 10px;
    border-bottom: 2px solid #e5e7eb;
    position: relative;
}

.css-bar {
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    min-height: 20px;
}

.css-bar:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.css-bar .bar-label {
    position: absolute;
    bottom: -25px;
    font-size: 11px;
    font-weight: 600;
    color: #6b7280;
    text-align: center;
    width: 100%;
}

.css-bar .bar-value {
    position: absolute;
    top: -25px;
    font-size: 10px;
    font-weight: 600;
    color: #667eea;
    text-align: center;
    width: 100%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.css-bar:hover .bar-value {
    opacity: 1;
}

/* CSS Donut Chart Styles */
.css-donut-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 400px;
    padding: 20px;
}

.css-donut {
    width: 180px;
    height: 180px;
    border-radius: 50%;
    background: conic-gradient(
        #3b82f6 0deg 116deg,
        #10b981 116deg 193deg,
        #f59e0b 193deg 193.01deg,
        #ef4444 193.01deg 309deg,
        #8b5cf6 309deg 309.1deg,
        #f97316 309.1deg 318deg,
        #06b6d4 318deg 360deg
    );
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.css-donut::before {
    content: '';
    width: 110px;
    height: 110px;
    border-radius: 50%;
    background: white;
    position: absolute;
}

.css-donut-center {
    position: relative;
    z-index: 1;
    text-align: center;
}

.css-donut-total {
    font-size: 15px;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.css-donut-label {
    font-size: 11px;
    color: #6b7280;
    margin: 0;
}

.css-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 20px;
    justify-content: center;
    max-width: 500px;
}

.css-legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.css-legend-color {
    width: 12px;
    height: 12px;
    border-radius: 3px;
}

</style>
@endsection

@section('script')
  <!-- Apex Chart js -->
  <script src="/assets/js/lib/apexcharts.min.js"></script>
  <script>
  // Debug Iconify loading
  console.log('Checking Iconify availability...');
  console.log('iconify-icon element:', typeof customElements.get('iconify-icon'));

  // Wait for Iconify to load and then check icons
  setTimeout(function() {
      console.log('Checking icons after delay...');
      const icons = document.querySelectorAll('iconify-icon');
      console.log('Found', icons.length, 'iconify-icon elements');
      icons.forEach((icon, index) => {
          console.log('Icon', index, ':', icon.getAttribute('icon'), 'loaded:', icon.iconLoaded);
      });
  }, 2000);
  </script>
  <script>
  // Check if ApexCharts is loaded
  console.log('Checking ApexCharts availability...');
  console.log('ApexCharts type:', typeof ApexCharts);
  if (typeof ApexCharts === 'undefined') {
      console.error('❌ ApexCharts library is not loaded!');
      // Try to load fallback charts after a delay
      setTimeout(function() {
          console.log('Rechecking ApexCharts after 2 seconds...');
          console.log('ApexCharts type (retry):', typeof ApexCharts);
          if (typeof ApexCharts === 'undefined') {
              console.error('ApexCharts still not available, showing error messages');
              $('#paymentStatusChart1').html('<div class="text-center py-5 text-warning"><i class="fas fa-exclamation-triangle mb-2"></i><br>Chart library loading... Please wait or refresh the page.</div>');
              $('#userOverviewDonutChart').parent().html('<div class="text-center py-5 text-warning"><i class="fas fa-exclamation-triangle mb-2"></i><br>Chart library loading... Please wait or refresh the page.</div>');
          } else {
              console.log('✅ ApexCharts loaded on retry');
          }
      }, 2000);
  } else {
      console.log('✅ ApexCharts loaded successfully');
      console.log('ApexCharts version:', ApexCharts.version || 'Unknown');
  }
  </script>
  <script>
  // ================================ Users Overview Donut Chart Start ================================

  $(document).ready(function () {
    var chart;
    var realDataLoaded = false;

    function formatCurrency(amount) {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }

    function loadPaymentData(startDate, endDate) {
        console.log('loadPaymentData called with:', startDate, endDate);
        console.log('Making AJAX request to:', "{{ route('dashboard.getPaymentData') }}");
        console.log('CSRF Token:', '{{ csrf_token() }}');

        // Show loading state
        const loadingElement = document.getElementById('donutChartLoading');
        const fallbackElement = document.getElementById('cssDonutFallback');

        if (loadingElement) {
            loadingElement.style.display = 'flex';
        }
        if (fallbackElement) {
            fallbackElement.style.display = 'none';
        }

        $.ajax({
            url: "{{ route('dashboard.getPaymentData') }}",
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: {
                start_date: startDate,
                end_date: endDate
            },
            success: function(response) {
                console.log('Payment data response:', response);
                console.log('Payment data labels:', response.data ? response.data.labels : 'No labels');
                console.log('Payment data amounts:', response.data ? response.data.amounts : 'No amounts');
                if (response.success && response.data && response.data.labels && response.data.labels.length > 0) {
                    console.log('Real payment data found:', response.data);

                    // Hide loading state
                    const loadingElement = document.getElementById('donutChartLoading');
                    const fallbackElement = document.getElementById('cssDonutFallback');

                    if (loadingElement) {
                        loadingElement.style.display = 'none';
                    }
                    if (fallbackElement) {
                        fallbackElement.style.display = 'none';
                    }

                    // Wait a moment for DOM to update, then create chart
                    setTimeout(() => {
                        updateChart(response.data);
                        updatePaymentSummary(response.data);
                        realDataLoaded = true;
                    }, 100);
                } else {
                    console.log('No payment data available:', response);

                    // Hide loading state
                    const loadingElement = document.getElementById('donutChartLoading');
                    const fallbackElement = document.getElementById('cssDonutFallback');

                    if (loadingElement) {
                        loadingElement.style.display = 'none';
                    }
                    if (fallbackElement) {
                        fallbackElement.style.display = 'block';
                    }

                    // Create sample chart
                    const sampleData = {
                        labels: ['Cash', 'Online Transfer', 'Bank Transfer'],
                        amounts: [25000, 35000, 15000]
                    };

                    setTimeout(() => {
                        updateChart(sampleData);
                        updatePaymentSummary(sampleData);
                    }, 100);
                }
            },
            error: function(xhr, status, error) {
                console.error('Payment Data AJAX Error:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });

                // Hide loading state
                const loadingState = document.getElementById('donutChartLoading');
                if (loadingState) {
                    loadingState.style.display = 'none';
                }

                // Show CSS fallback on error
                const cssFallback = document.getElementById('cssDonutFallback');
                if (cssFallback) {
                    cssFallback.style.display = 'block';
                }

                // Show fallback chart with sample data
                $('#payments_chart_section').html(`
                    <div class="d-flex align-items-center justify-content-center" style="background: rgba(255,255,255,0.5); border-radius: 15px; padding: 20px; min-height: 300px;">
                        <div id="userOverviewDonutChart" class="apexcharts-tooltip-z-none" style="width: 100%;"></div>
                    </div>
                    <div class="mt-4">
                        <ul id="paymentSummary" class="list-unstyled mb-0">
                            <!-- Payment summary will be dynamically inserted here -->
                        </ul>
                    </div>
                `);

                // Create fallback chart with sample data
                const fallbackData = {
                    labels: ['Cash', 'Online Transfer', 'Bank Transfer'],
                    amounts: [25000, 35000, 15000]
                };

                setTimeout(() => {
                    updateChart(fallbackData);
                    updatePaymentSummary(fallbackData);
                }, 100);
            }
        });
    }

    function updateChart(data) {
        console.log('updateChart called with data:', data);

        // Hide loading state first
        const loadingElement = document.getElementById('donutChartLoading');
        const fallbackElement = document.getElementById('cssDonutFallback');

        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
        if (fallbackElement) {
            fallbackElement.style.display = 'none';
        }

        // Check if container exists
        const container = document.querySelector("#userOverviewDonutChart");
        if (!container) {
            console.error('Chart container #userOverviewDonutChart not found');
            return;
        }
        console.log('Chart container found:', container);

        // Clear any existing content in the container
        container.innerHTML = '';

        if (chart) {
            console.log('Destroying existing chart');
            chart.destroy();
        }

        var options = {
            series: data.amounts,
            chart: {
                type: 'donut',
                height: 350,
                width: '100%',
                fontFamily: 'Inter, sans-serif',
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800
                }
            },
            labels: data.labels,
            colors: ['#4f46e5', '#059669', '#ea580c', '#dc2626', '#7c3aed', '#c2410c', '#0891b2'],
            plotOptions: {
                pie: {
                    donut: {
                        size: '65%',
                        labels: {
                            show: true,
                            total: {
                                show: true,
                                label: 'Total',
                                fontSize: '18px',
                                fontWeight: 600,
                                color: '#374151',
                                formatter: function (w) {
                                    const total = w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                                    return formatCurrency(total);
                                }
                            }
                        }
                    },
                    expandOnClick: false
                }
            },
            dataLabels: {
                enabled: false
            },
            legend: {
                show: false
            },
            tooltip: {
                theme: 'light',
                style: {
                    fontSize: '14px',
                    fontFamily: 'Inter, sans-serif'
                },
                y: {
                    formatter: function (val) {
                        return formatCurrency(val);
                    }
                }
            },
            stroke: {
                width: 2,
                colors: ['#ffffff']
            }
        };

        try {
            console.log('Creating ApexCharts with options:', options);
            chart = new ApexCharts(document.querySelector("#userOverviewDonutChart"), options);
            console.log('Chart object created:', chart);
            chart.render();
            console.log('Chart rendered successfully');
        } catch (error) {
            console.error('Error creating/rendering chart:', error);
            document.querySelector("#userOverviewDonutChart").innerHTML = '<div class="text-center py-5 text-danger">Error: ' + error.message + '</div>';
        }
    }

    function updatePaymentSummary(data) {
        var summaryHtml = '';
        var chartColors = ['#4f46e5', '#059669', '#ea580c', '#dc2626', '#7c3aed', '#c2410c', '#0891b2'];

        for (var i = 0; i < data.labels.length; i++) {
            var color = chartColors[i % chartColors.length];
            summaryHtml += `
                <li class="d-flex align-items-center justify-content-between mb-2">
                    <div class="d-flex align-items-center gap-2">
                        <div class="w-12-px h-12-px rounded-circle" style="background-color: ${color};"></div>
                        <span class="text-sm text-secondary-light">${data.labels[i]}</span>
                    </div>
                    <span class="fw-semibold text-primary-light">${formatCurrency(data.amounts[i])}</span>
                </li>
            `;
        }

        $('#paymentSummary').html(summaryHtml);
    }

    // Initialize with current month data
    var startDate = moment().startOf('month').format('YYYY-MM-DD');
    var endDate = moment().endOf('month').format('YYYY-MM-DD');
    loadPaymentData(startDate, endDate);

    // Date range picker
    $('#daterange').daterangepicker({
        startDate: moment().startOf('month'),
        endDate: moment().endOf('month'),
        ranges: {
           'Today': [moment(), moment()],
           'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
           'Last 7 Days': [moment().subtract(6, 'days'), moment()],
           'Last 30 Days': [moment().subtract(29, 'days'), moment()],
           'This Month': [moment().startOf('month'), moment().endOf('month')],
           'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        }
    }, function(start, end, label) {
        loadPaymentData(start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD'));
    });
});

// ================================ Invoice Bar Chart Start ================================
var barChart;

function loadInvoiceData(year) {
    console.log('loadInvoiceData called with year:', year);

    // Show loading state
    $('#paymentStatusChart1').html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');

    $.ajax({
        url: "{{ route('dashboard.invoice_date') }}",
        method: 'POST',
        data: {
            year: year,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            console.log('Invoice data response:', response);
            if (response.success && response.data) {
                updateBarChart(response.data, year);
            } else {
                console.log('Invoice data error or no data:', response);
                $('#paymentStatusChart1').html('<div class="text-center py-5 text-warning">No invoice data available for ' + year + '</div>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Invoice Data AJAX Error:', {
                status: xhr.status,
                statusText: xhr.statusText,
                responseText: xhr.responseText,
                error: error
            });

            // Show fallback chart with sample data
            const fallbackData = {
                months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                amounts: [50000, 65000, 45000, 75000, 85000, 95000, 70000, 80000, 90000, 100000, 85000, 95000]
            };
            updateBarChart(fallbackData, new Date().getFullYear());
        }
    });
}

// Create fallback bar chart immediately
function createFallbackBarChart(data) {
    console.log('Creating fallback bar chart...');

    const container = document.querySelector("#paymentStatusChart1");
    if (!container) {
        console.error('Bar chart container #paymentStatusChart1 not found');
        return;
    }

    // Clear any existing content
    container.innerHTML = '';

    if (barChart) {
        barChart.destroy();
    }

    var options = {
        series: [{
            name: 'Invoice Amount',
            data: data.amounts
        }],
        chart: {
            type: 'bar',
            height: 400,
            fontFamily: 'Inter, sans-serif',
            toolbar: {
                show: false
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            },
            background: 'transparent'
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '60%',
                endingShape: 'rounded',
                borderRadius: 8,
                dataLabels: {
                    position: 'top'
                }
            },
        },
        dataLabels: {
            enabled: true,
            formatter: function (val) {
                return '₹' + (val/1000).toFixed(0) + 'K';
            },
            offsetY: -20,
            style: {
                fontSize: '12px',
                colors: ['#3B82F6'],
                fontWeight: 600
            }
        },
        stroke: {
            show: true,
            width: 2,
            colors: ['transparent']
        },
        xaxis: {
            categories: data.months,
            axisBorder: {
                show: false,
            },
            axisTicks: {
                show: false,
            },
            labels: {
                style: {
                    colors: '#6b7280',
                    fontSize: '12px',
                    fontWeight: 500
                }
            }
        },
        yaxis: {
            labels: {
                formatter: function (val) {
                    return '₹' + (val/1000).toFixed(0) + 'K';
                },
                style: {
                    colors: '#6b7280',
                    fontSize: '12px',
                    fontWeight: 500
                }
            }
        },
        fill: {
            opacity: 1,
            colors: ['#3B82F6'],
            gradient: {
                shade: 'light',
                type: 'vertical',
                shadeIntensity: 0.3,
                gradientToColors: ['#1D4ED8'],
                inverseColors: false,
                opacityFrom: 1,
                opacityTo: 0.8
            }
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return '₹' + val.toLocaleString('en-IN');
                }
            },
            theme: 'light',
            style: {
                fontSize: '12px',
                fontFamily: 'Inter, sans-serif'
            }
        },
        grid: {
            borderColor: '#e5e7eb',
            strokeDashArray: 4,
            xaxis: {
                lines: {
                    show: false
                }
            },
            yaxis: {
                lines: {
                    show: true
                }
            },
            padding: {
                top: 0,
                right: 0,
                bottom: 0,
                left: 0
            }
        },
        legend: {
            show: false
        }
    };

    try {
        // Hide loading state
        const loadingState = document.getElementById('barChartLoading');
        if (loadingState) {
            loadingState.style.display = 'none';
        }

        // Hide CSS fallback
        const cssFallback = document.getElementById('cssChartFallback');
        if (cssFallback) {
            cssFallback.style.display = 'none';
        }

        barChart = new ApexCharts(document.querySelector("#paymentStatusChart1"), options);
        barChart.render();
        console.log('Fallback bar chart created successfully');
    } catch (error) {
        console.error('Error creating fallback bar chart:', error);
        // Hide loading state
        const loadingState = document.getElementById('barChartLoading');
        if (loadingState) {
            loadingState.style.display = 'none';
        }
        // Show CSS fallback if ApexCharts fails
        const cssFallback = document.getElementById('cssChartFallback');
        if (cssFallback) {
            cssFallback.style.display = 'block';
        }
    }
}

// Create fallback payment chart immediately
function createFallbackPaymentChart(data) {
    console.log('Creating fallback payment chart...');

    // Don't create fallback if real data has been loaded
    if (realDataLoaded) {
        console.log('Real data already loaded, skipping fallback chart');
        return;
    }

    const container = document.querySelector("#userOverviewDonutChart");
    if (!container) {
        console.error('Payment chart container #userOverviewDonutChart not found');
        return;
    }

    // Clear any existing content
    container.innerHTML = '';

    var options = {
        series: data.amounts,
        chart: {
            type: 'donut',
            height: 350,
            fontFamily: 'Inter, sans-serif',
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        labels: data.labels,
        colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#F97316', '#06B6D4'],
        plotOptions: {
            pie: {
                donut: {
                    size: '70%',
                    labels: {
                        show: true,
                        name: {
                            show: true,
                            fontSize: '14px',
                            fontWeight: 600,
                            color: '#374151'
                        },
                        value: {
                            show: true,
                            fontSize: '16px',
                            fontWeight: 700,
                            color: '#1f2937',
                            formatter: function (val) {
                                return '₹' + parseInt(val).toLocaleString('en-IN');
                            }
                        },
                        total: {
                            show: true,
                            showAlways: true,
                            label: 'Total',
                            fontSize: '14px',
                            fontWeight: 600,
                            color: '#6b7280',
                            formatter: function (w) {
                                const total = w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                                return '₹' + total.toLocaleString('en-IN');
                            }
                        }
                    }
                }
            }
        },
        dataLabels: {
            enabled: false
        },
        legend: {
            show: false
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return '₹' + val.toLocaleString('en-IN');
                }
            },
            theme: 'light'
        },
        stroke: {
            width: 0
        }
    };

    try {
        // Hide CSS fallback
        const cssFallback = document.getElementById('cssDonutFallback');
        if (cssFallback) {
            cssFallback.style.display = 'none';
        }

        const chart = new ApexCharts(document.querySelector("#userOverviewDonutChart"), options);
        chart.render();
        console.log('Fallback payment chart created successfully');

        // Also update the payment summary
        updatePaymentSummary(data);
    } catch (error) {
        console.error('Error creating fallback payment chart:', error);
        // Show CSS fallback if ApexCharts fails
        const cssFallback = document.getElementById('cssDonutFallback');
        if (cssFallback) {
            cssFallback.style.display = 'block';
        }
    }
}

function updateBarChart(data, year) {
    if (barChart) {
        barChart.destroy();
    }

    var options = {
        series: [{
            name: 'Invoice Amount',
            data: data.amounts
        }],
        chart: {
            type: 'bar',
            height: 350,
            fontFamily: 'Inter, sans-serif',
            toolbar: {
                show: false
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '60%',
                endingShape: 'rounded',
                borderRadius: 4
            },
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            show: true,
            width: 0,
            colors: ['transparent']
        },
        xaxis: {
            categories: data.months,
            title: {
                text: 'Months',
                style: {
                    fontSize: '14px',
                    fontWeight: 600,
                    color: '#6B7280'
                }
            },
            labels: {
                style: {
                    fontSize: '12px',
                    fontWeight: 500,
                    colors: '#6B7280'
                }
            }
        },
        yaxis: {
            title: {
                text: 'Amount (₹)',
                style: {
                    fontSize: '14px',
                    fontWeight: 600,
                    color: '#6B7280'
                }
            },
            labels: {
                style: {
                    fontSize: '12px',
                    fontWeight: 500,
                    colors: '#6B7280'
                },
                formatter: function (val) {
                    return '₹' + val.toLocaleString('en-IN');
                }
            }
        },
        fill: {
            opacity: 1,
            colors: ['#3B82F6'],
            gradient: {
                shade: 'light',
                type: 'vertical',
                shadeIntensity: 0.3,
                gradientToColors: ['#1D4ED8'],
                inverseColors: false,
                opacityFrom: 1,
                opacityTo: 0.8
            }
        },
        tooltip: {
            theme: 'light',
            style: {
                fontSize: '14px',
                fontFamily: 'Inter, sans-serif'
            },
            y: {
                formatter: function (val) {
                    return '₹' + val.toLocaleString('en-IN');
                }
            }
        },
        grid: {
            borderColor: '#E5E7EB',
            strokeDashArray: 3
        }
    };

    barChart = new ApexCharts(document.querySelector("#paymentStatusChart1"), options);
    barChart.render();
}

$(document).ready(function() {
    console.log('Dashboard DOM ready');

    // Check if required libraries are loaded
    console.log('jQuery loaded:', typeof $ !== 'undefined');
    console.log('Moment.js loaded:', typeof moment !== 'undefined');
    console.log('ApexCharts loaded:', typeof ApexCharts !== 'undefined');

    // Ensure ApexCharts is loaded before initializing charts
    if (typeof ApexCharts !== 'undefined') {
        console.log('Initializing charts...');

        // Check if chart containers exist
        const barChartContainer = document.querySelector("#paymentStatusChart1");
        const donutChartContainer = document.querySelector("#userOverviewDonutChart");

        console.log('Bar chart container found:', !!barChartContainer);
        console.log('Donut chart container found:', !!donutChartContainer);

        if (barChartContainer && donutChartContainer) {
            // Load real data immediately instead of showing fallback
            console.log('Loading real data immediately...');

            // Show loading state for charts
            barChartContainer.innerHTML = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
            donutChartContainer.innerHTML = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';

            // Load real invoice data immediately
            loadInvoiceData(new Date().getFullYear());

            // Load real payment data immediately
            if (typeof moment !== 'undefined') {
                const startDate = moment().startOf('month').format('YYYY-MM-DD');
                const endDate = moment().endOf('month').format('YYYY-MM-DD');
                loadPaymentData(startDate, endDate);
            } else {
                const now = new Date();
                const startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
                const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0];
                loadPaymentData(startDate, endDate);
            }


        } else {
            console.error('Chart containers not found!');
            setTimeout(() => {
                console.log('Retrying chart initialization...');
                const retryBarChart = document.querySelector("#paymentStatusChart1");
                const retryDonutChart = document.querySelector("#userOverviewDonutChart");

                if (retryBarChart && retryDonutChart) {
                    console.log('Containers found on retry, loading real data...');
                    // Show loading state for charts
                    retryBarChart.innerHTML = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
                    retryDonutChart.innerHTML = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';

                    // Load real data
                    loadInvoiceData(new Date().getFullYear());
                    if (typeof moment !== 'undefined') {
                        const startDate = moment().startOf('month').format('YYYY-MM-DD');
                        const endDate = moment().endOf('month').format('YYYY-MM-DD');
                        loadPaymentData(startDate, endDate);
                    } else {
                        const now = new Date();
                        const startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
                        const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0];
                        loadPaymentData(startDate, endDate);
                    }
                }
            }, 1000);
        }




    } else {
        console.error('ApexCharts not loaded, retrying in 1 second...');
        setTimeout(function() {
            if (typeof ApexCharts !== 'undefined') {
                console.log('ApexCharts loaded on retry, initializing...');
                loadInvoiceData(new Date().getFullYear());
                const startDate = moment().startOf('month').format('YYYY-MM-DD');
                const endDate = moment().endOf('month').format('YYYY-MM-DD');
                loadPaymentData(startDate, endDate);
            } else {
                console.error('ApexCharts still not loaded after retry');
                $('#paymentStatusChart1').html('<div class="text-center py-5 text-danger">Charts failed to load. Please refresh the page.</div>');
                $('#userOverviewDonutChart').html('<div class="text-center py-5 text-danger">Charts failed to load. Please refresh the page.</div>');
            }
        }, 1000);
    }

    $('#yearSelect').change(function() {
        let selectedYear = $(this).val();
        loadInvoiceData(selectedYear);
    });
});

// Aggressive chart initialization - try multiple times
function forceInitializeCharts() {
    console.log('Force initializing charts...');

    if (typeof ApexCharts !== 'undefined') {
        const barContainer = document.querySelector("#paymentStatusChart1");
        const donutContainer = document.querySelector("#userOverviewDonutChart");

        if (barContainer && donutContainer) {
            console.log('Containers found, loading real data...');

            // Show loading state for charts
            barContainer.innerHTML = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
            donutContainer.innerHTML = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';

            // Load real data
            loadInvoiceData(new Date().getFullYear());
            if (typeof moment !== 'undefined') {
                const startDate = moment().startOf('month').format('YYYY-MM-DD');
                const endDate = moment().endOf('month').format('YYYY-MM-DD');
                loadPaymentData(startDate, endDate);
            } else {
                const now = new Date();
                const startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
                const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0];
                loadPaymentData(startDate, endDate);
            }

            return true;
        }
    }
    return false;
}

// Try to initialize charts immediately
setTimeout(() => {
    if (!forceInitializeCharts()) {
        console.log('First attempt failed, retrying...');
        setTimeout(() => {
            if (!forceInitializeCharts()) {
                console.log('Second attempt failed, retrying...');
                setTimeout(() => {
                    forceInitializeCharts();
                }, 2000);
            }
        }, 1000);
    }
}, 500);

// Client Type Filter for Pending Payments Table - AJAX Implementation
$(document).ready(function() {
    $('#clientTypeFilter').change(function() {
        var selectedType = $(this).val();

        // Show loading state
        $('#pendingPaymentsTableBody').html(`
            <tr>
                <td colspan="6" class="text-center py-5">
                    <div class="d-flex flex-column align-items-center">
                        <div class="spinner-border text-primary mb-2" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="text-secondary-light mb-0">Loading clients...</p>
                    </div>
                </td>
            </tr>
        `);

        // Fetch filtered data from server
        $.ajax({
            url: '{{ route("dashboard.top_due_clients") }}',
            method: 'POST',
            data: {
                client_type: selectedType,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                updateTopDueClientsTable(response.clients, response.can_view_client);
            },
            error: function(xhr, status, error) {
                console.error('Error fetching top due clients:', error);
                $('#pendingPaymentsTableBody').html(`
                    <tr>
                        <td colspan="6" class="text-center py-5">
                            <div class="d-flex flex-column align-items-center">
                                <iconify-icon icon="hugeicons:file-not-found" class="text-danger text-4xl mb-2"></iconify-icon>
                                <p class="text-danger mb-0">Error loading data. Please try again.</p>
                            </div>
                        </td>
                    </tr>
                `);
            }
        });
    });

    // Function to update the table with new data
    function updateTopDueClientsTable(clients, canViewClient) {
        var tableBody = $('#pendingPaymentsTableBody');
        tableBody.empty();

        // Update count badge
        $('#clientCountBadge').text(clients.length);

        if (clients.length === 0) {
            tableBody.html(`
                <tr>
                    <td colspan="6" class="text-center py-5">
                        <div class="d-flex flex-column align-items-center">
                            <iconify-icon icon="hugeicons:file-not-found" class="text-secondary-light text-4xl mb-2"></iconify-icon>
                            <p class="text-secondary-light mb-0">No pending payments found</p>
                        </div>
                    </td>
                </tr>
            `);
            return;
        }

        clients.forEach(function(client, index) {
            var clientTypeText = client.client_type == 1 ? 'Private' : 'Government';
            var clientTypeColor = client.client_type == 1 ? 'success' : 'info';

            var clientNameHtml = '';
            if (canViewClient) {
                clientNameHtml = `<a href="/clients/view/${client.id}" class="fw-semibold text-primary-light hover-text-primary text-decoration-none client-name" title="${client.client_name}">${client.client_name}</a>`;
            } else {
                clientNameHtml = `<span class="fw-semibold text-primary-light client-name">${client.client_name}</span>`;
            }

            var employeeHtml = client.employee_name ?
                `<span class="fw-semibold text-primary-light">${client.employee_name}</span>` :
                `<span class="text-secondary-light">Not Assigned</span>`;

            var row = `
                <tr data-client-type="${client.client_type}" class="border-bottom">
                    <td class="py-3">
                        <div class="w-24-px h-24-px bg-primary-100 rounded-circle d-flex justify-content-center align-items-center">
                            <span class="text-xs fw-semibold text-primary-600">${index + 1}</span>
                        </div>
                    </td>
                    <td class="py-3">
                        <div class="d-flex align-items-center gap-3">
                            <div class="w-32-px h-32-px bg-${clientTypeColor}-100 rounded-circle d-flex justify-content-center align-items-center">
                                <iconify-icon icon="hugeicons:user-circle" class="text-${clientTypeColor}-600"></iconify-icon>
                            </div>
                            <div>
                                ${clientNameHtml}
                                <p class="text-xs text-secondary-light mb-0">${clientTypeText}</p>
                            </div>
                        </div>
                    </td>
                    <td class="py-3">
                        ${employeeHtml}
                    </td>
                    <td class="py-3 text-end">
                        <span class="fw-semibold text-primary-light">₹${formatIndianCurrency(client.total_invoice_amount)}</span>
                    </td>
                    <td class="py-3 text-end">
                        <span class="fw-semibold text-success-600">₹${formatIndianCurrency(client.total_paid_amount)}</span>
                    </td>
                    <td class="py-3 text-end">
                        <span class="fw-bold text-danger-600">₹${formatIndianCurrency(client.total_pending_amount)}</span>
                    </td>
                </tr>
            `;

            tableBody.append(row);
        });
    }

    // Helper function to format Indian currency
    function formatIndianCurrency(amount) {
        if (!amount) return '0';
        return Math.round(amount).toLocaleString('en-IN');
    }
});

// Dashboard refresh function
function refreshDashboard() {
    // Show loading state on refresh button
    const refreshBtn = document.querySelector('button[onclick="refreshDashboard()"]');
    const originalContent = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<iconify-icon icon="solar:refresh-outline" class="text-lg animate-spin"></iconify-icon><span class="d-none d-md-inline ms-2">Refreshing...</span>';
    refreshBtn.disabled = true;

    // Reload current data
    const currentYear = $('#yearSelect').val() || new Date().getFullYear();
    const dateRange = $('#daterange').data('daterangepicker');

    loadInvoiceData(currentYear);

    if (dateRange) {
        loadPaymentData(
            dateRange.startDate.format('YYYY-MM-DD'),
            dateRange.endDate.format('YYYY-MM-DD')
        );
    } else {
        const startDate = moment().startOf('month').format('YYYY-MM-DD');
        const endDate = moment().endOf('month').format('YYYY-MM-DD');
        loadPaymentData(startDate, endDate);
    }

    // Reset button after 2 seconds
    setTimeout(() => {
        refreshBtn.innerHTML = originalContent;
        refreshBtn.disabled = false;
    }, 2000);
}

// Add tooltips to cards
$(document).ready(function() {
    // Initialize tooltips
    $('[title]').tooltip();

    // Add smooth scrolling for better UX
    $('html').css('scroll-behavior', 'smooth');

    // Add keyboard shortcuts
    $(document).keydown(function(e) {
        // Ctrl/Cmd + R for refresh
        if ((e.ctrlKey || e.metaKey) && e.keyCode === 82) {
            e.preventDefault();
            refreshDashboard();
        }
    });

    // Auto-refresh every 5 minutes (optional)
    // setInterval(refreshDashboard, 300000);

    // Check for notifications every 30 seconds
    setInterval(checkForNotifications, 30000);

    // Real-time updates every 2 minutes
    setInterval(updateDashboardData, 120000);


});

// Refresh activities function
function refreshActivities() {
    // This would typically make an AJAX call to get fresh activities
    // For now, we'll just show a success message
    showNotification('Activities refreshed successfully!', 'success');
}

// Refresh dashboard function
function refreshDashboard() {
    console.log('Refreshing dashboard...');

    // Show loading indicators
    $('#paymentStatusChart1').html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
    $('#payments_chart_section').html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');

    // Reload charts
    loadInvoiceData(new Date().getFullYear());

    if (typeof moment !== 'undefined') {
        const startDate = moment().startOf('month').format('YYYY-MM-DD');
        const endDate = moment().endOf('month').format('YYYY-MM-DD');
        loadPaymentData(startDate, endDate);
    } else {
        const now = new Date();
        const startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
        const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0];
        loadPaymentData(startDate, endDate);
    }

    // Update dashboard data
    updateDashboardData();

    // Show success message
    setTimeout(function() {
        console.log('Dashboard refreshed successfully');
        showNotification('Dashboard refreshed successfully!', 'success');
    }, 1000);
}

// Mark all activities as read
function markAllAsRead() {
    $('.activity-timeline .d-flex').each(function() {
        $(this).removeClass('unread');
    });
    showNotification('All activities marked as read', 'info');
}

// Check for new notifications
function checkForNotifications() {
    // This would typically check for new activities, overdue invoices, etc.
    // For now, we'll implement a basic check
    const overdueAmount = parseFloat($('.text-danger-600:contains("₹")').first().text().replace(/[₹,]/g, ''));
    if (overdueAmount > 100000) { // If overdue amount is more than 1 lakh
        // Could show a notification here
    }
}

// Show notification function
function showNotification(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const notification = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `);

    $('body').append(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        notification.alert('close');
    }, 5000);
}

// Add animation classes
$(document).ready(function() {
    $('.card').each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
    });
});

// Real-time dashboard updates
function updateDashboardData() {
    $.ajax({
        url: "{{ route('dashboard.updates') }}",
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateQuickStats(response.data.quick_stats);
                updateRecentActivities(response.data.recent_activities);

                // Show subtle notification
                showNotification('Dashboard updated', 'info');
            }
        },
        error: function(xhr, status, error) {
            console.error('Failed to update dashboard data:', error);
        }
    });
}

// Update quick stats in real-time
function updateQuickStats(stats) {
    // Update today's stats if they exist on the page
    if (stats.today_invoices !== undefined) {
        $('.today-invoices-count').text(stats.today_invoices);
    }
    if (stats.today_payments !== undefined) {
        $('.today-payments-count').text(stats.today_payments);
    }
    if (stats.today_revenue !== undefined) {
        $('.today-revenue-amount').text('₹' + stats.today_revenue.toLocaleString('en-IN'));
    }
}

// Update recent activities in real-time
function updateRecentActivities(activities) {
    if (activities && activities.length > 0) {
        let activitiesHtml = '';
        activities.slice(0, 8).forEach(function(activity) {
            activitiesHtml += `
                <div class="d-flex align-items-start gap-3 mb-3">
                    <div class="w-32-px h-32-px bg-${activity.color}-100 rounded-circle d-flex justify-content-center align-items-center flex-shrink-0">
                        <iconify-icon icon="${activity.icon}" class="text-${activity.color}-600 text-sm"></iconify-icon>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="fw-semibold text-primary-light mb-1 text-sm">${activity.title}</h6>
                        <p class="text-xs text-secondary-light mb-1">${activity.description}</p>
                        ${activity.amount ? `<span class="badge bg-${activity.color}-100 text-${activity.color}-600 text-xs">₹${activity.amount.toLocaleString('en-IN')}</span>` : ''}
                        <p class="text-xs text-secondary-light mb-0 mt-1">${moment(activity.date).fromNow()}</p>
                    </div>
                </div>
            `;
        });

        $('.activity-timeline').html(activitiesHtml);
    }
}

// Load Monthly Invoice Analytics
function loadMonthlyInvoiceAnalytics(year) {
    // Clear any existing chart
    const chartContainer = document.querySelector("#monthlyInvoiceChart");
    if (chartContainer) {
        chartContainer.innerHTML = '<div class="d-flex align-items-center justify-content-center h-100"><div class="text-center"><div class="spinner-border text-warning mb-3" role="status"><span class="visually-hidden">Loading...</span></div><p class="text-muted">Loading invoice analytics...</p></div></div>';
    }

    $.ajax({
        url: '/dashboard/monthly-invoice-analytics',
        method: 'GET',
        data: { year: year },
        success: function(response) {
            if (response.success && typeof ApexCharts !== 'undefined') {
                const options = {
                    series: [{
                        name: 'Invoice Count',
                        type: 'column',
                        data: response.data.counts
                    }, {
                        name: 'Invoice Amount (₹)',
                        type: 'line',
                        data: response.data.amounts
                    }],
                    chart: {
                        height: 320,
                        type: 'line',
                        toolbar: { show: false },
                        background: 'transparent'
                    },
                    colors: ['#f59e0b', '#d97706'],
                    stroke: {
                        width: [0, 4],
                        curve: 'smooth'
                    },
                    plotOptions: {
                        bar: {
                            columnWidth: '50%',
                            borderRadius: 8
                        }
                    },
                    fill: {
                        type: ['solid', 'gradient'],
                        gradient: {
                            shade: 'light',
                            type: 'vertical',
                            shadeIntensity: 0.3,
                            gradientToColors: ['#fbbf24'],
                            inverseColors: false,
                            opacityFrom: 0.8,
                            opacityTo: 0.6
                        }
                    },
                    labels: response.data.months,
                    markers: {
                        size: 6,
                        colors: ['#d97706'],
                        strokeColors: '#fff',
                        strokeWidth: 2,
                        hover: { size: 8 }
                    },
                    xaxis: {
                        type: 'category',
                        labels: { style: { colors: '#6b7280', fontSize: '12px' } }
                    },
                    yaxis: [{
                        title: { text: 'Invoice Count', style: { color: '#6b7280' } },
                        labels: { style: { colors: '#6b7280' } }
                    }, {
                        opposite: true,
                        title: { text: 'Amount (₹)', style: { color: '#6b7280' } },
                        labels: {
                            style: { colors: '#6b7280' },
                            formatter: function(val) {
                                return '₹' + (val / 100000).toFixed(1) + 'L';
                            }
                        }
                    }],
                    tooltip: {
                        shared: true,
                        intersect: false,
                        y: [{
                            formatter: function(val) {
                                return val + ' invoices';
                            }
                        }, {
                            formatter: function(val) {
                                return '₹' + val.toLocaleString('en-IN');
                            }
                        }]
                    },
                    legend: {
                        position: 'top',
                        horizontalAlign: 'left',
                        offsetX: 40
                    },
                    grid: {
                        borderColor: '#e5e7eb',
                        strokeDashArray: 5
                    }
                };

                // Clear loading state and render chart
                const chartContainer = document.querySelector("#monthlyInvoiceChart");
                if (chartContainer) {
                    chartContainer.innerHTML = '';
                    const chart = new ApexCharts(chartContainer, options);
                    chart.render();
                }
            } else {
                $('#monthlyInvoiceChart').html('<div class="text-center py-5 text-warning">No data available for ' + year + '</div>');
            }
        },
        error: function() {
            $('#monthlyInvoiceChart').html('<div class="text-center py-5 text-danger">Failed to load invoice analytics</div>');
        }
    });
}

// Load Payment Trends Analytics
function loadPaymentTrendsAnalytics(year) {
    // Clear any existing chart
    const chartContainer = document.querySelector("#paymentTrendsChart");
    if (chartContainer) {
        chartContainer.innerHTML = '<div class="d-flex align-items-center justify-content-center h-100"><div class="text-center"><div class="spinner-border text-purple mb-3" role="status"><span class="visually-hidden">Loading...</span></div><p class="text-muted">Loading payment trends...</p></div></div>';
    }

    $.ajax({
        url: '/dashboard/payment-trends-analytics',
        method: 'GET',
        data: { year: year },
        success: function(response) {
            if (response.success && typeof ApexCharts !== 'undefined') {
                const options = {
                    series: [{
                        name: 'Payments Received',
                        data: response.data.amounts.map(val => Math.round(val))
                    }],
                    chart: {
                        height: 320,
                        type: 'area',
                        toolbar: { show: false },
                        background: 'transparent'
                    },
                    colors: ['#8b5cf6'],
                    fill: {
                        type: 'gradient',
                        gradient: {
                            shade: 'light',
                            type: 'vertical',
                            shadeIntensity: 0.3,
                            gradientToColors: ['#a78bfa'],
                            inverseColors: false,
                            opacityFrom: 0.8,
                            opacityTo: 0.1
                        }
                    },
                    stroke: {
                        curve: 'smooth',
                        width: 3
                    },
                    dataLabels: {
                        enabled: true,
                        formatter: function(val) {
                            const rounded = Math.round(val);
                            if (rounded >= 100000) {
                                return '₹' + (rounded / 100000).toFixed(1) + 'L';
                            } else if (rounded >= 1000) {
                                return '₹' + (rounded / 1000).toFixed(1) + 'K';
                            } else {
                                return '₹' + rounded;
                            }
                        },
                        style: {
                            fontSize: '11px',
                            fontWeight: 600,
                            colors: ['#6b7280']
                        },
                        background: {
                            enabled: true,
                            foreColor: '#fff',
                            borderRadius: 4,
                            padding: 4,
                            opacity: 0.9,
                            borderWidth: 1,
                            borderColor: '#e5e7eb'
                        }
                    },
                    xaxis: {
                        categories: response.data.months,
                        labels: { style: { colors: '#6b7280', fontSize: '12px' } }
                    },
                    yaxis: {
                        title: { text: 'Amount (₹)', style: { color: '#6b7280' } },
                        labels: {
                            style: { colors: '#6b7280' },
                            formatter: function(val) {
                                const rounded = Math.round(val);
                                return '₹' + (rounded / 100000).toFixed(1) + 'L';
                            }
                        }
                    },
                    tooltip: {
                        y: {
                            formatter: function(val) {
                                const rounded = Math.round(val);
                                return '₹' + rounded.toLocaleString('en-IN');
                            }
                        }
                    },
                    grid: {
                        borderColor: '#e5e7eb',
                        strokeDashArray: 5
                    },
                    markers: {
                        size: 5,
                        colors: ['#8b5cf6'],
                        strokeColors: '#fff',
                        strokeWidth: 2,
                        hover: { size: 7 }
                    }
                };

                // Clear loading state and render chart
                const chartContainer = document.querySelector("#paymentTrendsChart");
                if (chartContainer) {
                    chartContainer.innerHTML = '';
                    const chart = new ApexCharts(chartContainer, options);
                    chart.render();
                }
            } else {
                $('#paymentTrendsChart').html('<div class="text-center py-5 text-warning">No data available for ' + year + '</div>');
            }
        },
        error: function() {
            $('#paymentTrendsChart').html('<div class="text-center py-5 text-danger">Failed to load payment trends</div>');
        }
    });
}

// Load Pending Amount Analytics
function loadPendingAmountAnalytics() {
    // Clear any existing chart
    const chartContainer = document.querySelector("#pendingAmountChart");
    if (chartContainer) {
        chartContainer.innerHTML = '<div class="d-flex align-items-center justify-content-center h-100"><div class="text-center"><div class="spinner-border text-danger mb-3" role="status"><span class="visually-hidden">Loading...</span></div><p class="text-muted">Loading pending amount analytics...</p></div></div>';
    }

    $.ajax({
        url: '/dashboard/pending-amount-analytics',
        method: 'GET',
        success: function(response) {
            if (response.success && typeof ApexCharts !== 'undefined') {
                const options = {
                    series: response.data.amounts,
                    chart: {
                        height: 320,
                        type: 'donut',
                        background: 'transparent'
                    },
                    labels: response.data.labels,
                    colors: ['#ef4444', '#f97316', '#eab308', '#22c55e'],
                    plotOptions: {
                        pie: {
                            donut: {
                                size: '70%',
                                labels: {
                                    show: true,
                                    total: {
                                        show: true,
                                        label: 'Total Pending',
                                        formatter: function(w) {
                                            const total = w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                                            return '₹' + (total / 100000).toFixed(1) + 'L';
                                        }
                                    }
                                }
                            }
                        }
                    },
                    legend: {
                        position: 'bottom',
                        horizontalAlign: 'center'
                    },
                    tooltip: {
                        y: {
                            formatter: function(val) {
                                return '₹' + val.toLocaleString('en-IN');
                            }
                        }
                    },
                    responsive: [{
                        breakpoint: 480,
                        options: {
                            chart: { width: 200 },
                            legend: { position: 'bottom' }
                        }
                    }]
                };

                // Clear loading state and render chart
                const chartContainer = document.querySelector("#pendingAmountChart");
                if (chartContainer) {
                    chartContainer.innerHTML = '';
                    const chart = new ApexCharts(chartContainer, options);
                    chart.render();
                }
            } else {
                $('#pendingAmountChart').html('<div class="text-center py-5 text-warning">No pending amount data available</div>');
            }
        },
        error: function() {
            $('#pendingAmountChart').html('<div class="text-center py-5 text-danger">Failed to load pending amount analytics</div>');
        }
    });
}

// Load Client Growth Analytics
function loadClientGrowthAnalytics() {
    // Clear any existing chart
    const chartContainer = document.querySelector("#clientGrowthChart");
    if (chartContainer) {
        chartContainer.innerHTML = '<div class="d-flex align-items-center justify-content-center h-100"><div class="text-center"><div class="spinner-border text-success mb-3" role="status"><span class="visually-hidden">Loading...</span></div><p class="text-muted">Loading client growth data...</p></div></div>';
    }

    $.ajax({
        url: '/dashboard/client-growth-analytics',
        method: 'GET',
        success: function(response) {
            if (response.success && typeof ApexCharts !== 'undefined') {
                const options = {
                    series: [{
                        name: 'New Clients',
                        data: response.data.counts
                    }],
                    chart: {
                        height: 320,
                        type: 'bar',
                        toolbar: { show: false },
                        background: 'transparent'
                    },
                    colors: ['#22c55e'],
                    plotOptions: {
                        bar: {
                            borderRadius: 8,
                            columnWidth: '60%',
                            distributed: false
                        }
                    },
                    fill: {
                        type: 'gradient',
                        gradient: {
                            shade: 'light',
                            type: 'vertical',
                            shadeIntensity: 0.3,
                            gradientToColors: ['#16a34a'],
                            inverseColors: false,
                            opacityFrom: 0.9,
                            opacityTo: 0.7
                        }
                    },
                    xaxis: {
                        categories: response.data.months,
                        labels: { style: { colors: '#6b7280', fontSize: '12px' } }
                    },
                    yaxis: {
                        title: { text: 'New Clients', style: { color: '#6b7280' } },
                        labels: { style: { colors: '#6b7280' } }
                    },
                    tooltip: {
                        y: {
                            formatter: function(val) {
                                return val + ' new clients';
                            }
                        }
                    },
                    grid: {
                        borderColor: '#e5e7eb',
                        strokeDashArray: 5
                    }
                };

                // Clear loading state and render chart
                const chartContainer = document.querySelector("#clientGrowthChart");
                if (chartContainer) {
                    chartContainer.innerHTML = '';
                    const chart = new ApexCharts(chartContainer, options);
                    chart.render();
                }
            } else {
                $('#clientGrowthChart').html('<div class="text-center py-5 text-warning">No client growth data available</div>');
            }
        },
        error: function() {
            $('#clientGrowthChart').html('<div class="text-center py-5 text-danger">Failed to load client growth analytics</div>');
        }
    });
}

// Initialize new analytics charts
$(document).ready(function() {
    // Load initial data for new charts
    loadMonthlyInvoiceAnalytics(new Date().getFullYear());
    loadPaymentTrendsAnalytics(new Date().getFullYear());
    loadPendingAmountAnalytics();
    loadClientGrowthAnalytics();

    // Event handlers for year selectors
    $('#invoiceAnalyticsYear').change(function() {
        loadMonthlyInvoiceAnalytics($(this).val());
    });

    $('#paymentTrendsYear').change(function() {
        loadPaymentTrendsAnalytics($(this).val());
    });

    // Refresh pending data button
    $('#refreshPendingData').click(function() {
        const btn = $(this);
        const originalHtml = btn.html();
        btn.html('<div class="spinner-border spinner-border-sm" role="status"></div>');
        btn.prop('disabled', true);

        loadPendingAmountAnalytics();

        setTimeout(() => {
            btn.html(originalHtml);
            btn.prop('disabled', false);
        }, 2000);
    });
});

  </script>
@endsection
