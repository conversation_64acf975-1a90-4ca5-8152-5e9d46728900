@extends('layouts.master')
@section('title', 'Inventory Management')

@section('content')
<div class="dashboard-main-body">
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Inventory Management</h6>
        <div class="breadcrumb">
        <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">Dashboard</li>
            <li>-</li>
            <li class="fw-medium">Inventory</li>
        </ul>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <p class="text-muted mb-1 fw-medium">Total Items</p>
                            <h2 class="mb-0 fw-bold text-dark">{{ $totalItems }}</h2>
                            <small class="text-muted d-flex align-items-center gap-1">
                                <iconify-icon icon="solar:box-bold" class="me-1"></iconify-icon>All inventory items
                            </small>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded-3">
                            <iconify-icon icon="solar:box-bold" class="text-info" style="font-size: 28px;"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <p class="text-muted mb-1 fw-medium">In Stock</p>
                            <h2 class="mb-0 fw-bold text-success">{{ $inStockItems }}</h2>
                            <small class="text-muted d-flex align-items-center gap-1">
                                <iconify-icon icon="solar:check-circle-bold" class="me-1"></iconify-icon>Available items
                            </small>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded-3">
                            <iconify-icon icon="solar:check-circle-bold" class="text-success" style="font-size: 28px;"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <p class="text-muted mb-1 fw-medium">Low Stock</p>
                            <h2 class="mb-0 fw-bold text-warning">{{ $lowStockItems }}</h2>
                            <small class="text-muted d-flex align-items-center gap-1">
                                <iconify-icon icon="solar:danger-triangle-bold" class="me-1"></iconify-icon>Need attention
                            </small>
                        </div>
                        <div class="bg-warning bg-opacity-10 p-3 rounded-3">
                            <iconify-icon icon="solar:danger-triangle-bold" class="text-warning" style="font-size: 28px;"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <p class="text-muted mb-1 fw-medium">Out of Stock</p>
                            <h2 class="mb-0 fw-bold text-danger">{{ $outOfStockItems }}</h2>
                            <small class="text-muted d-flex align-items-center gap-1">
                                <iconify-icon icon="solar:close-circle-bold" class="me-1"></iconify-icon>Urgent restock
                            </small>
                        </div>
                        <div class="bg-danger bg-opacity-10 p-3 rounded-3">
                            <iconify-icon icon="solar:close-circle-bold" class="text-danger" style="font-size: 28px;"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Inventory Items Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0 fw-semibold d-flex align-items-center gap-1">
                        <iconify-icon icon="solar:list-bold" class="me-2 text-primary"></iconify-icon>Inventory Items
                    </h5>
                    <small class="text-muted">Manage your inventory items and stock levels</small>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <span class="badge bg-light text-dark me-2 d-flex align-items-center gap-1">
                        <iconify-icon icon="solar:database-bold" class="me-1"></iconify-icon>
                        <span id="totalRecords">Loading...</span> items
                    </span>
                    @can('inventory-create')
                <button type="button" class="btn btn-primary shadow-sm d-flex align-items-center gap-1" data-bs-toggle="modal" data-bs-target="#addItemModal">
                    <iconify-icon icon="solar:add-circle-bold" class="me-2"></iconify-icon>Add New Item
                </button>
                @endcan
                    
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table id="inventoryTable" class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="border-0 fw-semibold">
                                Product
                            </th>
                            <th class="border-0 fw-semibold">
                                Category
                            </th>
                            <th class="border-0 fw-semibold">
                                Current Stock
                            </th>
                            <th class="border-0 fw-semibold">
                                Min. Stock
                            </th>
                            <th class="border-0 fw-semibold">
                                Unit Price (₹)
                            </th>
                            <th class="border-0 fw-semibold">
                                GST Info
                            </th>
                            <th class="border-0 fw-semibold">
                                Last Updated
                            </th>
                            <th class="border-0 fw-semibold">
                                Status
                            </th>
                            <th class="border-0 fw-semibold text-center">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded via DataTables -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Toast Notifications Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
    <!-- Toasts will be dynamically added here -->
</div>

<!-- Add Item Modal -->
<div class="modal fade" id="addItemModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title d-flex align-items-center">
                    <iconify-icon icon="solar:add-circle-bold" class="me-2"></iconify-icon>Add New Inventory Item
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="addItemAlert" class="alert alert-danger d-none" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="addItemErrorMessage"></span>
                </div>

                <form id="addItemForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    Category <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" name="category_id" required>
                                    <option value="">Select Category</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                                    @endforeach
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    Item Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" name="name" placeholder="Enter item name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    SKU Code <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" name="sku_code" placeholder="e.g., WB-RED-001" required>
                                <div class="invalid-feedback"></div>
                                <small class="form-text text-muted">Unique identifier for this item</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    Unit of Measure <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" name="unit_of_measure" required>
                                    <option value="">Select Unit</option>
                                    <option value="pieces">Pieces</option>
                                    <option value="boxes">Boxes</option>
                                    <option value="liters">Liters</option>
                                    <option value="kilograms">Kilograms</option>
                                    <option value="meters">Meters</option>
                                    <option value="sets">Sets</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-semibold">
                            Description
                        </label>
                        <textarea class="form-control" name="description" rows="3" placeholder="Enter item description (optional)"></textarea>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    Initial Quantity <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control" name="quantity_available" min="0" placeholder="0" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    Minimum Required <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control" name="minimum_required" min="0" placeholder="0" required>
                                <div class="invalid-feedback"></div>
                                <small class="form-text text-muted">Alert when stock falls below this level</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    Unit Price <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number" step="0.01" class="form-control" name="unit_price" min="0" placeholder="0.00" required>
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- GST Configuration Row -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_gst_applicable" id="is_gst_applicable" checked>
                                    <label class="form-check-label fw-semibold" for="is_gst_applicable">
                                        GST Applicable
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="gst_rates_section">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    CGST Rate (%)
                                </label>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" name="cgst_rate" min="0" max="50" value="9.00" placeholder="9.00">
                                    <span class="input-group-text">%</span>
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    SGST Rate (%)
                                </label>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" name="sgst_rate" min="0" max="50" value="9.00" placeholder="9.00">
                                    <span class="input-group-text">%</span>
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    IGST Rate (%)
                                </label>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" name="igst_rate" min="0" max="50" value="18.00" placeholder="18.00">
                                    <span class="input-group-text">%</span>
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-semibold">
                            Image URL (Optional)
                        </label>
                        <input type="url" class="form-control" name="image_url" placeholder="https://example.com/image.jpg">
                        <div class="invalid-feedback"></div>
                        <small class="form-text text-muted">URL to an image of this item</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary d-flex align-items-center gap-1" data-bs-dismiss="modal">
                    <iconify-icon icon="solar:close-circle-bold" class="me-1"></iconify-icon>Cancel
                </button>
                <button type="button" class="btn btn-primary d-flex align-items-center gap-1" id="saveItem">
                    <iconify-icon icon="solar:diskette-bold" class="me-1"></iconify-icon>Save Item
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Item Modal -->
<div class="modal fade" id="editItemModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title d-flex align-items-center">
                    <iconify-icon icon="lucide:edit" class="me-2"></iconify-icon>Edit Inventory Item
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="editItemAlert" class="alert alert-danger d-none" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="editItemErrorMessage"></span>
                </div>

                <form id="editItemForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    Category <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" name="category_id" required>
                                    <option value="">Select Category</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                                    @endforeach
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    Item Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" name="name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    SKU Code <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" name="sku_code" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    Unit of Measure <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" name="unit_of_measure" required>
                                    <option value="">Select Unit</option>
                                    <option value="pieces">Pieces</option>
                                    <option value="boxes">Boxes</option>
                                    <option value="liters">Liters</option>
                                    <option value="kilograms">Kilograms</option>
                                    <option value="meters">Meters</option>
                                    <option value="sets">Sets</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-semibold">
                            Description
                        </label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    Minimum Required <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control" name="minimum_required" min="0" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    Unit Price <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number" step="0.01" class="form-control" name="unit_price" min="0" required>
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- GST Configuration Row -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_gst_applicable" id="edit_is_gst_applicable">
                                    <label class="form-check-label fw-semibold" for="edit_is_gst_applicable">
                                        GST Applicable
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="edit_gst_rates_section">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    CGST Rate (%)
                                </label>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" name="cgst_rate" min="0" max="50" placeholder="9.00">
                                    <span class="input-group-text">%</span>
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    SGST Rate (%)
                                </label>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" name="sgst_rate" min="0" max="50" placeholder="9.00">
                                    <span class="input-group-text">%</span>
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    IGST Rate (%)
                                </label>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" name="igst_rate" min="0" max="50" placeholder="18.00">
                                    <span class="input-group-text">%</span>
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-semibold">
                            Image URL (Optional)
                        </label>
                        <input type="url" class="form-control" name="image_url">
                        <div class="invalid-feedback"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary d-flex align-items-center gap-1" data-bs-dismiss="modal">
                    <iconify-icon icon="solar:close-circle-bold" class="me-1"></iconify-icon>Cancel
                </button>
                <button type="button" class="btn btn-info d-flex align-items-center gap-1" id="updateItem">
                    <iconify-icon icon="solar:diskette-bold" class="me-1"></iconify-icon>Update Item
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Update Stock Modal -->
<div class="modal fade" id="updateStockModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title d-flex align-items-center">
                    <iconify-icon icon="solar:box-bold" class="me-2"></iconify-icon>Update Stock Level
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="updateStockAlert" class="alert alert-danger d-none" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="updateStockErrorMessage"></span>
                </div>

                <form id="updateStockForm">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        Item Information
                                    </h6>
                                    <div class="mb-2">
                                        <label class="form-label fw-semibold">Item Name</label>
                                        <input type="text" class="form-control-plaintext" id="updateItemName" readonly>
                                    </div>
                                    <div class="mb-0">
                                        <label class="form-label fw-semibold">Current Stock</label>
                                        <input type="text" class="form-control-plaintext fw-bold text-primary" id="currentStock" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                   Transaction Type <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" name="transaction_type" required>
                                    <option value="">Select Type</option>
                                    <option value="in">Stock In (Add)</option>
                                    <option value="out">Stock Out (Remove)</option>
                                    <option value="adjustment">Adjustment</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    Quantity <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control" name="quantity" min="1" placeholder="Enter quantity" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-semibold">
                            Reason <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" name="reason" required>
                            <option value="">Select Reason</option>
                            <option value="Purchase">Purchase</option>
                            <option value="Sale">Sale</option>
                            <option value="Return">Return</option>
                            <option value="Damage">Damage</option>
                            <option value="Loss">Loss</option>
                            <option value="Transfer">Transfer</option>
                            <option value="Adjustment">Adjustment</option>
                            <option value="Other">Other</option>
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-semibold">
                            Additional Notes
                        </label>
                        <textarea class="form-control" name="notes" rows="3" placeholder="Enter additional notes (optional)"></textarea>
                        <div class="invalid-feedback"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary d-flex align-items-center gap-1" data-bs-dismiss="modal">
                    <iconify-icon icon="solar:close-circle-bold" class="me-1"></iconify-icon>Cancel
                </button>
                <button type="button" class="btn btn-warning d-flex align-items-center gap-1" id="updateStock">
                    <iconify-icon icon="solar:diskette-bold" class="me-1"></iconify-icon>Update Stock
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Stock History Modal -->
<div class="modal fade" id="stockHistoryModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title d-flex align-items-center">
                    <iconify-icon icon="solar:history-bold" class="me-2"></iconify-icon>Stock Transaction History
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="card bg-light mb-3">
                    <div class="card-body py-2">
                        <h6 id="historyItemName" class="mb-0 fw-semibold">
                            <i class="fas fa-box me-1"></i>Loading...
                        </h6>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th class="fw-semibold">
                                    Date & Time
                                </th>
                                <th class="fw-semibold">
                                    Type
                                </th>
                                <th class="fw-semibold">
                                    Quantity
                                </th>
                                <th class="fw-semibold">
                                    Before
                                </th>
                                <th class="fw-semibold">
                                    After
                                </th>
                                <th class="fw-semibold">
                                    Reason
                                </th>
                                <th class="fw-semibold">
                                    By
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Mar 20, 2024</td>
                                <td>Purchase</td>
                                <td>+50</td>
                                <td>100</td>
                                <td>150</td>
                                <td>Regular monthly purchase</td>
                            </tr>
                            <tr>
                                <td>Mar 15, 2024</td>
                                <td>Sale</td>
                                <td>-30</td>
                                <td>130</td>
                                <td>100</td>
                                <td>Client order #1234</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection



@section('script')
<script>
$(document).ready(function() {
    // Global modal conflict prevention
    window.inventoryModalManager = {
        activeModals: new Set(),

        openModal: function(modalId) {
            this.activeModals.add(modalId);
            // Temporarily disable session timeout warnings while inventory modals are open
            if (window.sessionManager) {
                window.sessionManager.pauseWarnings = true;
            }
        },

        closeModal: function(modalId) {
            this.activeModals.delete(modalId);
            // Re-enable session timeout warnings when no inventory modals are open
            if (this.activeModals.size === 0 && window.sessionManager) {
                window.sessionManager.pauseWarnings = false;
            }
        }
    };

    // Bind modal events to track open/close
    $('.modal').on('show.bs.modal', function() {
        window.inventoryModalManager.openModal(this.id);
    }).on('hidden.bs.modal', function() {
        window.inventoryModalManager.closeModal(this.id);

        // Clean up any modal artifacts
        setTimeout(() => {
            if (window.inventoryModalManager.activeModals.size === 0) {
                // Remove any orphaned backdrops
                $('.modal-backdrop').each(function() {
                    if (!$('.modal.show').length) {
                        $(this).remove();
                    }
                });

                // Restore body state if no modals are open
                if (!$('.modal.show').length) {
                    $('body').removeClass('modal-open').css({
                        'overflow': '',
                        'padding-right': ''
                    });
                }
            }
        }, 100);
    });
    // Initialize DataTable
    var table = $('#inventoryTable').DataTable({
        processing: true,
        serverSide: true,
        searching: false,
        ajax: {
            url: '{{ route("inventory.data") }}',
            type: 'GET',
            error: function(xhr, error, code) {
                console.error('DataTable AJAX error:', error, code);
                if (xhr.status === 401) {
                    // Session expired, redirect to login
                    window.location.href = '/login';
                } else if (xhr.status === 404) {
                    // Route not found, show error message
                    showNotification('error', 'Unable to load inventory data. Please refresh the page.');
                } else {
                    // Other errors
                    showNotification('error', 'Error loading inventory data: ' + (xhr.responseJSON?.message || error));
                }
            }
        },
        columns: [
            {
                data: 'name',
                name: 'name',
                render: function(data, type, row) {
                    return '<div class="d-flex align-items-center">' +
                           '<div>' +
                           '<p class="mb-0 fw-semibold">' + data + '</p>' +
                           '<small class="text-muted">SKU: ' + row.sku_code + '</small>' +
                           '</div>' +
                           '</div>';
                }
            },
            {data: 'category_name', name: 'category.name'},
            {
                data: 'quantity_available',
                name: 'quantity_available',
                render: function(data, type, row) {
                    return data + ' ' + row.unit_of_measure;
                }
            },
            {
                data: 'minimum_required',
                name: 'minimum_required',
                render: function(data, type, row) {
                    return data + ' ' + row.unit_of_measure;
                }
            },
            {data: 'unit_price', name: 'unit_price'},
            {
                data: 'gst_info',
                name: 'gst_info',
                orderable: false,
                searchable: false
            },
            {data: 'updated_at', name: 'updated_at'},
            {data: 'stock_status', name: 'status', orderable: false, searchable: true},
            {data: 'actions', name: 'actions', orderable: false, searchable: false}
        ],
        order: [[5, 'desc']], // Order by updated_at desc
        pageLength: 25,
        responsive: true
    });

    // Clear form when add modal is opened
    $('#addItemModal').on('show.bs.modal', function() {
        clearFormErrors('addItemForm');
        $('#addItemForm')[0].reset();
    });

    // Clear form when edit modal is opened
    $('#editItemModal').on('show.bs.modal', function() {
        clearFormErrors('editItemForm');
    });

    // Add Item Form
    $('#saveItem').click(function() {
        var formData = new FormData($('#addItemForm')[0]);

        $.ajax({
            url: '{{ route("inventory.store") }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    $('#addItemModal').modal('hide');
                    $('#addItemForm')[0].reset();
                    table.ajax.reload();
                    showNotification('success', response.message);
                }
            },
            error: function(xhr) {
                console.error('Add item error:', xhr);
                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    showFormErrors('addItemForm', xhr.responseJSON.errors);
                } else {
                    var errorMessage = xhr.responseJSON?.message || 'An error occurred while saving the item. Please try again.';
                    showNotification('error', errorMessage);
                }
            }
        });
    });

    // Edit Item
    $(document).on('click', '.edit-item', function() {
        var itemId = $(this).data('id');

        $.ajax({
            url: '{{ url("inventory") }}/' + itemId,
            type: 'GET',
            success: function(item) {
                $('#editItemForm input[name="name"]').val(item.name);
                $('#editItemForm select[name="category_id"]').val(item.category_id);
                $('#editItemForm input[name="sku_code"]').val(item.sku_code);
                $('#editItemForm textarea[name="description"]').val(item.description);
                $('#editItemForm input[name="minimum_required"]').val(item.minimum_required);
                $('#editItemForm select[name="unit_of_measure"]').val(item.unit_of_measure);
                $('#editItemForm input[name="unit_price"]').val(item.unit_price);
                $('#editItemForm input[name="image_url"]').val(item.image_url);
                $('#editItemForm input[name="cgst_rate"]').val(item.cgst_rate || 9.00);
                $('#editItemForm input[name="sgst_rate"]').val(item.sgst_rate || 9.00);
                $('#editItemForm input[name="igst_rate"]').val(item.igst_rate || 18.00);
                $('#editItemForm input[name="is_gst_applicable"]').prop('checked', item.is_gst_applicable !== false);
                $('#editItemForm').data('item-id', itemId);

                // Toggle GST rates section based on checkbox
                toggleEditGstRates();
            }
        });
    });

    // Update Item
    $('#updateItem').click(function() {
        var itemId = $('#editItemForm').data('item-id');
        var formData = new FormData($('#editItemForm')[0]);
        formData.append('_method', 'PUT');

        // Explicitly handle checkbox state
        var isGstApplicable = $('#edit_is_gst_applicable').is(':checked');
        formData.set('is_gst_applicable', isGstApplicable ? '1' : '0');

        // Debug: Log form data
        console.log('Updating item:', itemId);
        console.log('GST Applicable:', isGstApplicable);
        console.log('CGST Rate:', $('#editItemForm input[name="cgst_rate"]').val());
        console.log('SGST Rate:', $('#editItemForm input[name="sgst_rate"]').val());
        console.log('IGST Rate:', $('#editItemForm input[name="igst_rate"]').val());

        $.ajax({
            url: '{{ url("inventory") }}/' + itemId,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    $('#editItemModal').modal('hide');
                    table.ajax.reload();
                    showNotification('success', response.message);
                }
            },
            error: function(xhr) {
                console.error('Update error:', xhr);
                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    showFormErrors('editItemForm', xhr.responseJSON.errors);
                } else {
                    var errorMessage = xhr.responseJSON?.message || 'An error occurred while updating the item. Please try again.';
                    showNotification('error', errorMessage);
                }
            }
        });
    });

    // Delete Item
    $(document).on('click', '.delete-item', function() {
        var itemId = $(this).data('id');

        if (confirm('Are you sure you want to delete this item?')) {
            $.ajax({
                url: '{{ url("inventory") }}/' + itemId,
                type: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        table.ajax.reload();
                        showNotification('success', response.message);
                    }
                },
                error: function(xhr) {
                    showNotification('error', 'Error deleting item');
                }
            });
        }
    });

    // Update Stock - Load item data when modal opens
    $(document).on('click', '.update-stock', function() {
        var itemId = $(this).data('id');
        $('#updateStockForm').data('item-id', itemId);

        // Get item data from the table row
        var row = $(this).closest('tr');
        var itemName = row.find('td:first .fw-semibold').text();
        var currentStock = row.find('td:nth-child(3)').text().split(' ')[0];

        $('#updateItemName').val(itemName);
        $('#currentStock').val(currentStock + ' ' + row.find('td:nth-child(3)').text().split(' ')[1]);

        // Clear form errors
        clearFormErrors('updateStockForm');
    });

    $('#updateStock').click(function() {
        var itemId = $('#updateStockForm').data('item-id');
        var formData = new FormData($('#updateStockForm')[0]);

        $.ajax({
            url: '{{ url("inventory") }}/' + itemId + '/update-stock',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    $('#updateStockModal').modal('hide');
                    $('#updateStockForm')[0].reset();
                    table.ajax.reload();
                    showNotification('success', response.message);
                }
            },
            error: function(xhr) {
                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    showFormErrors('updateStockForm', xhr.responseJSON.errors);
                } else {
                    showNotification('error', 'An error occurred while updating stock. Please try again.');
                }
            }
        });
    });

    // View History
    $(document).on('click', '.view-history', function() {
        var itemId = $(this).data('id');

        $.ajax({
            url: '{{ url("inventory") }}/' + itemId + '/history',
            type: 'GET',
            success: function(response) {
                $('#historyItemName').text('History for: ' + response.item.name);
                var tbody = $('#stockHistoryModal tbody');
                tbody.empty();

                if (response.transactions.length > 0) {
                    $.each(response.transactions, function(index, transaction) {
                        var typeClass = transaction.transaction_type === 'in' ? 'text-success' :
                                       transaction.transaction_type === 'out' ? 'text-danger' : 'text-warning';
                        var typeBadge = transaction.transaction_type === 'in' ? 'badge bg-success' :
                                       transaction.transaction_type === 'out' ? 'badge bg-danger' : 'badge bg-warning';
                        var quantityPrefix = transaction.transaction_type === 'in' ? '+' :
                                           transaction.transaction_type === 'out' ? '-' : '';
                        var quantityClass = transaction.transaction_type === 'in' ? 'text-success fw-bold' :
                                           transaction.transaction_type === 'out' ? 'text-danger fw-bold' : 'text-warning fw-bold';

                        var date = new Date(transaction.created_at);
                        var formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                        var userName = transaction.created_by ? (transaction.created_by.name || 'Unknown') : 'System';

                        tbody.append(
                            '<tr>' +
                            '<td><small>' + formattedDate + '</small></td>' +
                            '<td><span class="' + typeBadge + '">' + transaction.transaction_type.toUpperCase() + '</span></td>' +
                            '<td><span class="' + quantityClass + '">' + quantityPrefix + transaction.quantity + '</span></td>' +
                            '<td><span class="badge bg-light text-dark">' + transaction.quantity_before + '</span></td>' +
                            '<td><span class="badge bg-primary">' + transaction.quantity_after + '</span></td>' +
                            '<td>' + (transaction.reason || '<em class="text-muted">No reason provided</em>') + '</td>' +
                            '<td><small class="text-muted">' + userName + '</small></td>' +
                            '</tr>'
                        );
                    });
                } else {
                    tbody.append('<tr><td colspan="7" class="text-center text-muted py-4"><i class="fas fa-inbox me-2"></i>No transactions found</td></tr>');
                }
            }
        });
    });

    // Toast Notification function
    function showNotification(type, message) {
        const toastContainer = document.querySelector('.toast-container');
        const toastId = 'toast-' + Date.now();

        const iconClass = type === 'success' ? 'fas fa-check-circle' :
                         type === 'error' ? 'fas fa-exclamation-circle' :
                         type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

        const bgClass = type === 'success' ? 'bg-success' :
                       type === 'error' ? 'bg-danger' :
                       type === 'warning' ? 'bg-warning' : 'bg-info';

        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="${iconClass} me-2"></i>${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: type === 'error' ? 8000 : 5000
        });

        toast.show();

        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    }

    // Form validation helpers
    function clearFormErrors(formId) {
        const form = document.getElementById(formId);
        form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        form.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
        form.querySelector('.alert')?.classList.add('d-none');
    }

    function showFormErrors(formId, errors) {
        const form = document.getElementById(formId);
        const alertDiv = form.querySelector('.alert');
        const errorMessageSpan = form.querySelector('[id$="ErrorMessage"]');

        // Clear previous errors
        clearFormErrors(formId);

        // Show general error alert
        if (alertDiv && errorMessageSpan) {
            let errorText = 'Please fix the following errors:';
            Object.keys(errors).forEach(field => {
                errorText += `\n• ${errors[field][0]}`;

                // Highlight specific field
                const fieldElement = form.querySelector(`[name="${field}"]`);
                if (fieldElement) {
                    fieldElement.classList.add('is-invalid');
                    const feedback = fieldElement.parentNode.querySelector('.invalid-feedback');
                    if (feedback) {
                        feedback.textContent = errors[field][0];
                    }
                }
            });

            errorMessageSpan.textContent = errorText;
            alertDiv.classList.remove('d-none');
        }
    }



    // GST checkbox toggle handlers
    $('#is_gst_applicable').on('change', function() {
        toggleGstRates();
    });

    $('#edit_is_gst_applicable').on('change', function() {
        toggleEditGstRates();
    });

    // Function to toggle GST rates section in add form
    function toggleGstRates() {
        if ($('#is_gst_applicable').is(':checked')) {
            $('#gst_rates_section').show();
        } else {
            $('#gst_rates_section').hide();
            // Clear GST rate values when disabled
            $('#gst_rates_section input').val('0.00');
        }
    }

    // Function to toggle GST rates section in edit form
    function toggleEditGstRates() {
        if ($('#edit_is_gst_applicable').is(':checked')) {
            $('#edit_gst_rates_section').show();
        } else {
            $('#edit_gst_rates_section').hide();
            // Clear GST rate values when disabled
            $('#edit_gst_rates_section input').val('0.00');
        }
    }

    // Initialize GST toggle state
    toggleGstRates();

    // Update record count
    table.on('draw', function() {
        const info = table.page.info();
        document.getElementById('totalRecords').textContent = info.recordsTotal;
    });
});
</script>
@endsection
