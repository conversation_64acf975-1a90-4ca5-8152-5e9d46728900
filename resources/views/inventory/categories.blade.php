@extends('layouts.master')

@section('title', 'Inventory Categories')

@section('content')
<div class="dashboard-main-body">
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Inventory Categories</h6>
        <div class="breadcrumb">
        <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">Dashboard</li>
            <li>-</li>
            <li class="fw-medium"><a href="/inventory" class="text-decoration-none">Inventory</a></li>
            <li>-</li>
            <li class="fw-medium">Categories</li>
        </ul>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <p class="text-muted mb-1 fw-medium">Total Categories</p>
                            <h2 class="mb-0 fw-bold text-dark">{{ $totalCategories }}</h2>
                            <small class="text-muted d-flex align-items-center gap-1">
                                <iconify-icon icon="solar:tag-bold" class="me-1"></iconify-icon>All categories
                            </small>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded-3">
                            <iconify-icon icon="solar:layers-bold" class="text-primary" style="font-size: 28px;"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <p class="text-muted mb-1 fw-medium">Active Categories</p>
                            <h2 class="mb-0 fw-bold text-success">{{ $activeCategories }}</h2>
                            <small class="text-muted d-flex align-items-center gap-1">
                                <iconify-icon icon="solar:check-circle-bold" class="me-1"></iconify-icon>Currently in use
                            </small>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded-3">
                            <iconify-icon icon="solar:check-circle-bold" class="text-success" style="font-size: 28px;"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <p class="text-muted mb-1 fw-medium">Inactive Categories</p>
                            <h2 class="mb-0 fw-bold text-secondary">{{ $inactiveCategories }}</h2>
                            <small class="text-muted d-flex align-items-center gap-1">
                                <iconify-icon icon="solar:pause-circle-bold" class="me-1"></iconify-icon>Not in use
                            </small>
                        </div>
                        <div class="bg-secondary bg-opacity-10 p-3 rounded-3">
                            <iconify-icon icon="solar:pause-circle-bold" class="text-secondary" style="font-size: 28px;"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Category Button -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                
            </div>
        </div>
    </div>

    <!-- Categories Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0 fw-semibold d-flex align-items-center gap-1">
                        <iconify-icon icon="solar:list-bold" class="me-2 text-primary"></iconify-icon>Categories List
                    </h5>
                    <small class="text-muted">Manage your inventory categories</small>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <span class="badge bg-light text-dark me-2 d-flex align-items-center gap-1">
                        <iconify-icon icon="solar:database-bold" class="me-1"></iconify-icon>
                        <span id="totalRecords">Loading...</span> categories
                    </span>
                    <a href="/inventory" class="btn btn-outline-secondary shadow-sm d-flex align-items-center gap-1">
                        <iconify-icon icon="solar:arrow-left-bold" class="me-2"></iconify-icon>Back to Inventory
                    </a>
                    @can('inventory-create')
                    <button type="button" class="btn btn-primary shadow-sm d-flex align-items-center gap-1" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <iconify-icon icon="solar:add-circle-bold" class="me-2"></iconify-icon>Add New Category
                    </button>
                    @endcan
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table id="categoriesTable" class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="border-0 fw-semibold">
                                Category Name
                            </th>
                            <th class="border-0 fw-semibold">
                                Description
                            </th>
                            <th class="border-0 fw-semibold">
                                Items Count
                            </th>
                            <th class="border-0 fw-semibold">
                                Status
                            </th>
                            <th class="border-0 fw-semibold">
                                Created
                            </th>
                            <th class="border-0 fw-semibold text-center">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded via DataTables -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Toast Notifications Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
    <!-- Toasts will be dynamically added here -->
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header d-flex align-items-center justify-content-between">
                <h5 class="modal-title d-flex align-items-center">
                    <iconify-icon icon="solar:add-circle-bold" class="me-2"></iconify-icon>Add New Category
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="addCategoryAlert" class="alert alert-danger d-none" role="alert">
                    <iconify-icon icon="solar:danger-triangle-bold" class="me-2"></iconify-icon>
                    <span id="addCategoryErrorMessage"></span>
                </div>

                <form id="addCategoryForm">
                    <div class="mb-3">
                        <label class="form-label fw-semibold">
                            Category Name <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" name="name" placeholder="Enter category name" required>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-semibold">
                            Description
                        </label>
                        <textarea class="form-control" name="description" rows="3" placeholder="Enter category description (optional)"></textarea>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="is_active" id="addIsActive" checked>
                            <label class="form-check-label fw-semibold" for="addIsActive">
                                Active Category
                            </label>
                            <small class="form-text text-muted d-block">Active categories are available for use in inventory items</small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary d-flex align-items-center gap-1" data-bs-dismiss="modal">
                    <iconify-icon icon="solar:close-circle-bold" class="me-1"></iconify-icon>Cancel
                </button>
                <button type="button" class="btn btn-primary d-flex align-items-center gap-1" id="saveCategory">
                    <iconify-icon icon="solar:diskette-bold" class="me-1"></iconify-icon>Save Category
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title d-flex align-items-center">
                    <iconify-icon icon="lucide:edit" class="me-2"></iconify-icon>Edit Category
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="editCategoryAlert" class="alert alert-danger d-none" role="alert">
                    <iconify-icon icon="solar:danger-triangle-bold" class="me-2"></iconify-icon>
                    <span id="editCategoryErrorMessage"></span>
                </div>

                <form id="editCategoryForm">
                    <div class="mb-3">
                        <label class="form-label fw-semibold">
                            Category Name <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" name="name" required>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-semibold">
                            Description
                        </label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="is_active" id="editIsActive">
                            <label class="form-check-label fw-semibold" for="editIsActive">
                                Active Category
                            </label>
                            <small class="form-text text-muted d-block">Active categories are available for use in inventory items</small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary d-flex align-items-center gap-1" data-bs-dismiss="modal">
                    <iconify-icon icon="solar:close-circle-bold" class="me-1"></iconify-icon>Cancel
                </button>
                <button type="button" class="btn btn-info d-flex align-items-center gap-1" id="updateCategory">
                    <iconify-icon icon="solar:diskette-bold" class="me-1"></iconify-icon>Update Category
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
$(document).ready(function() {
    // Global modal conflict prevention
    window.categoryModalManager = {
        activeModals: new Set(),

        openModal: function(modalId) {
            this.activeModals.add(modalId);
            if (window.sessionManager) {
                window.sessionManager.pauseWarnings = true;
            }
        },

        closeModal: function(modalId) {
            this.activeModals.delete(modalId);
            if (this.activeModals.size === 0 && window.sessionManager) {
                window.sessionManager.pauseWarnings = false;
            }
        }
    };

    // Bind modal events
    $('.modal').on('show.bs.modal', function() {
        window.categoryModalManager.openModal(this.id);
    }).on('hidden.bs.modal', function() {
        window.categoryModalManager.closeModal(this.id);

        setTimeout(() => {
            if (window.categoryModalManager.activeModals.size === 0) {
                $('.modal-backdrop').each(function() {
                    if (!$('.modal.show').length) {
                        $(this).remove();
                    }
                });

                if (!$('.modal.show').length) {
                    $('body').removeClass('modal-open').css({
                        'overflow': '',
                        'padding-right': ''
                    });
                }
            }
        }, 100);
    });

    // Initialize DataTable
    var table = $('#categoriesTable').DataTable({
        processing: true,
        serverSide: true,
        searching: false,
        ajax: {
            url: '{{ route("categories.data") }}',
            type: 'GET',
            error: function(xhr, error, code) {
                console.error('DataTable AJAX error:', error, code);
                if (xhr.status === 401) {
                    window.location.href = '/login';
                } else if (xhr.status === 404) {
                    showNotification('error', 'Unable to load categories data. Please refresh the page.');
                } else {
                    showNotification('error', 'Error loading categories data: ' + (xhr.responseJSON?.message || error));
                }
            }
        },
        columns: [
            {data: 'name', name: 'name'},
            {data: 'description', name: 'description', orderable: false},
            {data: 'items_count', name: 'items_count', searchable: false},
            {data: 'status', name: 'is_active', orderable: false, searchable: false},
            {data: 'created_at', name: 'created_at'},
            {data: 'actions', name: 'actions', orderable: false, searchable: false}
        ],
        order: [[0, 'asc']], // Order by name
        pageLength: 25,
        responsive: true
    });

    // Clear form when add modal is opened
    $('#addCategoryModal').on('show.bs.modal', function() {
        clearFormErrors('addCategoryForm');
        $('#addCategoryForm')[0].reset();
        $('#addIsActive').prop('checked', true);
    });

    // Clear form when edit modal is opened
    $('#editCategoryModal').on('show.bs.modal', function() {
        clearFormErrors('editCategoryForm');
    });

    // Add Category Form
    $('#saveCategory').click(function() {
        var formData = new FormData($('#addCategoryForm')[0]);

        $.ajax({
            url: '{{ route("categories.store") }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    $('#addCategoryModal').modal('hide');
                    $('#addCategoryForm')[0].reset();
                    table.ajax.reload();
                    showNotification('success', response.message);
                }
            },
            error: function(xhr) {
                console.error('Add category error:', xhr);
                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    showFormErrors('addCategoryForm', xhr.responseJSON.errors);
                } else {
                    var errorMessage = xhr.responseJSON?.message || 'An error occurred while saving the category. Please try again.';
                    showNotification('error', errorMessage);
                }
            }
        });
    });

    // Edit Category
    $(document).on('click', '.edit-category', function() {
        var categoryId = $(this).data('id');

        $.ajax({
            url: '{{ url("categories") }}/' + categoryId,
            type: 'GET',
            success: function(category) {
                $('#editCategoryForm input[name="name"]').val(category.name);
                $('#editCategoryForm textarea[name="description"]').val(category.description);
                $('#editIsActive').prop('checked', category.is_active);
                $('#editCategoryForm').data('category-id', categoryId);
            }
        });
    });

    // Update Category
    $('#updateCategory').click(function() {
        var categoryId = $('#editCategoryForm').data('category-id');
        var formData = new FormData($('#editCategoryForm')[0]);
        formData.append('_method', 'PUT');

        $.ajax({
            url: '{{ url("categories") }}/' + categoryId,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    $('#editCategoryModal').modal('hide');
                    table.ajax.reload();
                    showNotification('success', response.message);
                }
            },
            error: function(xhr) {
                console.error('Update category error:', xhr);
                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    showFormErrors('editCategoryForm', xhr.responseJSON.errors);
                } else {
                    var errorMessage = xhr.responseJSON?.message || 'An error occurred while updating the category. Please try again.';
                    showNotification('error', errorMessage);
                }
            }
        });
    });

    // Delete Category
    $(document).on('click', '.delete-category', function(e) {
        e.preventDefault();
        var $button = $(this);
        var categoryId = $button.data('id');

        console.log('Delete button clicked:', {
            categoryId: categoryId,
            hasDataId: $button.attr('data-id'),
            buttonClasses: $button.attr('class')
        });

        // Check if the button is disabled (no data-id means disabled)
        if (!categoryId) {
            console.log('Delete blocked: No category ID found');
            showNotification('warning', 'Cannot delete category that contains inventory items. Please move or delete the items first.');
            return;
        }

        if (confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
            // Store original button content
            var originalContent = $button.html();

            // Show loading state
            $button.html('<iconify-icon icon="solar:loading-bold" class="animate-spin"></iconify-icon>');
            $button.addClass('disabled');

            var deleteUrl = '{{ url("categories") }}/' + categoryId;
            var csrfToken = $('meta[name="csrf-token"]').attr('content');

            console.log('Sending DELETE request to:', deleteUrl);
            console.log('CSRF Token:', csrfToken ? 'Found' : 'Missing');

            $.ajax({
                url: deleteUrl,
                type: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                success: function(response) {
                    console.log('Delete response:', response);
                    if (response.success) {
                        table.ajax.reload();
                        showNotification('success', response.message);
                    } else {
                        showNotification('error', response.message || 'Failed to delete category');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Delete category error:', {
                        xhr: xhr,
                        status: status,
                        error: error,
                        responseText: xhr.responseText,
                        responseJSON: xhr.responseJSON
                    });

                    if (xhr.status === 403) {
                        showNotification('error', 'You do not have permission to delete categories.');
                    } else if (xhr.status === 404) {
                        showNotification('error', 'Category not found.');
                    } else if (xhr.responseJSON && xhr.responseJSON.message) {
                        showNotification('error', xhr.responseJSON.message);
                    } else {
                        showNotification('error', 'Error deleting category. Please try again.');
                    }
                },
                complete: function() {
                    // Reset button state
                    $button.html(originalContent);
                    $button.removeClass('disabled');

                    // Reload table after a short delay
                    setTimeout(function() {
                        table.ajax.reload();
                    }, 500);
                }
            });
        }
    });

    // Handle clicks on disabled delete buttons (for better UX)
    $(document).on('click', 'a[style*="cursor: not-allowed"]', function(e) {
        e.preventDefault();
        showNotification('warning', 'Cannot delete category that contains inventory items. Please move or delete the items first.');
    });

    // Toast Notification function
    function showNotification(type, message) {
        try {
            const toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                console.error('Toast container not found');
                alert(message); // Fallback to alert
                return;
            }

            const toastId = 'toast-' + Date.now();

            const iconClass = type === 'success' ? 'solar:check-circle-bold' :
                             type === 'error' ? 'solar:danger-triangle-bold' :
                             type === 'warning' ? 'solar:danger-triangle-bold' : 'solar:info-circle-bold';

            const bgClass = type === 'success' ? 'bg-success' :
                           type === 'error' ? 'bg-danger' :
                           type === 'warning' ? 'bg-warning' : 'bg-info';

            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <iconify-icon icon="${iconClass}" class="me-2"></iconify-icon>${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            const toastElement = document.getElementById(toastId);
            if (toastElement && typeof bootstrap !== 'undefined' && bootstrap.Toast) {
                const toast = new bootstrap.Toast(toastElement, {
                    autohide: true,
                    delay: type === 'error' ? 8000 : 5000
                });

                toast.show();

                toastElement.addEventListener('hidden.bs.toast', function() {
                    toastElement.remove();
                });
            } else {
                console.error('Bootstrap Toast not available, falling back to alert');
                alert(message);
            }
        } catch (error) {
            console.error('Error showing notification:', error);
            alert(message); // Fallback to alert
        }
    }

    // Form validation helpers
    function clearFormErrors(formId) {
        const form = document.getElementById(formId);
        form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        form.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
        form.querySelector('.alert')?.classList.add('d-none');
    }

    function showFormErrors(formId, errors) {
        const form = document.getElementById(formId);
        const alertDiv = form.querySelector('.alert');
        const errorMessageSpan = form.querySelector('[id$="ErrorMessage"]');

        clearFormErrors(formId);

        if (alertDiv && errorMessageSpan) {
            let errorText = 'Please fix the following errors:';
            Object.keys(errors).forEach(field => {
                errorText += `\n• ${errors[field][0]}`;

                const fieldElement = form.querySelector(`[name="${field}"]`);
                if (fieldElement) {
                    fieldElement.classList.add('is-invalid');
                    const feedback = fieldElement.parentNode.querySelector('.invalid-feedback');
                    if (feedback) {
                        feedback.textContent = errors[field][0];
                    }
                }
            });

            errorMessageSpan.textContent = errorText;
            alertDiv.classList.remove('d-none');
        }
    }



    // Update record count
    table.on('draw', function() {
        const info = table.page.info();
        document.getElementById('totalRecords').textContent = info.recordsTotal;
    });
});
</script>
@endsection
