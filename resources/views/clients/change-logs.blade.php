@php
    $title = 'Client';
    $moduleTitle = 'Clients';
    $moduleIcon = 'solar:users-group-rounded-bold';
    $entityIcon = 'solar:user-bold';
    $indexRoute = route('clients.index');
    $viewRoute = route('clients.show', $client->id);
    $editRoute = auth()->user()->can('client-edit') ? route('clients.edit', $client->id) : null;
    $entityInfo = [
        'Client ID' => $client->id,
        'Client Code' => $client->client_code ?? 'N/A',
        'Name' => $client->name ?? 'N/A',
        'Email' => $client->email ?? 'N/A',
        'Phone' => $client->phone ?? 'N/A',
        'City' => $client->city ?? 'N/A',
        'State' => $client->state ? $client->state->name : 'N/A',
        'District' => $client->district ? $client->district->name : 'N/A',
        'GST Number' => $client->gst_number ?? 'N/A',
        'Contact Person' => $client->contact_person ?? 'N/A',
        'Pending Amount' => '<span class="text-warning fw-bold">₹' . number_format($client->pending_amount ?? 0, 2) . '</span>',
        'Status' => ($client->status ?? 1) == 1 ? '<span class="badge bg-success">Active</span>' : '<span class="badge bg-danger">Inactive</span>',
        'Created At' => $client->created_at->format('d-m-Y h:i A')
    ];
    $changeLogs = $client->changeLogs()->orderBy('created_at', 'desc')->get();
@endphp

@include('shared.change-logs', [
    'title' => $title,
    'moduleTitle' => $moduleTitle,
    'moduleIcon' => $moduleIcon,
    'entityIcon' => $entityIcon,
    'indexRoute' => $indexRoute,
    'viewRoute' => $viewRoute,
    'editRoute' => $editRoute,
    'entityInfo' => $entityInfo,
    'changeLogs' => $changeLogs
])
