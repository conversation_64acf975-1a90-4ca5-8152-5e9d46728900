@extends('layouts.master')
@section('title', 'Clients View- Paidash')

@section('style')
<style>
/* Professional Account Ledger Styles - Clean & Modern */

/* Icon alignment fixes */
iconify-icon {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    vertical-align: middle !important;
}

/* Ensure proper icon spacing in flex containers */
.d-flex iconify-icon {
    flex-shrink: 0;
}

/* Professional card styling */
.card {
    transition: all 0.2s ease-in-out;
    border-radius: 8px;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

/* Summary tiles professional styling */
.card-body.p-3 {
    padding: 1.5rem !important;
}

/* Enhanced Table Styling */
.table-responsive {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 25px rgba(0,0,0,0.08);
    background: white;
}

.table {
    margin-bottom: 0;
}

.table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem 0.75rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

/* Enhanced Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Status badges */
.status-paid { background-color: #d4edda !important; color: #155724 !important; }
.status-pending { background-color: #fff3cd !important; color: #856404 !important; }
.status-overdue { background-color: #f8d7da !important; color: #721c24 !important; }
.status-partial { background-color: #d1ecf1 !important; color: #0c5460 !important; }

/* Professional Account Ledger Styles */

/* Professional button styling */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Professional badge styling */
.badge {
    font-weight: 500;
    letter-spacing: 0.3px;
    padding: 0.5rem 1rem;
}

/* Header styling */
.w-40-px {
    width: 40px;
}

.h-40-px {
    height: 40px;
}

/* Color enhancements */
.text-primary { color: #0d6efd !important; }
.text-success { color: #198754 !important; }
.text-warning { color: #ffc107 !important; }
.text-danger { color: #dc3545 !important; }
.text-info { color: #0dcaf0 !important; }
.text-secondary { color: #6c757d !important; }

/* Responsive adjustments */
@media (max-width: 768px) {
    .row-cols-xxl-6 > * {
        flex: 0 0 auto;
        width: 50%;
    }

    .card-body.p-3 {
        padding: 1rem !important;
    }

    .table th, .table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.85rem;
    }

    .d-flex.gap-3 {
        gap: 1rem !important;
    }

    .input-group {
        width: 100% !important;
    }
}

@media (max-width: 576px) {
    .row-cols-xxl-6 > * {
        width: 100%;
    }

    .d-flex.flex-wrap {
        flex-direction: column;
        align-items: stretch !important;
    }

    .text-end {
        text-align: left !important;
    }
}

/* Summary Tiles Styling */
.summary-tile {
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.summary-tile:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.summary-tile iconify-icon {
    width: 32px;
    height: 32px;
    display: block !important;
}

.summary-tile h6 {
    font-size: 1.1rem;
}

.summary-tile p {
    font-size: 0.75rem;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Footer styling */
tfoot tr {
    background-color: #f8f9fa !important;
}

tfoot td {
    font-weight: 600 !important;
    border-top: 2px solid #dee2e6 !important;
}

/* Button styling in tiles */
.summary-tile .btn {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
    white-space: nowrap;
}

.summary-tile .btn iconify-icon {
    width: 16px;
    height: 16px;
}

/* Professional spacing */
.gap-3 {
    gap: 1rem !important;
}

.gap-2 {
    gap: 0.5rem !important;
}

/* Enhanced visual hierarchy */
h5.fw-bold {
    font-weight: 700 !important;
    color: #212529;
}

h6.fw-bold {
    font-weight: 600 !important;
    color: #495057;
}

.text-muted {
    color: #6c757d !important;
}

/* Professional shadows */
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

/* Loading animation */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Enhanced button states */
.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Export Loader Styling */
.export-loader {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    text-align: center;
    border: 1px solid #e9ecef;
}

.export-loader .spinner-border {
    width: 2rem;
    height: 2rem;
    margin-bottom: 1rem;
}

.export-loader strong {
    color: #495057;
    font-size: 1rem;
    display: block;
}
</style>
@endsection

@section('content')
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">View Client: {{ $client->name }}</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="/clients" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="flowbite:users-group-outline" class="icon text-lg"></iconify-icon>
                        Clients
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">View Client</li>
            </ul>
        </div>


        <div class="row gy-4">

            <div class="col-lg-12">
                <div class="card h-100">
                    <div class="card-body p-24">
                        <ul class="w-100 nav border-gradient-tab nav-pills mb-20 d-inline-flex" id="pills-tab"
                            role="tablist">

                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24 active" id="client-information"
                                    data-bs-toggle="pill" data-bs-target="#client-information-d" type="button" role="tab"
                                    aria-controls="client-information-d" aria-selected="false" tabindex="-1">
                                    Client Information
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="service-log"
                                    data-bs-toggle="pill" data-bs-target="#service-log-d" type="button" role="tab"
                                    aria-controls="service-log-d" aria-selected="false" tabindex="-1">
                                    Service Log
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="invoice-history"
                                    data-bs-toggle="pill" data-bs-target="#invoice-history-d" type="button"
                                    role="tab" aria-controls="invoice-history-d" aria-selected="false"
                                    tabindex="-1">
                                    Invoice History
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="payments"
                                    data-bs-toggle="pill" data-bs-target="#payments-d" type="button" role="tab"
                                    aria-controls="payments-d" aria-selected="false" tabindex="-1">
                                    Payments
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="discounts"
                                    data-bs-toggle="pill" data-bs-target="#discounts-d" type="button" role="tab"
                                    aria-controls="discounts-d" aria-selected="false" tabindex="-1">
                                    Discounts
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="account-ledger"
                                    data-bs-toggle="pill" data-bs-target="#account-ledger-d" type="button"
                                    role="tab" aria-controls="account-ledger-d" aria-selected="false"
                                    tabindex="-1">
                                    Account Ledger
                                </button>
                            </li>

                        </ul>

                        <div class="tab-content" id="pills-tabContent">

                            <div class="tab-pane fade show active" id="client-information-d" role="tabpanel"
                                aria-labelledby="client-information" tabindex="0">
                                <div class="user-grid-card position-relative border radius-16 overflow-hidden bg-base h-100">
                                    <img src="./assets/images/user-grid/user-grid-bg1.png" alt="" class="w-100 object-fit-cover">
                                    <div class="pb-24 ms-16 mb-24 me-16 ">
                                        <div class="d-flex align-items-center justify-content-between px-4 py-3 border-bottom">
                                            <div class="d-flex align-items-center gap-3">
                                                @if ($client->logo && Storage::exists('public/' . $client->logo))
                                                    <img src="{{ asset('storage/' . $client->logo) }}" alt="Profile Image"
                                                        class="border border-white border-width-2-px w-64 h-64 rounded-circle object-fit-cover">
                                                @else
                                                    <div class="w-64 h-64 rounded-circle d-flex justify-content-center align-items-center bg-info-100 text-info-600 fw-bold fs-4 px-16 py-4">
                                                        {{ strtoupper(substr($client->business_name, 0, 1)) }}
                                                    </div>
                                                @endif

                                                <div>
                                                    <h5 class="mb-1">{{ $client->business_name }}</h5>

                                                    @if (Auth::user()->role_id != 3)
                                                        <p class="mb-0 text-sm">
                                                            Executive:
                                                            <span id="assigned-employee" class="editable text-decoration-underline fw-medium">
                                                                {{ $assignedEmployee && $assignedEmployee->employee ? $assignedEmployee->employee->emp_name : 'Not Assigned' }}
                                                            </span>
                                                            <a href="#" class="text-success ms-2" data-bs-toggle="modal" data-bs-target="#exampleModal">
                                                                <iconify-icon icon="mage:edit"></iconify-icon>
                                                            </a>
                                                        </p>
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="d-flex align-items-center gap-3">
                                                <!-- Edit Button -->
                                                @can('client-edit')
                                                    <a href="{{ route('clients.edit', $client->id) }}" class="btn btn-success btn-sm d-flex align-items-center gap-2">
                                                        <iconify-icon icon="lucide:edit" width="16"></iconify-icon>
                                                        Edit
                                                    </a>
                                                @endcan

                                                <!-- Change Logs Button -->
                                                <a href="{{ route('clients.change-logs', $client->id) }}" class="btn btn-outline-primary btn-sm d-flex align-items-center gap-2">
                                                    <iconify-icon icon="mdi:history" width="16"></iconify-icon>
                                                    Change Logs
                                                </a>

                                                @if (Auth::user()->role_id != 3)
                                                    <div class="form-switch switch-success d-flex align-items-center gap-2">
                                                        <input class="form-check-input" type="checkbox" role="switch" id="switch3"
                                                            data-id="{{ $client->id }}" {{ $client->status == 1 ? 'checked' : '' }}>
                                                        <label class="form-check-label fw-medium text-secondary-light mb-0" for="switch3">
                                                            {{ $client->status == 1 ? 'Active' : 'Inactive' }}
                                                        </label>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>


                                        <div class="row mt-4 gy-4">
                                            <!-- Business Information Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:office-building" class="me-2 icon-size-20"></iconify-icon>
                                                        Business Information
                                                    </div>
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            <li><strong>Business Name:</strong> <span class="text-muted">{{ $client->business_name }}</span></li>
                                                            <li><strong>Client Code:</strong> <span class="text-muted">{{ $client->client_code }}</span></li>
                                                            <li><strong>Contact Person:</strong> <span class="text-muted">{{ $client->name }}</span></li>
                                                            <li><strong>GST No.:</strong> <span class="text-muted">{{ $client->gst ?? 'NA' }}</span></li>
                                                            <li><strong>PAN No.:</strong> <span class="text-muted">{{ $client->pan ?? 'NA' }}</span></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Contact Information Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:phone" class="me-2 icon-size-20"></iconify-icon>
                                                        Contact Information
                                                    </div>
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            <li><strong>Primary Mobile:</strong> <span class="text-muted">{{ $client->phone }}</span></li>
                                                            @if($client->secondary_phone ?? false)
                                                            <li><strong>Secondary Mobile:</strong> <span class="text-muted">{{ $client->secondary_phone }}</span></li>
                                                            @endif
                                                            <li><strong>Primary Email:</strong> <span class="text-muted">{{ $client->email }}</span></li>
                                                            @if($client->secondary_email ?? false)
                                                            <li><strong>Secondary Email:</strong> <span class="text-muted">{{ $client->secondary_email }}</span></li>
                                                            @endif
                                                            <li><strong>Address:</strong> <span class="text-muted">{{ $client->address }}</span></li>
                                                            <li><strong>Area:</strong> <span class="text-muted">{{ $client->area ?? 'NA' }}</span></li>
                                                            <li class="d-flex align-items-center">
                                                                <strong>Location:</strong>
                                                                @php
                                                                    $mapLink = !empty($client->lang_lat)
                                                                        ? 'https://www.google.com/maps?q=' . urlencode($client->lang_lat)
                                                                        : '#';
                                                                @endphp
                                                                <a href="{{ $mapLink }}"
                                                                class="d-flex align-items-center text-decoration-none {{ empty($client->lang_lat) ? 'text-muted' : 'text-success' }}"
                                                                target="_blank" {{ empty($client->lang_lat) ? 'aria-disabled=true' : '' }}>
                                                                    <iconify-icon icon="mdi:map-marker" class="me-1 icon-size-18"></iconify-icon>
                                                                    {{ empty($client->lang_lat) ? 'No Location' : 'View on Map' }}
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Additional Details Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:information-outline" class="me-2 icon-size-20"></iconify-icon>
                                                        Additional Details
                                                    </div>
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            <li><strong>HCF No.:</strong> <span class="text-muted">{{ $client->hcf_no }}</span></li>
                                                            <li><strong>HCF Type:</strong> <span class="text-muted">{{ $client->hcf_type }}</span></li>
                                                            <li><strong>Description:</strong> <span class="text-muted">{{ $client->description }}</span></li>
                                                            <li>
                                                                <strong>Client Type:</strong>
                                                                <span class="text-muted">
                                                                    {{ $client->client_type == 1 ? 'Private' : ($client->client_type == 2 ? 'Government' : 'N/A') }}
                                                                </span>
                                                            </li>

                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade show" id="service-log-d" role="tabpanel"
                                aria-labelledby="service-log" tabindex="0">

                                <!-- Enhanced Header Section -->
                                <div class="card mb-4">
                                    <div class="card-body py-3">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <h6 class="mb-0 fw-semibold d-flex align-items-center gap-2">
                                                    <iconify-icon icon="solar:settings-bold" class="text-info"></iconify-icon>
                                                    Service Management
                                                </h6>
                                                <small class="text-muted">Manage and track all services assigned to {{ $client->name }} ({{ $client->client_code }})</small>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center justify-content-end gap-3">
                                                    <button type="button" class="btn btn-success btn-sm d-flex align-items-center gap-2" onclick="exportServiceLog()">
                                                        <iconify-icon icon="hugeicons:csv-02"></iconify-icon>
                                                        Export CSV
                                                    </button>
                                                    <button type="button" class="btn btn-primary btn-sm d-flex align-items-center gap-2" onclick="assignNewService()">
                                                        <iconify-icon icon="solar:add-circle-bold"></iconify-icon>
                                                        Assign New Service
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Enhanced Service Log Table -->
                                <div class="table-responsive">
                                    <table id="serviceLogTable" class="table xsm-table table-striped table-hover table-bordered w-100">
                                        <thead>
                                            <tr>
                                                <th>Service Type</th>
                                                <th>Service Name</th>
                                                <th>Start Date</th>
                                                <th>End Date</th>
                                                <th>Units</th>
                                                <th>Total Price</th>
                                                <th>Payment Cycle</th>
                                                <th>Next Invoice</th>
                                                <th>Status</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($client_services as $client_service)
                                                <tr>
                                                    <td>{{ $client_service->service_type_data->name }}</td>
                                                    <td>{{ $client_service->service->user_label }}</td>
                                                    <td>{{ $client_service->start_date ? date('d-m-Y', strtotime($client_service->start_date)) : 'NA' }}</td>
                                                    <td>{{ $client_service->end_date ? date('d-m-Y', strtotime($client_service->end_date)) : 'NA' }}</td>
                                                    <td>{{ $client_service->beds_count ?? 'NA' }}</td>
                                                    <td>₹{{ $client_service->total_price }}</td>
                                                    <td>{{ $client_service->payment_cycle }}</td>
                                                    <td>{{ $client_service->next_invoice_date ? date('d-m-Y', strtotime($client_service->next_invoice_date)) : 'NA' }}</td>
                                                    <td>
                                                        @if ($client_service->status == 1)
                                                            <span class="badge bg-success text-white">Active</span>
                                                        @else
                                                            <span class="badge bg-primary text-white">Completed</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <a href="{{ route('service.show', $client_service->id) }}" class="w-32-px h-32-px bg-primary-light text-primary-600 rounded-circle d-inline-flex align-items-center justify-content-center">
                                                            <iconify-icon icon="iconamoon:eye-light"></iconify-icon>
                                                        </a>
                                                        @can('service-edit')
                                                        <a href="{{ route('service.edit', $client_service->id) }}" class="w-32-px h-32-px bg-success-light text-success-600 rounded-circle d-inline-flex align-items-center justify-content-center ms-1">
                                                            <iconify-icon icon="lucide:edit"></iconify-icon>
                                                        </a>
                                                        @endcan
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                            </div>
                            <div class="tab-pane fade" id="account-ledger-d" role="tabpanel"
                                aria-labelledby="account-ledger" tabindex="0">

                                <!-- Enhanced Header Section -->
                                <div class="card mb-4">
                                    <div class="card-body py-3">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <h6 class="mb-0 fw-semibold d-flex align-items-center gap-2">
                                                    <iconify-icon icon="solar:document-text-bold" class="text-info"></iconify-icon>
                                                    Account Ledger Management
                                                </h6>
                                                <small class="text-muted">Statement of accounts for this {{ $client->name }} ({{ $client->client_code }})</small>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center justify-content-end gap-3">
                                                    <button type="button" class="btn btn-success btn-sm d-flex align-items-center gap-2" onclick="exportClientLedger()">
                                                        <iconify-icon icon="hugeicons:csv-02"></iconify-icon>
                                                        Export CSV
                                                    </button>
                                                    <button type="button" class="btn btn-primary btn-sm d-flex align-items-center gap-2" id="account_settle">
                                                        <iconify-icon icon="solar:check-circle-bold"></iconify-icon>
                                                        Account Settle
                                                    </button>

                                                    <div class="input-group date_range max-w-280">
                                                        <input type="text" class="form-control" id="daterange" name="daterange" placeholder="01/04/2025 - 31/03/2026">
                                                        <div class="input-group-append">
                                                            <span class="input-group-text bg-info-100 text-info-600 border-info-100">
                                                                <iconify-icon icon="mdi:calendar" width="20" height="20"></iconify-icon>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Account Summary Cards - Exact Reference Match -->
                                <div class="row g-3 mb-4">
                                    <div class="col-md-2">
                                        <div class="summary-tile text-center p-3 bg-white rounded shadow-sm">
                                            <div class="d-flex">
                                                <iconify-icon icon="solar:wallet-bold" class="text-info mb-2 icon-size-24"></iconify-icon>
                                                <p class="text-muted mb-1 small fw-medium">ADVANCE AMOUNT</p>
                                            </div>
                                            <h6 class="mb-0 fw-bold text-info" id="advanceAmount">₹ 0</h6>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="summary-tile text-center p-3 bg-white rounded shadow-sm">
                                            <div class="d-flex">
                                                <iconify-icon icon="solar:clock-circle-bold" class="text-secondary mb-2 icon-size-24"></iconify-icon>
                                            <p class="text-muted mb-1 small fw-medium">OPENING BALANCE</p>
                                            </div>
                                            <h6 class="mb-0 fw-bold text-secondary" id="openingBalance">₹ 2,240</h6>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="summary-tile text-center p-3 bg-white rounded shadow-sm">
                                            <div class="d-flex">
                                                <iconify-icon icon="solar:document-bold" class="text-warning mb-2 icon-size-24"></iconify-icon>
                                                <p class="text-muted mb-1 small fw-medium">INVOICED AMOUNT</p>
                                            </div>
                                            <h6 class="mb-0 fw-bold text-warning" id="invoicedAmount">₹ 40,364.80</h6>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="summary-tile text-center p-3 bg-white rounded shadow-sm">
                                            <div class="d-flex">
                                                <iconify-icon icon="solar:money-bag-bold" class="text-success mb-2 icon-size-24"></iconify-icon>
                                            <p class="text-muted mb-1 small fw-medium">AMOUNT RECEIVED</p>
                                            </div>
                                            <h6 class="mb-0 fw-bold text-success" id="amountReceived">₹ 500</h6>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="summary-tile text-center p-3 bg-white rounded shadow-sm">
                                            <div class="d-flex">
                                                <iconify-icon icon="solar:danger-circle-bold" class="text-danger mb-2 icon-size-24"></iconify-icon>
                                            <p class="text-muted mb-1 small fw-medium">BALANCE DUE</p>
                                            </div>
                                            <h6 class="mb-0 fw-bold text-danger" id="balanceDue">₹ 42,104.80</h6>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="summary-tile text-center p-3 bg-white rounded shadow-sm">
                                            <div class="d-flex">
                                                <iconify-icon icon="solar:calculator-bold" class="text-info mb-2 icon-size-24"></iconify-icon>
                                            <p class="text-muted mb-1 small fw-medium">TOTAL TRANSACTIONS</p>
                                            </div>
                                            <h6 class="mb-0 fw-bold text-info" id="totalTransactions">0</h6>
                                        </div>
                                    </div>
                                </div>
                                <!-- Transaction History Section -->
                                <div class="card shadow-sm border-0">
                                    <div class="card-header bg-white border-bottom py-3">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="d-flex align-items-center gap-3">
                                                <div class="w-40-px h-40-px bg-primary-subtle rounded-circle d-flex justify-content-center align-items-center">
                                                    <iconify-icon icon="solar:list-bold" class="text-primary"></iconify-icon>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0 fw-bold text-dark">Transaction History</h6>
                                                    <small class="text-muted">Detailed account movements and balances</small>
                                                </div>
                                            </div>
                                            <div class="d-flex align-items-center gap-2">

                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body p-0">
                                        <div class="table-responsive">
                                            <table id="accountsTable" class="table table-hover mb-0 w-100-important">
                                                <thead class="bg-light">
                                                    <tr>
                                                        <th class="fw-semibold text-dark border-0 py-3 px-4">
                                                            <div class="d-flex align-items-center gap-2">
                                                                Transaction Date
                                                            </div>
                                                        </th>
                                                        <th class="fw-semibold text-dark border-0 py-3 px-4">
                                                            <div class="d-flex align-items-center gap-2">
                                                                <iconify-icon icon="solar:transfer-horizontal-bold" class="text-primary text-sm"></iconify-icon>
                                                                Transaction
                                                            </div>
                                                        </th>
                                                        <th class="fw-semibold text-dark border-0 py-3 px-4">
                                                            <div class="d-flex align-items-center gap-2">
                                                                <iconify-icon icon="solar:info-circle-bold" class="text-primary text-sm"></iconify-icon>
                                                                Details
                                                            </div>
                                                        </th>
                                                        <th class="fw-semibold text-dark border-0 py-3 px-4">
                                                            <div class="d-flex align-items-center justify-content-end gap-2">
                                                                <iconify-icon icon="solar:arrow-up-bold" class="text-warning text-sm"></iconify-icon>
                                                                Invoice Amount
                                                            </div>
                                                        </th>
                                                        <th class="fw-semibold text-dark border-0 py-3 px-4">
                                                            <div class="d-flex align-items-center justify-content-end gap-2">
                                                                <iconify-icon icon="solar:arrow-down-bold" class="text-success text-sm"></iconify-icon>
                                                                Payment
                                                            </div>
                                                        </th>
                                                        <th class="fw-semibold text-dark border-0 py-3 px-4">
                                                            <div class="d-flex align-items-center justify-content-end gap-2">
                                                                <iconify-icon icon="solar:scale-bold" class="text-info text-sm"></iconify-icon>
                                                                Running Balance
                                                            </div>
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tfoot>
                                                    <tr class="bg-light border-top">
                                                        <td colspan="3" class="text-end py-3 px-4 border-0">
                                                            <div class="d-flex align-items-center justify-content-end gap-2">
                                                                <iconify-icon icon="solar:calculator-bold" class="text-primary"></iconify-icon>
                                                                <span class="fw-bold text-dark">Period Total:</span>
                                                            </div>
                                                        </td>
                                                        <td class="py-3 px-4 border-0">
                                                            <span class="fw-bold text-warning" id="totalInvoiceAmount">₹ 42604.80</span>
                                                        </td>
                                                        <td class="py-3 px-4 border-0">
                                                            <span class="fw-bold text-success" id="totalPaymentAmount">₹ 500.00</span>
                                                        </td>
                                                        <td class="py-3 px-4 border-0">
                                                            <span class="fw-bold text-info" id="finalBalance">₹ 42104.80</span>
                                                        </td>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Mobile View for Account Ledger -->
                                <div class="d-block d-md-none">
                                    <div id="accountsGrid" class="row g-3">
                                        <!-- Mobile cards will be populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="payments-d" role="tabpanel" aria-labelledby="payments"
                                tabindex="0">

                                <!-- Enhanced Header Section -->
                                <div class="card mb-4">
                                    <div class="card-body py-3">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <h6 class="mb-0 fw-semibold d-flex align-items-center gap-2">
                                                    <iconify-icon icon="solar:wallet-money-bold" class="text-success"></iconify-icon>
                                                    Payment Management
                                                </h6>
                                                <small class="text-muted">Track all payments received from this {{ $client->name }} ({{ $client->client_code }})</small>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center justify-content-end gap-3">
                                                    <button type="button" class="btn btn-success btn-sm d-flex align-items-center gap-2" onclick="exportClientPayments()">
                                                        <iconify-icon icon="hugeicons:csv-02"></iconify-icon>
                                                        Export CSV
                                                    </button>
                                                    <button type="button" class="btn btn-primary btn-sm d-flex align-items-center gap-2" onclick="addNewPayment()">
                                                        <iconify-icon icon="solar:add-circle-bold"></iconify-icon>
                                                        Add Payment
                                                    </button>
                                                    <div class="input-group date_range max-w-280">
                                                        <input type="text" class="form-control" name="payment_daterange" id="payment_daterange" placeholder="Select date range">
                                                        <div class="input-group-append">
                                                            <span class="input-group-text bg-success-100 text-success-600 border-success-100">
                                                                <iconify-icon icon="mdi:calendar" width="20" height="20"></iconify-icon>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Enhanced Payments Table -->
                                <div class="d-none d-md-block">
                                    <div class="table-responsive">
                                        <table id="paymentsTable" class="table text-wrap w-100-important">
                                            <thead>
                                                <tr>
                                                    <th>Invoice Code</th>
                                                    <th>Payment Date</th>
                                                    <th>Payment Mode</th>
                                                    <th>Paid Amount</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                            <tfoot>
                                                <tr>
                                                    <th colspan="3" class="text-end">Total Payments</th>
                                                    <th id="totalPayment" colspan="2"><strong>₹ 0.00</strong></th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="discounts-d" role="tabpanel" aria-labelledby="discounts"
                                tabindex="0">

                                <!-- Enhanced Header Section -->
                                <div class="card mb-4">
                                    <div class="card-body py-3">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <h6 class="mb-0 fw-semibold d-flex align-items-center gap-2">
                                                    <iconify-icon icon="solar:tag-price-bold" class="text-warning"></iconify-icon>
                                                    Discount Management
                                                </h6>
                                                <small class="text-muted">Manage and track all discounts applied to this {{ $client->name }} ({{ $client->client_code }})</small>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center justify-content-end gap-3">
                                                    <button type="button" class="btn btn-success btn-sm d-flex align-items-center gap-2" onclick="exportClientDiscounts()">
                                                        <iconify-icon icon="hugeicons:csv-02"></iconify-icon>
                                                        Export CSV
                                                    </button>
                                                    @can('client-discount')
                                                        <button class="btn btn-primary btn-sm d-flex align-items-center gap-2" data-bs-toggle="modal" data-bs-target="#discountModal">
                                                            <iconify-icon icon="solar:add-circle-bold"></iconify-icon>
                                                            Apply Discount
                                                        </button>
                                                    @endcan
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-none d-md-block">
                                    <table id="discountsTable"
                                        class="table table-sm table-striped table-hover table-bordered text-wrap mt-4 w-100-important">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Discount Amount</th>
                                                <th>Reason</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="1" class="text-end">Total </th>
                                                <th id="totalDiscount" colspan="2"><strong>₹ 0.00</strong></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="invoice-history-d" role="tabpanel"
                                aria-labelledby="invoice-history" tabindex="0">

                                <!-- Enhanced Header Section -->
                                <div class="card mb-4">
                                    <div class="card-body py-3">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <h6 class="mb-0 fw-semibold d-flex align-items-center gap-2">
                                                    <iconify-icon icon="solar:bill-list-bold" class="text-primary"></iconify-icon>
                                                    Invoice Management
                                                </h6>
                                                <small class="text-muted">View and manage all invoices for this {{ $client->name }} ({{ $client->client_code }})</small>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center justify-content-end gap-3">
                                                    <button type="button" class="btn btn-success btn-sm d-flex align-items-center gap-2" onclick="exportClientInvoices()">
                                                        <iconify-icon icon="hugeicons:csv-02"></iconify-icon>
                                                        Export CSV
                                                    </button>
                                                    <button type="button" class="btn btn-primary btn-sm d-flex align-items-center gap-2" onclick="addNewInvoice()">
                                                        <iconify-icon icon="solar:add-circle-bold"></iconify-icon>
                                                        Add Invoice
                                                    </button>
                                                    <div class="input-group date_range max-w-280">
                                                        <input type="text" class="form-control" name="daterange" id="invoice_daterange" placeholder="Select date range">
                                                        <div class="input-group-append">
                                                            <span class="input-group-text bg-primary-100 text-primary-600 border-primary-100">
                                                                <iconify-icon icon="mdi:calendar" width="20" height="20"></iconify-icon>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Enhanced Table View for Desktop -->
                                <div class="d-none d-md-block">
                                    <input type="hidden" name="client" id="client" value="{{ $client->id }}">
                                    <div class="table-responsive">
                                        <table id="invoicesTable" class="table text-wrap w-100-important">
                                            <thead>
                                                <tr>
                                                    <th>Invoice Code</th>
                                                    <th>Invoice Date</th>
                                                    <th>Invoice Amount</th>
                                                    <th>Due Amount</th>
                                                    <th>Payment Status</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                            <tfoot>
                                                <tr>
                                                    <th colspan="2" class="text-end">Total</th>
                                                    <th id="totalInvoiceAmount"><strong>₹ 0.00</strong></th>
                                                    <th id="totalDueAmount"><strong>₹ 0.00</strong></th>
                                                    <th colspan="2"></th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
    <!-- Modal Start -->
    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog modal-dialog-centered">
            <div class="modal-content radius-16 bg-base">
                <div class="modal-header py-16 px-24 border border-top-0 border-start-0 border-end-0">
                    <h1 class="modal-title fs-5" id="exampleModalLabel">Assign Employee</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-24">
                    <form id="assignEmployeeForm">
                        @csrf
                        <div class="row">
                            <div class="col-12 mb-20">
                                <label class="form-label fw-semibold text-primary-light text-sm mb-8">Employee</label>
                                <select class="form-control select2" name="employee_id" id="employee_id">
                                    <option value="">Select Employee</option>
                                    @foreach ($employees as $employee)
                                        <option value="{{ $employee->id }}"
                                            {{ old('employee_id', $client->employee_id) == $employee->id ? 'selected' : '' }}>
                                            {{ $employee->emp_name }}
                                        </option>
                                    @endforeach
                                </select>
                                <input type="hidden" name="client_id" id="client_id" value="{{ $client->id }}">
                            </div>

                            <div class="d-flex align-items-center justify-content-end gap-3 mt-24">
                                <button type="reset"
                                    class="btn btn-sm btn-neutral-500 border-neutral-100 px-32 radius-8"
                                    data-bs-dismiss="modal">
                                    Cancel
                                </button>
                                <button type="submit" class="btn btn-sm btn-primary-600 px-32 radius-8">
                                    Save
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal End -->
    <!-- Discount Modal -->
    <div class="modal fade" id="discountModal" tabindex="-1" aria-labelledby="discountModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h7 class="modal-title" id="discountModalLabel"><b>Apply Discount</b></h7>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="discountForm">
                        @csrf
                        <div class="mb-3">
                            <label for="pendingAmount" class="form-label">Total Pending Amount</label>
                            <input type="text" class="form-control" id="pendingAmount"
                                value="{{ $client->pending_amount }}" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="discountAmount" class="form-label">Discount Amount<span
                                    class="text-danger-600">*</span></label>
                            <input type="number" class="form-control" id="discountAmount" name="discount_amount"
                                required>
                            <span class="text-danger d-none" id="discountError">Discount cannot exceed pending
                                amount</span>
                        </div>

                        <div class="mb-3">
                            <label for="discountRemarks" class="form-label">Remarks <span
                                    class="text-danger-600">*</span></label>
                            <textarea class="form-control" id="discountRemarks" name="discountRemarks" required></textarea>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary">Apply Discount</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

<div id="exportLoader" class="export-loader">
    <span class="spinner-border text-primary" role="status"></span>
    <strong>Exporting...</strong>
</div>
@stop
@section('script')
    <script>
        $(document).ready(function() {
            // Hide export loader on page load
            $('#exportLoader').hide();

            var invoiceTable;
            var paymentTable;
            //   $('#employee_id').select2();
            $('#employee_id').select2({
                width: '100%', // Ensure it fits the container
                placeholder: "Select an employee",
                allowClear: true
            });
            $(".form-check-input").on("change", function() {
                let isChecked = $(this).prop("checked"); // Get switch status
                let clientId = $(this).data("id"); // Get Client ID
                let status = isChecked ? 1 : 0; // 1 = Active, 0 = Inactive
                let switchElement = $(this); // Store switch reference
                let labelElement = $(this).next("label"); // Get label next to switch

                let actionText = status === 1 ? "activate" : "deactivate";
                let warningText = status === 1 ?
                    "Do you want to activate this client?" :
                    "Changing the status will also deactivate client services. Are you sure you want to proceed?";

                Swal.fire({
                    title: "Are you sure?",
                    text: warningText,
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#28a745",
                    cancelButtonColor: "#d33",
                    confirmButtonText: `Yes, ${actionText} it!`,
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: "/clients/update-status", // Your route to update status
                            type: "POST",
                            data: {
                                client_id: clientId,
                                status: status,
                                _token: $('meta[name="csrf-token"]').attr(
                                    "content") // CSRF token
                            },
                            success: function(response) {
                                Swal.fire("Updated!", "Client status has been changed.",
                                    "success");

                                // Update text dynamically
                                if (status === 1) {
                                    labelElement.text("Active");
                                } else {
                                    labelElement.text("Inactive");
                                }
                            },
                            error: function() {
                                Swal.fire("Error!", "Something went wrong.", "error");
                                switchElement.prop("checked", !
                                    isChecked); // Revert switch if error
                            }
                        });
                    } else {
                        switchElement.prop("checked", !isChecked); // Revert switch if canceled
                    }
                });
            });
        });

        $(document).ready(function() {
            var invoiceTableInitialized = false;
            var paymentsTableInitialized = false;
            var accountsTableInitialized = false;
            var discountTableInitialized = false;

            // Invoice History Tab
            $('#invoice-history').on('shown.bs.tab', function(e) {
                if (!invoiceTableInitialized) {
                    if (!$.fn.DataTable.isDataTable(
                            '#invoicesTable')) { // Check if DataTable is already initialized
                    invoiceTable=  $('#invoicesTable').DataTable({
                            processing: true,
                            serverSide: true,
                            searching: false,
                            pageLength: 25,
                            ajax: {
                                url: "{{ route('invoices.data') }}",
                                data: function(d) {
                                    d.client = $('#client').val();
                                    d.daterange = $('#invoice_daterange').val();
                                }
                            },
                            columns: [{
                                    data: 'invoice_code',
                                    name: 'invoice_code',
                                    width: '20%',
                                    render: function(data, type, row) {
                                        return `<span class="fw-medium text-primary">${data}</span>`;
                                    }
                                },
                                {
                                    data: 'invoice_date',
                                    name: 'invoice_date',
                                    width: '15%',
                                    render: function(data, type, row) {
                                        return `<span class="text-muted">${data}</span>`;
                                    }
                                },
                                {
                                    data: 'total_amount_due',
                                    name: 'total_amount_due',
                                    width: '18%',
                                    render: function(data, type, row) {
                                        return `<span class="fw-semibold text-success">₹ ${parseFloat(data).toLocaleString('en-IN', {minimumFractionDigits: 2})}</span>`;
                                    }
                                },
                                {
                                    data: 'unpaid_amount',
                                    name: 'unpaid_amount',
                                    width: '18%',
                                    render: function(data, type, row) {
                                        const amount = parseFloat(data);
                                        const colorClass = amount > 0 ? 'text-danger' : 'text-success';
                                        return `<span class="fw-semibold ${colorClass}">₹ ${amount.toLocaleString('en-IN', {minimumFractionDigits: 2})}</span>`;
                                    }
                                },
                                {
                                    data: 'invoice_status',
                                    name: 'invoice_status',
                                    width: '15%',
                                    orderable: false,
                                    searchable: false,
                                    render: function(data, type, row) {
                                        let badgeClass = '';
                                        let statusText = data;

                                        switch(data.toLowerCase()) {
                                            case 'paid':
                                                badgeClass = 'status-paid';
                                                statusText = '✅ Paid';
                                                break;
                                            case 'pending':
                                                badgeClass = 'status-pending';
                                                statusText = '⏳ Pending';
                                                break;
                                            case 'overdue':
                                                badgeClass = 'status-overdue';
                                                statusText = '🔴 Overdue';
                                                break;
                                            case 'partial':
                                                badgeClass = 'status-partial';
                                                statusText = '🟡 Partial';
                                                break;
                                            default:
                                                badgeClass = 'bg-secondary-100 text-secondary-600';
                                        }

                                        return `<span class="badge ${badgeClass}">${statusText}</span>`;
                                    }
                                },
                                {
                                    data: 'action',
                                    name: 'action',
                                    width: '14%',
                                    orderable: false,
                                    searchable: false
                                }
                            ],
                            drawCallback: function(settings) {
                                $('#clientsGrid').empty();
                            },
                            dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" +
                                "<'row'<'col-md-12'tr>>" +
                                "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>",
                            buttons: [],
                            infoCallback: function(settings, start, end, max, total, pre) {
                                return `Showing ${start} to ${end} of ${total} records`;
                            },
                            footerCallback: function(row, data, start, end, display) {
                                var api = this.api();

                                // Function to apply Indian Number Format
                                function IND_money_format(num) {
                                    return num.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
                                }

                                // Sum for column 2 (Invoice Amount)
                                var totalInvoiceAmount = api
                                    .column(2, { page: 'current' })
                                    .data()
                                    .reduce(function(a, b) {
                                        return (parseFloat(a) || 0) + (parseFloat(b) || 0);
                                    }, 0);

                                // Sum for column 3 (Due Amount)
                                var totalDueAmount = api
                                    .column(3, { page: 'current' })
                                    .data()
                                    .reduce(function(a, b) {
                                        return (parseFloat(a) || 0) + (parseFloat(b) || 0);
                                    }, 0);

                                // Update footer values with enhanced formatting
                                $('#totalInvoiceAmount').html(`<strong class="text-success">₹ ${IND_money_format(totalInvoiceAmount)}</strong>`);
                                $('#totalDueAmount').html(`<strong class="text-danger">₹ ${IND_money_format(totalDueAmount)}</strong>`);
                            }

                        });

                        invoiceTableInitialized = true; // Mark as initialized
                    }
                }
            });

            // Payments Tab
            $('#payments').on('shown.bs.tab', function(e) {
                if (!paymentsTableInitialized) {
                    if (!$.fn.DataTable.isDataTable('#paymentsTable')) { // Check if already initialized
                        paymentTable=  $('#paymentsTable').DataTable({
                            processing: true,
                            serverSide: true,
                            searching: false,
                            pageLength: 25,
                            ajax: {
                                url: "{{ route('payments.data') }}",
                                data: function(d) {
                                    d.client = $('#client').val();
                                    d.daterange = $('#payment_daterange').val();
                                }
                            },
                            columns: [
                                {
                                    data: 'invoice_code',
                                    name: 'invoice_code',
                                    width: '25%',
                                    render: function(data, type, row) {
                                        return `<span class="fw-medium text-primary">${data}</span>`;
                                    }
                                },
                                {
                                    data: 'paid_on',
                                    name: 'paid_on',
                                    width: '20%',
                                    render: function(data, type, row) {
                                        return `<span class="text-muted">${data}</span>`;
                                    }
                                },
                                {
                                    data: 'payment_mode',
                                    name: 'payment_mode',
                                    width: '20%',
                                    render: function(data, type, row) {
                                        let modeClass = '';
                                        let modeIcon = '';

                                        switch(data.toLowerCase()) {
                                            case 'cash':
                                                modeClass = 'bg-success-100 text-success-600';
                                                modeIcon = '💵';
                                                break;
                                            case 'bank':
                                            case 'bank transfer':
                                                modeClass = 'bg-info-100 text-info-600';
                                                modeIcon = '🏦';
                                                break;
                                            case 'upi':
                                                modeClass = 'bg-primary-100 text-primary-600';
                                                modeIcon = '📱';
                                                break;
                                            case 'cheque':
                                                modeClass = 'bg-warning-100 text-warning-600';
                                                modeIcon = '📝';
                                                break;
                                            default:
                                                modeClass = 'bg-secondary-100 text-secondary-600';
                                                modeIcon = '💳';
                                        }

                                        return `<span class="badge ${modeClass}">${modeIcon} ${data}</span>`;
                                    }
                                },
                                {
                                    data: 'amount',
                                    name: 'amount',
                                    width: '20%',
                                    render: function(data, type, row) {
                                        return `<span class="fw-semibold text-success">₹ ${parseFloat(data).toLocaleString('en-IN', {minimumFractionDigits: 2})}</span>`;
                                    }
                                },
                                {
                                    data: 'action',
                                    name: 'action',
                                    width: '15%',
                                    orderable: false,
                                    searchable: false
                                }
                            ],
                            drawCallback: function(settings) {
                                $('#clientsGrid').empty();
                            },
                            dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" +
                                "<'row'<'col-md-12'tr>>" +
                                "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>",
                            buttons: [],
                            infoCallback: function(settings, start, end, max, total, pre) {
                                return `Showing ${start} to ${end} of ${total} records`;
                            },
                            footerCallback: function(row, data, start, end, display) {
                                var api = this.api();

                                // Calculate total discount amount
                                var total = api
                                    .column(3, {
                                        page: 'current'
                                    })
                                    .data()
                                    .reduce(function(a, b) {
                                        return parseFloat(a) + parseFloat(b);
                                    }, 0);

                                // Format total and display in footer
                                $(api.column(3).footer()).html(
                                    `<strong>₹ ${total.toFixed(2)}</strong>`);
                            }
                        });

                        paymentsTableInitialized = true; // Mark as initialized
                    }
                }
            });

            $(function() {
                function getCurrentFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, set FY to start from April 1st of this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().month(3).startOf('month'); // April 1st current year
                        fyEnd = moment().add(1, 'year').month(2).endOf('month'); // March 31st next year
                    } else {
                        // Before April, set FY to start from last year's April 1st
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    }
                    return [fyStart, fyEnd];
                }

                function getLastFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, last FY is April 1st of last year - March 31st this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    } else {
                        // If before April, last FY is two years ago April 1st - last year March 31st
                        fyStart = moment().subtract(2, 'year').month(3).startOf(
                        'month'); // April 1st two years ago
                        fyEnd = moment().subtract(1, 'year').month(2).endOf(
                        'month'); // March 31st last year
                    }
                    return [fyStart, fyEnd];
                }

                function cb(start, end) {
                    $('#invoice_daterange').val(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
                }

                $('#invoice_daterange').daterangepicker({
                    startDate: getCurrentFinancialYear()[0], // Default to Current Financial Year
                    endDate: getCurrentFinancialYear()[1],
                    autoApply: true,
                    locale: {
                        format: 'DD/MM/YYYY'
                    },
                    ranges: {
                        'Today': [moment(), moment()],
                        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                        'This Month': [moment().startOf('month'), moment().endOf('month')],
                        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment()
                            .subtract(1, 'month').endOf('month')
                        ],
                        'Current Financial Year': getCurrentFinancialYear(),
                        'Last Financial Year': getLastFinancialYear()
                    }
                }, cb);

                // Set initial date display
                cb(getCurrentFinancialYear()[0], getCurrentFinancialYear()[1]);

            });
            $(function() {
                function getCurrentFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, set FY to start from April 1st of this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().month(3).startOf('month'); // April 1st current year
                        fyEnd = moment().add(1, 'year').month(2).endOf('month'); // March 31st next year
                    } else {
                        // Before April, set FY to start from last year's April 1st
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    }
                    return [fyStart, fyEnd];
                }

                function getLastFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, last FY is April 1st of last year - March 31st this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    } else {
                        // If before April, last FY is two years ago April 1st - last year March 31st
                        fyStart = moment().subtract(2, 'year').month(3).startOf(
                        'month'); // April 1st two years ago
                        fyEnd = moment().subtract(1, 'year').month(2).endOf(
                        'month'); // March 31st last year
                    }
                    return [fyStart, fyEnd];
                }

                function cb(start, end) {
                    $('#payment_daterange').val(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
                }

                $('#payment_daterange').daterangepicker({
                    startDate: getCurrentFinancialYear()[0], // Default to Current Financial Year
                    endDate: getCurrentFinancialYear()[1],
                    autoApply: true,
                    locale: {
                        format: 'DD/MM/YYYY'
                    },
                    ranges: {
                        'Today': [moment(), moment()],
                        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                        'This Month': [moment().startOf('month'), moment().endOf('month')],
                        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment()
                            .subtract(1, 'month').endOf('month')
                        ],
                        'Current Financial Year': getCurrentFinancialYear(),
                        'Last Financial Year': getLastFinancialYear()
                    }
                }, cb);

                // Set initial date display
                cb(getCurrentFinancialYear()[0], getCurrentFinancialYear()[1]);

            });
            // Custom search event triggers
            $('#invoice_daterange').on('apply.daterangepicker', function() {
                invoiceTable.draw();
            });
            $('#payment_daterange').on('apply.daterangepicker', function() {
                paymentTable.draw();
            });
            // Discounts Tab
            $('#discounts').on('shown.bs.tab', function(e) {
                if (!discountTableInitialized) {
                    if (!$.fn.DataTable.isDataTable('#discountsTable')) { // Check if already initialized
                        $('#discountsTable').DataTable({
                            processing: true,
                            serverSide: true,
                            searching: false,
                            pageLength: 25,
                            ajax: {
                                url: "{{ route('discounts.data') }}",
                                data: function(d) {
                                    d.client = $('#client').val();
                                }
                            },
                            columns: [{
                                    data: 'created_at',
                                    name: 'created_at'
                                },
                                {
                                    data: 'discount_amount',
                                    name: 'discount_amount',
                                    render: function(data, type, row) {
                                        return `₹ ${parseFloat(data).toFixed(2)}`;
                                    }
                                },
                                {
                                    data: 'remarks',
                                    name: 'remarks'
                                }
                            ],
                            drawCallback: function(settings) {
                                $('#discountsGrid').empty();
                            },
                            dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" +
                                "<'row'<'col-md-12'tr>>" +
                                "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>",
                            buttons: [],
                            infoCallback: function(settings, start, end, max, total, pre) {
                                return `Showing ${start} to ${end} of ${total} records`;
                            },
                            footerCallback: function(row, data, start, end, display) {
                                console.log('Footer callback executed'); // Debugging

                                var api = this.api();

                                var total = api
                                    .column(1, {
                                        page: 'current'
                                    }) // Column index 1 (discount_amount)
                                    .data()
                                    .reduce(function(a, b) {
                                        return (parseFloat(a) || 0) + (parseFloat(b) || 0);
                                    }, 0);

                                console.log('Total discount:', total); // Debugging

                                $(api.column(1).footer()).html(
                                    `<strong>₹ ${total.toFixed(2)}</strong>`);
                            }
                        });

                        discountTableInitialized = true; // Mark as initialized
                    }
                }
            });

            // Account Ledger Tab
            $('#account-ledger').on('shown.bs.tab', function(e) {
                if (!accountsTableInitialized) {
                    if (!$.fn.DataTable.isDataTable('#accountsTable')) { // Check if already initialized
                        var table = $('#accountsTable').DataTable({
                            processing: true,
                            serverSide: true,
                            searching: false,
                            pageLength: 25,
                            ajax: {
                                url: "{{ route('accounts.data') }}",
                                data: function(d) {
                                    // d.searchkey = $('#searchkey').val();
                                    d.daterange = $('#daterange').val();
                                    d.client = $('#client').val();
                                },
                                beforeSend: function() {
                                    fetchSummary(); // Fetch summary data
                                }
                            },
                            columns: [{
                                    data: 'transaction_date',
                                    name: 'transaction_date',
                                    render: function(data, type, row) {
                                        if (!data) return "";

                                        // Parse date using moment.js (ensure moment.js is included in your project)
                                        return moment(data, ['YYYY-MM-DD', 'DD-MM-YYYY', 'MM/DD/YY']).format('DD-MM-YYYY');
                                    }
                                },
                                {
                                    data: 'transaction',
                                    name: 'transaction'
                                },
                                {
                                    data: 'detials',
                                    name: 'detials'
                                },
                                {
                                    data: 'amount',
                                    name: 'amount',
                                    render: function(data, type, row) {
                                        if (!data || parseFloat(data) === 0) {
                                            return ""; // You can replace this with "N/A" or any other text
                                        }
                                        return `₹ ${parseFloat(data).toFixed(2)}`;
                                    }
                                },
                                {
                                    data: 'payments',
                                    name: 'payments',
                                    render: function(data, type, row) {
                                        if (!data || parseFloat(data) === 0) {
                                            return ""; // You can replace this with "N/A" or any other text
                                        }
                                        return `₹ ${parseFloat(data).toFixed(2)}`;
                                    }
                                },
                                {data:null, name:'balance'},
                                //here balance need to calculated based on amount and payments
                                // {
                                //     data: 'balance',
                                //     name: 'balance',
                                //     render: function(data, type, row) {
                                //         if (!data || parseFloat(data) === 0) {
                                //             return ""; // You can replace this with "N/A" or any other text
                                //         }
                                //         return `₹ ${parseFloat(data).toFixed(2)}`;
                                //     }
                                // },
                            ],
                            columnDefs: [{
                                    orderable: false,
                                    targets: '_all'
                                } // Disables sorting for all columns
                            ],
                            drawCallback: function(settings) {
                                var api = this.api();
                                var data = api.rows().data();
                                var gridContainer = $('#clientsGrid');
                                gridContainer.empty();

                                let runningBalance = 0;

                                // Loop through table rows and calculate running balance
                                api.rows({ page: 'current' }).every(function(rowIdx, tableLoop, rowLoop) {
                                    var row = this.data();
                                    var amount = parseFloat(row.amount) || 0;
                                    var payments = parseFloat(row.payments) || 0;

                                    runningBalance += (amount - payments);

                                    // Update the cell in the "balance" column (assuming index 5)
                                    $(this.node()).find('td:eq(5)').html(`₹ ${runningBalance.toFixed(2)}`);
                                });
                            }
,
                            dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" +
                                // Entries Dropdown & CSV Button
                                "<'row'<'col-md-12'tr>>" +
                                "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>", // Info at left, Pagination at right
                                buttons: [],
                            infoCallback: function(settings, start, end, max, total, pre) {
                                return `Showing ${start} to ${end} of ${total} records`;
                            },
                            footerCallback: function(row, data, start, end, display) {
                                var api = this.api();

                                // Function to calculate the total of a column
                                function calculateTotal(columnIndex) {
                                    return api
                                        .column(columnIndex, {
                                            page: 'current'
                                        })
                                        .data()
                                        .reduce(function(a, b) {
                                            return (parseFloat(a) || 0) + (parseFloat(b) || 0);
                                        }, 0);
                                }

                                // Calculate totals for Invoice Amount (column 3) and Payment (column 4)
                                var totalInvoiceAmount = calculateTotal(3);
                                var totalPaymentAmount = calculateTotal(4);
                                var finalBalance = totalInvoiceAmount - totalPaymentAmount;

                                // Update the footer totals in the custom footer
                                $('#totalInvoiceAmount').text(`₹ ${totalInvoiceAmount.toFixed(2)}`);
                                $('#totalPaymentAmount').text(`₹ ${totalPaymentAmount.toFixed(2)}`);
                                $('#finalBalance').text(`₹ ${finalBalance.toFixed(2)}`);
                            }
                        });
                        // Custom search event triggers
                        $('#daterange').on('apply.daterangepicker', function() {
                            table.draw();
                        });
                        accountsTableInitialized = true; // Mark as initialized
                    }
                }
            });
            // Function to normalize date format to DD-MM-YYYY
            function formatDate(inputDate) {
                if (!inputDate) return '';

                // If date contains '/', assume it is M/D/YY or M/D/YYYY
                if (inputDate.includes('/')) {
                    let dateParts = inputDate.split('/');
                    let month = dateParts[0], day = dateParts[1], year = dateParts[2];

                    if (year.length === 2) {
                        year = '20' + year; // Convert YY to YYYY
                    }

                    return `${('0' + day).slice(-2)}-${('0' + month).slice(-2)}-${year}`;
                }

                // If already in DD-MM-YYYY format, return as is
                if (inputDate.includes('-')) {
                    return inputDate;
                }

                return inputDate; // If format is unknown, return as is
            }

            $("#assignEmployeeForm").submit(function(e) {
                e.preventDefault();

                let client_id = $("#client_id").val();
                let employee_id = $("#employee_id").val();
                let _token = $('input[name="_token"]').val();

                $.ajax({
                    url: "{{ route('assign.employee') }}",
                    type: "POST",
                    data: {
                        client_id: client_id,
                        employee_id: employee_id,
                        _token: _token
                    },
                    success: function(response) {
                        if (response.status === "success") {
                            // Update the displayed employee name
                            $("#assigned-employee").text(response.employee_name);

                            // Show success message using SweetAlert
                            Swal.fire({
                                icon: "success",
                                title: "Success",
                                text: "Employee assigned successfully!",
                                timer: 2000,
                                showConfirmButton: false
                            });

                            // Close the modal
                            $("#exampleModal").modal("hide");
                        } else {
                            Swal.fire({
                                icon: "error",
                                title: "Error",
                                text: response.message
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            icon: "error",
                            title: "Error",
                            text: "Something went wrong!"
                        });
                    }
                });
            });
            $(function() {
                function getCurrentFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, set FY to start from April 1st of this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().month(3).startOf('month'); // April 1st current year
                        fyEnd = moment().add(1, 'year').month(2).endOf('month'); // March 31st next year
                    } else {
                        // Before April, set FY to start from last year's April 1st
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    }
                    return [fyStart, fyEnd];
                }

                function getLastFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, last FY is April 1st of last year - March 31st this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    } else {
                        // If before April, last FY is two years ago April 1st - last year March 31st
                        fyStart = moment().subtract(2, 'year').month(3).startOf(
                        'month'); // April 1st two years ago
                        fyEnd = moment().subtract(1, 'year').month(2).endOf(
                        'month'); // March 31st last year
                    }
                    return [fyStart, fyEnd];
                }

                function cb(start, end) {
                    $('#daterange').val(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
                }

                $('#daterange').daterangepicker({
                    startDate: getCurrentFinancialYear()[0], // Default to Current Financial Year
                    endDate: getCurrentFinancialYear()[1],
                    autoApply: true,
                    locale: {
                        format: 'DD/MM/YYYY'
                    },
                    ranges: {
                        'Today': [moment(), moment()],
                        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                        'This Month': [moment().startOf('month'), moment().endOf('month')],
                        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment()
                            .subtract(1, 'month').endOf('month')
                        ],
                        'Current Financial Year': getCurrentFinancialYear(),
                        'Last Financial Year': getLastFinancialYear()
                    }
                }, cb);

                // Set initial date display
                cb(getCurrentFinancialYear()[0], getCurrentFinancialYear()[1]);

                // Fetch initial summary data to populate total transactions counter
                fetchSummary();

            });

        });

        const dummyData = [{
                service_name: "Biomedical Waste",
                service_type: "Bedded",
                beds_count: 5,
                unit_price: 240,
                start_date: "2024-01-01",
                end_date: "2025-01-01",
                total_price: 1200,
                payment_cycle: "Yearly",
                status: "Active",
                comments: "Renewal due next year"
            },
            {
                service_name: "Biomedical Waste",
                service_type: "Bedded",
                beds_count: 4,
                unit_price: 200,
                start_date: "2023-09-10",
                end_date: "2024-09-10",
                total_price: 800,
                payment_cycle: "Yearly",
                status: "Completed",
                comments: "Client upgraded to premium plan"
            },
            {
                service_name: "Biomedical Waste",
                service_type: "Bedded",
                beds_count: 2,
                unit_price: 250,
                start_date: "2024-02-01",
                end_date: "2024-08-01",
                total_price: 500,
                payment_cycle: "Quarterly",
                status: "Completed",
                comments: "Ongoing risk analysis"
            },
            {
                service_name: "Biomedical Waste",
                service_type: "Bedded",
                beds_count: 10,
                unit_price: 500,
                start_date: "2023-05-15",
                end_date: "2023-11-30",
                total_price: 5000,
                payment_cycle: "One-time",
                status: "Completed",
                comments: "Project successfully delivered"
            },
            {
                service_name: "Biomedical Waste",
                service_type: "Bedded",
                beds_count: 3,
                unit_price: 100,
                start_date: "2024-04-01",
                end_date: "",
                total_price: 300,
                payment_cycle: "Monthly",
                status: "Active",
                comments: "Completed"
            },
            {
                service_name: "Biomedical Waste",
                service_type: "Bedded",
                beds_count: 6,
                unit_price: 250,
                start_date: "2023-07-10",
                end_date: "2024-07-10",
                total_price: 1500,
                payment_cycle: "Yearly",
                status: "Cancelled",
                comments: "Client moved to another provider"
            }
        ];

        function loadServiceLog() {
            let serviceLogBody = $("#serviceLogBody");
            serviceLogBody.empty();

            dummyData.forEach(service => {
                let badgeClass = service.status === "Active" ? "bg-success text-white" :
                    service.status === "Completed" ? "bg-primary text-white" :
                    "bg-danger text-white";

                let additionalDetails = "";
                if (service.service_type === "Bedded") {
                    additionalDetails = `
                    <div class="col-4 d-flex align-items-center">
                        <iconify-icon icon="mdi:bed-outline" width="20"></iconify-icon>
                        <strong class="ms-2">Units:</strong> ${service.beds_count}
                    </div>
                    <div class="col-4 d-flex align-items-center">
                        <iconify-icon icon="mdi:cash" width="20"></iconify-icon>
                        <strong class="ms-2">Unit Price:</strong> ₹${service.unit_price.toFixed(2)}
                    </div>
                `;
                } else {
                    additionalDetails = `
                    <div class="col-4 d-flex align-items-center">
                        <iconify-icon icon="mdi:label-outline" width="20"></iconify-icon>
                        <strong class="ms-2">Non-Bedded Type:</strong> ${service.non_bedded_type || "N/A"}
                    </div>
                `;
                }

                let card = `
                <div class="col-xxl-12 mb-4">
                    <div class="card shadow-none border">
                        <!-- Card Header with Service Name, Type, and Status Badge -->
                        <div class="card-header bg-light fw-bold d-flex justify-content-between align-items-center">
                            <div class=" d-flex align-items-center">
                                <iconify-icon icon="mdi:cog-outline" width="22"></iconify-icon>
                                ${service.user_label} <small class="text-muted">(${service.service_type})</small>
                            </div>
                            <span class="badge ${badgeClass}">${service.status}</span>
                        </div>

                        <!-- Card Body with Grid Layout -->
                        <div class="card-body">
                            <div class="row">
                                <div class="col-4 d-flex align-items-center">
                                    <iconify-icon icon="mdi:calendar-start" width="20"></iconify-icon>
                                    <strong class="ms-2">Start:</strong> ${service.start_date}
                                </div>
                                <div class="col-4 d-flex align-items-center">
                                    <iconify-icon icon="mdi:calendar-end" width="20"></iconify-icon>
                                    <strong class="ms-2">End:</strong> ${service.end_date || "Ongoing"}
                                </div>

                                ${additionalDetails}

                                <div class="col-4 d-flex align-items-center mt-2">
                                    <iconify-icon icon="mdi:cash" width="20"></iconify-icon>
                                    <strong class="ms-2">Total Price:</strong> ₹${service.total_price.toFixed(2)}
                                </div>
                                <div class="col-4 d-flex align-items-center mt-2">
                                    <iconify-icon icon="mdi:credit-card-outline" width="20"></iconify-icon>
                                    <strong class="ms-2">Cycle:</strong> ${service.payment_cycle}
                                </div>

                                <div class="col-12 mt-2 d-flex align-items-center">
                                    <iconify-icon icon="mdi:comment-text-outline" width="20"></iconify-icon>
                                    <strong class="ms-2">Comments:</strong> ${service.comments || "-"}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

                serviceLogBody.append(card);
            });
        }

        function fetchSummary() {
            $.ajax({
                url: "{{ route('accounts.summary') }}",
                type: "GET",
                data: {
                    daterange: $('#daterange').val(),
                    client: $('#client').val()
                },
                success: function(response) {
                    $('#dateRangeText').text(response.date_range);
                    $('#openingBalance').text('₹ ' + response.opening_balance);
                    $('#invoicedAmount').text('₹ ' + response.invoiced_amount);
                    $('#amountReceived').text('₹ ' + response.amount_received);
                    $('#balanceDue').text('₹ ' + response.balance_due);
                    $('#advanceAmount').text('₹ ' + response.advanc_amount);
                    $('#totalTransactions').text(response.total_transactions || 0);
                    if (parseFloat(response.advanc_amount) > 0) {
                        $('#account_settle').show();  // Show button
                    } else {
                        $('#account_settle').hide();  // Hide button
                    }
                }
            });
        }
        $(document).ready(loadServiceLog);

        $(document).ready(function() {
            $('#serviceLogTable').DataTable({
                pageLength: 25,
                ordering: true,
                dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" +
                    "<'row'<'col-md-12'tr>>" +
                    "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>",
                buttons: [],
                infoCallback: function(settings, start, end, max, total, pre) {
                    return `Showing ${start} to ${end} of ${total} records`;
                }
            });
        });

        function downloadCSV() {
            $('#serviceLogTable').DataTable().button(0).trigger();
        }

        $(document).ready(function() {
            $("#discountForm").on("submit", function(e) {
                e.preventDefault();

                var pendingAmount = parseFloat($("#pendingAmount").val());
                var discountAmount = parseFloat($("#discountAmount").val());
                var discountRemarks = $("#discountRemarks").val();

                if (discountAmount > pendingAmount) {
                    $("#discountError").removeClass("d-none");
                } else {
                    $("#discountError").addClass("d-none");

                    // Submit the form via AJAX (Modify URL as needed)
                    $.ajax({
                        url: "{{ route('apply.discount', $client->id) }}",
                        type: "POST",
                        data: {
                            _token: "{{ csrf_token() }}",
                            discount_amount: discountAmount,
                            remarks: discountRemarks
                        },
                        success: function(response) {
                            Swal.fire({
                                title: "Success!",
                                text: "Discount applied successfully!",
                                icon: "success",
                                timer: 2000,
                                showConfirmButton: false,
                                toast: true,
                                position: 'top-end'
                            }).then(() => {
                                location.reload(); // Reload to update the UI
                            });
                        },
                        error: function(xhr, status, error) {
                            let errorMessage = "Error applying discount.";
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMessage = xhr.responseJSON.message;
                            }
                            Swal.fire({
                                title: "Error!",
                                text: errorMessage,
                                icon: "error",
                                timer: 3000,
                                showConfirmButton: false,
                                toast: true,
                                position: 'top-end'
                            });
                        }
                    });
                }
            });
        });
        $(document).on("click", "#account_settle", function () {
            let clientId = $("#client").val(); // Get client ID from dropdown or hidden input

            if (!clientId) {
                Swal.fire("Error", "Please select a client.", "error");
                return;
            }

            Swal.fire({
                title: "Are you sure?",
                text: "This will settle outstanding invoices using the available advance amount.",
                icon: "warning",
                showCancelButton: true,
                confirmButtonText: "Yes, Settle Now",
                cancelButtonText: "Cancel",
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: "{{ route('client.settle_account') }}", // Adjust to your route
                        type: "POST",
                        data: {
                            _token: "{{ csrf_token() }}",
                            client_id: clientId,
                        },
                        beforeSend: function () {
                            $("#account_settle").prop("disabled", true).text("Processing...");
                        },
                        success: function (response) {
                            Swal.fire("Success", response.message, "success").then(() => {
                                location.reload(); // Reload to update the UI
                            });
                        },
                        error: function (xhr) {
                            Swal.fire("Error", xhr.responseJSON.message || "Something went wrong!", "error");
                            $("#account_settle").prop("disabled", false).text("Account Settle");
                        },
                    });
                }
            });
        });

        // Export functions for client view
        function exportClientServices() {
            // Show loading indicator
            $('#exportLoader').show();

            // Direct export using backend route
            var exportUrl = "{{ route('clients.export.services', $client->id) }}";
            var link = document.createElement('a');
            link.href = exportUrl;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Hide loading indicator and show success message
            setTimeout(() => {
                $('#exportLoader').hide();
                Swal.fire({
                    title: "Export Successful!",
                    text: "Client services data has been exported successfully.",
                    icon: "success",
                    timer: 2000,
                    showConfirmButton: false,
                    toast: true,
                    position: 'top-end'
                });
            }, 1000);
        }

        function exportClientInvoices() {
            // Show loading indicator
            $('#exportLoader').show();

            // Get filters
            var filters = {
                daterange: $('#invoice_daterange').val()
            };

            // Direct export using backend route
            var exportUrl = "{{ route('clients.export.invoices', $client->id) }}?" + $.param(filters);
            var link = document.createElement('a');
            link.href = exportUrl;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Hide loading indicator and show success message
            setTimeout(() => {
                $('#exportLoader').hide();
                Swal.fire({
                    title: "Export Successful!",
                    text: "Client invoices data has been exported successfully.",
                    icon: "success",
                    timer: 2000,
                    showConfirmButton: false,
                    toast: true,
                    position: 'top-end'
                });
            }, 1000);
        }

        function exportClientPayments() {
            // Show loading indicator
            $('#exportLoader').show();

            // Get filters
            var filters = {
                daterange: $('#payment_daterange').val()
            };

            // Direct export using backend route
            var exportUrl = "{{ route('clients.export.payments', $client->id) }}?" + $.param(filters);
            var link = document.createElement('a');
            link.href = exportUrl;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Hide loading indicator and show success message
            setTimeout(() => {
                $('#exportLoader').hide();
                Swal.fire({
                    title: "Export Successful!",
                    text: "Client payments data has been exported successfully.",
                    icon: "success",
                    timer: 2000,
                    showConfirmButton: false,
                    toast: true,
                    position: 'top-end'
                });
            }, 1000);
        }

        function exportClientLedger() {
            // Show loading indicator
            $('#exportLoader').show();

            // Get filters
            var filters = {
                daterange: $('#daterange').val()
            };

            // Direct export using backend route
            var exportUrl = "{{ route('clients.export.ledger', $client->id) }}?" + $.param(filters);
            var link = document.createElement('a');
            link.href = exportUrl;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Hide loading indicator and show success message
            setTimeout(() => {
                $('#exportLoader').hide();
                Swal.fire({
                    title: "Export Successful!",
                    text: "Client account ledger has been exported successfully.",
                    icon: "success",
                    timer: 2000,
                    showConfirmButton: false,
                    toast: true,
                    position: 'top-end'
                });
            }, 1000);
        }

        function exportClientDiscounts() {
            // Directly call the custom export function since table button was removed
            exportClientDiscountsData();
        }

        function exportClientDiscountsData() {
            $('#exportLoader').show();

            // Get discount data from the DataTable if available
            if ($.fn.DataTable.isDataTable('#discountsTable')) {
                var table = $('#discountsTable').DataTable();
                var data = table.rows().data().toArray();

                // Prepare CSV data
                var csvContent = "Date,Discount Amount,Remarks\n";

                data.forEach(function(row) {
                    var date = row.created_at || '';
                    var amount = row.discount_amount || '0';
                    var remarks = (row.remarks || '').replace(/"/g, '""'); // Escape quotes

                    csvContent += `"${date}","₹${parseFloat(amount).toFixed(2)}","${remarks}"\n`;
                });

                // Create and download CSV
                var blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                var link = document.createElement('a');
                var url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `client-discounts-{{ $client->id }}-${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } else {
                // If no DataTable, show message
                Swal.fire({
                    title: "No Data",
                    text: "No discount data available to export.",
                    icon: "info",
                    timer: 2000,
                    showConfirmButton: false,
                    toast: true,
                    position: 'top-end'
                });
            }

            setTimeout(() => {
                $('#exportLoader').hide();
            }, 1000);
        }

        // Service Log Action Functions
        function exportServiceLog() {
            // Use the existing export function for consistency
            exportClientServices();
        }

        // Invoice delete function with reason capture (copied from invoices list)
        function confirmDelete(url, invoiceId) {
            // 1️⃣ First, fetch payment info
            $.ajax({
                url: `/invoice-payments/${invoiceId}`,
                type: "GET",
                success: function(response) {
                    const hasDiscountPayment = response.payments.some(payment =>
                        payment.payment_mode === 'Discount');

                    if (hasDiscountPayment) {
                        Swal.fire({
                            title: "Cannot Delete!",
                            text: "This invoice has discount payments and cannot be deleted.",
                            icon: "error"
                        });
                        return;
                    }

                    // 2️⃣ Build payment list HTML
                    let paymentListHtml = '';
                    if (response.payments.length > 0) {
                        paymentListHtml = '<br><strong>This invoice has the following payments that will also be deleted:</strong><ul style="text-align: left; margin-top: 10px;">';
                        response.payments.forEach(payment => {
                            paymentListHtml += `<li>₹${parseFloat(payment.amount).toLocaleString('en-IN', {minimumFractionDigits: 2})} - ${payment.payment_mode} (${payment.paid_on})</li>`;
                        });
                        paymentListHtml += '</ul>';
                    }

                    // 3️⃣ Show main confirm dialog
                    Swal.fire({
                        title: 'Are you sure?',
                        html: `You won't be able to revert this!${paymentListHtml}`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes, continue'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // 4️⃣ Show input dialog for reason
                            Swal.fire({
                                title: 'Reason Required',
                                input: 'textarea',
                                inputLabel: 'Please enter the reason for deleting this invoice:',
                                inputPlaceholder: 'Type your reason here...',
                                inputAttributes: {
                                    'aria-label': 'Type your reason here'
                                },
                                inputValidator: (value) => {
                                    if (!value.trim()) {
                                        return 'Reason is required!';
                                    }
                                },
                                showCancelButton: true,
                                confirmButtonText: 'Submit',
                                cancelButtonText: 'Cancel'
                            }).then((reasonResult) => {
                                if (reasonResult.isConfirmed) {
                                    const reason = reasonResult.value.trim();

                                    // 5️⃣ Send AJAX delete with reason
                                    $.ajax({
                                        url: url,
                                        type: "POST",
                                        data: {
                                            _token: "{{ csrf_token() }}",
                                            reason: reason // Pass reason to backend
                                        },
                                        success: function(response) {
                                            if (response.success) {
                                                Swal.fire({
                                                    title: "Deleted!",
                                                    text: response.message,
                                                    icon: "success",
                                                    timer: 2000,
                                                    showConfirmButton: false
                                                }).then(() => {
                                                    // Refresh the invoices table instead of page reload
                                                    if (typeof invoiceTable !== 'undefined' && invoiceTable) {
                                                        invoiceTable.draw();
                                                    }
                                                });
                                            } else {
                                                Swal.fire({
                                                    title: "Error!",
                                                    text: response.message,
                                                    icon: "error",
                                                });
                                            }
                                        },
                                        error: function() {
                                            Swal.fire({
                                                title: "Error!",
                                                text: "An error occurred while deleting the invoice.",
                                                icon: "error",
                                            });
                                        }
                                    });
                                }
                            });
                        }
                    });
                },
                error: function() {
                    Swal.fire({
                        title: "Error!",
                        text: "Unable to fetch invoice payment information.",
                        icon: "error",
                    });
                }
            });
        }

        function assignNewService() {
            // Show loading state
            const assignBtn = event.target.closest('button');
            const originalText = assignBtn.innerHTML;
            assignBtn.innerHTML = '<iconify-icon icon="solar:loading-bold" class="animate-spin"></iconify-icon> Loading...';
            assignBtn.disabled = true;

            // Redirect to assign service page with client ID
            const clientId = {{ $client->id }};
            window.location.href = `/services/assign/${clientId}`;

            // Reset button state (in case redirect fails)
            setTimeout(() => {
                assignBtn.innerHTML = originalText;
                assignBtn.disabled = false;
            }, 3000);
        }

        function addNewInvoice() {
            // Show loading state
            const invoiceBtn = event.target.closest('button');
            const originalText = invoiceBtn.innerHTML;
            invoiceBtn.innerHTML = '<iconify-icon icon="solar:loading-bold" class="animate-spin"></iconify-icon> Loading...';
            invoiceBtn.disabled = true;

            // Redirect to invoice creation page with client ID
            const clientId = {{ $client->id }};
            window.location.href = `/invoices/add/${clientId}`;

            // Reset button state (in case redirect fails)
            setTimeout(() => {
                invoiceBtn.innerHTML = originalText;
                invoiceBtn.disabled = false;
            }, 3000);
        }

        function addNewPayment() {
            // Show loading state
            const paymentBtn = event.target.closest('button');
            const originalText = paymentBtn.innerHTML;
            paymentBtn.innerHTML = '<iconify-icon icon="solar:loading-bold" class="animate-spin"></iconify-icon> Loading...';
            paymentBtn.disabled = true;

            // Redirect to payment creation page with client ID
            const clientId = {{ $client->id }};
            window.location.href = `/payments/add/${clientId}`;

            // Reset button state (in case redirect fails)
            setTimeout(() => {
                paymentBtn.innerHTML = originalText;
                paymentBtn.disabled = false;
            }, 3000);
        }

    </script>
@stop
