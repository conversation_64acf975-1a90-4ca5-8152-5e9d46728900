@extends('layouts.master')
@section('title', 'Clients Edit- Paidash')

@section('content')
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">Edit Client</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="/clients" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="flowbite:users-group-outline" class="icon text-lg"></iconify-icon>
                        Clients
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">Edit Client</li>
            </ul>
        </div>

        <div class="card">
            <div class="card-body">
                <h6 class="mb-4 text-xl">Fill the required Details to update client</h6>
                <!-- <p class="text-neutral-500">Fill up your details and proceed next steps.</p> -->
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
                <!-- Form Wizard Start -->
                <div class="form-wizard">
                    <form action="{{ route('clients.update', $client->id) }}" method="POST" id="form"
                        enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <fieldset class="wizard-fieldset show">
                            <div class="row gy-3">
                                <div class="col-sm-12">
                                    <label class="form-label">Client Logo</label>
                                    <div class="position-relative">
                                        <div class="upload-image-wrapper d-flex align-items-center gap-3">
                                            <div
                                                class="uploaded-img position-relative h-120-px w-120-px border input-form-light radius-8 overflow-hidden border-dashed bg-neutral-50
                                            {{ $client->logo ? '' : 'd-none' }}">
                                                <button type="button"
                                                    class="uploaded-img__remove position-absolute top-0 end-0 z-1 text-2xxl line-height-1 me-8 mt-8 d-flex">
                                                    <iconify-icon icon="radix-icons:cross-2"
                                                        class="text-xl text-danger-600"></iconify-icon>
                                                </button>
                                                <img id="uploaded-img__preview" class="w-100 h-100 object-fit-cover"
                                                    src="{{ $client->logo ? asset('storage/' . $client->logo) : asset('assets/images/user.png') }}"
                                                    alt="image">
                                            </div>

                                            <label
                                                class="upload-file h-120-px w-120-px border input-form-light radius-8 overflow-hidden border-dashed bg-neutral-50 bg-hover-neutral-200 d-flex align-items-center flex-column justify-content-center gap-1"
                                                for="upload-file">
                                                <iconify-icon icon="solar:camera-outline"
                                                    class="text-xl text-secondary-light"></iconify-icon>
                                                <span class="fw-semibold text-secondary-light">Upload</span>
                                                <input id="upload-file" type="file" hidden name="logo">
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">Client Name<span class="text-danger-600">*</span></label>
                                    <input type="text" class="form-control wizard-required1" name="name"
                                        placeholder="Enter Client Name" value="{{ old('name', $client->name) }}">
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">Business Name<span class="text-danger-600">*</span></label>
                                    <input type="text" class="form-control wizard-required1" name="business_name"
                                        placeholder="Enter Business Name"
                                        value="{{ old('business_name', $client->business_name) }}">
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">Phone<span class="text-danger-600">*</span></label>
                                    <input type="text" class="form-control wizard-required1" placeholder="Phone"
                                        name="phone" value="{{ old('phone', $client->phone) }}">
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">Secondary Phone</label>
                                    <input type="text" class="form-control" placeholder="Secondary Phone (Optional)"
                                        name="secondary_phone" value="{{ old('secondary_phone', $client->secondary_phone ?? '') }}">
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">Email<span class="text-danger-600">*</span></label>
                                    <input type="email" class="form-control wizard-required1" placeholder="Enter Email"
                                        name="email" value="{{ old('email', $client->email) }}">
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">Secondary Email</label>
                                    <input type="email" class="form-control" placeholder="Secondary Email (Optional)"
                                        name="secondary_email" value="{{ old('secondary_email', $client->secondary_email ?? '') }}">
                                </div>

                                <div class="col-12">
                                    <label class="form-label">Area / Street<span class="text-danger-600">*</span></label>
                                    <textarea name="address" class="form-control wizard-required" rows="4" placeholder="Enter a Area / Street...">{{ old('address', $client->address) }}</textarea>
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">City<span class="text-danger-600">*</span></label>
                                    <input type="text" class="form-control wizard-required" placeholder="Enter City"
                                        name="city" value="{{ old('city', $client->city) }}">
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">Area<span class="text-danger-600">*</span></label>
                                    <input type="text" class="form-control wizard-required" placeholder="Enter Area"
                                        name="area" value="{{ old('area', $client->area) }}">
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">State<span class="text-danger-600">*</span></label>
                                    <select class="form-control wizard-required select2" name="state_id">
                                        @foreach ($states as $state)
                                            <option value="{{ $state->id }}"
                                                {{ old('state_id', $client->state_id) == $state->id ? 'selected' : '' }}>
                                                {{ $state->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">District<span class="text-danger-600">*</span></label>
                                    <select class="form-control wizard-required select2" name="district_id"
                                        id="district_id">
                                        @foreach ($districts as $district)
                                            <option value="{{ $district->id }}"
                                                {{ old('district_id', $client->district_id) == $district->id ? 'selected' : '' }}>
                                                {{ $district->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">Zipcode<span class="text-danger-600">*</span></label>
                                    <input type="number" class="form-control wizard-required"
                                        placeholder="Enter Zipcode" name="pincode"
                                        value="{{ old('pincode', $client->pincode) }}">
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">GST No.</label>
                                    <input type="text" class="form-control wizard-required"
                                        placeholder="Enter GST No." name="gst"
                                        value="{{ old('gst', $client->gst) }}">
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">Pancard No.</label>
                                    <input type="text" class="form-control wizard-required"
                                        placeholder="Enter Pancard No." name="pan"
                                        value="{{ old('pan', $client->pan) }}">
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">HCF No.<span class="text-danger-600">*</span></label>
                                    <input type="text" class="form-control wizard-required"
                                        placeholder="Enter HCF No." name="hcf_no"
                                        value="{{ old('hcf_no', $client->hcf_no) }}">
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">HCF Type<span class="text-danger-600">*</span></label>
                                    <input type="text" class="form-control wizard-required" placeholder="HCF Type"
                                        name="hcf_type" value="{{ old('hcf_type', $client->hcf_type) }}">
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">Latitude - Longitude</label>
                                    <input type="text" class="form-control wizard-required"
                                        placeholder="latitude - longitude" name="lang_lat"
                                        value="{{ old('lang_lat', $client->lang_lat) }}">
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label">Advance Amount</label>
                                    <input type="text" class="form-control wizard-required"
                                        placeholder="Advance Amount" name="advance_amount"
                                        value="{{ old('advance_amount', $client->advance_amount) }}">
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label">Client Type<span class="text-danger-600">*</span></label>
                                    <select class="form-control wizard-required select2" id="client_type" name="client_type">
                                        <option value="">Select Client Type</option>
                                        <option value="1" {{ old('client_type', $client->client_type) == 1 ? 'selected' : '' }}>Private</option>
                                        <option value="2" {{ old('client_type', $client->client_type) == 2 ? 'selected' : '' }}>Government</option>
                                    </select>
                                </div>

                                <div class="col-12">
                                    <label class="form-label">Description<span class="text-danger-600">*</span></label>
                                    <textarea class="form-control" rows="4" placeholder="Enter Description" name="description">{{ old('description', $client->description) }}</textarea>
                                </div>

                                <div class="form-group d-flex align-items-center justify-content-end gap-8">
                                    <a href="/clients" class="btn btn-danger-500 border-neutral-100 px-32">Cancel</a>
                                    <button type="submit" class="btn btn-primary-600 px-32">Update</button>
                                </div>
                            </div>
                        </fieldset>
                    </form>

                </div>
                <!-- Form Wizard End -->
            </div>
        </div>

    </div>
@stop

@section('script')
    <script>
        $(document).ready(function() {
            // Initialize Select2 for State
            $('select[name="state_id"]').select2({
                placeholder: 'Select State',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        return {
                            get_type: 4,
                            datafrom: "states",
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });
            // Initialize Select2 for districts
            $('select[name="district_id"]').select2({
                placeholder: 'Select District',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        let stateId = $('select[name="state_id"]').val();
                        if (!stateId) {
                            // Show error message if state is not selected
                            Swal.fire({
                                icon: 'error',
                                title: 'Validation Error',
                                text: 'Please select a state first!',
                            });
                            return false; // Prevent the request from being sent
                        }
                        return {
                            get_type: 4,
                            datafrom: "districts",
                            state_id: stateId, // Send selected state_id
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });

            // Prevent selecting district without state
            $('select[name="district_id"]').on('select2:opening', function(e) {
                let stateId = $('select[name="state_id"]').val();
                if (!stateId) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Validation Error',
                        text: 'Please select a state first!',
                    });
                    e.preventDefault(); // Prevent opening district dropdown
                }
            });


            // Initialize jQuery validation
            $("form").validate({
                ignore: [], // Ensures Select2 fields are not ignored
                rules: {
                    name: {
                        required: true
                    },
                    business_name: {
                        required: true
                    },
                    phone: {
                        required: true,
                        digits: true,
                        minlength: 10,
                        maxlength: 10
                    },
                    secondary_phone: {
                        digits: true,
                        minlength: 10,
                        maxlength: 10
                    },
                    email: {
                        required: true,
                        email: true
                    },
                    secondary_email: {
                        email: true
                    },
                    address: {
                        required: true
                    },
                    city: {
                        required: true
                    },
                    area: {
                        required: true
                    },
                    state_id: {
                        required: true
                    }, // Required Select2 field
                    district_id: {
                        required: true
                    },
                    pincode: {
                        required: true,
                        digits: true,
                        min: 0
                    },
                    hcf_no: {
                        required: true
                    },
                    hcf_type: {
                        required: true
                    },
                    description: {
                        required: true
                    },
                    client_type: {
                        required: true
                    }
                },
                messages: {
                    name: "Client name is required",
                    business_name: "Business name is required",
                    phone: {
                        required: "Phone number is required",
                        digits: "Only numbers are allowed",
                        minlength: "Phone number must be 10 digits",
                        maxlength: "Phone number must be 10 digits"
                    },
                    secondary_phone: {
                        digits: "Only numbers are allowed",
                        minlength: "Phone number must be 10 digits",
                        maxlength: "Phone number must be 10 digits"
                    },
                    email: {
                        required: "Email is required",
                        email: "Enter a valid email"
                    },
                    secondary_email: {
                        email: "Enter a valid email"
                    },
                    address: "Address is required",
                    city: "City is required",
                    area: "Area is required",
                    state_id: "State selection is required",
                    district_id: "District selection is required",
                    pincode: {
                        required: "Zipcode is required",
                        digits: "Enter a valid Zipcode",
                        min: "Zipcode cannot be negative."
                    },
                    hcf_no: "HCF No. is required",
                    hcf_type: {
                        required: "HCF Type is required"
                    },
                    description: "Description is required",
                    client_type: "Client type is required"
                },
                errorPlacement: function(error, element) {
                    // Handle Select2 validation error placement
                    if (element.hasClass("select2-hidden-accessible")) {
                        error.insertAfter(element.next('.select2-container'));
                    } else {
                        error.insertAfter(element);
                    }
                },
                highlight: function(element) {
                    $(element).addClass("is-invalid");
                },
                unhighlight: function(element) {
                    $(element).removeClass("is-invalid");
                }
            });

            // Ensure validation triggers on Select2 field change
            $('select[name="state_id"]').change(function() {
                $('#district_id').html('<option value="">Select District</option>').val(null).trigger(
                    'change.select2');
            });



            // Trigger validation on blur for all fields
            $(document).on("focusout", "input, select, textarea", function() {
                var form = $(this).closest("form");
                if (form.data("validator")) { // Check if the form has been initialized with validate()
                    $(this).valid();
                }
            });

        });

        // =============================== Upload Single Image js start here ================================================
        const fileInput = document.getElementById("upload-file");
        const imagePreview = document.getElementById("uploaded-img__preview");
        const uploadedImgContainer = document.querySelector(".uploaded-img");
        const removeButton = document.querySelector(".uploaded-img__remove");

        fileInput.addEventListener("change", (e) => {
            if (e.target.files.length) {
                const src = URL.createObjectURL(e.target.files[0]);
                imagePreview.src = src;
                uploadedImgContainer.classList.remove('d-none');
            }
        });
        removeButton.addEventListener("click", () => {
            imagePreview.src = "";
            uploadedImgContainer.classList.add('d-none');
            fileInput.value = "";
        });
        // =============================== Upload Single Image js End here ================================================
    </script>
@stop
@section('css')
    <style>
        .error-border {
            border: 1px solid #dc3545 !important;
            /* Bootstrap red */
            border-radius: 5px;
        }
    </style>
@stop
