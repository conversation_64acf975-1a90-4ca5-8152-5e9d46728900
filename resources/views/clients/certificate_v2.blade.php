<!DOCTYPE html>
<!-- =============================================================
     CERTIFICATE OF MEMBERSHIP – Table-only layout (no watermark)
     Cleaner visual hierarchy, lighter borders, centred header block
     ============================================================= -->
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Certificate - {{ config('company.details.legal_name') }}</title>
  <style>
    /* ---------- PRINT SETTINGS ---------- */
    @page { size: A4 landscape; margin: 0; }

    /* ---------- BASE ---------- */
    body {
      margin: 0; padding: 0;
      font-family: "Poppins", Arial, sans-serif;
      font-size: 14px; line-height: 1.55;
      background: #ffffff; color:#303030;
    }

    table { width:100%; border-collapse:collapse; }
    td, th { vertical-align:top; padding:0; }

    /* ---------- OUTER BORDER ---------- */
    .page-wrapper      { width:100%; height:100vh; }
    .page-wrapper > td { border:2px solid #28a745; border-radius:6px; padding:12mm; }

    /* ---------- HEADER ---------- */
    .logo        { width: 90px; }
    .cert-title  { font-size:28px; font-weight:700; color:#2c3e50; margin:0; letter-spacing:0.5px; }
    .cert-sub    { font-size:17px; margin:4px 0 0 0; color:#5c5c5c; }
    .cert-num    { font-size:15px; background:#f8f9fa; border:1px solid #d0d0d0; padding:5px 10px; border-radius:4px; display:inline-block; }
    .qr-code     { width:85px; height:85px; border:1px solid #d0d0d0; border-radius:4px; }
    .qr-text     { font-size:10px; color:#666; margin-top:2px; }

    /* ---------- BODY TEXT ---------- */
    .intro       { text-align:center; font-size:16px; margin:22px 0; position:relative; padding:0 30px; }
    .intro:before, .intro:after {
      content:"\201C"; font-size:70px; color:#28a745; opacity:.12; position:absolute;
    }
    .intro:before { left:-22px; top:-26px; }
    .intro:after  { content:"\201D"; right:-22px; bottom:-46px; }

    /* ---------- CLIENT INFO BOX ---------- */
    .client-box  { width:82%; margin:0 auto; border:1px solid #d0d0d0; border-radius:6px; background:#f9fafb; }
    .left-bar    { width:4px; background:#28a745; border-radius:6px 0 0 6px; }
    .client-inner td{ padding:8px 14px; }
    .client-name { font-size:20px; font-weight:600; }

    /* ---------- VALIDITY GRID ---------- */
    .val-table   { width:70%; margin:26px auto; }
    .val-table th{ background:#e9f3ed; color:#2c3e50; font-weight:600; padding:8px; border:1px solid #c6decf; }
    .val-table td{ text-align:center; padding:10px 8px; border:1px solid #c6decf; }

    /* ---------- SIGNATURE BLOCK ---------- */
    .sign-wrapper{ width:100%; margin-top:34px; }
    .sign-line   { border-top:1.2px solid #333; width:220px; margin:0 0 6px auto; }
    .sign-text   { text-align:right; font-weight:600; }
    .sign-company{ text-align:right; font-size:13px; color:#666; }

    /* ---------- FOOTER & META ---------- */
    .track-info  { position:absolute; bottom:8mm; left:12mm; font-size:9px; color:#888; line-height:1.4; }
    .footer      { position:absolute; bottom:8mm; left:0; right:0; text-align:center; font-size:11px; color:#666; line-height:1.4; }
  </style>
</head>
<body>
  <!-- FULL-PAGE TABLE HOLDS EVERYTHING INSIDE GREEN BORDER -->
  <table class="page-wrapper"><tr><td>

    <!-- ====================== HEADER ====================== -->
    <table style="margin-bottom:18px;">
      <tr>
        <!-- LEFT: logo -->
        <td style="width:20%; text-align:left;">
          @php
            $logoPath = isset($print)
              ? asset('storage/'.$company_details['logo'])
              : public_path('storage/'.$company_details['logo']);
          @endphp
          <img src="{{ $logoPath }}" alt="Logo" class="logo" />
        </td>

        <!-- CENTER: title & subtitle (centred) -->
        <td style="width:60%; text-align:center;">
          <h1 class="cert-title">Certificate of Membership</h1>
          <h2 class="cert-sub">Bio-Medical Waste Management Services</h2>
        </td>

        <!-- RIGHT: certificate no & QR -->
        <td style="width:20%; text-align:right;">
          <span class="cert-num">Certificate No: {{ $client->client_code }}</span><br/>
          <img src="{{ app()->make(App\Http\Controllers\ClientController::class)->verify_certificate($client->id) }}" class="qr-code" alt="QR" /><br/>
          <span class="qr-text">Scan to verify</span>
        </td>
      </tr>
    </table>

    <!-- ====================== CERTIFICATE INTRO TEXT ====================== -->
    <p class="intro">
      This is to certify that the following healthcare institution is an authorized member of our Bio-Medical Waste Management Services:
    </p>

    <!-- ====================== CLIENT INFO ====================== -->
    <table class="client-box"><tr>
      <td class="left-bar"></td>
      <td>
        <table class="client-inner" style="width:100%;">
          <tr>
            <td class="client-name">{{ $client->business_name }}</td>
            <td style="text-align:right;">Client ID: {{ $client->client_code }}</td>
          </tr>
          <tr>
            <td> {{ $client->address }}, {{ $client->city }}, {{ $client->district->name }}</td>
            <td style="text-align:right;"> HCF No: {{ $client->hcf_no }}</td>
          </tr>
        </table>
      </td>
    </tr></table>

    <!-- ====================== DESCRIPTION ====================== -->
    <p style="margin:24px 12px; text-align:center;">
      The above-mentioned institution is a registered member of <strong>{{ config('company.details.legal_name') }}</strong> and is
      authorized to receive Bio-Medical Waste Management Services as per the guidelines of the Central Pollution Control Board (CPCB) and State Pollution Control Board (SPCB).
    </p>

    <!-- ====================== VALIDITY GRID ====================== -->
    <table class="val-table">
      <tr>
        <th>Validity</th>
        @if($client_service->service_id == 1 || $client_service->service_id == 4)
        <th>Total Bed Strength</th>
        @else
        <th>Service for </th>
        @endif
      </tr>
      <tr>
        <td>{{ $validity_date ? date('d M Y', strtotime($validity_date)) : 'N/A' }}</td>
        @if($client_service->service_id == 1 || $client_service->service_id == 4)
        <td>{{ $client_service->beds_count ?? 0 }}</td>
        @elseif($client_service->service_id == 2)
        <td>{{ $client_service->non_bedded_type ?? 'N/A' }}</td>
        @else
        <td>Pharma</td>
        @endif

      </tr>
    </table>

    <!-- ====================== SIGNATURE ====================== -->
    <table class="sign-wrapper">
      <tr>
        <td></td>
        <td style="width:260px;">
          @if(!empty($company_details['auth_sign']))
            @php
              $signPath = isset($print)
                ? asset('storage/'.$company_details['auth_sign'])
                : public_path('storage/'.$company_details['auth_sign']);
            @endphp
            <img src="{{ $signPath }}" alt="Signature" style="width:110px; display:block; margin-left:auto;" />
          @endif
          <div class="sign-line"></div>
          <div class="sign-text">Authorized Signatory</div>
          <div class="sign-company">{{ config('company.details.legal_name') }}</div>
        </td>
      </tr>
    </table>

    <!-- ====================== FOOTERS ====================== -->
    <div class="track-info">
      Printed by: {{ Auth::user()->name ?? 'System' }} (ID: {{ Auth::user()->id ?? 'N/A' }})<br/>
      Printed on: {{ date('d-m-Y H:i:s') }}
    </div>

    <div class="footer">
      <div>{{ $company_details['address'] ?? '' }}</div>
      <div>Phone: {{ $company_details['phone'] ?? '' }} | Email: {{ $company_details['email'] ?? '' }}</div>
      <div>&copy; {{ date('Y') }} {{ config('company.details.legal_name') }}. All rights reserved.</div>
    </div>

  </td></tr></table>
</body>
</html>
