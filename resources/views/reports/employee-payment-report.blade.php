@extends('layouts.master')
@section('title', 'Employee Report - Paidash')
@section('content')
<div class="dashboard-main-body">
    {{-- @php
    $payment_modes = ['cash', 'online', 'cheque', 'net_banking']; // Make sure these match the JSON keys
@endphp --}}

@if(session('success'))
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('success') }}</span>
        </div>
        <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
            <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
        </button>
    </div>
@endif
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Employee Payment Report</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Employee Payment Report</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
            <div class="d-flex flex-wrap align-items-center gap-3 w-25 w-mb-100">
                <div class="icon-field w-100">
                    <input type="text" name="searchkey" class="form-control form-control-sm w-100" placeholder="Search Employee" id="searchkey">
                    <span class="icon">
                        <iconify-icon icon="ion:search-outline"></iconify-icon>
                    </span>
                </div>
            </div>
            <div class="d-flex flex-no-wrap align-items-center gap-3">
                <div class="input-group date_range">
                    <input type="text" class="form-control" name="daterange" id="daterange">
                    <div class="input-group-append">
                        <span class="input-group-text" id="basic-addon2">
                            <iconify-icon icon="mdi:calendar" width="24" height="24"></iconify-icon>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <table id="employeesPaymentTable" class="table table-bordered">
                <thead>
                    <tr>
                        <th>Employee</th>
                        @foreach ($payment_modes as $mode)
                            <th>{{ ucfirst($mode) }}</th>
                        @endforeach
                        <th>Total</th>
                    </tr>
                </thead>
                <tfoot>
                    <tr>
                        <th>Total</th>
                        @foreach ($payment_modes as $mode)
                            <th></th>
                        @endforeach
                        <th></th>
                    </tr>
                </tfoot>
            </table>

        </div>
    </div>
</div>
@stop
@section('script')
<script>

    $(document).ready(function () {
        var table = $('#employeesPaymentTable').DataTable({
            processing: true,
            serverSide: true,
            searching: false,
            pageLength: 25,
            ordering: false,
            ajax: {
                url: "{{ route('reports.getEmployeePaymentReport') }}",
                data: function (d) {
                    d.daterange = $('#daterange').val();
                    d.searchkey = $('#searchkey').val();
                }
            },
            columns: [
                @php //$paymentModes = session('payment_modes') ?? [];
                @endphp

                {
                    data: 'employee_name',
                    name: 'employee_name'
                },
                @foreach ($paymentModes as $key => $label)
                {
                    data: '{{ $key }}',
                    name: '{{ $key }}',
                    className: 'text-end',
                    render: function (data, type, row) {
                        if (!data) return '₹ 0';

                        switch (type) {
                            case 'display':
                            case 'filter':
                                return data.display ?? '₹ 0';
                            case 'sort':
                                return data.raw ?? 0;
                            default:
                                return data.raw ?? 0;
                        }
                    }
                },
                @endforeach
                {
                    data: 'total',
                    name: 'total',
                    className: 'text-end',
                    render: function (data, type, row) {
                        if (!data) return '₹ 0';

                        switch (type) {
                            case 'display':
                            case 'filter':
                                return data.display ?? '₹ 0';
                            case 'sort':
                                return data.raw ?? 0;
                            default:
                                return data.raw ?? 0;
                        }
                    }
                }



            ],
            dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" +
                 "<'row'<'col-md-12'tr>>" +
                 "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>",
            buttons: [
                {
                    text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
                    className: 'btn btn-success btn-sm bg-success-500',
                    action: function (e, dt, node, config) {
                        exportEmployeePaymentsReport();
                    }
                }
            ],
            footerCallback: function(row, data, start, end, display) {
                var api = this.api();

                function formatCurrency(num) {
                    return num.toLocaleString('en-IN', {
                        style: 'currency',
                        currency: 'INR',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    });
                }

                @foreach ($payment_modes as $index => $mode)
                @php
                    $safeVar = \Illuminate\Support\Str::camel($mode); // e.g., "netBanking"
                @endphp
                var total{{ $safeVar }} = api
                    .column({{ $index + 1 }}, { page: 'current' })
                    .data()
                    .reduce(function(a, b) {
                        var val = 0;

                        if (b && typeof b === 'object' && b.raw !== undefined) {
                            val = parseFloat(b.raw);
                        } else if (!isNaN(b)) {
                            val = parseFloat(b);
                        }

                        return (a || 0) + (val || 0);
                    }, 0);


                $(api.column({{ $index + 1 }}).footer()).html(`<strong>${formatCurrency(total{{ $safeVar }})}</strong>`);
            @endforeach


                var totalOverall = api
                    .column({{ count($payment_modes) + 1 }}, { page: 'current' })
                    .data()
                    .reduce(function(a, b) {
                        var val = (typeof b === 'object' && b.raw !== undefined) ? parseFloat(b.raw) : parseFloat(b);
                        return (a || 0) + (val || 0);
                    }, 0);

                $(api.column({{ count($payment_modes) + 1 }}).footer()).html(`<strong>${formatCurrency(totalOverall)}</strong>`);
            }
        });

        $('#daterange').on('apply.daterangepicker', function () {
            table.draw();
        });
        $("#searchkey").on("keyup", function () {
            table.draw();
        });
    });



    var start = moment().subtract(29, 'days');
    var end = moment();
    function cb(start, end) {
        $('#daterange').val(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
    }
    $('#daterange').daterangepicker({
        startDate: start,
        endDate: end,
        autoApply: true,
        locale: { format: 'DD/MM/YYYY' },
        ranges: {
           'Today': [moment(), moment()],
           'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
           'Last 7 Days': [moment().subtract(6, 'days'), moment()],
           'Last 30 Days': [moment().subtract(29, 'days'), moment()],
           'This Month': [moment().startOf('month'), moment().endOf('month')],
           'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        }
    }, cb);

    // Export function with all filters
    function exportEmployeePaymentsReport() {
        // Show loader
        $('#exportLoader').show();

        // Get current filter values
        var filters = {
            searchkey: $('#searchkey').val(),
            daterange: $('#daterange').val()
        };

        // Build export URL with filters
        var exportUrl = "{{ route('reports.employee.payments.export') }}?" + $.param(filters);

        // Create temporary link and trigger download
        var link = document.createElement('a');
        link.href = exportUrl;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Hide loader after a short delay
        setTimeout(() => {
            $('#exportLoader').hide();
        }, 1000);
    }
</script>
@stop
