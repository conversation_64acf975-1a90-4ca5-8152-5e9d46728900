@extends('layouts.master')
@section('title', 'Employee Report - Paidash')
@section('content')
<div class="dashboard-main-body">
@if(session('success'))
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('success') }}</span>
        </div>
        <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
            <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
        </button>
    </div>
@endif
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Employee Report</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Employee Report</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
            <div class="d-flex flex-wrap align-items-center gap-3 w-25 w-mb-100">
                <div class="icon-field w-100">
                    <input type="text" name="searchkey" class="form-control form-control-sm w-100" placeholder="Search Employee" id="searchkey">
                    <span class="icon">
                        <iconify-icon icon="ion:search-outline"></iconify-icon>
                    </span>
                </div>
            </div>
            <div class="d-flex flex-no-wrap align-items-center gap-3">
                <div class="input-group date_range">
                    <input type="text" class="form-control" name="daterange" id="daterange">
                    <div class="input-group-append">
                        <span class="input-group-text" id="basic-addon2">
                            <iconify-icon icon="mdi:calendar" width="24" height="24"></iconify-icon>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <table id="employeesTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                <thead>
                    <tr>
                        <th>Employee Name</th>
                        <th>Total Clients</th>
                        <th>Total Invoice Amount</th>
                        <th>Total Paid Amount</th>
                        <th>Paid %</th>
                        <th>Pending Amount</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tfoot>
                    <tr>
                        <th colspan="2" class="text-end">Total</th>
                        <th id="totalInvoice"> <strong>₹ 0.00</strong></th>
                        <th id="totalPaid"> <strong>₹ 0.00</strong></th>
                        <th id="totalPaidPercent"> <strong>0%</strong></th>
                        <th id="totalPending" colspan="2"> <strong>₹ 0.00</strong></th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>
@stop
@section('script')
<script>
    $(document).ready(function () {
        var table = $('#employeesTable').DataTable({
            processing: true,
            serverSide: true,
            searching: false,
            pageLength: 25,
            ajax: {
                url: "{{ route('reports.getEmployeeReport') }}",
                data: function (d) {
                    d.searchkey = $('#searchkey').val();
                    d.daterange = $('#daterange').val();
                }
            },
            columns: [
                { data: 'employee_name', name: 'employee_name' },
                { data: 'total_clients', name: 'total_clients' },
                { data: 'total_invoice_amount', name: 'total_invoice_amount', render: { _: 'display', sort: 'raw' } },
                { data: 'total_paid_amount', name: 'total_paid_amount', render: { _: 'display', sort: 'raw' } },
                { data: 'percentage_paid', name: 'percentage_paid', render: { _: 'display', sort: 'raw' }
                },
                { data: 'pending_amount', name: 'pending_amount', render: { _: 'display', sort: 'raw' } },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" +
                 "<'row'<'col-md-12'tr>>" +
                 "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>",
            buttons: [
                {
                    text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
                    className: 'btn btn-success btn-sm bg-success-500',
                    action: function (e, dt, node, config) {
                        exportEmployeeReport();
                    }
                }
            ],
            footerCallback: function(row, data, start, end, display) {
                var api = this.api();
                function IND_money_format(num) {
                    return num.toLocaleString('en-IN', { minimumFractionDigits: 0, maximumFractionDigits: 0 });
                }
                function calculateTotal(columnIndex) {
                    return api
                        .column(columnIndex, { page: 'current' })
                        .data()
                        .reduce(function(a, b) {
                            var value = typeof b === 'object' ? parseFloat(b.raw) : parseFloat(b);
                            return (a || 0) + (value || 0);
                        }, 0);
                }
                var totalInvoice = calculateTotal(2);
                var totalPaid = calculateTotal(3);
                var totalPending = calculateTotal(5);
                var paidPercent = totalInvoice > 0 ? ((totalPaid / totalInvoice) * 100).toFixed(2) + '%' : '0%';
                $(api.column(2).footer()).html(`<strong>₹ ${IND_money_format(totalInvoice)}</strong>`);
                $(api.column(3).footer()).html(`<strong>₹ ${IND_money_format(totalPaid)}</strong>`);
                $(api.column(4).footer()).html(`<strong>${paidPercent}</strong>`);
                $(api.column(5).footer()).html(`<strong>₹ ${IND_money_format(totalPending)}</strong>`);
            }
        });
        $('#searchkey').on("keyup", function () { table.draw(); });
        $('#daterange').on('apply.daterangepicker', function () { table.draw(); });
    });
    var start = moment().subtract(29, 'days');
    var end = moment();
    function cb(start, end) {
        $('#daterange').val(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
    }
    $('#daterange').daterangepicker({
        startDate: start,
        endDate: end,
        autoApply: true,
        locale: { format: 'DD/MM/YYYY' },
        ranges: {
           'Today': [moment(), moment()],
           'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
           'Last 7 Days': [moment().subtract(6, 'days'), moment()],
           'Last 30 Days': [moment().subtract(29, 'days'), moment()],
           'This Month': [moment().startOf('month'), moment().endOf('month')],
           'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        }
    }, cb);

    // Export function with all filters
    function exportEmployeeReport() {
        // Show loader
        $('#exportLoader').show();

        // Get current filter values
        var filters = {
            searchkey: $('#searchkey').val(),
            daterange: $('#daterange').val()
        };

        // Build export URL with filters
        var exportUrl = "{{ route('reports.employee.export') }}?" + $.param(filters);

        // Create temporary link and trigger download
        var link = document.createElement('a');
        link.href = exportUrl;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Hide loader after a short delay
        setTimeout(() => {
            $('#exportLoader').hide();
        }, 1000);
    }
</script>
@stop
