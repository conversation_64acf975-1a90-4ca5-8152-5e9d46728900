@extends('layouts.master')

@php
    $title = 'Client Dues Report';
    $subTitle = 'Comprehensive client outstanding amounts analysis';
@endphp

@section('css')
<link rel="stylesheet" href="/assets/css/reports/client-dues-report.css">
@endsection

@section('content')
<div class="dashboard-main-body">
    <!-- Breadcrumb -->
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">{{ $title }}</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="{{ route('dashboard') }}" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Reports</li>
            <li>-</li>
            <li class="fw-medium">{{ $title }}</li>
        </ul>
    </div>

    <!-- Enhanced Header Section -->
    <div class="card border-0 shadow-sm mb-24">
        <div class="card-body py-20">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center gap-16">
                        <div class="w-64-px h-64-px bg-primary-100 text-primary-600 rounded-circle d-flex align-items-center justify-content-center">
                            <iconify-icon icon="mdi:account-cash" class="text-2xl"></iconify-icon>
                        </div>
                        <div>
                            <h4 class="mb-8 fw-bold text-primary-600">{{ $title }}</h4>
                            <p class="mb-0 text-secondary-light fw-medium">{{ $subTitle }}</p>
                            
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex align-items-center gap-8 justify-content-end">
                        <!-- <button type="button" class="btn btn-outline-primary radius-8 px-20 py-12 d-flex align-items-center gap-8" id="refreshBtn">
                            <iconify-icon icon="mdi:refresh" class="text-lg"></iconify-icon>
                            Refresh
                        </button> -->
                        <button type="button" class="btn btn-primary-600 radius-8 px-20 py-12 d-flex align-items-center gap-8" id="exportBtn">
                            <iconify-icon icon="lucide:download" class="text-lg"></iconify-icon>
                            Export
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Filter Section -->
    <div class="card border-0 shadow-sm mb-24">
        <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
            <div class="d-flex align-items-center gap-2">
                <iconify-icon icon="mdi:filter-variant" class="text-xl text-primary-600"></iconify-icon>
                <h6 class="text-lg fw-semibold mb-0">Smart Filters</h6>
                <span class="badge bg-primary-100 text-primary-600 px-8 py-4 radius-8 fw-medium text-sm" id="activeFiltersCount">0 Active</span>
            </div>
            <div class="d-flex align-items-center gap-2">
                <button type="button" class="btn btn-sm btn-outline-primary radius-8 px-16 py-8 d-flex align-items-center" id="toggleFilters">
                    <iconify-icon icon="mdi:chevron-down" class="text-lg" id="filterToggleIcon"></iconify-icon>
                    <span id="filterToggleText">Show Filters</span>
                </button>
            </div>
        </div>
        <div class="card-body py-20" id="filterSection" style="display: none;">
            <form id="filterForm">
                <div class="row g-16">
                    <!-- Quick Filter Buttons -->
                    <div class="col-12">
                        <label class="form-label fw-semibold text-primary-light mb-8">Quick Filters</label>
                        <div class="d-flex flex-wrap gap-8">
                            <button type="button" class="btn btn-sm btn-outline-danger radius-8 px-16 py-8 quick-filter d-flex align-items-center" data-filter="high_risk">
                                <iconify-icon icon="mdi:alert-circle" class="me-4"></iconify-icon>
                                High Risk (180+ days)
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-warning radius-8 px-16 py-8 quick-filter d-flex align-items-center" data-filter="medium_risk">
                                <iconify-icon icon="mdi:alert" class="me-4"></iconify-icon>
                                Medium Risk (90-180 days)
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info radius-8 px-16 py-8 quick-filter d-flex align-items-center" data-filter="low_risk">
                                <iconify-icon icon="mdi:clock-alert" class="me-4"></iconify-icon>
                                Low Risk (30-90 days)
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-success radius-8 px-16 py-8 quick-filter d-flex align-items-center" data-filter="high_amount">
                                <iconify-icon icon="mdi:currency-inr" class="me-4"></iconify-icon>
                                High Amount
                            </button>
                        </div>
                    </div>

                    <!-- Search -->
                    <div class="col-lg-3 col-md-6">
                        <label class="form-label fw-semibold text-primary-light mb-8">Search Client</label>
                        <div class="icon-field">
                            <input type="text" class="form-control form-control-sm radius-8" id="searchkey" name="searchkey" placeholder="Client name or code...">
                            <span class="icon">
                                <iconify-icon icon="ion:search-outline"></iconify-icon>
                            </span>
                        </div>
                    </div>

                    <!-- Employee Filter -->
                    <div class="col-lg-3 col-md-6">
                        <label class="form-label fw-semibold text-primary-light mb-8">Employee</label>
                        <select class="form-select form-control-sm radius-8" id="employee" name="employee">
                            <option value="">All Employees</option>
                            @foreach($employees as $employee)
                                <option value="{{ $employee->id }}">{{ $employee->emp_name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Area Filter -->
                    <div class="col-lg-3 col-md-6">
                        <label class="form-label fw-semibold text-primary-light mb-8">Area</label>
                        <select class="form-select form-control-sm radius-8" id="searchArea" name="searchArea">
                            <option value="">All Areas</option>
                            @foreach($client_areas as $area)
                                <option value="{{ $area }}">{{ $area }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Duration Filter -->
                    <div class="col-lg-3 col-md-6">
                        <label class="form-label fw-semibold text-primary-light mb-8">Duration</label>
                        <select class="form-select form-control-sm radius-8" id="duration_filter" name="duration_filter">
                            <option value="">All Durations</option>
                            <option value="30_days">30+ Days Overdue</option>
                            <option value="60_days">60+ Days Overdue</option>
                            <option value="90_days">90+ Days Overdue</option>
                            <option value="6_months">6+ Months Overdue</option>
                            <option value="1_year">1+ Year Overdue</option>
                            <option value="above_1_year">Above 1 Year</option>
                        </select>
                    </div>

                    <!-- Amount Range Filter -->
                    <div class="col-lg-6 col-md-12">
                        <label class="form-label fw-semibold text-primary-light mb-8">
                            Amount Range
                            <span class="badge bg-info-100 text-info-600 px-6 py-2 radius-8 fw-medium text-xs ms-2">Dynamic</span>
                        </label>
                        <div class="d-flex gap-2">
                            <select class="form-select form-control-sm radius-8" id="amount_range" name="amount_range">
                                <option value="">All Amounts</option>
                                @foreach($amount_ranges as $range)
                                    <option value="{{ $range['value'] }}">{{ $range['label'] }}</option>
                                @endforeach
                            </select>
                            <button type="button" class="btn btn-sm btn-outline-secondary radius-8 px-12 py-6" id="refreshRanges" title="Refresh amount ranges">
                                <iconify-icon icon="mdi:refresh" class="text-lg"></iconify-icon>
                            </button>
                        </div>
                    </div>

                    <!-- Filter Actions -->
                    <div class="col-lg-6 col-md-12 d-flex align-items-end gap-8">
                        <button type="button" class="btn btn-sm btn-primary-600 radius-8 px-20 py-8 d-flex align-items-center gap-8" id="applyFilters">
                            <iconify-icon icon="mdi:magnify" class="text-lg"></iconify-icon>
                            Apply Filters
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary radius-8 px-20 py-8 d-flex align-items-center gap-8" id="clearFilters">
                            <iconify-icon icon="mdi:refresh" class="text-lg"></iconify-icon>
                            Clear All
                        </button>
                        
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row g-3 mb-24" id="summaryCards">
        <!-- Financial Overview Card -->
        <div class="col-lg-8 col-md-12">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body py-20">
                    <div class="row g-3">
                        <div class="col-3 text-center">
                            <div class="w-48-px h-48-px bg-success-100 text-success-600 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-12">
                                <iconify-icon icon="mdi:currency-inr" class="text-xl"></iconify-icon>
                            </div>
                            <h5 class="mb-4 fw-bold text-success-600" id="totalInvoiceAmount">₹0</h5>
                            <p class="mb-0 text-secondary-light fw-medium text-sm">Total Invoice</p>
                        </div>
                        <div class="col-3 text-center">
                            <div class="w-48-px h-48-px bg-info-100 text-info-600 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-12">
                                <iconify-icon icon="mdi:cash-check" class="text-xl"></iconify-icon>
                            </div>
                            <h5 class="mb-4 fw-bold text-info-600" id="totalPaidAmount">₹0</h5>
                            <p class="mb-0 text-secondary-light fw-medium text-sm">Total Paid</p>
                        </div>
                        <div class="col-3 text-center">
                            <div class="w-48-px h-48-px bg-danger-100 text-danger-600 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-12">
                                <iconify-icon icon="mdi:cash-remove" class="text-xl"></iconify-icon>
                            </div>
                            <h5 class="mb-4 fw-bold text-danger-600" id="totalPendingAmount">₹0</h5>
                            <p class="mb-0 text-secondary-light fw-medium text-sm">Total Pending</p>
                        </div>
                        <div class="col-3 text-center">
                            <div class="w-48-px h-48-px bg-purple-100 text-purple-600 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-12">
                                <iconify-icon icon="mdi:file-clock" class="text-xl"></iconify-icon>
                            </div>
                            <h5 class="mb-4 fw-bold text-purple-600" id="pendingInvoices">0</h5>
                            <p class="mb-0 text-secondary-light fw-medium text-sm">Pending Invoices</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Clients & Invoices Card -->
        <div class="col-lg-4 col-md-12">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body py-20">
                    <div class="d-flex align-items-center justify-content-between mb-20">
                        <div class="w-56-px h-56-px bg-primary-100 text-primary-600 rounded-circle d-flex align-items-center justify-content-center">
                            <iconify-icon icon="mdi:account-group" class="text-2xl"></iconify-icon>
                        </div>
                        <div class="text-end">
                            <h4 class="mb-4 fw-bold text-primary-600" id="totalClients">0</h4>
                            <p class="mb-0 text-secondary-light fw-medium">Total Clients</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="w-56-px h-56-px bg-warning-100 text-warning-600 rounded-circle d-flex align-items-center justify-content-center">
                            <iconify-icon icon="mdi:file-document" class="text-2xl"></iconify-icon>
                        </div>
                        <div class="text-end">
                            <h4 class="mb-4 fw-bold text-warning-600" id="totalInvoices">0</h4>
                            <p class="mb-0 text-secondary-light fw-medium">Total Invoices</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Aging Analysis with Chart -->
    <div class="row g-20 mb-24">
        <!-- Aging Cards -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-neutral-50 border-bottom border-neutral-200 py-16">
                    <h6 class="text-lg fw-semibold mb-0 d-flex align-items-center gap-2">
                        <iconify-icon icon="mdi:chart-timeline-variant" class="text-xl text-primary-600"></iconify-icon>
                        Aging Analysis
                    </h6>
                </div>
                <div class="card-body py-20">
                    <div class="row g-3" id="agingAnalysis">
                        <div class="col-lg col-md-4 col-sm-6">
                            <div class="text-center p-20 bg-success-50 rounded-12 border border-success-100">
                                <div class="w-40-px h-40-px bg-success-100 text-success-600 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-12">
                                    <iconify-icon icon="mdi:check-circle" class="text-lg"></iconify-icon>
                                </div>
                                <h6 class="mb-8 fw-bold text-success-600" id="current30">₹0</h6>
                                <p class="mb-0 text-success-600 fw-medium text-sm">Current (0-30 days)</p>
                            </div>
                        </div>
                        <div class="col-lg col-md-4 col-sm-6">
                            <div class="text-center p-20 bg-primary-50 rounded-12 border border-primary-100">
                                <div class="w-40-px h-40-px bg-primary-100 text-primary-600 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-12">
                                    <iconify-icon icon="mdi:clock" class="text-lg"></iconify-icon>
                                </div>
                                <h6 class="mb-8 fw-bold text-primary-600" id="days3160">₹0</h6>
                                <p class="mb-0 text-primary-600 fw-medium text-sm">31-60 days</p>
                            </div>
                        </div>
                        <div class="col-lg col-md-4 col-sm-6">
                            <div class="text-center p-20 bg-info-50 rounded-12 border border-info-100">
                                <div class="w-40-px h-40-px bg-info-100 text-info-600 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-12">
                                    <iconify-icon icon="mdi:clock-alert" class="text-lg"></iconify-icon>
                                </div>
                                <h6 class="mb-8 fw-bold text-info-600" id="days6190">₹0</h6>
                                <p class="mb-0 text-info-600 fw-medium text-sm">61-90 days</p>
                            </div>
                        </div>
                        <div class="col-lg col-md-4 col-sm-6">
                            <div class="text-center p-20 bg-warning-50 rounded-12 border border-warning-100">
                                <div class="w-40-px h-40-px bg-warning-100 text-warning-600 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-12">
                                    <iconify-icon icon="mdi:alert" class="text-lg"></iconify-icon>
                                </div>
                                <h6 class="mb-8 fw-bold text-warning-600" id="days91180">₹0</h6>
                                <p class="mb-0 text-warning-600 fw-medium text-sm">91-180 days</p>
                            </div>
                        </div>
                        <div class="col-lg col-md-4 col-sm-6">
                            <div class="text-center p-20 bg-danger-50 rounded-12 border border-danger-100">
                                <div class="w-40-px h-40-px bg-danger-100 text-danger-600 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-12">
                                    <iconify-icon icon="mdi:alert-circle" class="text-lg"></iconify-icon>
                                </div>
                                <h6 class="mb-8 fw-bold text-danger-600" id="above180">₹0</h6>
                                <p class="mb-0 text-danger-600 fw-medium text-sm">Above 180 days</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Aging Chart -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header">
                    <h6 class="text-lg fw-semibold mb-0 d-flex align-items-center gap-2">
                        <iconify-icon icon="mdi:chart-donut" class="text-xl text-primary-600"></iconify-icon>
                        Distribution Chart
                    </h6>
                </div>
                <div class="card-body py-20">
                    <div id="agingChart" class="d-flex align-items-center justify-content-center" style="min-height: 250px;">
                        <!-- Chart will be rendered here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Data Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
            <div class="d-flex align-items-center gap-2">
                <iconify-icon icon="mdi:table" class="text-xl text-primary-600"></iconify-icon>
                <h6 class="text-lg fw-semibold mb-0">Client Dues Details</h6>
                <span class="badge bg-neutral-100 text-neutral-600 px-8 py-4 radius-8 fw-medium text-sm" id="tableRecordCount">0 Records</span>
            </div>
            <div class="d-flex align-items-center gap-2">
                
            </div>
        </div>
        <div class="card-body">
            <!-- Loading State -->
            <div id="tableLoading" class="text-center py-40" style="display: none;">
                <div class="spinner-border text-primary mb-16" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="text-secondary-light">Loading client dues data...</p>
            </div>

            <!-- Table View for Desktop -->
            <div class="d-none d-md-block" id="desktopTableView">
                <table id="clientDuesTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4 clientDuesTable" style="width:100%">
                    <thead>
                        <tr>
                            <th>Client Code</th>
                            <th>Client Name</th>
                            <th>Area</th>
                            <th>Employee</th>
                            <th>Total Invoice</th>
                            <th>Total Paid</th>
                            <th>Pending Amount</th>
                            <th>Last Invoice</th>
                            <th>Days Overdue</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tfoot>
                        <tr>
                            <th colspan="4" class="text-end">Total</th>
                            <th id="footerTotalInvoice"><strong>₹ 0.00</strong></th>
                            <th id="footerTotalPaid"><strong>₹ 0.00</strong></th>
                            <th id="footerTotalPending"><strong>₹ 0.00</strong></th>
                            <th colspan="3"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- Enhanced Mobile View -->
            <div class="d-md-none" id="mobileTableView">
                <div id="mobileClientDuesView" class="row g-3">
                    <!-- Mobile cards will be populated here -->
                </div>
                <!-- Mobile Pagination -->
                <div class="d-flex justify-content-center mt-20" id="mobilePagination">
                    <!-- Pagination will be added here -->
                </div>
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="text-center py-40" style="display: none;">
                <div class="w-80-px h-80-px bg-neutral-100 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-16">
                    <iconify-icon icon="mdi:database-search" class="text-4xl text-neutral-400"></iconify-icon>
                </div>
                <h5 class="mb-8 text-neutral-600">No Data Found</h5>
                <p class="text-neutral-500 mb-16">No client dues match your current filters. Try adjusting your search criteria.</p>
                <button type="button" class="btn btn-outline-primary radius-8 px-20 py-8" id="resetFiltersBtn">
                    <iconify-icon icon="mdi:refresh" class="me-8"></iconify-icon>
                    Reset Filters
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<!-- Apex Chart js -->
<script src="/assets/js/lib/apexcharts.min.js"></script>
<script>
$(document).ready(function() {
    console.log('Client Dues Report: Document ready');
    let table;
    let agingChart;
    let isFilterVisible = false;

    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize components
    initializeTable();
    initializeAgingChart();
    initializeEventHandlers();
    initializeAnimations();

    // Initialize DataTable with enhanced features
    function initializeTable() {
        console.log('Client Dues Report: Initializing table');

        if (table) {
            table.destroy();
        }

        // Show loading state
        showTableLoading();

        table = $('#clientDuesTable').DataTable({
            processing: true,
            serverSide: true,
            searching: false,
            ajax: {
                url: "{{ route('reports.getClientDuesReport') }}",
                type: 'GET',
                data: function(d) {
                    d.searchkey = $('#searchkey').val();
                    d.employee = $('#employee').val();
                    d.searchArea = $('#searchArea').val();
                    d.duration_filter = $('#duration_filter').val();
                    d.amount_range = $('#amount_range').val();
                },
                beforeSend: function() {
                    showTableLoading();
                },
                complete: function() {
                    hideTableLoading();
                },
                error: function(xhr, error, thrown) {
                    console.error('Client Dues Report: AJAX Error:', error);
                    hideTableLoading();
                    showEmptyState();
                }
            },
            columns: [
                { data: 'client_code', name: 'clients.client_code' },
                { data: 'client_name', name: 'clients.name' },
                { data: 'area', name: 'clients.area' },
                { data: 'employee_name', name: 'employees.emp_name' },
                {
                    data: 'total_invoice_amount',
                    name: 'total_invoice_amount',
                    render: function(data, type, row) {
                        return type === 'display' ? data.display : data.raw;
                    }
                },
                {
                    data: 'total_paid_amount',
                    name: 'total_paid_amount',
                    render: function(data, type, row) {
                        return type === 'display' ? data.display : data.raw;
                    }
                },
                {
                    data: 'pending_amount',
                    name: 'pending_amount',
                    render: function(data, type, row) {
                        if (type === 'display') {
                            return '<span class="fw-bold ' + data.color + '">' + data.display + '</span>';
                        }
                        return data.raw;
                    }
                },
                { data: 'last_invoice_date', name: 'last_invoice_date' },
                { data: 'days_overdue', name: 'days_overdue', orderable: false },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            order: [[6, 'desc']], // Order by pending amount desc
            pageLength: 25,
            responsive: true,
            language: {
                processing: '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>',
                emptyTable: '',
                zeroRecords: ''
            },
            drawCallback: function(settings) {
                hideTableLoading();

                // Check if table has data
                if (this.api().rows().count() === 0) {
                    showEmptyState();
                } else {
                    hideEmptyState();
                    updateRecordCount(this.api().page.info());
                }

                // Update summary after table draw
                updateSummary();
                updateFooterTotals();
            },
            footerCallback: function(row, data, start, end, display) {
                var api = this.api();

                // Calculate totals for current page
                var totalInvoice = api.column(4, {page: 'current'}).data().reduce(function(a, b) {
                    return (typeof b.raw !== 'undefined' ? b.raw : parseFloat(b.replace(/[₹,]/g, '') || 0)) + a;
                }, 0);

                var totalPaid = api.column(5, {page: 'current'}).data().reduce(function(a, b) {
                    return (typeof b.raw !== 'undefined' ? b.raw : parseFloat(b.replace(/[₹,]/g, '') || 0)) + a;
                }, 0);

                var totalPending = api.column(6, {page: 'current'}).data().reduce(function(a, b) {
                    return (typeof b.raw !== 'undefined' ? b.raw : parseFloat(b.replace(/[₹,]/g, '') || 0)) + a;
                }, 0);

                // Update footer
                $('#footerTotalInvoice').html('<strong>₹ ' + totalInvoice.toLocaleString('en-IN', {minimumFractionDigits: 2}) + '</strong>');
                $('#footerTotalPaid').html('<strong>₹ ' + totalPaid.toLocaleString('en-IN', {minimumFractionDigits: 2}) + '</strong>');
                $('#footerTotalPending').html('<strong>₹ ' + totalPending.toLocaleString('en-IN', {minimumFractionDigits: 2}) + '</strong>');
            }
        });
    }

    // Initialize Aging Chart
    function initializeAgingChart() {
        const options = {
            series: [0, 0, 0, 0, 0],
            chart: {
                type: 'donut',
                height: 250,
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800,
                    animateGradually: {
                        enabled: true,
                        delay: 150
                    },
                    dynamicAnimation: {
                        enabled: true,
                        speed: 350
                    }
                }
            },
            colors: ['#22c55e', '#3b82f6', '#06b6d4', '#f59e0b', '#ef4444'],
            labels: ['0-30 days', '31-60 days', '61-90 days', '91-180 days', '180+ days'],
            legend: {
                position: 'bottom',
                fontSize: '12px',
                fontWeight: 500
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '70%',
                        labels: {
                            show: true,
                            name: {
                                show: true,
                                fontSize: '14px',
                                fontWeight: 600
                            },
                            value: {
                                show: true,
                                fontSize: '16px',
                                fontWeight: 700,
                                formatter: function (val) {
                                    return '₹' + parseFloat(val).toLocaleString('en-IN');
                                }
                            },
                            total: {
                                show: true,
                                label: 'Total Due',
                                fontSize: '14px',
                                fontWeight: 600,
                                formatter: function (w) {
                                    const total = w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                                    return '₹' + total.toLocaleString('en-IN');
                                }
                            }
                        }
                    }
                }
            },
            dataLabels: {
                enabled: false
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return '₹' + parseFloat(val).toLocaleString('en-IN');
                    }
                }
            }
        };

        agingChart = new ApexCharts(document.querySelector("#agingChart"), options);
        agingChart.render();
    }

    // Enhanced summary update with animations
    function updateSummary() {
        const formData = {
            searchkey: $('#searchkey').val(),
            employee: $('#employee').val(),
            searchArea: $('#searchArea').val(),
            duration_filter: $('#duration_filter').val(),
            amount_range: $('#amount_range').val()
        };

        $.post("{{ route('reports.getClientDuesSummary') }}", formData)
            .done(function(response) {
                // Update summary cards with rounded values and tooltips
                updateValueWithTooltip('#totalClients', response.summary.total_clients, formatNumber(response.summary.total_clients));
                updateValueWithTooltip('#totalInvoiceAmount', response.summary.total_invoice_amount, formatCurrency(response.summary.total_invoice_amount), '₹');
                updateValueWithTooltip('#totalPaidAmount', response.summary.total_paid_amount, formatCurrency(response.summary.total_paid_amount), '₹');
                updateValueWithTooltip('#totalPendingAmount', response.summary.total_pending_amount, formatCurrency(response.summary.total_pending_amount), '₹');
                updateValueWithTooltip('#totalInvoices', response.summary.total_invoices, formatNumber(response.summary.total_invoices));
                updateValueWithTooltip('#pendingInvoices', response.summary.pending_invoices, formatNumber(response.summary.pending_invoices));

                // Update aging analysis with rounded values and tooltips
                updateValueWithTooltip('#current30', response.aging.current_30, formatCurrency(response.aging.current_30), '₹');
                updateValueWithTooltip('#days3160', response.aging.days_31_60, formatCurrency(response.aging.days_31_60), '₹');
                updateValueWithTooltip('#days6190', response.aging.days_61_90, formatCurrency(response.aging.days_61_90), '₹');
                updateValueWithTooltip('#days91180', response.aging.days_91_180, formatCurrency(response.aging.days_91_180), '₹');
                updateValueWithTooltip('#above180', response.aging.above_180, formatCurrency(response.aging.above_180), '₹');

                // Update aging chart
                updateAgingChart(response.aging);
            })
            .fail(function() {
                console.error('Failed to fetch summary data');
                showNotification('Failed to fetch summary data', 'error');
            });
    }

    // Update footer totals
    function updateFooterTotals() {
        // This is handled in the footerCallback of DataTables
        // Additional footer updates can be added here if needed
    }

    // Initialize Event Handlers
    function initializeEventHandlers() {
        // Filter toggle
        $('#toggleFilters').click(function() {
            isFilterVisible = !isFilterVisible;
            if (isFilterVisible) {
                $('#filterSection').slideDown(300);
                $('#filterToggleIcon').removeClass('mdi-chevron-down').addClass('mdi-chevron-up');
                $('#filterToggleText').text('Hide Filters');
            } else {
                $('#filterSection').slideUp(300);
                $('#filterToggleIcon').removeClass('mdi-chevron-up').addClass('mdi-chevron-down');
                $('#filterToggleText').text('Show Filters');
            }
        });

        // Quick filters
        $('.quick-filter').click(function() {
            const filter = $(this).data('filter');
            applyQuickFilter(filter);
            $(this).addClass('active').siblings().removeClass('active');
        });

        // Apply filters with loading state
        $('#applyFilters').click(function() {
            const btn = $(this);
            btn.prop('disabled', true).html('<div class="spinner-border spinner-border-sm me-2"></div>Applying...');

            setTimeout(() => {
                table.ajax.reload();
                updateActiveFiltersCount();
                btn.prop('disabled', false).html('<iconify-icon icon="mdi:magnify" class="text-lg"></iconify-icon> Apply Filters');
                showNotification('Filters applied successfully', 'success');
            }, 500);
        });

        // Clear filters
        $('#clearFilters').click(function() {
            $('#filterForm')[0].reset();
            $('.quick-filter').removeClass('active');
            table.ajax.reload();
            updateActiveFiltersCount();
            showNotification('Filters cleared', 'info');
        });

        // Reset filters from empty state
        $('#resetFiltersBtn').click(function() {
            $('#clearFilters').click();
        });

        // Refresh data
        $('#refreshBtn').click(function() {
            const btn = $(this);
            btn.prop('disabled', true).html('<div class="spinner-border spinner-border-sm me-2"></div>Refreshing...');

            table.ajax.reload();
            updateSummary();

            setTimeout(() => {
                btn.prop('disabled', false).html('<iconify-icon icon="mdi:refresh" class="text-lg"></iconify-icon> Refresh');
                showNotification('Data refreshed successfully', 'success');
            }, 1000);
        });

        // Export functionality with progress
        $('#exportBtn').click(function() {
            const btn = $(this);
            btn.prop('disabled', true).html('<div class="spinner-border spinner-border-sm me-2"></div>Exporting...');

            const formData = {
                searchkey: $('#searchkey').val(),
                employee: $('#employee').val(),
                searchArea: $('#searchArea').val(),
                duration_filter: $('#duration_filter').val(),
                amount_range: $('#amount_range').val()
            };

            // Create form and submit for download
            const form = $('<form>', {
                method: 'GET',
                action: "{{ route('reports.client-dues.export') }}"
            });

            $.each(formData, function(key, value) {
                if (value) {
                    form.append($('<input>', {
                        type: 'hidden',
                        name: key,
                        value: value
                    }));
                }
            });

            $('body').append(form);
            form.submit();
            form.remove();

            setTimeout(() => {
                btn.prop('disabled', false).html('<iconify-icon icon="lucide:download" class="text-lg"></iconify-icon> Export');
                showNotification('Export started successfully', 'success');
            }, 2000);
        });

        // Enter key support for search
        $('#searchkey').keypress(function(e) {
            if (e.which == 13) {
                $('#applyFilters').click();
            }
        });

        // Auto-apply filters on change (with debounce)
        let filterTimeout;
        $('#employee, #searchArea, #duration_filter').change(function() {
            clearTimeout(filterTimeout);
            filterTimeout = setTimeout(() => {
                updateActiveFiltersCount();
                refreshAmountRanges(); // Refresh amount ranges when other filters change
            }, 300);
        });

        $('#amount_range').change(function() {
            updateActiveFiltersCount();
        });

        // Manual refresh amount ranges
        $('#refreshRanges').click(function() {
            const btn = $(this);
            btn.prop('disabled', true).html('<div class="spinner-border spinner-border-sm"></div>');

            refreshAmountRanges();

            setTimeout(() => {
                btn.prop('disabled', false).html('<iconify-icon icon="mdi:refresh" class="text-lg"></iconify-icon>');
                showNotification('Amount ranges refreshed', 'success');
            }, 1000);
        });
    }

    // Utility Functions
    function showTableLoading() {
        $('#tableLoading').show();
        $('#desktopTableView, #mobileTableView, #emptyState').hide();
    }

    function hideTableLoading() {
        $('#tableLoading').hide();
        $('#desktopTableView, #mobileTableView').show();
    }

    function showEmptyState() {
        $('#emptyState').show();
        $('#desktopTableView, #mobileTableView').hide();
    }

    function hideEmptyState() {
        $('#emptyState').hide();
    }

    function updateRecordCount(info) {
        $('#tableRecordCount').text(info.recordsDisplay.toLocaleString() + ' Records');
    }

    function updateActiveFiltersCount() {
        let count = 0;
        $('#filterForm input, #filterForm select').each(function() {
            if ($(this).val() && $(this).val() !== '') count++;
        });
        $('#activeFiltersCount').text(count + ' Active');
    }

    function updateSelectedCount() {
        // You can add bulk action buttons here based on selection
    }

    function applyQuickFilter(filter) {
        // Clear existing filters
        $('#filterForm')[0].reset();

        switch(filter) {
            case 'high_risk':
                $('#duration_filter').val('above_1_year');
                break;
            case 'medium_risk':
                $('#duration_filter').val('6_months');
                break;
            case 'low_risk':
                $('#duration_filter').val('90_days');
                break;
            case 'high_amount':
                // Find the highest amount range option (last option that starts with 'above_')
                const amountOptions = $('#amount_range option');
                let highestRange = '';
                amountOptions.each(function() {
                    const value = $(this).val();
                    if (value.startsWith('above_') || value.includes('above')) {
                        highestRange = value;
                    }
                });
                if (highestRange) {
                    $('#amount_range').val(highestRange);
                } else {
                    // Fallback to last option if no 'above_' found
                    const lastOption = amountOptions.last().val();
                    if (lastOption) {
                        $('#amount_range').val(lastOption);
                    }
                }
                break;
        }

        $('#applyFilters').click();
    }

    function animateValue(selector, value, prefix = '', suffix = '') {
        const element = $(selector);
        const formatted = prefix + parseFloat(value).toLocaleString('en-IN', {minimumFractionDigits: 2}) + suffix;
        element.html('<strong>' + formatted + '</strong>');
    }

    function updateAgingChart(aging) {
        if (agingChart) {
            const series = [
                aging.current_30,
                aging.days_31_60,
                aging.days_61_90,
                aging.days_91_180,
                aging.above_180
            ];
            agingChart.updateSeries(series);
        }
    }

    function initializeAnimations() {
        // Simple hover effects for cards only (not table)
        $('.summary-card, .aging-card').hover(
            function() {
                $(this).css('transform', 'translateY(-2px)');
            },
            function() {
                $(this).css('transform', 'translateY(0)');
            }
        );
    }

    function showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' :
                          type === 'warning' ? 'alert-warning' : 'alert-info';

        const notification = $(`
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                <strong>${message}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);

        $('body').append(notification);

        setTimeout(() => {
            notification.alert('close');
        }, 3000);
    }

    // Refresh amount ranges based on current filters
    function refreshAmountRanges() {
        const formData = {
            searchkey: $('#searchkey').val(),
            employee: $('#employee').val(),
            searchArea: $('#searchArea').val(),
            duration_filter: $('#duration_filter').val()
            // Exclude amount_range to avoid circular dependency
        };

        const amountSelect = $('#amount_range');
        const currentValue = amountSelect.val();

        // Show loading state
        amountSelect.prop('disabled', true);

        $.post("{{ route('reports.getDynamicAmountRanges') }}", formData)
            .done(function(response) {
                if (response.success && response.ranges) {
                    // Clear existing options except "All Amounts"
                    amountSelect.find('option:not(:first)').remove();

                    // Add new dynamic options
                    response.ranges.forEach(function(range) {
                        amountSelect.append(new Option(range.label, range.value));
                    });

                    // Try to restore previous selection if it still exists
                    if (currentValue && amountSelect.find('option[value="' + currentValue + '"]').length > 0) {
                        amountSelect.val(currentValue);
                    } else {
                        amountSelect.val(''); // Reset to "All Amounts"
                    }

                    console.log('Amount ranges updated:', response.ranges.length, 'ranges');
                } else {
                    console.error('Invalid response format for amount ranges');
                }
            })
            .fail(function(xhr, status, error) {
                console.error('Failed to refresh amount ranges:', error);
                showNotification('Failed to refresh amount ranges', 'error');
            })
            .always(function() {
                // Re-enable the select
                amountSelect.prop('disabled', false);
            });
    }

    // Number formatting functions
    function formatNumber(num) {
        if (num >= 10000000) { // 1 Crore
            return (num / 10000000).toFixed(1) + 'Cr';
        } else if (num >= 100000) { // 1 Lakh
            return (num / 100000).toFixed(1) + 'L';
        } else if (num >= 1000) { // 1 Thousand
            return (num / 1000).toFixed(1) + 'K';
        } else {
            return num.toString();
        }
    }

    function formatCurrency(amount) {
        if (amount >= 10000000) { // 1 Crore
            return '₹' + (amount / 10000000).toFixed(1) + 'Cr';
        } else if (amount >= 100000) { // 1 Lakh
            return '₹' + (amount / 100000).toFixed(1) + 'L';
        } else if (amount >= 1000) { // 1 Thousand
            return '₹' + (amount / 1000).toFixed(1) + 'K';
        } else {
            return '₹' + Math.round(amount).toLocaleString();
        }
    }

    // Update element with rounded value and original value tooltip
    function updateValueWithTooltip(selector, originalValue, formattedValue, prefix = '') {
        const element = $(selector);
        const originalFormatted = prefix + parseFloat(originalValue).toLocaleString('en-IN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });

        element.text(formattedValue)
               .attr('title', 'Exact value: ' + originalFormatted)
               .attr('data-bs-toggle', 'tooltip')
               .attr('data-bs-placement', 'top')
               .css('cursor', 'help');

        // Initialize Bootstrap tooltip
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            new bootstrap.Tooltip(element[0]);
        }
    }

    // Initialize everything
    updateActiveFiltersCount();
});
</script>

@endsection
