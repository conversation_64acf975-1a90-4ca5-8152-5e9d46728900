@extends('layouts.master')
@section('title', 'Client Report - Paidash')
@section('content')
<div class="dashboard-main-body">
@if(session('success'))
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('success') }}</span>

        </div>
        <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
            <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
        </button>
    </div>
@endif
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Client Report</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Client Report</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
            <div class="d-flex flex-wrap align-items-center gap-3 w-25 w-mb-100">
                <div class="icon-field w-100">
                    <input type="text" name="searchkey" class="form-control form-control-sm w-100" placeholder="Search Client" id="searchkey">
                    <span class="icon">
                        <iconify-icon icon="ion:search-outline"></iconify-icon>
                    </span>
                </div>
            </div>
            <div class="d-flex flex-no-wrap align-items-center gap-3">
                <select id="searchArea" class="form-control form-select" title="select Area">
                    <option selected value="">Select Area</option>
                    <option value="">All</option>
                    @foreach ($client_areas as $client_area)
                         <option value="{{$client_area}}">{{$client_area}}</option>
                    @endforeach
                </select>

                <select id="employee_id" class="form-control form-select" title="select Employee">
                    <option selected value="">Select Employee</option>
                    <option value="">All</option>
                    @foreach ($employees as $employee)
                         <option value="{{$employee->id}}">{{$employee->emp_name}}</option>
                    @endforeach
                </select>
                <div class="input-group date_range " >
                    <input type="text" class="form-control" name="daterange" id="daterange">
                    <div class="input-group-append">
                        <span class="input-group-text" id="basic-addon2">
                            <iconify-icon icon="mdi:calendar" width="24" height="24"></iconify-icon>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- Table View for Desktop -->
            <div class="d-none d-md-block">
                <table id="clientsTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Area</th>
                            <th>Employee Name</th>
                            <th>Total Invoice Amount</th>
                            <th>Total Paid Amount</th>
                            <th>Pending Amount</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tfoot>
                        <tr>
                            <th colspan="3" class="text-end">Total</th>
                            <th id="totalInvoice" > <strong>₹ 0.00</strong></th>
                            <th id="totalPaid"> <strong>₹ 0.00</strong></th>
                            <th id="totalPending" colspan="2"> <strong>₹ 0.00</strong></th>

                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- Grid View for Mobile -->
            <div id="clientsGrid" class="d-block d-md-none"></div>
        </div>
    </div>

</div>
<div id="exportLoader" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999;">
    <span class="spinner-border text-primary" role="status"></span>
    <strong>Exporting...</strong>
</div>
@stop
@section('script')
<script>
    $(document).ready(function () {
        $('#searchArea').select2({
            width: '300px'
        });
        $('#searchStatus').select2({
            width: '200px'
        });
        var table = $('#clientsTable').DataTable({
            processing: true,
            serverSide: true,
            searching: false,
            pageLength: 25,
            ajax: {
                url: "{{ route('reports.getClientReport') }}",
                data: function (d) {
                    d.searchkey = $('#searchkey').val();
                    d.daterange = $('#daterange').val();
                    d.searchArea = $('#searchArea').val();
                    d.employee = $('#employee_id').val();
                }
            },
            columns: [
                { data: 'name', name: 'name' },
                { data: 'area', name: 'area' },
                { data: 'employee_name', name: 'employee_name' },
                {
                    data: 'total_invoice_amount',
                    name: 'total_invoice_amount',
                    render: { _: 'display', sort: 'raw' }
                },
                {
                    data: 'total_paid_amount',
                    name: 'total_paid_amount',
                    render: { _: 'display', sort: 'raw' }
                },
                {
                    data: 'pending_amount',
                    name: 'pending_amount',
                    render: { _: 'display', sort: 'raw' }
                },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            drawCallback: function (settings) {
                var api = this.api();
                var data = api.rows().data();
            },
            dom:
            "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" + // Entries Dropdown & CSV Button
            "<'row'<'col-md-12'tr>>" +
            "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>", // Info at left, Pagination at right
            // buttons: [
            //     {
            //         extend: 'csv',
            //         text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
            //         className: 'btn btn-success btn-sm bg-success-500',
            //         filename: 'Clients_' + new Date().toISOString().slice(0, 10),
            //                             customize: function (csv) {
            //                                 return '\uFEFF' + csv; // Add BOM to ensure proper UTF-8 encoding
            //                             },
            //     }
            // ],
            buttons: [
                    {
                        text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
                        className: 'btn btn-success btn-sm bg-success-500',
                        action: function (e, dt, node, config) {
                            exportClientReport();
                        }
                    }
                ],
            infoCallback: function(settings, start, end, max, total, pre) {
                return `Showing ${start} to ${end} of ${total} records`;
            },
            footerCallback: function(row, data, start, end, display) {
                var api = this.api();

                // Function to apply Indian Number Format
                function IND_money_format(num) {
                    return num.toLocaleString('en-IN', { minimumFractionDigits: 0, maximumFractionDigits: 0 });
                }

                function calculateTotal(columnIndex) {
                    return api
                        .column(columnIndex, { page: 'current' })
                        .data()
                        .reduce(function(a, b) {
                            var value = typeof b === 'object' ? parseFloat(b.raw) : parseFloat(b);
                            return (a || 0) + (value || 0);
                        }, 0);
                }

                var totalInvoice = calculateTotal(3);
                var totalPaid = calculateTotal(4);
                var totalPending = calculateTotal(5);

                $(api.column(3).footer()).html(`<strong>₹ ${IND_money_format(totalInvoice)}</strong>`);
                $(api.column(4).footer()).html(`<strong>₹ ${IND_money_format(totalPaid)}</strong>`);
                $(api.column(5).footer()).html(`<strong>₹ ${IND_money_format(totalPending)}</strong>`);
            }

        });

        // Custom search event triggers
        $("#searchkey, #searchEmail").on("keyup", function () {
            table.draw();
        });

        $("#employee_id, #searchArea").on("change", function () {
            table.draw();
        });
        $('#daterange').on('apply.daterangepicker', function () {
            table.draw();
                });
    });
    var start = moment().subtract(29, 'days');  // Default start date
    var end = moment();  // Default end date
     function cb(start, end) {
    $('#daterange').val(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
}

$('#daterange').daterangepicker({
    startDate: start,
    endDate: end,
    autoApply: true,
    locale: { format: 'DD/MM/YYYY' }, // Set format
    ranges: {
       'Today': [moment(), moment()],
       'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
       'Last 7 Days': [moment().subtract(6, 'days'), moment()],
       'Last 30 Days': [moment().subtract(29, 'days'), moment()],
       'This Month': [moment().startOf('month'), moment().endOf('month')],
       'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    }
}, cb);

// Set initial date display
cb(start, end);
// Custom search event triggers

// Export function with all filters
function exportClientReport() {
    // Show loader
    $('#exportLoader').show();

    // Get current filter values
    var filters = {
        employee: $('#employee').val(),
        searchkey: $('#searchkey').val(),
        client: $('#client').val(),
        searchArea: $('#searchArea').val()
    };

    // Build export URL with filters
    var exportUrl = "{{ route('reports.client.export') }}?" + $.param(filters);

    // Create temporary link and trigger download
    var link = document.createElement('a');
    link.href = exportUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Hide loader after a short delay
    setTimeout(() => {
        $('#exportLoader').hide();
    }, 1000);
}
</script>
@stop
