@extends('layouts.master')

@php
    $title = 'Client Services Report';
    $subTitle = 'Comprehensive client services analysis and reporting';
@endphp

@section('content')
<div class="dashboard-main-body">
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">{{ $title }}</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="{{ route('dashboard') }}" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Reports</li>
            <li>-</li>
            <li class="fw-medium">{{ $title }}</li>
        </ul>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card shadow-sm border-0 rounded-3 h-100">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <p class="fw-medium text-primary-light mb-1 small">Total Clients</p>
                            <h5 class="mb-0 text-primary">{{ number_format($stats['total_clients']) }}</h5>
                        </div>
                        <div class="w-40-px h-40-px bg-primary-50 rounded-circle d-flex justify-content-center align-items-center">
                            <iconify-icon icon="solar:users-group-rounded-outline" class="text-primary text-xl"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card shadow-sm border-0 rounded-3 h-100">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <p class="fw-medium text-success-600 mb-1 small">With Service</p>
                            <h5 class="mb-0 text-success-600">{{ number_format($stats['clients_with_service']) }}</h5>
                        </div>
                        <div class="w-40-px h-40-px bg-success-50 rounded-circle d-flex justify-content-center align-items-center">
                            <iconify-icon icon="solar:shield-check-outline" class="text-success-600 text-xl"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card shadow-sm border-0 rounded-3 h-100">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <p class="fw-medium text-secondary mb-1 small">No Service</p>
                            <h5 class="mb-0 text-secondary">{{ number_format($stats['clients_no_service']) }}</h5>
                        </div>
                        <div class="w-40-px h-40-px bg-neutral-100 rounded-circle d-flex justify-content-center align-items-center">
                            <iconify-icon icon="solar:shield-cross-outline" class="text-secondary text-xl"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card shadow-sm border-0 rounded-3 h-100">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <p class="fw-medium text-danger-600 mb-1 small">Expired</p>
                            <h5 class="mb-0 text-danger-600">{{ number_format($stats['expired_services']) }}</h5>
                        </div>
                        <div class="w-40-px h-40-px bg-danger-50 rounded-circle d-flex justify-content-center align-items-center">
                            <iconify-icon icon="solar:clock-circle-outline" class="text-danger-600 text-xl"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card shadow-sm border-0 rounded-3 h-100">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <p class="fw-medium text-warning-600 mb-1 small">Expiring Soon</p>
                            <h5 class="mb-0 text-warning-600">{{ number_format($stats['expiring_soon']) }}</h5>
                        </div>
                        <div class="w-40-px h-40-px bg-warning-50 rounded-circle d-flex justify-content-center align-items-center">
                            <iconify-icon icon="solar:hourglass-outline" class="text-warning-600 text-xl"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card shadow-sm border-0 rounded-3 h-100">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <p class="fw-medium text-info-600 mb-1 small">Invoice Due</p>
                            <h5 class="mb-0 text-info-600">{{ number_format($stats['invoice_due_soon']) }}</h5>
                        </div>
                        <div class="w-40-px h-40-px bg-info-50 rounded-circle d-flex justify-content-center align-items-center">
                            <iconify-icon icon="solar:bill-list-outline" class="text-info-600 text-xl"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Report Table -->
    <div class="card shadow-sm border-0 rounded-3">
        <div class="card-header bg-white border-bottom-0 pb-0">
            <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                <div class="d-flex align-items-center gap-2">
                    <iconify-icon icon="solar:chart-square-outline" class="text-primary text-xl"></iconify-icon>
                    <h6 class="mb-0 fw-bold">Client Services Analysis</h6>
                </div>
                <div class="d-flex align-items-center gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm d-flex align-items-center" id="resetFilters">
                        <iconify-icon icon="solar:refresh-outline" class="me-1"></iconify-icon>
                        <span>Reset Filters</span>
                    </button>
                    <button type="button" class="btn btn-success btn-sm d-flex align-items-center" id="exportReport">
                        <iconify-icon icon="solar:download-outline" class="me-1"></iconify-icon>
                        <span>Export Report</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="card-body">
            <!-- Advanced Filters -->
            <div class="row g-3 mb-4 p-3 bg-light rounded-3">
                <div class="col-md-3">
                    <label class="form-label fw-semibold">Service Status</label>
                    <select class="form-select" id="serviceStatusFilter">
                        <option value="">All Status</option>
                        <option value="active">Active Services</option>
                        <option value="no_service">No Service</option>
                        <option value="expired">Expired Services</option>
                        <option value="expiring_soon">Expiring Soon (30 days)</option>
                        <option value="invoice_due_soon">Invoice Due Soon (7 days)</option>
                    </select>
                </div>

                <div class="col-md-3">
                    <label class="form-label fw-semibold">Service Type</label>
                    <select class="form-select" id="serviceTypeFilter">
                        <option value="">All Service Types</option>
                        @foreach($serviceTypes as $serviceType)
                            <option value="{{ $serviceType->id }}">{{ $serviceType->user_label }}</option>
                        @endforeach
                    </select>
                </div>

                @if(Auth::user()->role_id != 3)
                <div class="col-md-3">
                    <label class="form-label fw-semibold">Employee</label>
                    <select class="form-select" id="employeeFilter">
                        <option value="">All Employees</option>
                        @foreach($employees as $employee)
                            <option value="{{ $employee->id }}">{{ $employee->emp_name }}</option>
                        @endforeach
                    </select>
                </div>
                @endif

                <div class="col-md-3">
                    <label class="form-label fw-semibold">Area</label>
                    <select class="form-select" id="areaFilter">
                        <option value="">All Areas</option>
                        @foreach($areas as $area)
                            <option value="{{ $area }}">{{ $area }}</option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-3">
                    <label class="form-label fw-semibold">District</label>
                    <select class="form-select" id="districtFilter">
                        <option value="">All Districts</option>
                        @foreach($districts as $district)
                            <option value="{{ $district->id }}">{{ $district->name }}</option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-3">
                    <label class="form-label fw-semibold">Search</label>
                    <input type="text" class="form-control" id="searchFilter" placeholder="Search clients...">
                </div>

                <div class="col-md-3 d-flex align-items-end">
                    <button type="button" class="btn btn-primary w-100 d-flex align-items-center justify-content-center" id="applyFilters">
                        <iconify-icon icon="solar:filter-outline" class="me-1"></iconify-icon>
                        <span>Apply Filters</span>
                    </button>
                </div>
            </div>

            <!-- Data Table -->
            <div class="table-responsive">
                <table class="table table-striped table-hover align-middle" id="clientServicesTable" style="width: 100%">
                    <thead class="table-dark">
                        <tr>
                            <th>Client Info</th>
                            <th>Contact Info</th>
                            <th>Service Info</th>
                            <th>Status</th>
                            <th>Service Dates</th>
                            <th>Location</th>
                            <th>Employee</th>
                            <th>Service Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded via DataTables -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@endsection

@section('script')
<script>
$(document).ready(function() {
    // Initialize DataTable
    let table = $('#clientServicesTable').DataTable({
        processing: true,
        serverSide: true,
        searching: false,
        ajax: {
            url: "{{ route('reports.getClientServicesReport') }}",
            data: function(d) {
                d.service_status = $('#serviceStatusFilter').val();
                d.service_type = $('#serviceTypeFilter').val();
                d.employee_id = $('#employeeFilter').val();
                d.area = $('#areaFilter').val();
                d.district_id = $('#districtFilter').val();
                d.search = $('#searchFilter').val();
            }
        },
        columns: [
            { data: 'client_info', name: 'clients.name', orderable: true },
            { data: 'contact_info', name: 'clients.phone', orderable: false },
            { data: 'service_info', name: 'services.user_label', orderable: true },
            { data: 'status_badge', name: 'service_status', orderable: true },
            { data: 'dates_info', name: 'client_services.start_date', orderable: true },
            { data: 'location_info', name: 'clients.area', orderable: true },
            { data: 'employee_info', name: 'employees.emp_name', orderable: true },
            { data: 'service_value', name: 'client_services.total_price', orderable: true }
        ],
        order: [[0, 'asc']],
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        responsive: true,
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
        language: {
            processing: '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>',
            emptyTable: "No client service data available",
            zeroRecords: "No matching records found"
        },
        drawCallback: function(settings) {
            // Update statistics after table draw
            updateStatistics();
        }
    });

    // Apply filters
    $('#applyFilters').click(function() {
        table.ajax.reload();
        showToast('Filters applied successfully', 'success');
    });

    // Reset filters
    $('#resetFilters').click(function() {
        $('#serviceStatusFilter').val('');
        $('#serviceTypeFilter').val('');
        $('#employeeFilter').val('');
        $('#areaFilter').val('');
        $('#districtFilter').val('');
        $('#searchFilter').val('');
        table.ajax.reload();
        showToast('Filters reset successfully', 'info');
    });

    // Export functionality
    $('#exportReport').click(function() {
        let params = new URLSearchParams({
            service_status: $('#serviceStatusFilter').val(),
            service_type: $('#serviceTypeFilter').val(),
            employee_id: $('#employeeFilter').val(),
            area: $('#areaFilter').val(),
            district_id: $('#districtFilter').val(),
            search: $('#searchFilter').val()
        });

        // Remove empty parameters
        for (let [key, value] of [...params]) {
            if (!value) {
                params.delete(key);
            }
        }

        let exportUrl = "{{ route('reports.client-services.export') }}?" + params.toString();

        showToast('Preparing export...', 'info');

        // Create a temporary link and trigger download
        let link = document.createElement('a');
        link.href = exportUrl;
        link.download = 'client_services_report.csv';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setTimeout(() => {
            showToast('Export completed successfully', 'success');
        }, 1000);
    });

    // Quick filter buttons
    $('.stats-card').click(function() {
        let filterValue = $(this).data('filter');
        if (filterValue) {
            $('#serviceStatusFilter').val(filterValue);
            table.ajax.reload();
        }
    });

    // Search on enter key
    $('#searchFilter').keypress(function(e) {
        if (e.which == 13) {
            table.ajax.reload();
        }
    });

    // Auto-refresh every 5 minutes
    setInterval(function() {
        table.ajax.reload(null, false);
    }, 300000);

    function updateStatistics() {
        // This function can be used to update statistics in real-time
        // For now, we'll just show a subtle indication that data is fresh
        $('.card-header').addClass('border-success').removeClass('border-success');
    }

    function showToast(message, type = 'info') {
        // Create toast notification
        let toastClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-info';
        let toast = `
            <div class="toast align-items-center text-white ${toastClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        // Add to toast container (create if doesn't exist)
        if (!$('#toastContainer').length) {
            $('body').append('<div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3"></div>');
        }

        let $toast = $(toast);
        $('#toastContainer').append($toast);

        // Initialize and show toast
        let bsToast = new bootstrap.Toast($toast[0]);
        bsToast.show();

        // Remove from DOM after hiding
        $toast.on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }

    // Initialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();
});
</script>

<style>
.stats-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

.table th {
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.table-responsive {
    border-radius: 0.5rem;
    overflow: hidden;
}

.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border-radius: 0.375rem;
    border: 1px solid #e5e7eb;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #3b82f6 !important;
    border-color: #3b82f6 !important;
    color: white !important;
}

.toast-container {
    z-index: 9999;
}

/* Button styling fixes */
.btn {
    white-space: nowrap;
    display: inline-flex !important;
    align-items: center;
    gap: 0.25rem;
}

.btn iconify-icon {
    flex-shrink: 0;
    font-size: 1rem;
}

.btn span {
    flex-shrink: 0;
}

/* Ensure buttons don't wrap on smaller screens */
@media (max-width: 768px) {
    .btn-sm {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }

    .btn-sm iconify-icon {
        font-size: 0.9rem;
    }
}

/* Fix for filter section responsiveness */
.row.g-3.mb-4.p-3 {
    background: #f8f9fa;
    border-radius: 0.5rem;
}

/* Ensure proper alignment for action buttons */
.d-flex.align-items-center.gap-2 {
    flex-wrap: nowrap;
}

@media (max-width: 576px) {
    .d-flex.align-items-center.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
        align-items: stretch;
    }

    .btn {
        justify-content: center;
    }
}
</style>
@endsection
