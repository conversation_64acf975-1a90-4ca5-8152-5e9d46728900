@extends('layouts.master')
@section('title', 'GST Report - Paidash')
@section('content')
<style>
    .gst-summary-card {
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
    }
    .gst-summary-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .summary-metric {
        background: rgba(255,255,255,0.9);
        border-radius: 8px;
        transition: all 0.2s ease;
    }
    .summary-metric:hover {
        background: rgba(255,255,255,1);
        transform: scale(1.02);
    }
    .summary-icon {
        background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
        backdrop-filter: blur(10px);
    }
    .summary-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
    }
    .summary-title {
        display: flex;
        align-items: center;
        margin: 0;
        line-height: 1.2;
    }
    .summary-title iconify-icon {
        margin-right: 0.5rem;
        vertical-align: middle;
    }
    .gst-main-heading {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }
    .gst-main-heading iconify-icon {
        margin-right: 0.5rem;
        vertical-align: middle;
    }
    @media (max-width: 768px) {
        .gst-summary-card .row.g-2 {
            gap: 0.5rem !important;
        }
        .summary-metric {
            padding: 0.75rem !important;
        }
        .summary-title {
            font-size: 0.9rem;
        }
        .gst-main-heading {
            font-size: 1rem;
        }
    }
</style>
<div class="dashboard-main-body">
@if(session('success'))
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('success') }}</span>

        </div>
        <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
            <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
        </button>
    </div>
@endif
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">GST Report</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">GST Report</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
            <div class="d-flex flex-wrap align-items-center gap-3 w-25 w-mb-100">
                <div class="icon-field w-100">
                    <input type="text" name="searchkey" class="form-control form-control-sm w-100" placeholder="Search Invoice No" id="searchkey">
                    <span class="icon">
                        <iconify-icon icon="ion:search-outline"></iconify-icon>
                    </span>
                </div>

            </div>
            <div class="d-flex flex-no-wrap align-items-center gap-3">
                <select id="client" class="form-control form-select select2" title="select Client">
                    <option selected value="">Select Client</option>
                    <option value="">All</option>
                    @foreach ($clients as $client)
                         <option value="{{$client->id}}">{{$client->name}}</option>
                    @endforeach
                </select>


                <div class="input-group date_range " >
                    <input type="text" class="form-control" name="daterange" id="daterange">
                    <div class="input-group-append">
                        <span class="input-group-text" id="basic-addon2">
                            <iconify-icon icon="mdi:calendar" width="24" height="24"></iconify-icon>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- GST Summary Section -->
        <div class="card-body border-bottom">
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="gst-main-heading fw-semibold text-primary-600">
                        <iconify-icon icon="material-symbols:summarize" class="icon text-xl"></iconify-icon>
                        GST Summary Report
                    </h6>
                </div>
            </div>

            <div class="row g-4" id="gstSummaryCards">
                <!-- Summary cards will be loaded here -->
                <div class="col-12 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading summary...</span>
                    </div>
                    <p class="text-muted mt-2">Loading GST summary data...</p>
                </div>
            </div>
        </div>

        <div class="card-body">
            <!-- Table View for Desktop -->
            <div class="d-none d-md-block">
                <table id="gstTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                    <thead>
                        <tr>
                            <th>Invoice Date</th>
                            <th>Invoice No</th>
                            <th>Party Name</th>
                            <th>GST Number</th>
                            <th>Taxable amount</th>
                            <th>SGST</th>
                            <th>CGST</th>
                            <th>Total Invoice Value</th>
                            <th>Receviced Amount</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tfoot>
                        <tr>
                            <th colspan="4" class="text-end">Total</th>
                            <th id="totalInvoice" > <strong>₹ 0.00</strong></th>
                            <th id="totalPaid"> <strong>₹ 0.00</strong></th>
                            <th> <strong>₹ 0.00</strong></th>
                            <th> <strong>₹ 0.00</strong></th>
                            <th> <strong>₹ 0.00</strong></th>

                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- Grid View for Mobile -->
            <div id="clientsGrid" class="d-block d-md-none"></div>
        </div>
    </div>

</div>
<div id="exportLoader" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999;">
    <span class="spinner-border text-primary" role="status"></span>
    <strong>Exporting...</strong>
</div>
@stop
@section('script')
<script>
    $(document).ready(function () {
        $('#client').select2({
            width: '400px'
        });
        $('#searchStatus').select2({
            width: '200px'
        });
        var table = $('#gstTable').DataTable({
            processing: true,
            serverSide: true,
            searching: false,
            pageLength: 25,
            ajax: {
                url: "{{ route('reports.getGstReport') }}",
                data: function (d) {
                    d.searchkey = $('#searchkey').val();
                    d.daterange = $('#daterange').val();
                    d.client = $('#client').val();
                }
            },
            columns: [
                { data: 'invoice_date', name: 'invoice_date' },
                { data: 'invoice_code', name: 'invoice_code' },
                { data: 'client_name', name: 'client_name' },
                { data: 'gst', name: 'gst' },
                {
                    data: 'gross_amount',
                    name: 'gross_amount',
                    render: { _: 'display', sort: 'raw' }
                },{
                    data: 'cgst_amount',
                    name: 'cgst_amount',
                    render: { _: 'display', sort: 'raw' }
                },{
                    data: 'sgst_amount',
                    name: 'sgst_amount',
                    render: { _: 'display', sort: 'raw' }
                },{
                    data: 'total_amount_due',
                    name: 'total_amount_due',
                    render: { _: 'display', sort: 'raw' }
                },{
                    data: 'paid_amount',
                    name: 'paid_amount',
                    render: { _: 'display', sort: 'raw' }
                },


                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            drawCallback: function (settings) {
                var api = this.api();
                var data = api.rows().data();
            },
            dom:
            "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" + // Entries Dropdown & CSV Button
            "<'row'<'col-md-12'tr>>" +
            "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>", // Info at left, Pagination at right
            // buttons: [
            //     {
            //         extend: 'csv',
            //         text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
            //         className: 'btn btn-success btn-sm bg-success-500',
            //         filename: 'Clients_' + new Date().toISOString().slice(0, 10),
            //                             customize: function (csv) {
            //                                 return '\uFEFF' + csv; // Add BOM to ensure proper UTF-8 encoding
            //                             },
            //     }
            // ],
            buttons: [
                    {
                        text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
                        className: 'btn btn-success btn-sm bg-success-500',
                        action: function (e, dt, node, config) {
                            exportGstReport();
                        }
                    }
                ],
            infoCallback: function(settings, start, end, max, total, pre) {
                return `Showing ${start} to ${end} of ${total} records`;
            },
            footerCallback: function(row, data, start, end, display) {
                var api = this.api();

                // Function to apply Indian Number Format
                function IND_money_format(num) {
                    return num.toLocaleString('en-IN', { minimumFractionDigits: 0, maximumFractionDigits: 0 });
                }

                function calculateTotal(columnIndex) {
                    return api
                        .column(columnIndex, { page: 'current' })
                        .data()
                        .reduce(function(a, b) {
                            var value = typeof b === 'object' ? parseFloat(b.raw) : parseFloat(b);
                            return (a || 0) + (value || 0);
                        }, 0);
                }

                // var totalInvoice = calculateTotal(3);
                // var totalPaid = calculateTotal(4);
                // var totalPending = calculateTotal(5);

                $(api.column(4).footer()).html(`<strong>₹ ${IND_money_format( calculateTotal(4))}</strong>`);
                $(api.column(5).footer()).html(`<strong>₹ ${IND_money_format( calculateTotal(5))}</strong>`);
                $(api.column(6).footer()).html(`<strong>₹ ${IND_money_format( calculateTotal(6))}</strong>`);
                $(api.column(7).footer()).html(`<strong>₹ ${IND_money_format( calculateTotal(7))}</strong>`);
                $(api.column(8).footer()).html(`<strong>₹ ${IND_money_format( calculateTotal(8))}</strong>`);
            }

        });

        // Load GST Summary on page load
        loadGstSummary();

        // Custom search event triggers
        $("#searchkey, #searchEmail").on("keyup", function () {
            table.draw();
            loadGstSummary(); // Reload summary when filters change
        });

        $("#employee_id, #client").on("change", function () {
            table.draw();
            loadGstSummary(); // Reload summary when filters change
        });
        $('#daterange').on('apply.daterangepicker', function () {
            table.draw();
            loadGstSummary(); // Reload summary when filters change
        });

        // Function to load GST Summary
        function loadGstSummary() {
            $.ajax({
                url: "{{ route('reports.getGstSummary') }}",
                type: "POST",
                data: {
                    searchkey: $('#searchkey').val(),
                    daterange: $('#daterange').val(),
                    client: $('#client').val(),
                    _token: "{{ csrf_token() }}"
                },
                success: function(response) {
                    displayGstSummary(response);
                },
                error: function(xhr, status, error) {
                    console.error('Error loading GST summary:', error);
                    $('#gstSummaryCards').html('<div class="col-12 text-center text-danger">Error loading summary data</div>');
                }
            });
        }

        // Function to display GST Summary
        function displayGstSummary(data) {
            let summaryHtml = '';

            // Clients with GST
            summaryHtml += `
                <div class="col-lg-4 col-md-6">
                    <div class="card gst-summary-card border border-success-200 bg-success-50 h-100">
                        <div class="card-body p-20">
                            <div class="summary-header">
                                <h6 class="summary-title fw-semibold text-success-600">
                                    <iconify-icon icon="material-symbols:verified" class="icon"></iconify-icon>
                                    Clients with GST
                                </h6>
                                <div class="w-40-px h-40-px bg-success-600 rounded-circle d-flex justify-content-center align-items-center summary-icon">
                                    <iconify-icon icon="material-symbols:check-circle" class="text-white text-xl"></iconify-icon>
                                </div>
                            </div>
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="text-center p-2 summary-metric">
                                        <p class="text-xs text-muted mb-1">Invoices</p>
                                        <h6 class="fw-bold text-success-600 mb-0">${data.with_gst.count}</h6>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 summary-metric">
                                        <p class="text-xs text-muted mb-1">Taxable Amount</p>
                                        <h6 class="fw-bold text-success-600 mb-0">₹${formatMoney(data.with_gst.taxable_amount)}</h6>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 summary-metric">
                                        <p class="text-xs text-muted mb-1">Tax Amount</p>
                                        <h6 class="fw-bold text-success-600 mb-0">₹${formatMoney(data.with_gst.tax_amount)}</h6>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 summary-metric">
                                        <p class="text-xs text-muted mb-1">Total Value</p>
                                        <h6 class="fw-bold text-success-600 mb-0">₹${formatMoney(data.with_gst.total_value)}</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Clients without GST
            summaryHtml += `
                <div class="col-lg-4 col-md-6">
                    <div class="card gst-summary-card border border-warning-200 bg-warning-50 h-100">
                        <div class="card-body p-20">
                            <div class="summary-header">
                                <h6 class="summary-title fw-semibold text-warning-600">
                                    <iconify-icon icon="material-symbols:warning" class="icon"></iconify-icon>
                                    Clients without GST
                                </h6>
                                <div class="w-40-px h-40-px bg-warning-600 rounded-circle d-flex justify-content-center align-items-center summary-icon">
                                    <iconify-icon icon="material-symbols:cancel" class="text-white text-xl"></iconify-icon>
                                </div>
                            </div>
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="text-center p-2 summary-metric">
                                        <p class="text-xs text-muted mb-1">Invoices</p>
                                        <h6 class="fw-bold text-warning-600 mb-0">${data.without_gst.count}</h6>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 summary-metric">
                                        <p class="text-xs text-muted mb-1">Taxable Amount</p>
                                        <h6 class="fw-bold text-warning-600 mb-0">₹${formatMoney(data.without_gst.taxable_amount)}</h6>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 summary-metric">
                                        <p class="text-xs text-muted mb-1">Tax Amount</p>
                                        <h6 class="fw-bold text-warning-600 mb-0">₹${formatMoney(data.without_gst.tax_amount)}</h6>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 summary-metric">
                                        <p class="text-xs text-muted mb-1">Total Value</p>
                                        <h6 class="fw-bold text-warning-600 mb-0">₹${formatMoney(data.without_gst.total_value)}</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Total (Both)
            summaryHtml += `
                <div class="col-lg-4 col-md-12">
                    <div class="card gst-summary-card border border-primary-200 bg-primary-50 h-100">
                        <div class="card-body p-20">
                            <div class="summary-header">
                                <h6 class="summary-title fw-semibold text-primary-600">
                                    <iconify-icon icon="material-symbols:analytics" class="icon"></iconify-icon>
                                    Total Summary
                                </h6>
                                <div class="w-40-px h-40-px bg-primary-600 rounded-circle d-flex justify-content-center align-items-center summary-icon">
                                    <iconify-icon icon="material-symbols:summarize" class="text-white text-xl"></iconify-icon>
                                </div>
                            </div>
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="text-center p-2 summary-metric">
                                        <p class="text-xs text-muted mb-1">Total Invoices</p>
                                        <h6 class="fw-bold text-primary-600 mb-0">${data.total.count}</h6>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 summary-metric">
                                        <p class="text-xs text-muted mb-1">Total Taxable</p>
                                        <h6 class="fw-bold text-primary-600 mb-0">₹${formatMoney(data.total.taxable_amount)}</h6>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 summary-metric">
                                        <p class="text-xs text-muted mb-1">Total Tax</p>
                                        <h6 class="fw-bold text-primary-600 mb-0">₹${formatMoney(data.total.tax_amount)}</h6>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 summary-metric">
                                        <p class="text-xs text-muted mb-1">Grand Total</p>
                                        <h6 class="fw-bold text-primary-600 mb-0">₹${formatMoney(data.total.total_value)}</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#gstSummaryCards').html(summaryHtml);
        }

        // Helper function to format money
        function formatMoney(amount) {
            return new Intl.NumberFormat('en-IN', {
                maximumFractionDigits: 0,
                minimumFractionDigits: 0
            }).format(amount || 0);
        }
    });
    var start = moment().subtract(29, 'days');  // Default start date
    var end = moment();  // Default end date
     function cb(start, end) {
    $('#daterange').val(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
}

$('#daterange').daterangepicker({
    startDate: start,
    endDate: end,
    autoApply: true,
    locale: { format: 'DD/MM/YYYY' }, // Set format
    ranges: {
       'Today': [moment(), moment()],
       'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
       'Last 7 Days': [moment().subtract(6, 'days'), moment()],
       'Last 30 Days': [moment().subtract(29, 'days'), moment()],
       'This Month': [moment().startOf('month'), moment().endOf('month')],
       'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    }
}, cb);

// Set initial date display
cb(start, end);
// Custom search event triggers

// Export function with all filters
function exportGstReport() {
    // Show loader
    $('#exportLoader').show();

    // Get current filter values
    var filters = {
        searchkey: $('#searchkey').val(),
        client: $('#client').val(),
        daterange: $('#daterange').val()
    };

    // Build export URL with filters
    var exportUrl = "{{ route('reports.gst.export') }}?" + $.param(filters);

    // Create temporary link and trigger download
    var link = document.createElement('a');
    link.href = exportUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Hide loader after a short delay
    setTimeout(() => {
        $('#exportLoader').hide();
    }, 1000);
}
</script>
@stop
