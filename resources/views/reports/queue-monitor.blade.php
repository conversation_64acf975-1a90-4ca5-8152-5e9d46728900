@extends('layouts.master')
@section('title', 'Queue Monitor - Paidash')
@section('content')

<style>
    .stats-card {
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
        min-height: 120px;
    }
    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .stats-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        flex-shrink: 0;
    }

    .stats-icon i {
        font-size: 20px !important;
        line-height: 1 !important;
        display: block !important;
    }
    .stats-content {
        flex: 1;
        min-width: 0;
    }
    .stats-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .stats-number {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 2px;
        line-height: 1;
    }
    .stats-subtitle {
        font-size: 12px;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .activity-item {
        border-left: 3px solid #e9ecef;
        padding-left: 15px;
        margin-bottom: 10px;
        position: relative;
        font-size: 13px;
    }
    .activity-item::before {
        content: '';
        position: absolute;
        left: -6px;
        top: 8px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #6c757d;
    }
    .activity-item.success::before {
        background: #28a745;
    }
    .activity-item.error::before {
        background: #dc3545;
    }
    .activity-item.processing::before {
        background: #007bff;
    }
    .queue-config-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 8px 12px;
        margin-bottom: 6px;
        font-size: 13px;
    }
    .auto-refresh {
        animation: spin 1s linear infinite;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Fix layout stability */
    .queue-control-panel {
        position: relative;
        z-index: 1;
    }

    .stats-card {
        min-height: 120px;
    }

    .control-btn {
        min-height: 38px;
    }

    /* Prevent layout shifts */
    .card {
        transform: translateZ(0);
    }

    /* Auto refresh toggle styling */
    .form-check-label {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 0;
        cursor: pointer;
    }

    .form-check-input {
        margin-right: 8px;
    }
    .control-btn {
        white-space: nowrap;
        font-size: 14px;
    }
    .form-check-label {
        font-size: 13px;
        white-space: nowrap;
    }
    .card-header h6 {
        font-size: 15px;
        margin: 0;
    }
    .nav-tabs .nav-link {
        font-size: 13px;
        padding: 8px 12px;
    }
    .table th, .table td {
        font-size: 12px;
        padding: 8px;
    }

    /* Custom animations */
    .animate-spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Text size utilities */
    .text-4xl {
        font-size: 2.25rem;
        line-height: 2.5rem;
    }

    /* Icon alignment fixes */
    iconify-icon {
        display: inline-flex;
        align-items: center;
        vertical-align: middle;
    }

    .btn iconify-icon {
        margin-right: 0.5rem;
    }

    .nav-link iconify-icon {
        margin-right: 0.25rem;
    }

    h6 iconify-icon {
        margin-right: 0.5rem;
    }
</style>

<div class="dashboard-main-body">
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Queue Monitor</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="{{ route('dashboard') }}" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Reports</li>
            <li>-</li>
            <li class="fw-medium">Queue Monitor</li>
        </ul>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card shadow-none border bg-gradient-start-2 h-100">
                <div class="card-body p-20">
                    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                        <div>
                            <p class="fw-medium text-primary-light mb-1">Pending Jobs</p>
                            <h6 class="mb-0" id="pending-jobs">{{ $stats['pending_jobs'] }}</h6>
                            <p class="fw-medium text-sm text-primary-light mb-0">In Queue</p>
                        </div>
                        <div class="w-50-px h-50-px bg-primary-600 rounded-circle d-flex justify-content-center align-items-center">
                            <iconify-icon icon="solar:clock-circle-outline" class="text-white text-2xl mb-0"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card shadow-none border bg-gradient-start-3 h-100">
                <div class="card-body p-20">
                    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                        <div>
                            <p class="fw-medium text-primary-light mb-1">Failed Jobs</p>
                            <h6 class="mb-0" id="failed-jobs">{{ $stats['failed_jobs'] }}</h6>
                            <p class="fw-medium text-sm text-primary-light mb-0">Total Failed</p>
                        </div>
                        <div class="w-50-px h-50-px bg-danger-main rounded-circle d-flex justify-content-center align-items-center">
                            <iconify-icon icon="solar:danger-triangle-outline" class="text-white text-2xl mb-0"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card shadow-none border bg-gradient-start-4 h-100">
                <div class="card-body p-20">
                    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                        <div>
                            <p class="fw-medium text-primary-light mb-1">Success Jobs</p>
                            <h6 class="mb-0" id="success-jobs">{{ $stats['success_jobs'] ?? 0 }}</h6>
                            <p class="fw-medium text-sm text-primary-light mb-0">Last 48 Hours</p>
                        </div>
                        <div class="w-50-px h-50-px bg-success-main rounded-circle d-flex justify-content-center align-items-center">
                            <iconify-icon icon="solar:check-circle-outline" class="text-white text-2xl mb-0"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card shadow-none border bg-gradient-start-1 h-100">
                <div class="card-body p-20">
                    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                        <div>
                            <p class="fw-medium text-primary-light mb-1">Queue Status</p>
                            <h6 class="mb-0">Active</h6>
                            <p class="fw-medium text-sm text-primary-light mb-0" id="last-updated">{{ $stats['last_updated'] }}</p>
                        </div>
                        <div class="w-50-px h-50-px bg-cyan rounded-circle d-flex justify-content-center align-items-center">
                            <iconify-icon icon="material-symbols:monitor-heart" class="text-white text-2xl mb-0"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Control Panel -->
    <div class="row g-3 mb-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm queue-control-panel">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex flex-wrap align-items-center justify-content-between gap-2">
                        <h6 class="fw-semibold mb-0">
                            <iconify-icon icon="solar:settings-outline" class="me-2"></iconify-icon>
                            Queue Control Panel
                        </h6>
                        <div class="d-flex flex-wrap align-items-center gap-3">
                            <button class="btn btn-sm btn-outline-primary d-flex align-items-center" id="refresh-stats">
                                <iconify-icon icon="solar:refresh-outline" id="refresh-icon"></iconify-icon>
                                <span>Refresh</span>
                            </button>
                            <div class="form-check form-switch mb-0">
                                <input class="form-check-input" type="checkbox" id="auto-refresh" checked>
                                <label class="form-check-label d-flex align-items-center" for="auto-refresh">
                                    <iconify-icon icon="solar:refresh-circle-outline" class="me-1"></iconify-icon>
                                    Auto Refresh
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-15">
                    <div class="row g-2">
                        <div class="col-md-6">
                            <button class="btn btn-primary w-100 d-flex align-items-center justify-content-center" id="process-queue">
                                <iconify-icon icon="solar:play-outline" class="me-2"></iconify-icon>
                                Process Queue Now
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button class="btn btn-danger w-100 d-flex align-items-center justify-content-center" id="clear-failed">
                                <iconify-icon icon="solar:trash-bin-minimalistic-outline" class="me-2"></iconify-icon>
                                Clear Failed Jobs
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="fw-semibold mb-0">
                        <iconify-icon icon="solar:settings-outline" class="me-2"></iconify-icon>
                        Queue Configuration
                    </h6>
                </div>
                <div class="card-body p-15">
                    <div class="queue-config-item">
                        <strong>Connection:</strong> {{ $stats['queue_config']['connection'] ?? 'N/A' }}
                    </div>
                    <div class="queue-config-item">
                        <strong>Email Queue:</strong> {{ $stats['queue_config']['email_queue'] ?? 'N/A' }}
                    </div>
                    <div class="queue-config-item">
                        <strong>Retry Attempts:</strong> {{ $stats['queue_config']['retry_attempts'] ?? 'N/A' }}
                    </div>
                    <div class="queue-config-item">
                        <strong>Delay Range:</strong> {{ $stats['queue_config']['delay_range'] ?? 'N/A' }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity and Jobs Monitoring -->
    <div class="row g-3">
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="card-header bg-white border-bottom">
                        <h6 class="fw-semibold mb-0 d-flex align-items-center">
                            <iconify-icon icon="solar:clock-circle-outline" class="me-2"></iconify-icon>
                            Recent Activity
                        </h6>
                    </div>
                </div>
                <div class="card-body p-15">
                    <div id="recent-activity">
                        @if(count($stats['recent_activity']) > 0)
                            @foreach($stats['recent_activity'] as $activity)
                                <div class="activity-item border-bottom pb-2 mb-2">
                                    <small class="text-muted">{{ $activity['timestamp'] }}</small>
                                    <p class="mb-0">{{ $activity['message'] }}</p>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center text-muted py-3">
                                <iconify-icon icon="solar:clock-circle-outline" class="text-4xl mb-2 opacity-50"></iconify-icon>
                                <p class="mb-0">No recent activity</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="fw-semibold mb-0">
                        <iconify-icon icon="solar:danger-triangle-outline" class="me-2"></iconify-icon>
                        Failed Jobs
                    </h6>
                </div>
                <div class="card-body p-15">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm" id="failed-jobs-table">
                            <thead>
                                <tr>
                                    <th>Job</th>
                                    <th>Queue</th>
                                    <th>Failed</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('js')
<script>
$(document).ready(function() {
    let autoRefreshInterval;

    // Initialize DataTable for failed jobs
    const failedJobsTable = $('#failed-jobs-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: "{{ route('queue-monitor.failed-jobs') }}",
        columns: [
            {data: 'job_class', name: 'job_class'},
            {data: 'queue', name: 'queue'},
            {data: 'failed_time', name: 'failed_time'},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        pageLength: 5,
        lengthChange: false,
        info: false
    });

    // Auto refresh functionality
    function startAutoRefresh() {
        autoRefreshInterval = setInterval(refreshStats, 30000); // Refresh every 30 seconds
    }

    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    }

    // Refresh statistics
    function refreshStats() {
        $('#refresh-icon').addClass('auto-refresh');

        $.get("{{ route('queue-monitor.stats') }}")
            .done(function(data) {
                $('#pending-jobs').text(data.pending_jobs);
                $('#failed-jobs').text(data.failed_jobs);
                $('#success-jobs').text(data.success_jobs || 0);
                $('#last-updated').text(data.last_updated);

                // Update recent activity
                updateRecentActivity(data.recent_activity || []);

                // Refresh failed jobs table
                failedJobsTable.ajax.reload(null, false);
            })
            .always(function() {
                $('#refresh-icon').removeClass('auto-refresh');
            });
    }

    // Update recent activity
    function updateRecentActivity(activities) {
        const container = $('#recent-activity');
        if (activities.length > 0) {
            let html = '';
            activities.forEach(function(activity) {
                html += `
                    <div class="activity-item success">
                        <small class="text-muted">${activity.timestamp}</small>
                        <p class="mb-0">${activity.message}</p>
                    </div>
                `;
            });
            container.html(html);
        } else {
            container.html(`
                <div class="text-center text-muted py-3">
                    <i class="fas fa-clock fa-3x mb-2 opacity-50"></i>
                    <p class="mb-0">No recent activity</p>
                </div>
            `);
        }
    }



    // Manual refresh
    $('#refresh-stats').click(function() {
        refreshStats();
    });

    // Auto refresh toggle
    $('#auto-refresh').change(function() {
        if ($(this).is(':checked')) {
            startAutoRefresh();
        } else {
            stopAutoRefresh();
        }
    });

    // Process queue
    $('#process-queue').click(function() {
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Processing...');

        $.post("{{ route('queue-monitor.process') }}")
            .done(function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    refreshStats();
                } else {
                    toastr.error(response.message);
                }
            })
            .fail(function() {
                toastr.error('Failed to process queue');
            })
            .always(function() {
                btn.prop('disabled', false).html('<iconify-icon icon="material-symbols:play-arrow" class="me-2"></iconify-icon>Process Queue Now');
            });
    });

    // Clear failed jobs
    $('#clear-failed').click(function() {
        if (confirm('Are you sure you want to clear all failed jobs?')) {
            const btn = $(this);
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Clearing...');

            $.post("{{ route('queue-monitor.clear-failed') }}")
                .done(function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        refreshStats();
                    } else {
                        toastr.error(response.message);
                    }
                })
                .fail(function() {
                    toastr.error('Failed to clear failed jobs');
                })
                .always(function() {
                    btn.prop('disabled', false).html('<iconify-icon icon="material-symbols:delete" class="me-2"></iconify-icon>Clear Failed Jobs');
                });
        }
    });

    // Retry individual job
    $(document).on('click', '.retry-job', function() {
        const jobId = $(this).data('id');
        const btn = $(this);

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');

        $.post("{{ route('queue-monitor.retry') }}", {
            job_id: jobId,
            _token: "{{ csrf_token() }}"
        })
        .done(function(response) {
            if (response.success) {
                toastr.success(response.message);
                failedJobsTable.ajax.reload();
                refreshStats();
            } else {
                toastr.error(response.message);
            }
        })
        .fail(function() {
            toastr.error('Failed to retry job');
        })
        .always(function() {
            btn.prop('disabled', false).html('<i class="fas fa-redo"></i> Retry');
        });
    });

    // Delete individual job
    $(document).on('click', '.delete-job', function() {
        if (confirm('Are you sure you want to delete this job?')) {
            const jobId = $(this).data('id');
            const btn = $(this);

            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');

            $.post("{{ route('queue-monitor.delete') }}", {
                job_id: jobId,
                _token: "{{ csrf_token() }}"
            })
            .done(function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    failedJobsTable.ajax.reload();
                    refreshStats();
                } else {
                    toastr.error(response.message);
                }
            })
            .fail(function() {
                toastr.error('Failed to delete job');
            })
            .always(function() {
                btn.prop('disabled', false).html('<i class="fas fa-trash"></i> Delete');
            });
        }
    });



    // Start auto refresh by default
    startAutoRefresh();
});
</script>
@endsection
