@extends('layouts.master')
@section('title', 'Employee View - Paidash')

@section('content')
@if (session('success'))
            <div id="snackbar"
                class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
                <div class="d-flex align-items-center gap-2">
                    <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
                    <span id="snackbar-message"> {{ session('success') }}</span>

                </div>
                <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
                    <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
                </button>
            </div>
        @endif
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">View Employee </h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="/employees" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:user-check-broken" class="icon text-lg"></iconify-icon>
                        Employees
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">View Employee</li>
            </ul>
        </div>

        <div class="row gy-4">

            <div class="col-lg-12">
                <div class="card h-100">
                    <div class="card-body p-24">
                        <ul class="nav border-gradient-tab nav-pills mb-20 d-inline-flex w-100" id="pills-tab"
                            role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24 active" id="emp-details-tab"
                                    data-bs-toggle="pill" data-bs-target="#emp-details" type="button" role="tab"
                                    aria-controls="emp-details" aria-selected="true">
                                    Employee Details
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="emp-dashboard-tab"
                                    data-bs-toggle="pill" data-bs-target="#emp-dashboard" type="button" role="tab"
                                    aria-controls="emp-dashboard" aria-selected="true">
                                    Dash Board
                                </button>
                            </li>
                            @if ($enable['clients'])
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center px-24" id="emp-clients-tab"
                                        data-bs-toggle="pill" data-bs-target="#emp-clients" type="button"
                                        role="tab" aria-controls="emp-clients" aria-selected="false" tabindex="-1">
                                        Clients
                                    </button>
                                </li>
                            @endif
                            @if ($enable['invoices'])
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center px-24" id="emp-invoices-tab"
                                        data-bs-toggle="pill" data-bs-target="#emp-invoices" type="button"
                                        role="tab" aria-controls="emp-invoices" aria-selected="false"
                                        tabindex="-1">
                                        invoices
                                    </button>
                                </li>
                            @endif
                            @if ($enable['payments'])
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center px-24" id="emp-payments-tab"
                                        data-bs-toggle="pill" data-bs-target="#emp-payments" type="button"
                                        role="tab" aria-controls="emp-payments" aria-selected="false"
                                        tabindex="-1">
                                        Payments
                                    </button>
                                </li>
                            @endif
                            @if ($enable['password_change'])
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center px-24" id="change-password-tab"
                                        data-bs-toggle="pill" data-bs-target="#change-password" type="button"
                                        role="tab" aria-controls="change-password" aria-selected="false"
                                        tabindex="-1">
                                        Change Password
                                    </button>
                                </li>
                            @endif
                        </ul>

                        <div class="tab-content" id="pills-tabContent">
                            <div class="tab-pane fade show active" id="emp-details" role="tabpanel"
                                aria-labelledby="emp-detail-tab" tabindex="0">
                                    <div class="user-grid-card position-relative border radius-16 overflow-hidden bg-base h-100">
                                        <div class="pb-24 ms-16 mb-24 me-16">
                                            <div class="card border-0 shadow-none rounded-3">
                                                <div class="card-body d-flex align-items-center justify-content-between gap-3 p-4 border-bottom">
                                                    <div class="d-flex align-items-center gap-3">
                                                        @if ($employee->photo && Storage::exists('public/' . $employee->photo))
                                                            <img src="{{ asset('storage/' . $employee->photo) }}" alt="Profile Image"
                                                                class="rounded-circle object-fit-cover border border-white" style="width: 80px; height: 80px;">
                                                        @else
                                                            <div class="w-64 h-64 rounded-circle d-flex justify-content-center align-items-center bg-info-100 text-info-600 fw-bold fs-4 px-16 py-4">
                                                                {{ strtoupper(substr($employee->emp_name, 0, 1)) }}
                                                            </div>
                                                        @endif

                                                        <div>
                                                            <h5 class="mb-1 fw-semibold">{{ $employee->emp_name }}</h5>
                                                            <p class="mb-0 text-muted">{{ $employee->email }}</p>
                                                        </div>
                                                    </div>

                                                    <div class="d-flex align-items-center gap-3">
                                                        <!-- Change Logs Button -->
                                                        <a href="{{ route('employees.change-logs', $employee->id) }}" class="btn btn-outline-primary btn-sm d-flex align-items-center gap-2">
                                                            <iconify-icon icon="mdi:history" width="16"></iconify-icon>
                                                            Change Logs
                                                        </a>

                                                        @if (Auth::user()->id != $employee->user_id && Auth::user()->can('employee-edit') && $employee->user)
                                                            <div class="form-switch switch-success d-flex align-items-center gap-2">
                                                                <input class="form-check-input" type="checkbox" role="switch" id="employeeStatusSwitch"
                                                                    data-id="{{ $employee->id }}" {{ $employee->user && $employee->user->status == 1 ? 'checked' : '' }}>
                                                                <label class="form-check-label fw-medium text-secondary-light mb-0" for="employeeStatusSwitch">
                                                                    {{ $employee->user && $employee->user->status == 1 ? 'Active' : 'Inactive' }}
                                                                </label>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>


                                            <div class="mt-24">
                                                <div class="row mt-4">
                                                    <div class="col-6">
                                                        <!-- Personal Info Section -->
                                                        <div class="card shadow-none border mb-3">
                                                            <div class="card-header border-primary d-flex align-items-center">
                                                                <iconify-icon icon="mdi:account-circle" class="me-2"></iconify-icon>
                                                                <strong>Personal Info</strong>
                                                            </div>
                                                            <div class="card-body">
                                                                <ul class="list-unstyled mb-0">
                                                                    <li class="d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:account" class="me-2 text-primary"></iconify-icon>
                                                                        <strong>Full Name:</strong> <span
                                                                            class="ms-2">{{ $employee->emp_name }}</span>
                                                                    </li>
                                                                    <li class="d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:cake-variant"
                                                                            class="me-2 text-primary"></iconify-icon>
                                                                        <strong>Date of Birth:</strong> <span
                                                                            class="ms-2">{{ date('m-d-Y', strtotime($employee->dob)) }}</span>
                                                                    </li>
                                                                    <li class="d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:gender-male-female"
                                                                            class="me-2 text-primary"></iconify-icon>
                                                                        <strong>Gender:</strong> <span class="ms-2">{{ $employee->gender }}</span>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <!-- Professional Info Section -->
                                                        <div class="card shadow-none border mb-3">
                                                            <div class="card-header border-success d-flex align-items-center">
                                                                <iconify-icon icon="mdi:briefcase" class="me-2"></iconify-icon>
                                                                <strong>Professional Info</strong>
                                                            </div>
                                                            <div class="card-body">
                                                                <ul class="list-unstyled mb-0">
                                                                    <li class="d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:domain" class="me-2 text-success"></iconify-icon>
                                                                        <strong>Department:</strong> <span
                                                                            class="ms-2">{{ $employee->department }}</span>
                                                                    </li>
                                                                    <li class="d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:card-account-details"
                                                                            class="me-2 text-success"></iconify-icon>
                                                                        <strong>Designation:</strong> <span
                                                                            class="ms-2">{{ $employee->job_title }}</span>
                                                                    </li>
                                                                    <li class="d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:account-cog"
                                                                            class="me-2 text-success"></iconify-icon>
                                                                        <strong>Role:</strong> <span
                                                                            class="ms-2">{{ $employee->role->name }}</span>
                                                                    </li>
                                                                    <li class="d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:calendar" class="me-2 text-success"></iconify-icon>
                                                                        <strong>Date of Joining:</strong> <span
                                                                            class="ms-2">{{ date('m-d-Y', strtotime($employee->doj)) }}</span>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>


                                                    </div>
                                                    <div class="col-6">
                                                        <!-- Admin Info Section -->
                                                        <div class="card shadow-none mb-3 border">
                                                            <div class="card-header border-info d-flex align-items-center">
                                                                <iconify-icon icon="mdi:shield-lock" class="me-2"></iconify-icon>
                                                                <strong>Admin Info</strong>
                                                            </div>
                                                            <div class="card-body">
                                                                <ul class="list-unstyled mb-0">
                                                                    <li class="d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:email" class="me-2 text-info"></iconify-icon>
                                                                        <strong>Email:</strong> <span class="ms-2">{{ $employee->email }}</span>
                                                                    </li>
                                                                    <li class="d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:phone" class="me-2 text-info"></iconify-icon>
                                                                        <strong>Phone Number:</strong> <span
                                                                            class="ms-2">{{ $employee->phone }}</span>
                                                                    </li>
                                                                    <li class="d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:map-marker" class="me-2 text-info"></iconify-icon>
                                                                        <strong>Address:</strong> <span class="ms-2">{{ $employee->address }},
                                                                            {{ $employee->city }}, {{ $employee->district->name }},
                                                                            {{ $employee->state->name }}, {{ $employee->zipcode }}</span>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>


                                            </div>
                                        </div>
                                    </div>
                            </div>
                            <div class="tab-pane fade show" id="emp-dashboard" role="tabpanel"
                                aria-labelledby="emp-dashboard-tab" tabindex="0">
                                <div class="card shadow-none border">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h7 class="mb-0"></h7>
                                        {{-- <div>
                                        <input
                                            type="date"
                                            class="form-control"
                                            name=""
                                            id=""
                                            aria-describedby="emailHelpId"
                                            placeholder="<EMAIL>"
                                        />
                                    </div> --}}

                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-4">
                                            @if ($enable['dashboard'])
                                                <div class="col-md-6">
                                                    <div class="card shadow-none border bg-gradient-start-2 h-100">
                                                        <div class="card-body p-20">
                                                            <div
                                                                class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                                                                <div>
                                                                    <p class="fw-medium text-primary-light mb-1">Total
                                                                        Revenue</p>
                                                                    <h6 class="mb-0">
                                                                        ₹{{ App\Http\Controllers\Controller::IND_money_format($dashboard_data['total_revenue'],0) }}
                                                                    </h6>
                                                                </div>
                                                                <div
                                                                    class="w-50-px h-50-px bg-purple rounded-circle d-flex justify-content-center align-items-center">
                                                                    <iconify-icon icon="gridicons:money"
                                                                        class="text-white text-2xl mb-0"></iconify-icon>
                                                                </div>
                                                            </div>
                                                            <p
                                                                class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                                                                {{ App\Http\Controllers\Controller::IND_money_format($dashboard_data['total_invoices'],0) }}
                                                                Invoices
                                                            </p>
                                                        </div>
                                                    </div><!-- card end -->
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card shadow-none border bg-gradient-start-3 h-100">
                                                        <div class="card-body p-20">
                                                            <div
                                                                class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                                                                <div>
                                                                    <p class="fw-medium text-primary-light mb-1">Pending
                                                                        Payments</p>
                                                                    <h6 class="mb-0">
                                                                        ₹{{ App\Http\Controllers\Controller::IND_money_format($dashboard_data['total_pending_amount'],0) }}
                                                                    </h6>
                                                                </div>
                                                                <div
                                                                    class="w-50-px h-50-px bg-info rounded-circle d-flex justify-content-center align-items-center">
                                                                    <iconify-icon icon="hugeicons:clock-02"
                                                                        class="text-white text-2xl mb-0"></iconify-icon>
                                                                </div>
                                                            </div>
                                                            <p
                                                                class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                                                                {{ App\Http\Controllers\Controller::IND_money_format($dashboard_data['pending_invoices_count'],0) }}
                                                                Invoices
                                                            </p>
                                                        </div>
                                                    </div><!-- card end -->
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card shadow-none border bg-gradient-start-4 h-100">
                                                        <div class="card-body p-20">
                                                            <div
                                                                class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                                                                <div>
                                                                    <p class="fw-medium text-primary-light mb-1">Total
                                                                        Income</p>
                                                                    <h6 class="mb-0">
                                                                        ₹{{ App\Http\Controllers\Controller::IND_money_format($dashboard_data['total_paid_amount'],0) }}
                                                                    </h6>
                                                                </div>
                                                                <div
                                                                    class="w-50-px h-50-px bg-success-main rounded-circle d-flex justify-content-center align-items-center">
                                                                    <iconify-icon icon="solar:wallet-bold"
                                                                        class="text-white text-2xl mb-0"></iconify-icon>
                                                                </div>
                                                            </div>
                                                            <p
                                                                class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                                                                {{ App\Http\Controllers\Controller::IND_money_format($dashboard_data['paid_invoices_count'],0) }}
                                                                Invoices
                                                            </p>
                                                        </div>
                                                    </div><!-- card end -->
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card shadow-none border bg-gradient-start-1 h-100">
                                                        <div class="card-body p-20">
                                                            <div
                                                                class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                                                                <div>
                                                                    <p class="fw-medium text-primary-light mb-1">Total
                                                                        Active Clients</p>
                                                                    <h6 class="mb-0">
                                                                        {{ App\Http\Controllers\Controller::IND_money_format($dashboard_data['active_clients'],0) }}
                                                                    </h6>
                                                                </div>
                                                                <div
                                                                    class="w-50-px h-50-px bg-cyan rounded-circle d-flex justify-content-center align-items-center">
                                                                    <iconify-icon icon="gridicons:multiple-users"
                                                                        class="text-white text-2xl mb-0"></iconify-icon>
                                                                </div>
                                                            </div>
                                                            {{-- <p class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                                                        {{App\Http\Controllers\Controller::IND_money_format($dashboard_data['total_revenue'])}} Invoices
                                                    </p> --}}
                                                        </div>
                                                    </div><!-- card end -->
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card shadow-none border bg-gradient-start-5 h-100">
                                                        <div class="card-body p-20">
                                                            <div
                                                                class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                                                                <div>
                                                                    <p class="fw-medium text-primary-light mb-1">Inactive
                                                                        clients</p>
                                                                    <h6 class="mb-0">
                                                                        {{ App\Http\Controllers\Controller::IND_money_format($dashboard_data['inactive_clients'],0) }}
                                                                    </h6>
                                                                </div>
                                                                <div
                                                                    class="w-50-px h-50-px bg-red rounded-circle d-flex justify-content-center align-items-center">
                                                                    <iconify-icon icon="hugeicons:clock-02"
                                                                        class="text-white text-2xl mb-0"></iconify-icon>
                                                                </div>
                                                            </div>
                                                            {{-- <p class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                                                        {{App\Http\Controllers\Controller::IND_money_format($dashboard_data['total_revenue'])}} Invoices
                                                    </p> --}}
                                                        </div>
                                                    </div><!-- card end -->
                                                </div>
                                            @else
                                                <h6> Client(s) not assigned</h6>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="emp-clients" role="tabpanel"
                                aria-labelledby="emp-clients-tab" tabindex="0">

                                <!-- Enhanced Header Section -->
                                <div class="card mb-4">
                                    <div class="card-body py-3">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <h6 class="mb-0 fw-semibold d-flex align-items-center gap-2">
                                                    <iconify-icon icon="mdi:account-group" class="text-primary"></iconify-icon>
                                                    Client Management
                                                </h6>
                                                <small class="text-muted">Manage client assignments for {{ $employee->emp_name }}</small>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center justify-content-end gap-3">
                                                    <button type="button" class="btn btn-success btn-sm d-flex align-items-center gap-2" onclick="exportEmployeeClients()">
                                                        <iconify-icon icon="hugeicons:csv-02"></iconify-icon>
                                                        Export CSV
                                                    </button>
                                                    <button type="button" class="btn btn-primary btn-sm d-flex align-items-center gap-2"
                                                        data-bs-toggle="modal" data-bs-target="#assignClientModal">
                                                        <iconify-icon icon="mdi:account-plus"></iconify-icon>
                                                        Assign Clients
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Client Table Section -->
                                <div class="card shadow-none border">
                                    <div class="card-body">
                                        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-3">
                                            <div class="d-flex flex-wrap align-items-center gap-3 w-25 w-mb-100">
                                                <div class="icon-field w-100">
                                                    <input type="text" name="searchkey" class="form-control form-control-sm w-100" placeholder="Search Client" id="clientSearchkey">
                                                    <span class="icon">
                                                        <iconify-icon icon="ion:search-outline"></iconify-icon>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="d-flex flex-no-wrap align-items-center gap-3">
                                                <select id="clientSearchStatus" class="form-control form-select form-select-sm" title="select Status">
                                                    <option selected value="">Select Status</option>
                                                    <option value="">All</option>
                                                    <option value="1">Active</option>
                                                    <option value="0">Inactive</option>
                                                </select>
                                            </div>
                                        </div>

                                            <div class="d-none d-md-block">
                                                <input type="hidden" name="employee" id="employee_clients" value="{{ $employee->id }}">
                                                <table id="assignedClientsTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                                                    <thead>
                                                        <tr>
                                                            <th>Client Code</th>
                                                            <th>Name</th>
                                                            <th>Status</th>
                                                            <th>Action</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @if (count($dashboard_data['clients']) == 0)
                                                            <tr>
                                                                <td colspan="4" class="text-center text-muted">
                                                                    No clients assigned. <a href="#" class="text-primary"
                                                                        data-bs-toggle="modal"
                                                                        data-bs-target="#assignClientModal">Click here to add clients</a>.
                                                                </td>
                                                            </tr>
                                                        @endif
                                                    </tbody>
                                                </table>
                                            </div>

                                            <!-- Grid View for Mobile -->
                                            <div id="assignedClientsGrid" class="d-block d-md-none">
                                                @if (count($dashboard_data['clients']) == 0)
                                                    <div class="text-center text-muted">
                                                        No clients assigned. <a href="#" class="text-primary"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#assignClientModal">Click here to add clients</a>.
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>

                                <!-- Enhanced Assign Client Modal -->
                                <div class="modal modal-xl fade" id="assignClientModal" tabindex="-1"
                                    aria-labelledby="assignClientModalLabel" aria-hidden="true">
                                    <div class="modal-dialog modal-dialog-centered">
                                        <div class="modal-content">
                                            <div class="modal-header bg-primary-50 border-bottom">
                                                <div class="d-flex align-items-center gap-3">
                                                    <div class="w-40-px h-40-px bg-primary-600 rounded-circle d-flex justify-content-center align-items-center">
                                                        <iconify-icon icon="mdi:account-multiple-plus" class="text-white text-xl"></iconify-icon>
                                                    </div>
                                                    <div>
                                                        <h5 class="mb-0 fw-semibold text-primary-600" id="assignClientModalLabel">Assign Clients to {{ $employee->emp_name }}</h5>
                                                        <p class="mb-0 text-sm text-muted">Manage client assignments efficiently</p>
                                                    </div>
                                                </div>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>

                                            <div class="modal-body p-0">
                                                <input type="hidden" name="employee_id" value="{{ $employee->id }}" id="employee_id">

                                                <!-- Search and Filter Section -->
                                                <div class="p-24 bg-neutral-50 border-bottom">
                                                    <div class="row g-3">
                                                        <div class="col-md-4">
                                                            <div class="icon-field">
                                                                <input type="text" class="form-control form-control-sm"
                                                                       placeholder="Search clients..." id="modalClientSearch">
                                                                <span class="icon">
                                                                    <iconify-icon icon="ion:search-outline"></iconify-icon>
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <select class="form-select form-select-sm" id="modalStatusFilter">
                                                                <option value="">All Status</option>
                                                                <option value="1">Active</option>
                                                                <option value="0">Inactive</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <select class="form-select form-select-sm" id="modalDistrictFilter">
                                                                <option value="">All Districts</option>
                                                                @foreach($districts ?? [] as $district)
                                                                    <option value="{{ $district->id }}">{{ $district->name }}</option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="d-flex gap-2">
                                                                <button type="button" class="btn btn-sm btn-outline-primary d-flex flex-fill" id="selectAllClients">
                                                                    <iconify-icon icon="mdi:select-all" class="me-1"></iconify-icon>
                                                                    Select All
                                                                </button>
                                                                <button type="button" class="btn btn-sm btn-outline-secondary d-flex flex-fill" id="clearAllClients">
                                                                    <iconify-icon icon="mdi:select-off" class="me-1"></iconify-icon>
                                                                    Clear All
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Client Assignment Interface -->
                                                <div class="row g-0" style="min-height: 500px;">
                                                    <!-- Available Clients -->
                                                    <div class="col-md-6 border-end">
                                                        <div class="p-20">
                                                            <div class="d-flex align-items-center justify-content-between mb-16">
                                                                <div class="d-flex align-items-center gap-3">
                                                                    <h6 class="mb-0 fw-semibold text-secondary-light d-flex">
                                                                        <iconify-icon icon="mdi:account-group" class="me-2"></iconify-icon>
                                                                        Available Clients
                                                                    </h6>
                                                                    <div class="form-check form-switch">
                                                                        <input class="form-check-input" type="checkbox" id="showUnassignedOnly" checked>
                                                                        <label class="form-check-label text-xs text-muted" for="showUnassignedOnly">
                                                                            Unassigned only
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                                <span class="badge bg-info-100 text-info-600" id="availableCount">0</span>
                                                            </div>
                                                            <div id="availableClientsContainer" class="client-container" style="max-height: 400px; overflow-y: auto;">
                                                                <!-- Available clients will be populated here -->
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Assigned Clients -->
                                                    <div class="col-md-6">
                                                        <div class="p-20">
                                                            <div class="d-flex align-items-center justify-content-between mb-16">
                                                                <h6 class="mb-0 fw-semibold text-success-600 d-flex">
                                                                    <iconify-icon icon="mdi:account-check" class="me-2"></iconify-icon>
                                                                    Assigned Clients
                                                                </h6>
                                                                <span class="badge bg-success-100 text-success-600" id="assignedCount">0</span>
                                                            </div>
                                                            <div id="assignedClientsContainer" class="client-container" style="max-height: 400px; overflow-y: auto;">
                                                                <!-- Assigned clients will be populated here -->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="modal-footer bg-neutral-50 border-top">
                                                <div class="d-flex align-items-center justify-content-between w-100">
                                                    <div class="text-sm text-muted d-flex align-items-center gap-2">
                                                        <iconify-icon icon="mdi:information" class="me-1"></iconify-icon>
                                                        Click on client cards to assign/unassign
                                                    </div>
                                                    <div class="d-flex gap-2">
                                                        <button type="button" class="btn btn-neutral-500 border-neutral-100 px-24 d-flex align-items-center gap-2" data-bs-dismiss="modal">
                                                            <iconify-icon icon="mdi:close" class="me-1"></iconify-icon>
                                                            Cancel
                                                        </button>
                                                        <button type="button" class="btn btn-primary-600 px-24 d-flex align-items-center gap-2" id="saveAssignments">
                                                            <iconify-icon icon="mdi:content-save" class="me-1"></iconify-icon>
                                                            Save Changes
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="emp-invoices" role="tabpanel"
                                aria-labelledby="emp-invoices-tab" tabindex="0">

                                <!-- Enhanced Header Section -->
                                <div class="card mb-4">
                                    <div class="card-body py-3">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <h6 class="mb-0 fw-semibold d-flex align-items-center gap-2">
                                                    <iconify-icon icon="solar:bill-list-bold" class="text-primary"></iconify-icon>
                                                    Invoice Management
                                                </h6>
                                                <small class="text-muted">View and manage all invoices for {{ $employee->emp_name }}'s clients</small>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center justify-content-end gap-3">
                                                    <button type="button" class="btn btn-success btn-sm d-flex align-items-center gap-2" onclick="exportEmployeeInvoices()">
                                                        <iconify-icon icon="hugeicons:csv-02"></iconify-icon>
                                                        Export CSV
                                                    </button>
                                                    <div class="input-group date_range" style="max-width: 280px;">
                                                        <input type="text" class="form-control" name="daterange" id="invoice_daterange" placeholder="Select date range">
                                                        <div class="input-group-append">
                                                            <span class="input-group-text bg-primary-100 text-primary-600 border-primary-100">
                                                                <iconify-icon icon="mdi:calendar" width="20" height="20"></iconify-icon>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-none d-md-block">
                                    <input type="hidden" name="employee" id="employee" value="{{ $employee->id }}">
                                    <table id="invoicesTable"
                                        class="table table-sm table-striped table-hover table-bordered text-wrap mt-4"
                                        style="width:100%">
                                        <thead>
                                            <tr>
                                                <th>Invoice</th>
                                                <th>Invoice Date</th>
                                                <th>Invoice Amount</th>
                                                <th>Due Amount</th>
                                                <th>Status</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="2" class="text-end">Total</th>
                                                <th id="totalInvoice" ><strong>₹ 0.00</strong></th>
                                                <th></th>
                                                <th></th>
                                                <th></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="emp-payments" role="tabpanel"
                                aria-labelledby="emp-payments-tab" tabindex="0">

                                <!-- Enhanced Header Section -->
                                <div class="card mb-4">
                                    <div class="card-body py-3">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <h6 class="mb-0 fw-semibold d-flex align-items-center gap-2">
                                                    <iconify-icon icon="solar:wallet-money-bold" class="text-success"></iconify-icon>
                                                    Payment Management
                                                </h6>
                                                <small class="text-muted">Track all payments received from {{ $employee->emp_name }}'s clients</small>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center justify-content-end gap-3">
                                                    <button type="button" class="btn btn-success btn-sm d-flex align-items-center gap-2" onclick="exportEmployeePayments()">
                                                        <iconify-icon icon="hugeicons:csv-02"></iconify-icon>
                                                        Export CSV
                                                    </button>
                                                    <div class="input-group date_range" style="max-width: 280px;">
                                                        <input type="text" class="form-control" name="payment_daterange" id="payment_daterange" placeholder="Select date range">
                                                        <div class="input-group-append">
                                                            <span class="input-group-text bg-success-100 text-success-600 border-success-100">
                                                                <iconify-icon icon="mdi:calendar" width="20" height="20"></iconify-icon>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-none d-md-block">
                                    <table id="paymentsTable"
                                        class="table table-sm table-striped table-hover table-bordered text-wrap mt-4"
                                        style="width:100%">
                                        <thead>
                                            <tr>
                                                <th>Invoice Code</th>
                                                <th>Payment Date</th>
                                                <th>Payment Mode</th>
                                                <th>Paid Amount</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="3" class="text-end">Total</th>
                                                <th id="totalInvoice"><strong>₹ 0.00</strong></th>
                                                 <th></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="change-password" role="tabpanel"
                                aria-labelledby="change-password-tab" tabindex="0">
                                <form action="{{ route('password.change') }}" id="changePasswordForm" method="POST">
                                    @csrf

                                    <div class="mb-20">
                                        <label for="old-password"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">Old Password
                                            <span class="text-danger-600">*</span></label>
                                        <div class="position-relative">
                                            <input type="password" class="form-control radius-8" name="old_password" id="old-password"
                                                placeholder="Enter Old Password*">
                                            <span
                                                class="toggle-password ri-eye-line cursor-pointer position-absolute end-0 top-50 translate-middle-y me-16 text-secondary-light"
                                                data-toggle="#old-password"></span>
                                        </div>
                                    </div>
                                    <div class="mb-20">
                                        <label for="new-password"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">New Password
                                            <span class="text-danger-600">*</span></label>
                                        <div class="position-relative">
                                            <input type="password" class="form-control radius-8" name="new_password" id="new-password"
                                                placeholder="Enter New Password*">
                                            <span
                                                class="toggle-password ri-eye-line cursor-pointer position-absolute end-0 top-50 translate-middle-y me-16 text-secondary-light"
                                                data-toggle="#new-password"></span>
                                        </div>
                                    </div>
                                    <div class="mb-20">
                                        <label for="confirm-password"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">Confirmed
                                            Password <span class="text-danger-600">*</span></label>
                                        <div class="position-relative">
                                            <input type="password" class="form-control radius-8" name="new_password_confirmation" id="confirm-password"
                                                placeholder="Confirm Password*">
                                            <span
                                                class="toggle-password ri-eye-line cursor-pointer position-absolute end-0 top-50 translate-middle-y me-16 text-secondary-light"
                                                data-toggle="#confirm-password"></span>
                                        </div>
                                    </div>
                                    <div class="form-group d-flex align-items-center justify-content-end gap-8">
                                        <button type="submit"
                                            class="form-wizard-next-btn btn btn-primary-600 px-32">Submit</button>
                                    </div>
                                </form>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div id="exportLoader" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999;">
        <span class="spinner-border text-primary" role="status"></span>
        <strong>Exporting...</strong>
    </div>
@stop

@section('style')
<style>
    /* Enhanced Assign Clients Modal Styles */
    .client-card {
        transition: all 0.2s ease;
        cursor: pointer;
        user-select: none;
    }

    .client-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .client-card.available:hover {
        border-color: var(--success-600) !important;
        background-color: var(--success-50) !important;
    }

    .client-card.assigned:hover {
        border-color: var(--danger-600) !important;
        background-color: var(--danger-50) !important;
    }

    .client-card.available.border-warning:hover {
        border-color: var(--warning-600) !important;
        background-color: var(--warning-100) !important;
    }

    .client-card .action-icon {
        opacity: 0.7;
        transition: opacity 0.2s ease;
    }

    .client-card:hover .action-icon {
        opacity: 1;
    }

    .client-container {
        min-height: 200px;
    }

    .client-container:empty::after {
        content: '';
        display: block;
        height: 200px;
    }

    /* Custom scrollbar for client containers */
    .client-container::-webkit-scrollbar {
        width: 6px;
    }

    .client-container::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .client-container::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    .client-container::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Modal enhancements */
    .modal-xl {
        max-width: 1200px;
    }

    @media (max-width: 768px) {
        .modal-xl {
            max-width: 95%;
            margin: 10px auto;
        }

        .modal-body .row {
            flex-direction: column;
        }

        .modal-body .col-md-6 {
            border-end: none !important;
            border-bottom: 1px solid var(--neutral-200);
        }

        .modal-body .col-md-6:last-child {
            border-bottom: none;
        }

        .client-card {
            margin-bottom: 8px !important;
            padding: 8px !important;
        }

        .client-card .text-xs {
            font-size: 0.7rem;
        }

        .client-card h6 {
            font-size: 0.85rem;
        }

        .client-container {
            max-height: 300px !important;
        }

        .modal-header .d-flex {
            flex-direction: column;
            align-items: flex-start !important;
            gap: 10px;
        }

        /* Mobile adjustments for filter checkbox */
        .col-md-6 .d-flex.align-items-center.gap-3 {
            flex-direction: column;
            align-items: flex-start !important;
            gap: 8px !important;
        }

        .form-check.form-switch {
            padding-left: 1.5rem;
        }

        .form-check.form-switch .form-check-input {
            width: 1.5rem;
            height: 0.8rem;
            margin-left: -1.5rem;
        }

        /* Mobile adjustments for enhanced headers */
        .card-body.py-3 .row {
            flex-direction: column;
            gap: 15px;
        }

        .card-body.py-3 .col-md-6:last-child {
            text-align: left !important;
        }

        .card-body.py-3 .d-flex.align-items-center.justify-content-end {
            justify-content: flex-start !important;
            flex-wrap: wrap;
            gap: 10px !important;
        }

        .input-group.date_range {
            max-width: 100% !important;
            margin-top: 10px;
        }

        .btn-sm {
            font-size: 0.8rem;
            padding: 0.3rem 0.6rem;
        }

        .modal-header .w-40-px {
            width: 30px !important;
            height: 30px !important;
        }

        .modal-footer .d-flex {
            flex-direction: column;
            gap: 10px;
        }

        .modal-footer .text-sm {
            text-align: center;
            margin-bottom: 10px;
        }

        .p-24 .row .col-md-4,
        .p-24 .row .col-md-2,
        .p-24 .row .col-md-3 {
            margin-bottom: 10px;
        }

        .p-24 .row .col-md-3 .d-flex {
            flex-direction: column;
            gap: 5px;
        }

        .p-24 .row .col-md-3 .btn {
            width: 100%;
        }
    }

    /* Loading animation */
    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    /* Badge improvements */
    .badge {
        font-size: 0.65rem;
        padding: 0.25em 0.5em;
    }

    /* Unassigned filter checkbox styling */
    .form-check.form-switch {
        padding-left: 2rem;
        margin-bottom: 0;
    }

    .form-check.form-switch .form-check-input {
        width: 2rem;
        height: 1rem;
        margin-left: -2rem;
        background-color: var(--neutral-300);
        border: none;
        border-radius: 1rem;
    }

    .form-check.form-switch .form-check-input:checked {
        background-color: var(--primary-600);
        border-color: var(--primary-600);
    }

    .form-check.form-switch .form-check-input:focus {
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-600-rgb), 0.25);
        border-color: var(--primary-600);
    }

    .form-check.form-switch .form-check-label {
        font-size: 0.75rem;
        color: var(--text-secondary-light);
        cursor: pointer;
        user-select: none;
    }

    /* Animation for card movements */
    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .client-card {
        animation: slideIn 0.3s ease-out;
    }

    /* Pulse animation for counts */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    .badge.pulse {
        animation: pulse 0.5s ease-in-out;
    }

    /* Loading state for containers */
    .client-container.loading {
        position: relative;
        pointer-events: none;
    }

    .client-container.loading::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }

    .client-container.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 30px;
        height: 30px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid var(--primary-600);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        z-index: 11;
    }

    @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* Success state animation */
    .client-card.success {
        animation: successPulse 0.6s ease-in-out;
    }

    @keyframes successPulse {
        0% { background-color: var(--success-50); }
        50% { background-color: var(--success-100); }
        100% { background-color: var(--success-50); }
    }

    /* Hover effects for buttons */
    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .btn:active {
        transform: translateY(0);
    }

    /* Enhanced header styling to match client view */
    .card-body.py-3 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;
    }

    .card-body.py-3 h6 {
        font-size: 1.1rem;
        font-weight: 600;
    }

    .card-body.py-3 small {
        font-size: 0.85rem;
        opacity: 0.8;
    }

    .input-group.date_range .form-control {
        border-right: none;
        font-size: 0.875rem;
    }

    .input-group.date_range .input-group-text {
        border-left: none;
        padding: 0.375rem 0.75rem;
    }

    /* Button styling consistency */
    .btn-sm.d-flex.align-items-center.gap-2 {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .btn-sm.d-flex.align-items-center.gap-2 iconify-icon {
        font-size: 1rem;
    }
</style>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            var invoiceTable;
            var paymentTable;
            $('.select2').select2();

            // Initialize Bootstrap tabs
            var triggerTabList = [].slice.call(document.querySelectorAll('#pills-tab button'))
            triggerTabList.forEach(function (triggerEl) {
                var tabTrigger = new bootstrap.Tab(triggerEl)

                triggerEl.addEventListener('click', function (event) {
                    event.preventDefault()
                    tabTrigger.show()
                })
            })

            // Employee Status Toggle
            $("#employeeStatusSwitch").on("change", function() {
                let isChecked = $(this).prop("checked"); // Get switch status
                let employeeId = $(this).data("id"); // Get Employee ID
                let status = isChecked ? 1 : 0; // 1 = Active, 0 = Inactive
                let switchElement = $(this); // Store switch reference
                let labelElement = $(this).next("label"); // Get label next to switch

                let actionText = status === 1 ? "activate" : "deactivate";
                let warningText = status === 1 ?
                    "Do you want to activate this employee?" :
                    "Deactivating will remove all assigned clients from this employee and prevent them from logging in. Are you sure you want to proceed?";

                Swal.fire({
                    title: "Are you sure?",
                    text: warningText,
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#28a745",
                    cancelButtonColor: "#d33",
                    confirmButtonText: `Yes, ${actionText} it!`,
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: "{{ route('employees.update-status') }}",
                            type: "POST",
                            data: {
                                employee_id: employeeId,
                                status: status,
                                _token: "{{ csrf_token() }}" // CSRF token
                            },
                            success: function(response) {
                                Swal.fire("Updated!", "Employee status has been changed.",
                                    "success").then(() => {
                                        if (status === 0) {
                                            // Reload to reflect client assignment changes
                                            location.reload();
                                        }
                                    });

                                // Update text dynamically
                                if (status === 1) {
                                    labelElement.text("Active");
                                } else {
                                    labelElement.text("Inactive");
                                }
                            },
                            error: function() {
                                Swal.fire("Error!", "Something went wrong.", "error");
                                switchElement.prop("checked", !
                                    isChecked); // Revert switch if error
                            }
                        });
                    } else {
                        switchElement.prop("checked", !isChecked); // Revert switch if canceled
                    }
                });
            });

            // Enhanced Assign Clients Modal Functionality
            let allClients = [];
            let assignedClientIds = [];
            let filteredClients = [];

            // Load clients when modal is opened
            $('#assignClientModal').on('show.bs.modal', function() {
                loadClientsForAssignment();
            });

            function loadClientsForAssignment() {
                // Show loading state
                $('#availableClientsContainer, #assignedClientsContainer').addClass('loading');

                $.ajax({
                    url: "{{ route('employees.get-clients-for-assignment') }}",
                    type: "GET",
                    data: {
                        employee_id: $("#employee_id").val()
                    },
                    beforeSend: function() {
                        // Disable search and filters during loading
                        $('#modalClientSearch, #modalStatusFilter, #modalDistrictFilter').prop('disabled', true);
                    },
                    success: function(response) {
                        allClients = response.clients;
                        assignedClientIds = response.assigned_client_ids;
                        filteredClients = [...allClients];
                        renderClientCards();
                        updateCounts();
                    },
                    error: function() {
                        Swal.fire({
                            icon: "error",
                            title: "Error",
                            text: "Failed to load clients. Please try again.",
                        });
                    },
                    complete: function() {
                        // Remove loading state and re-enable controls
                        $('#availableClientsContainer, #assignedClientsContainer').removeClass('loading');
                        $('#modalClientSearch, #modalStatusFilter, #modalDistrictFilter').prop('disabled', false);
                    }
                });
            }

            function renderClientCards() {
                const availableContainer = $('#availableClientsContainer');
                const assignedContainer = $('#assignedClientsContainer');
                const showUnassignedOnly = $('#showUnassignedOnly').is(':checked');

                availableContainer.empty();
                assignedContainer.empty();

                if (filteredClients.length === 0) {
                    availableContainer.html(`
                        <div class="text-center py-4 text-muted">
                            <iconify-icon icon="mdi:account-search" class="text-4xl mb-2"></iconify-icon>
                            <p class="mb-0">No clients found</p>
                        </div>
                    `);
                    return;
                }

                let availableClientsToShow = [];

                filteredClients.forEach(client => {
                    const isAssigned = assignedClientIds.includes(client.id);
                    const isAssignedToOther = client.assigned_employee_id && client.assigned_employee_id != $("#employee_id").val();

                    if (isAssigned) {
                        const clientCard = createClientCard(client, isAssigned);
                        assignedContainer.append(clientCard);
                    } else {
                        // Apply unassigned-only filter
                        if (!showUnassignedOnly || !isAssignedToOther) {
                            availableClientsToShow.push(client);
                        }
                    }
                });

                // Render available clients
                if (availableClientsToShow.length === 0) {
                    const emptyMessage = showUnassignedOnly
                        ? `<div class="text-center py-4 text-muted">
                            <iconify-icon icon="mdi:account-check-outline" class="text-4xl mb-2"></iconify-icon>
                            <p class="mb-0">No unassigned clients found</p>
                            <small>All filtered clients are already assigned to employees</small>
                           </div>`
                        : `<div class="text-center py-4 text-muted">
                            <iconify-icon icon="mdi:account-search" class="text-4xl mb-2"></iconify-icon>
                            <p class="mb-0">No available clients found</p>
                           </div>`;
                    availableContainer.html(emptyMessage);
                } else {
                    availableClientsToShow.forEach(client => {
                        const clientCard = createClientCard(client, false);
                        availableContainer.append(clientCard);
                    });
                }

                // Add empty state for assigned clients if none
                if (assignedClientIds.length === 0) {
                    assignedContainer.html(`
                        <div class="text-center py-4 text-muted">
                            <iconify-icon icon="mdi:account-plus" class="text-4xl mb-2"></iconify-icon>
                            <p class="mb-0">No clients assigned yet</p>
                            <small>Click on available clients to assign them</small>
                        </div>
                    `);
                }
            }

            function createClientCard(client, isAssigned) {
                const statusBadge = client.status == 1
                    ? '<span class="badge bg-success-100 text-success-600 text-xs">Active</span>'
                    : '<span class="badge bg-danger-100 text-danger-600 text-xs">Inactive</span>';

                // Check if client is assigned to another employee
                const isAssignedToOther = client.assigned_employee_id && client.assigned_employee_id != $("#employee_id").val();

                let cardClass, actionIcon, assignedEmployeeInfo = '';

                if (isAssigned) {
                    cardClass = 'client-card assigned border-success bg-success-50';
                    actionIcon = '<iconify-icon icon="mdi:minus-circle" class="text-danger-600"></iconify-icon>';
                } else if (isAssignedToOther) {
                    cardClass = 'client-card available border-warning bg-warning-50';
                    actionIcon = '<iconify-icon icon="mdi:account-switch" class="text-warning-600"></iconify-icon>';
                    assignedEmployeeInfo = `
                        <div class="text-xs text-warning-600 mt-1 d-flex align-items-center">
                            <iconify-icon icon="mdi:account-tie" class="me-1"></iconify-icon>
                            Assigned to: ${client.assigned_employee_name}
                        </div>
                    `;
                } else {
                    cardClass = 'client-card available border-primary bg-white';
                    actionIcon = '<iconify-icon icon="mdi:plus-circle" class="text-success-600"></iconify-icon>';
                }

                return $(`
                    <div class="${cardClass} border rounded-8 p-12 mb-12 cursor-pointer transition-all"
                         data-client-id="${client.id}"
                         data-assigned="${isAssigned}"
                         data-assigned-to-other="${isAssignedToOther}">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center gap-2 mb-1">
                                    <h6 class="mb-0 fw-semibold text-sm">${client.name}</h6>
                                    ${statusBadge}
                                </div>
                                <div class="d-flex align-items-center gap-3 text-xs text-muted">
                                    <span class="d-flex align-items-center">
                                        <iconify-icon icon="mdi:card-account-details" class="me-1"></iconify-icon>
                                        ${client.client_code}
                                    </span>
                                    <span class="d-flex align-items-center">
                                        <iconify-icon icon="mdi:phone" class="me-1"></iconify-icon>
                                        ${client.phone || 'N/A'}
                                    </span>
                                </div>
                                <div class="text-xs text-muted mt-1 d-flex align-items-center">
                                    <iconify-icon icon="mdi:map-marker" class="me-1"></iconify-icon>
                                    ${client.district_name || 'N/A'} • ${client.area || 'N/A'}
                                </div>
                                ${assignedEmployeeInfo}
                            </div>
                            <div class="action-icon">
                                ${actionIcon}
                            </div>
                        </div>
                    </div>
                `);
            }
            // Client card click handlers
            $(document).on('click', '.client-card', function(e) {
                e.preventDefault();

                const clientId = parseInt($(this).data('client-id'));
                const isCurrentlyAssigned = $(this).data('assigned');
                const isAssignedToOther = $(this).data('assigned-to-other');

                // If client is assigned to another employee, show confirmation
                if (isAssignedToOther && !isCurrentlyAssigned) {
                    const clientName = $(this).find('h6').text();
                    const assignedEmployeeName = $(this).find('.text-warning-600').text().replace('Assigned to: ', '');

                    Swal.fire({
                        title: 'Client Already Assigned',
                        text: `${clientName} is currently assigned to ${assignedEmployeeName}. Do you want to reassign this client to the current employee?`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes, Reassign',
                        cancelButtonText: 'Cancel'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Add to assigned
                            assignedClientIds.push(clientId);

                            // Update the card's assigned status
                            $(this).data('assigned', true);
                            $(this).data('assigned-to-other', false);

                            // Add visual feedback
                            $(this).addClass('success');
                            setTimeout(() => $(this).removeClass('success'), 600);

                            // Re-render cards with updated assignments
                            renderClientCards();
                            updateCounts();
                        }
                    });
                    return;
                }

                // Add visual feedback
                $(this).addClass('success');
                setTimeout(() => $(this).removeClass('success'), 600);

                if (isCurrentlyAssigned) {
                    // Remove from assigned
                    assignedClientIds = assignedClientIds.filter(id => id !== clientId);
                } else {
                    // Add to assigned
                    assignedClientIds.push(clientId);
                }

                // Update the card's assigned status
                $(this).data('assigned', !isCurrentlyAssigned);

                // Re-render cards with updated assignments
                renderClientCards();
                updateCounts();
            });

            // Search functionality
            $('#modalClientSearch').on('input', function() {
                applyFilters();
            });

            // Filter functionality
            $('#modalStatusFilter, #modalDistrictFilter').on('change', function() {
                applyFilters();
            });

            // Unassigned-only filter
            $('#showUnassignedOnly').on('change', function() {
                renderClientCards();
                updateCounts();
            });

            function applyFilters() {
                const searchTerm = $('#modalClientSearch').val().toLowerCase();
                const statusFilter = $('#modalStatusFilter').val();
                const districtFilter = $('#modalDistrictFilter').val();

                filteredClients = allClients.filter(client => {
                    const matchesSearch = !searchTerm ||
                        client.name.toLowerCase().includes(searchTerm) ||
                        client.client_code.toLowerCase().includes(searchTerm) ||
                        (client.phone && client.phone.includes(searchTerm));

                    const matchesStatus = !statusFilter || client.status == statusFilter;
                    const matchesDistrict = !districtFilter || client.district_id == districtFilter;

                    return matchesSearch && matchesStatus && matchesDistrict;
                });

                renderClientCards();
                updateCounts();
            }

            // Bulk actions
            $('#selectAllClients').on('click', function() {
                assignedClientIds = [...new Set([...assignedClientIds, ...filteredClients.map(c => c.id)])];
                renderClientCards();
                updateCounts();
            });

            $('#clearAllClients').on('click', function() {
                assignedClientIds = [];
                renderClientCards();
                updateCounts();
            });

            function updateCounts() {
                const showUnassignedOnly = $('#showUnassignedOnly').is(':checked');

                // Calculate available count based on filter
                let availableCount;
                if (showUnassignedOnly) {
                    // Count only clients that are not assigned to anyone
                    availableCount = filteredClients.filter(c => {
                        const isAssigned = assignedClientIds.includes(c.id);
                        const isAssignedToOther = c.assigned_employee_id && c.assigned_employee_id != $("#employee_id").val();
                        return !isAssigned && !isAssignedToOther;
                    }).length;
                } else {
                    // Count all clients not assigned to current employee
                    availableCount = filteredClients.filter(c => !assignedClientIds.includes(c.id)).length;
                }

                const assignedCount = assignedClientIds.length;

                // Add pulse animation to badges when counts change
                const availableBadge = $('#availableCount');
                const assignedBadge = $('#assignedCount');

                if (availableBadge.text() !== availableCount.toString()) {
                    availableBadge.addClass('pulse');
                    setTimeout(() => availableBadge.removeClass('pulse'), 500);
                }

                if (assignedBadge.text() !== assignedCount.toString()) {
                    assignedBadge.addClass('pulse');
                    setTimeout(() => assignedBadge.removeClass('pulse'), 500);
                }

                availableBadge.text(availableCount);
                assignedBadge.text(assignedCount);
            }

            // Save assignments
            $('#saveAssignments').on('click', function() {
                const employeeId = $("#employee_id").val();

                // Validation
                if (!employeeId) {
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: "Employee ID is missing. Please refresh the page and try again.",
                    });
                    return;
                }

                const confirmText = assignedClientIds.length === 0
                    ? "This will remove all client assignments from this employee."
                    : `This will assign ${assignedClientIds.length} clients to this employee.`;

                Swal.fire({
                    title: "Save Changes?",
                    text: confirmText,
                    icon: "question",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Yes, Save Changes",
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: "{{ route('employees.assign-clients') }}",
                            type: "POST",
                            data: {
                                _token: "{{ csrf_token() }}",
                                employee_id: employeeId,
                                client_ids: assignedClientIds,
                            },
                            beforeSend: function() {
                                $('#saveAssignments').prop('disabled', true).html(`
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    Saving...
                                `);
                            },
                            success: function(response) {
                                // Show success snackbar instead of alert
                                const message = assignedClientIds.length === 0
                                    ? 'All client assignments have been removed successfully!'
                                    : 'Clients have been assigned successfully!';
                                showSnackbar(message, 'success');
                                $('#assignClientModal').modal('hide');

                                // Refresh the assigned clients table
                                if ($.fn.DataTable.isDataTable('#assignedClientsTable')) {
                                    $('#assignedClientsTable').DataTable().ajax.reload();
                                }
                            },
                            error: function(xhr) {
                                let errorMessage = "Something went wrong!";
                                if (xhr.responseJSON?.message) {
                                    errorMessage = xhr.responseJSON.message;
                                } else if (xhr.status === 422) {
                                    errorMessage = "Validation error. Please check your input and try again.";
                                } else if (xhr.status === 500) {
                                    errorMessage = "Server error. Please try again later.";
                                }

                                Swal.fire({
                                    icon: "error",
                                    title: "Error",
                                    text: errorMessage,
                                });
                            },
                            complete: function() {
                                $('#saveAssignments').prop('disabled', false).html(`
                                    <iconify-icon icon="mdi:content-save" class="me-1"></iconify-icon>
                                    Save Changes
                                `);
                            }
                        });
                    }
                });
            });

            // Helper function to show snackbar
            function showSnackbar(message, type = 'success') {
                const snackbar = $('#snackbar');
                const messageElement = $('#snackbar-message');

                // Update message
                messageElement.text(message);

                // Update classes based on type
                snackbar.removeClass('alert-success alert-danger alert-info')
                       .addClass(`alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'}`);

                // Show snackbar
                snackbar.addClass('show');

                // Auto hide after 3 seconds
                setTimeout(() => {
                    snackbar.removeClass('show');
                }, 3000);
            }

            // Keyboard shortcuts for better accessibility
            $('#assignClientModal').on('keydown', function(e) {
                // Ctrl/Cmd + A: Select all clients
                if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
                    e.preventDefault();
                    $('#selectAllClients').click();
                }

                // Ctrl/Cmd + D: Clear all selections
                if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
                    e.preventDefault();
                    $('#clearAllClients').click();
                }

                // Ctrl/Cmd + S: Save assignments
                if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                    e.preventDefault();
                    $('#saveAssignments').click();
                }

                // Escape: Close modal
                if (e.key === 'Escape') {
                    $('#assignClientModal').modal('hide');
                }
            });

            // Focus search input when modal opens
            $('#assignClientModal').on('shown.bs.modal', function() {
                $('#modalClientSearch').focus();
            });

            var clientseTableInitialized = false;
            var paymentsTableInitialized = false;
            var accountsTableInitialized = false;
            var assignedClientsTableInitialized = false;

            // Clients Tab
            $('#emp-clients-tab').on('shown.bs.tab', function(e) {
                if (!assignedClientsTableInitialized) {
                    if (!$.fn.DataTable.isDataTable('#assignedClientsTable')) {
                        var assignedClientsTable = $('#assignedClientsTable').DataTable({
                            processing: true,
                            serverSide: true,
                            searching: false,
                            pageLength: 25,
                            ajax: {
                                url: "{{ route('employees.assigned-clients') }}",
                                data: function (d) {
                                    d.employee_id = $('#employee_clients').val();
                                    d.searchkey = $('#clientSearchkey').val();
                                    d.searchStatus = $('#clientSearchStatus').val();
                                }
                            },
                            columns: [
                                { data: 'client_code', name: 'client_code' },
                                { data: 'name', name: 'name' },
                                { data: 'status', name: 'status', orderable: false, searchable: false },
                                { data: 'action', name: 'action', orderable: false, searchable: false }
                            ],
                            drawCallback: function(settings) {
                                var api = this.api();
                                var data = api.rows().data();
                                var gridContainer = $('#assignedClientsGrid');
                                gridContainer.empty();

                                if (data.length === 0) {
                                    gridContainer.html('<div class="text-center text-muted">No clients assigned. <a href="#" class="text-primary" data-bs-toggle="modal" data-bs-target="#assignClientModal">Click here to add clients</a>.</div>');
                                    return;
                                }

                                data.each(function(row) {
                                    var clientCard = `
                                        <div class="card mb-4 shadow-none border">
                                            <div class="card-body">
                                                <h6 class="d-flex align-items-center fw-normal">
                                                    <iconify-icon icon="mdi:account" class="me-2" width="20"></iconify-icon>
                                                    <strong>${row.name}</strong>
                                                </h6>
                                                <p class="mb-1 d-flex align-items-center">
                                                    <iconify-icon icon="mdi:card-account-details-outline" class="me-2" width="18"></iconify-icon>
                                                    <small><strong>Client Code:</strong> ${row.client_code}</small>
                                                </p>
                                                <p class="mb-2 d-flex align-items-center">
                                                    <iconify-icon icon="${row.status === 'Active' ? 'mdi:check-circle' : 'mdi:alert-circle'}" class="me-2" width="18"></iconify-icon>
                                                    <small><strong>Status:</strong> ${row.status}</small>
                                                </p>
                                                <div class="mt-2">${row.action}</div>
                                            </div>
                                        </div>
                                    `;
                                    gridContainer.append(clientCard);
                                });
                            },
                            dom: "<'row'<'col-md-12'tr>>" +
                                 "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>",
                            infoCallback: function(settings, start, end, max, total, pre) {
                                return `Showing ${start} to ${end} of ${total} records`;
                            }
                        });

                        // Search functionality
                        $('#clientSearchkey').on('keyup', function() {
                            assignedClientsTable.ajax.reload();
                        });

                        $('#clientSearchStatus').on('change', function() {
                            assignedClientsTable.ajax.reload();
                        });

                        assignedClientsTableInitialized = true;
                    }
                }
            });

            // Invoice History Tab
            $('#emp-invoices-tab').on('shown.bs.tab', function(e) {
                if (!clientseTableInitialized) {
                    if (!$.fn.DataTable.isDataTable(
                        '#invoicesTable')) { // Check if DataTable is already initialized
                        invoiceTable = $('#invoicesTable').DataTable({
                            processing: true,
                            serverSide: true,
                            searching: false,
                            pageLength: 25,
                            ajax: {
                                url: "{{ route('invoices.data') }}",
                                data: function(d) {
                                    d.employee = $('#employee').val();
                                    d.daterange = $('#invoice_daterange').val();
                                }
                            },
                            columns: [{
                                    data: 'invoice_code',
                                    name: 'invoice_code'
                                },
                                {
                                    data: 'invoice_date',
                                    name: 'invoice_date'
                                },
                                {
                                    data: 'total_amount_due',
                                    name: 'total_amount_due',
                                    render: function(data, type, row) {
                                        return `₹ ${parseFloat(data).toFixed(2)}`;
                                    }
                                },
                                {
                                    data: 'unpaid_amount',
                                    name: 'unpaid_amount',
                                    render: function(data, type, row) {
                                        return `₹ ${parseFloat(data).toFixed(2)}`;
                                    }
                                },
                                {
                                    data: 'invoice_status',
                                    name: 'invoice_status',
                                    orderable: false,
                                    searchable: false
                                },
                                {
                                    data: 'action',
                                    name: 'action',
                                    orderable: false,
                                    searchable: false
                                }
                            ],
                            drawCallback: function(settings) {
                                $('#clientsGrid').empty();
                            },
                            dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" +
                                "<'row'<'col-md-12'tr>>" +
                                "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>",
                            // buttons: [{
                            //     extend: 'csv',
                            //     text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
                            //     className: 'btn btn-success btn-sm bg-success-500',
                            //     filename: 'Invoices_' + new Date().toISOString().slice(0,
                            //         10),
                            //         customize: function (csv) {
                            //             return '\uFEFF' + csv; // Add BOM to ensure proper UTF-8 encoding
                            //         }
                            // }],
                            buttons: [
                    {
                        text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
                        className: 'btn btn-success btn-sm bg-success-500',
                        action: function (e, dt, node, config) {
                            exportEmployeeInvoices();
                        }
                    }
                ],

                            infoCallback: function(settings, start, end, max, total, pre) {
                                return `Showing ${start} to ${end} of ${total} records`;
                            },
                            footerCallback: function(row, data, start, end, display) {
                                // console.log('Footer callback executed'); // Debugging

                                var api = this.api();

                                // Sum for column 2
                                var totalColumn2 = api
                                    .column(2, { page: 'current' }) // Column index 2 (discount_amount)
                                    .data()
                                    .reduce(function(a, b) {
                                        return (parseFloat(a) || 0) + (parseFloat(b) || 0);
                                    }, 0);

                                // Sum for column 3
                                var totalColumn3 = api
                                    .column(3, { page: 'current' }) // Column index 3
                                    .data()
                                    .reduce(function(a, b) {
                                        return (parseFloat(a) || 0) + (parseFloat(b) || 0);
                                    }, 0);

                                // console.log('Total Column 2:', totalColumn2);
                                // console.log('Total Column 3:', totalColumn3);

                                // Update footer values
                                $(api.column(2).footer()).html(`<strong>₹ ${totalColumn2.toFixed(2)}</strong>`);
                                $(api.column(3).footer()).html(`<strong>₹ ${totalColumn3.toFixed(2)}</strong>`);
                            }

                        });

                        clientseTableInitialized = true; // Mark as initialized
                    }
                }
            });
            // Payments Tab
            $('#emp-payments-tab').on('shown.bs.tab', function(e) {
                if (!paymentsTableInitialized) {
                    if (!$.fn.DataTable.isDataTable('#paymentsTable')) { // Check if already initialized
                        paymentTable = $('#paymentsTable').DataTable({
                            processing: true,
                            serverSide: true,
                            searching: false,
                            pageLength: 25,
                            ajax: {
                                url: "{{ route('payments.data') }}",
                                data: function(d) {
                                    d.employee = $('#employee').val();
                                    d.daterange = $('#payment_daterange').val();
                                }
                            },
                            columns: [

                                {
                                    data: 'invoice_code',
                                    name: 'invoice_code'
                                },
                                {
                                    data: 'paid_on',
                                    name: 'paid_on'
                                },
                                {
                                    data: 'payment_mode',
                                    name: 'payment_mode'
                                },
                                {
                                    data: 'amount',
                                    name: 'amount',
                                    render: function(data, type, row) {
                                        return `₹ ${parseFloat(data).toFixed(2)}`;
                                    }
                                },
                                {
                                    data: 'action',
                                    name: 'action',
                                    orderable: false,
                                    searchable: false
                                }
                            ],
                            drawCallback: function(settings) {
                                $('#clientsGrid').empty();
                            },
                            dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" +
                                "<'row'<'col-md-12'tr>>" +
                                "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>",

                            buttons: [
                    {
                        text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
                        className: 'btn btn-success btn-sm bg-success-500',
                        action: function (e, dt, node, config) {
                            exportEmployeePayments();
                        }
                    }
                ],
                            infoCallback: function(settings, start, end, max, total, pre) {
                                return `Showing ${start} to ${end} of ${total} records`;
                            },
                            footerCallback: function(row, data, start, end, display) {
                                console.log('Footer callback executed'); // Debugging

                                var api = this.api();

                                var total = api
                                    .column(3, {
                                        page: 'current'
                                    }) // Column index 1 (discount_amount)
                                    .data()
                                    .reduce(function(a, b) {
                                        return (parseFloat(a) || 0) + (parseFloat(b) || 0);
                                    }, 0);

                                console.log('Total discount:', total); // Debugging

                                $(api.column(3).footer()).html(
                                    `<strong>₹ ${total.toFixed(2)}</strong>`);
                            }
                        });

                        paymentsTableInitialized = true; // Mark as initialized
                    }
                }
            });
            $(function() {
                function getCurrentFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, set FY to start from April 1st of this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().month(3).startOf('month'); // April 1st current year
                        fyEnd = moment().add(1, 'year').month(2).endOf('month'); // March 31st next year
                    } else {
                        // Before April, set FY to start from last year's April 1st
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    }
                    return [fyStart, fyEnd];
                }

                function getLastFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, last FY is April 1st of last year - March 31st this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    } else {
                        // If before April, last FY is two years ago April 1st - last year March 31st
                        fyStart = moment().subtract(2, 'year').month(3).startOf(
                        'month'); // April 1st two years ago
                        fyEnd = moment().subtract(1, 'year').month(2).endOf(
                        'month'); // March 31st last year
                    }
                    return [fyStart, fyEnd];
                }

                function cb(start, end) {
                    $('#invoice_daterange').val(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
                }

                $('#invoice_daterange').daterangepicker({
                    startDate: getCurrentFinancialYear()[0], // Default to Current Financial Year
                    endDate: getCurrentFinancialYear()[1],
                    autoApply: true,
                    locale: {
                        format: 'DD/MM/YYYY'
                    },
                    ranges: {
                        'Today': [moment(), moment()],
                        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                        'This Month': [moment().startOf('month'), moment().endOf('month')],
                        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment()
                            .subtract(1, 'month').endOf('month')
                        ],
                        'Current Financial Year': getCurrentFinancialYear(),
                        'Last Financial Year': getLastFinancialYear()
                    }
                }, cb);

                // Set initial date display
                cb(getCurrentFinancialYear()[0], getCurrentFinancialYear()[1]);

            });
            $(function() {
                function getCurrentFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, set FY to start from April 1st of this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().month(3).startOf('month'); // April 1st current year
                        fyEnd = moment().add(1, 'year').month(2).endOf('month'); // March 31st next year
                    } else {
                        // Before April, set FY to start from last year's April 1st
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    }
                    return [fyStart, fyEnd];
                }

                function getLastFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, last FY is April 1st of last year - March 31st this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    } else {
                        // If before April, last FY is two years ago April 1st - last year March 31st
                        fyStart = moment().subtract(2, 'year').month(3).startOf(
                        'month'); // April 1st two years ago
                        fyEnd = moment().subtract(1, 'year').month(2).endOf(
                        'month'); // March 31st last year
                    }
                    return [fyStart, fyEnd];
                }

                function cb(start, end) {
                    $('#payment_daterange').val(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
                }

                $('#payment_daterange').daterangepicker({
                    startDate: getCurrentFinancialYear()[0], // Default to Current Financial Year
                    endDate: getCurrentFinancialYear()[1],
                    autoApply: true,
                    locale: {
                        format: 'DD/MM/YYYY'
                    },
                    ranges: {
                        'Today': [moment(), moment()],
                        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                        'This Month': [moment().startOf('month'), moment().endOf('month')],
                        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment()
                            .subtract(1, 'month').endOf('month')
                        ],
                        'Current Financial Year': getCurrentFinancialYear(),
                        'Last Financial Year': getLastFinancialYear()
                    }
                }, cb);

                // Set initial date display
                cb(getCurrentFinancialYear()[0], getCurrentFinancialYear()[1]);

            });
            // Custom search event triggers
            $('#invoice_daterange').on('apply.daterangepicker', function() {
                invoiceTable.draw();
            });
            $('#payment_daterange').on('apply.daterangepicker', function() {
                paymentTable.draw();
            });
        });
        // ================== Password Show Hide Js Start ==========
        function initializePasswordToggle(toggleSelector) {
            $(toggleSelector).on("click", function() {
                $(this).toggleClass("ri-eye-off-line");
                var input = $($(this).attr("data-toggle"));
                if (input.attr("type") === "password") {
                    input.attr("type", "text");
                } else {
                    input.attr("type", "password");
                }
            });
        }
        // Call the function
        initializePasswordToggle(".toggle-password");
        // ========================= Password Show Hide Js End ===========================



    </script>
<script>
$(document).ready(function () {
    $("#changePasswordForm").validate({
        rules: {
            old_password: {
                required: true,
                minlength: 6
            },
            new_password: {
                required: true,
                minlength: 6
            },
            new_password_confirmation: {
                required: true,
                equalTo: "#new-password"
            }
        },
        messages: {
            old_password: {
                required: "Please enter your old password.",
                minlength: "Password must be at least 6 characters long."
            },
            new_password: {
                required: "Please enter a new password.",
                minlength: "Password must be at least 6 characters long."
            },
            new_password_confirmation: {
                required: "Please confirm your new password.",
                equalTo: "Passwords do not match."
            }
        },
        errorPlacement: function (error, element) {
            error.addClass("text-danger mt-2").insertAfter(element);
        }
    });
});

// Export functions for employee view
function exportEmployeeClients() {
    $('#exportLoader').show();

    var exportUrl = "{{ route('employees.export.clients', $employee->id) }}";

    var link = document.createElement('a');
    link.href = exportUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setTimeout(() => {
        $('#exportLoader').hide();
    }, 1000);
}

function exportEmployeeInvoices() {
    $('#exportLoader').show();

    // Get current filter values
    var filters = {
        daterange: $('#invoice_daterange').val()
    };

    // Build export URL with filters
    var exportUrl = "{{ route('employees.export.invoices', $employee->id) }}?" + $.param(filters);

    var link = document.createElement('a');
    link.href = exportUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setTimeout(() => {
        $('#exportLoader').hide();
    }, 1000);
}

function exportEmployeePayments() {
    $('#exportLoader').show();

    // Get current filter values
    var filters = {
        daterange: $('#payment_daterange').val()
    };

    // Build export URL with filters
    var exportUrl = "{{ route('employees.export.payments', $employee->id) }}?" + $.param(filters);

    var link = document.createElement('a');
    link.href = exportUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setTimeout(() => {
        $('#exportLoader').hide();
    }, 1000);
}

</script>

@stop
