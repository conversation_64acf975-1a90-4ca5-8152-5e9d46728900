@extends('layouts.master')
@section('title', 'Employee List - Paidash')
@section('content')
    <div class="dashboard-main-body">
        @if (session('success'))
            <div id="snackbar"
                class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
                <div class="d-flex align-items-center gap-2">
                    <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
                    <span id="snackbar-message"> {{ session('success') }}</span>

                </div>
                <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
                    <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
                </button>
            </div>
        @endif
        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">Employees List</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">Employees List</li>
            </ul>
        </div>

        <div class="card">
            <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
                <div class="d-flex flex-wrap align-items-center gap-3 w-25 w-mb-100">
                    <div class="icon-field w-100">
                        <input type="text" name="searchkey" class="form-control form-control-sm w-100"
                            placeholder="Search Employee" id="searchkey">
                        <span class="icon">
                            <iconify-icon icon="ion:search-outline"></iconify-icon>
                        </span>
                    </div>
                </div>
                <div class="d-flex flex-no-wrap align-items-center gap-3">
                    @can('employee-create')
                        <a href="/employees/add" class="btn btn-sm btn-primary-600 d-flex"><i
                                class="ri-add-line"></i>&nbsp;<span class="d-none d-md-block">Create&nbsp;</span>Employee</a>
                    @endcan

                </div>
            </div>
            <div class="card-body">
                <!-- Table View for Desktop -->
                <div class="d-none d-md-block">
                    <table id="employeesTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4"
                        style="width:100%">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Mobile</th>
                                <th>email</th>
                                <th>Role</th>
                                <th>Address</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                    </table>
                </div>

                <!-- Grid View for Mobile -->
                <div id="clientsGrid" class="d-block d-md-none"></div>
            </div>
        </div>

    </div>
<div id="exportLoader" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999;">
    <span class="spinner-border text-primary" role="status"></span>
    <strong>Exporting...</strong>
</div>
<div id="exportLoader" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999;">
    <span class="spinner-border text-primary" role="status"></span>
    <strong>Exporting...</strong>
</div>
@stop
@section('script')
    <script>
        $(document).ready(function() {
            $('#searchArea').select2({
                width: '300px'
            });
            $('#searchStatus').select2({
                width: '200px'
            });
            var table = $('#employeesTable').DataTable({
                processing: true,
                serverSide: true,
                searching: false,
                pageLength: 25,
                ajax: {
                    url: "{{ route('employees.data') }}",
                    data: function(d) {
                        d.searchkey = $('#searchkey').val();
                        d.searchStatus = $('#searchStatus').val();
                        d.searchArea = $('#searchArea').val();
                    }
                },
                columns: [{
                        data: 'emp_name',
                        name: 'emp_name'
                    },
                    {
                        data: 'phone',
                        name: 'phone'
                    },
                    {
                        data: 'email',
                        name: 'email'
                    },
                    {
                        data: 'role',
                        name: 'role'
                    },
                    {
                        data: 'address',
                        name: 'address'
                    },
                    {
                        data: 'status',
                        name: 'status',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                drawCallback: function(settings) {
                    var api = this.api();
                    var data = api.rows().data();
                    var gridContainer = $('#clientsGrid');
                    gridContainer.empty();

                    data.each(function(row) {
                        var clientCard = `
                        <div class="card mb-4 shadow-none border">
                            <div class="card-body">
                                <h6 class="d-flex align-items-center fw-normal">
                                    <iconify-icon icon="mdi:account" class="me-2" width="20"></iconify-icon>
                                    <strong>${row.name}</strong>
                                </h6>
                                <p class="mb-1 d-flex align-items-center">
                                    <iconify-icon icon="mdi:card-account-details-outline" class="me-2" width="18"></iconify-icon>
                                    <small><strong>Client Code:</strong> ${row.client_code}</small>
                                </p>
                                <p class="mb-1 d-flex align-items-center">
                                    <iconify-icon icon="mdi:phone" class="me-2" width="18"></iconify-icon>
                                    <small><strong>Phone:</strong> ${row.phone}</small>
                                </p>
                                <p class="mb-1 d-flex align-items-center">
                                    <iconify-icon icon="mdi:map-marker" class="me-2" width="18"></iconify-icon>
                                    <small><strong>Area:</strong> ${row.area}</small>
                                </p>
                                <p class="mb-1 d-flex align-items-center">
                                    <iconify-icon icon="mdi:hospital-building" class="me-2" width="18"></iconify-icon>
                                    <small><strong>HCF Type:</strong> ${row.hcf_type}</small>
                                </p>
                                <p class="mb-2 d-flex align-items-center">
                                    <iconify-icon icon="${row.status === 'Active' ? 'mdi:check-circle' : 'mdi:alert-circle'}" class="me-2" width="18"></iconify-icon>
                                    <small><strong>Status:</strong> ${row.status}</small>
                                </p>
                                <div class="mt-2">${row.action}</div>
                            </div>
                        </div>
                    `;
                        gridContainer.append(clientCard);
                    });
                },
                dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" +
                    // Entries Dropdown & CSV Button
                    "<'row'<'col-md-12'tr>>" +
                    "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>", // Info at left, Pagination at right
                    buttons: [
                    {
                        text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export',
                        className: 'btn btn-success btn-sm bg-success-500',
                        action: function (e, dt, node, config) {
                            exportEmployees();
                        }
                    }
                ],
                infoCallback: function(settings, start, end, max, total, pre) {
                    return `Showing ${start} to ${end} of ${total} records`;
                }
            });

            // Custom search event triggers
            $("#searchkey, #searchEmail").on("keyup", function() {
                table.draw();
            });

            $("#searchStatus, #searchArea").on("change", function() {
                table.draw();
            });
        });

        // Export function with all filters
        function exportEmployees() {
            // Show loader
            $('#exportLoader').show();

            // Get current filter values
            var filters = {
                searchkey: $('#searchkey').val()
            };

            // Build export URL with filters
            var exportUrl = "{{ route('employees.export') }}?" + $.param(filters);

            // Create temporary link and trigger download
            var link = document.createElement('a');
            link.href = exportUrl;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Hide loader after a short delay
            setTimeout(() => {
                $('#exportLoader').hide();
            }, 1000);
        }
    </script>
@stop
