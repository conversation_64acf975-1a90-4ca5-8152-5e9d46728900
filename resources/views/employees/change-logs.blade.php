@php
    $title = 'Employee';
    $moduleTitle = 'Employees';
    $indexRoute = route('employees.index');
    $viewRoute = route('employees.show', $employee->id);
    $entityInfo = [
        'Employee ID' => $employee->id,
        'Name' => $employee->emp_name ?? $employee->name ?? 'N/A',
        'Email' => $employee->email ?? 'N/A',
        'Phone' => $employee->phone ?? 'N/A',
        'Designation' => $employee->designation ?? 'N/A',
        'Department' => $employee->department ?? 'N/A',
        'Status' => ($employee->status ?? 1) == 1 ? '<span class="badge bg-success">Active</span>' : '<span class="badge bg-danger">Inactive</span>',
        'Created At' => $employee->created_at->format('d-m-Y h:i A')
    ];
    $changeLogs = $employee->changeLogs()->orderBy('created_at', 'desc')->get();
@endphp

@include('shared.change-logs', [
    'title' => $title,
    'moduleTitle' => $moduleTitle,
    'indexRoute' => $indexRoute,
    'viewRoute' => $viewRoute,
    'entityInfo' => $entityInfo,
    'changeLogs' => $changeLogs
])
