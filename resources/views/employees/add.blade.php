@extends('layouts.master')
@section('title', 'Employee Add - Paidash')

@section('content')
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">Add Employee </h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="/employees" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:user-check-broken" class="icon text-lg"></iconify-icon>
                        Employees
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">Add Employee</li>
            </ul>
        </div>

        <div class="card h-100 p-0 radius-12">
            <div class="card-body p-24">
                <div class="row justify-content-center">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul>
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    <div class="col-xxl-12 col-xl-12 col-lg-12">
                        <h6 class="text-md text-primary-light mb-16">Profile Image</h6>
                        <form action="{{ route('employees.store') }}" method="POST"  id="form" enctype="multipart/form-data">
                            <div class="row gy-3">
                                <div class="col-sm-12">
                                @csrf
                                <!-- Upload Image Start -->
                                    <div class="avatar-upload">
                                        <div
                                            class="avatar-edit position-absolute bottom-0 end-0 me-24 mt-16 z-1 cursor-pointer">
                                            <input type='file' id="imageUpload" accept=".png, .jpg, .jpeg" hidden
                                                name="photo">
                                            <label for="imageUpload"
                                                class="w-32-px h-32-px d-flex justify-content-center align-items-center bg-primary-50 text-primary-600 border border-primary-600 bg-hover-primary-100 text-lg rounded-circle">
                                                <iconify-icon icon="solar:camera-outline" class="icon"></iconify-icon>
                                            </label>
                                        </div>
                                        <div class="avatar-preview">
                                            <div id="imagePreview"> </div>
                                        </div>
                                    </div>
                                </div>
                                    <!-- Upload Image End -->

                                    <div class="col-sm-6">
                                        <label for="name"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">Full Name <span
                                                class="text-danger-600">*</span></label>
                                        <input type="text" class="form-control radius-8" id="name"
                                            placeholder="Enter Full Name" name="emp_name">
                                    </div>
                                    <div class="col-sm-6">
                                        <label for="email"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">Email <span
                                                class="text-danger-600">*</span></label>
                                        <input type="email" class="form-control radius-8" id="email" name="email"
                                            placeholder="Enter email address">
                                    </div>
                                    <div class="col-sm-6">
                                        <label for="phone"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">Phone</label>
                                        <input type="phone" class="form-control radius-8" id="phone" name="phone"
                                            placeholder="Enter phone number">
                                    </div>
                                    <div class="col-sm-6">
                                        <label for="dob"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">Date of
                                            Birth</label>
                                        <input type="text" class="form-control radius-8" id="dob" name="dob"
                                            placeholder="Select Date of Birth" readonly>
                                    </div>
                                    <div class="col-sm-6">
                                        <label for="gender"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">Gender</label>
                                        <select name="gender" id="gender" class="form-control select2">
                                            <option value="Male">Male</option>
                                            <option value="Female">Female</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                    <div class="col-sm-6">
                                        <label for="doj"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">Date of
                                            Joining</label>
                                        <input type="text" class="form-control radius-8" id="doj" name="doj"
                                            placeholder="Enter Date of Joining" readonly>
                                    </div>
                                    <div class="col-sm-6">
                                        <label for="role_id"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">Role <span
                                                class="text-danger-600">*</span> </label>
                                        <select class="form-control radius-8 form-select" id="role_id" name="role_id">
                                            @foreach ($roles as $role)
                                                <option value="{{$role->id}}">{{$role->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-sm-6">
                                        <label for="department"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">Department <span
                                                class="text-danger-600">*</span> </label>
                                        <input type="text" class="form-control radius-8"  id="department" name="department" placeholder="Enter Department Name">
                                    </div>
                                    <div class="col-sm-6">
                                        <label for="job_title"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">Job Title <span
                                                class="text-danger-600">*</span> </label>
                                        <input type="text" class="form-control radius-8"  id="job_title" name="job_title" placeholder="Enter Job Title">
                                    </div>
                                    <div class="col-sm-12">
                                        <label for="address"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">Address <span
                                                class="text-danger-600">*</span></label>
                                        <textarea class="form-control radius-8" id="address" name="address" placeholder="Enter Address"></textarea>
                                    </div>
                                    <div class="col-sm-6">
                                        <label for="zipcode"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">Zipcode <span
                                                class="text-danger-600">*</span> </label>
                                        <input type="text" class="form-control radius-8"  id="zipcode" name="zipcode" placeholder="Enter Zipcode">
                                    </div>
                                    <div class="col-sm-6">
                                        <label for="city"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">City<span
                                                class="text-danger-600">*</span> </label>
                                        <input type="text" class="form-control radius-8"  id="city" name="city" placeholder="Enter City">
                                    </div>
                                    <div class="col-sm-6">
                                        <label for="state_id"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">State<span
                                                class="text-danger-600">*</span> </label>
                                                <select class="form-control radius-8 form-select  wizard-required select2" id="state_id" name="state_id">

                                                </select>
                                    </div>
                                    <div class="col-sm-6">
                                        <label for="district_id"
                                            class="form-label fw-semibold text-primary-light text-sm mb-8">District<span
                                                class="text-danger-600">*</span> </label>
                                                <select class="form-control radius-8 form-select wizard-required select2" id="district_id" name="district_id">

                                                </select>
                                    </div>
                                    <div class="d-flex align-items-center justify-content-end gap-3">
                                        <a type="button" href="/employees"
                                            class="btn btn-sm btn-neutral-500 border-neutral-100 px-32">
                                            Cancel
                                        </a>
                                        <button type="submit"
                                            class="btn btn-sm btn-primary-600 px-32 btn-submit">
                                            Save
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

    </div>
@stop
@section('script')
    <script>
            $(function () {
            $('.select2').select2();
            $('#dob').daterangepicker({
                singleDatePicker: true,  // Enable single date selection
                showDropdowns: true,     // Allow month & year selection
                autoApply: true,         // Apply selection immediately
                // minDate: moment().add(1, 'days').startOf('day'), // Disable today, allow from tomorrow
                locale: {
                    format: 'DD/MM/YYYY' // Your required format
                }
            }, function (chosen_date) {
                $('#dob').val(chosen_date.format('DD/MM/YYYY')); // Set selected date
            });

            // Clear the input field initially
            $('#dob').val('');
        });

        $(function () {
            $('.select2').select2();
            $('#doj').daterangepicker({
                singleDatePicker: true,  // Enable single date selection
                showDropdowns: true,     // Allow month & year selection
                autoApply: true,         // Apply selection immediately
                // minDate: moment().add(1, 'days').startOf('day'), // Disable today, allow from tomorrow
                locale: {
                    format: 'DD/MM/YYYY' // Your required format
                }
            }, function (chosen_date) {
                $('#doj').val(chosen_date.format('DD/MM/YYYY')); // Set selected date
            });

            // Clear the input field initially
            $('#doj').val('');
        });
        // ================== Image Upload Js Start ===========================
        function readURL(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    $('#imagePreview').css('background-image', 'url(' + e.target.result + ')');
                    $('#imagePreview').hide();
                    $('#imagePreview').fadeIn(650);
                }
                reader.readAsDataURL(input.files[0]);
            }
        }
        $("#imageUpload").change(function() {
            readURL(this);
        });
        // ================== Image Upload Js End ===========================
        $(document).ready(function () {
    // Initialize Select2 for State
    $('select[name="state_id"]').select2({
        placeholder: 'Select State',
        width: "100%",
        ajax: {
            url: '/api/fetch_data',
            dataType: 'json',
            delay: 250,
            type: "POST",
            data: function (params) {
                return {
                    get_type: 4,
                    datafrom: "states",
                    q: params.term  // search term
                };
            },
            processResults: function (data) {
                return {
                    results: data
                };
            },
            cache: true
        },
        allowClear: true
    });
    // Initialize Select2 for districts
    $('select[name="district_id"]').select2({
            placeholder: 'Select District',
            width: "100%",
            ajax: {
                url: '/api/fetch_data',
                dataType: 'json',
                delay: 250,
                type: "POST",
                data: function (params) {
                    let stateId = $('select[name="state_id"]').val();
                    if (!stateId) {
                        // Show error message if state is not selected
                        Swal.fire({
                            icon: 'error',
                            title: 'Validation Error',
                            text: 'Please select a state first!',
                        });
                        return false; // Prevent the request from being sent
                    }
                    return {
                        get_type: 4,
                        datafrom: "districts",
                        state_id: stateId, // Send selected state_id
                        q: params.term  // search term
                    };
                },
                processResults: function (data) {
                    return {
                        results: data
                    };
                },
                cache: true
            },
            allowClear: true
        });

        // Prevent selecting district without state
        $('select[name="district_id"]').on('select2:opening', function (e) {
            let stateId = $('select[name="state_id"]').val();
            if (!stateId) {
                Swal.fire({
                    icon: 'error',
                    title: 'Validation Error',
                    text: 'Please select a state first!',
                });
                e.preventDefault(); // Prevent opening district dropdown
            }
        });
        $('select[name="state_id"]').change(function(){
                    biller = $(this).val();
                    $("#district_id").empty().trigger('change');
                });
                $("#form").validate({
            rules: {
                emp_name: {
                    required: true,
                    minlength: 3
                },
                email: {
                    required: true,
                    email: true
                },
                phone: {
                    required: true,
                    digits: true,
                    minlength: 10,
                    maxlength: 15
                },
                dob: {
                    required: true
                    // date: true
                },
                doj: {
                    required: true
                    // date: true
                },
                role_id: {
                    required: true
                },
                department: {
                    required: true
                },
                job_title: {
                    required: true
                },
                zipcode: {
                    required: true,
                    digits: true
                },
                city: {
                    required: true
                },
                state_id: {
                    required: true
                },
                district_id: {
                    required: true
                },
                photo: {
                    extension: "jpg|jpeg|png"
                }
            },
            messages: {
                emp_name: {
                    required: "Please enter the full name",
                    minlength: "Name should be at least 3 characters"
                },
                email: {
                    required: "Please enter an email address",
                    email: "Enter a valid email address"
                },
                phone: {
                    required: "Please enter a phone number",
                    digits: "Only numbers are allowed",
                    minlength: "Phone number must be at least 10 digits",
                    maxlength: "Phone number cannot exceed 15 digits"
                },
                dob: {
                    required: "Please select date of birth"
                },
                doj: {
                    required: "Please select date of joining"
                },
                role_id: {
                    required: "Please select a role"
                },
                department: {
                    required: "Please enter the department name"
                },
                job_title: {
                    required: "Please enter the job title"
                },
                zipcode: {
                    required: "Please enter the zipcode",
                    digits: "Only numbers are allowed"
                },
                city: {
                    required: "Please enter the city name"
                },
                state_id: {
                    required: "Please select a state"
                },
                district_id: {
                    required: "Please select a district"
                },
                photo: {
                    extension: "Only JPG, JPEG, and PNG formats are allowed"
                }
            },
            errorElement: "span",
            errorClass: "text-danger",
            errorPlacement: function(error, element) {
                    // Handle Select2 validation error placement
                    if (element.hasClass("select2-hidden-accessible")) {
                        error.insertAfter(element.next('.select2-container'));
                    } else {
                        error.insertAfter(element);
                    }
                },
            highlight: function (element) {
                $(element).addClass("is-invalid");
            },
            unhighlight: function (element) {
                $(element).removeClass("is-invalid");
            },
            submitHandler: function(form) {
                $(".btn-submit").prop("disabled", true).text("Processing..."); // Disable button and change text
                form.submit();
            }
        });
         // Ensure validation triggers on Select2 field change
         $('select[name="state_id"]').on('change', function () {
            $(this).valid();
        });

        // Trigger validation on blur for all fields
        $(document).on("focusout", "input, select, textarea", function () {
        var form = $(this).closest("form");
        if (form.data("validator")) {  // Check if the form has been initialized with validate()
            $(this).valid();
        }
    });
            });
    </script>

@stop
