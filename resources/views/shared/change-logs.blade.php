@extends('layouts.master')
@section('title', $title . ' Change Logs - Paidash')

@section('content')
<div class="dashboard-main-body">

    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">{{ $title }} Change Logs</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">
                <a href="{{ $indexRoute }}" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="{{ $moduleIcon ?? 'solar:widget-bold' }}" class="icon text-lg"></iconify-icon>
                    {{ $moduleTitle }}
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">
                <a href="{{ $viewRoute }}" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:eye-bold" class="icon text-lg"></iconify-icon>
                    View {{ $moduleTitle }}
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Change Logs</li>
        </ul>
    </div>

    <!-- Enhanced Entity Information Card -->
    <div class="card mb-4">
        <div class="card-body py-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="mb-0 fw-semibold d-flex align-items-center gap-2">
                        <iconify-icon icon="{{ $entityIcon ?? 'solar:widget-bold' }}" class="text-success"></iconify-icon>
                        {{ $title }} Information
                    </h6>
                    <small class="text-muted">{{ $title }} details and configuration information</small>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center justify-content-md-end justify-content-start gap-3 flex-wrap">
                        <a href="{{ $viewRoute }}" class="btn btn-primary btn-sm d-flex align-items-center gap-2">
                            <iconify-icon icon="solar:eye-bold"></iconify-icon>
                            View {{ $moduleTitle }}
                        </a>
                        @if(isset($editRoute) && $editRoute)
                            <a href="{{ $editRoute }}" class="btn btn-success btn-sm d-flex align-items-center gap-2">
                                <iconify-icon icon="solar:pen-bold"></iconify-icon>
                                Edit {{ $moduleTitle }}
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Entity Details Card -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                @foreach($entityInfo as $label => $value)
                    <div class="col-md-3 col-sm-6">
                        <div class="entity-detail-item">
                            <label class="text-muted small">{{ $label }}</label>
                            <div class="fw-semibold">{!! $value !!}</div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Enhanced Change Logs Section -->
    <div class="card mb-4">
        <div class="card-body py-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="mb-0 fw-semibold d-flex align-items-center gap-2">
                        <iconify-icon icon="solar:history-3-bold" class="text-primary"></iconify-icon>
                        Change History & Audit Trail
                    </h6>
                    <small class="text-muted">Complete timeline of all modifications and updates</small>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center justify-content-md-end justify-content-start gap-3 flex-wrap">
                        <span class="badge bg-info text-white d-flex align-items-center">
                            <iconify-icon icon="solar:document-text-bold" class="me-1"></iconify-icon>
                            {{ $changeLogs->count() }} Records
                        </span>
                        <button type="button" class="btn btn-success btn-sm d-flex align-items-center gap-2" onclick="window.print()">
                            <iconify-icon icon="solar:printer-bold"></iconify-icon>
                            Print History
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Logs Timeline -->
    <div class="card">
        <div class="card-body">
                    @if($changeLogs->count() > 0)
                        <div class="timeline">
                            @foreach($changeLogs as $log)
                                <div class="timeline-item mb-4">
                                    <div class="row">
                                        <div class="col-md-2 text-center">
                                            <div class="timeline-badge
                                                @if($log->action == 'created') bg-success
                                                @elseif($log->action == 'updated') bg-primary
                                                @elseif($log->action == 'deleted') bg-danger
                                                @else bg-secondary
                                                @endif
                                            ">
                                                @if($log->action == 'created')
                                                    <iconify-icon icon="solar:add-circle-bold" width="20"></iconify-icon>
                                                @elseif($log->action == 'updated')
                                                    <iconify-icon icon="solar:pen-bold" width="20"></iconify-icon>
                                                @elseif($log->action == 'deleted')
                                                    <iconify-icon icon="solar:trash-bin-trash-bold" width="20"></iconify-icon>
                                                @else
                                                    <iconify-icon icon="solar:info-circle-bold" width="20"></iconify-icon>
                                                @endif
                                            </div>
                                            <small class="text-muted d-block mt-2">
                                                {{ $log->created_at->format('d M Y') }}<br>
                                                {{ $log->created_at->format('h:i A') }}<br>
                                                <span class="text-xs">{{ $log->created_at->diffForHumans() }}</span>
                                            </small>
                                        </div>
                                        <div class="col-md-10">
                                            <div class="card border-0 shadow-sm">
                                                <div class="card-body">
                                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                                        <div>
                                                            <h6 class="mb-1 text-capitalize">
                                                                <strong>{{ ucfirst($log->action) }}</strong>
                                                                @if($log->description)
                                                                    - {{ $log->description }}
                                                                @elseif(isset($log->change_description) && $log->change_description)
                                                                    - {{ $log->change_description }}
                                                                @endif
                                                            </h6>
                                                            @if($log->changed_fields && count($log->changed_fields) > 0)
                                                                <small class="text-muted">
                                                                    {{ count($log->changed_fields) }} field(s) changed:
                                                                    {{ implode(', ', array_map(function($field) use ($log) { return $log->getFieldLabel($field); }, $log->changed_fields)) }}
                                                                </small>
                                                            @elseif(isset($log->field_name) && $log->field_name)
                                                                <small class="text-muted">Changed: {{ ucfirst(str_replace('_', ' ', $log->field_name)) }}</small>
                                                            @endif
                                                            @if(isset($log->reason) && $log->reason)
                                                                <small class="text-muted">Reason: {{ $log->reason }}</small>
                                                            @endif
                                                        </div>
                                                        <div class="text-end">
                                                            <small class="text-muted d-block">
                                                                by <strong>{{ $log->changed_by }}</strong>
                                                            </small>
                                                            <small class="text-muted">
                                                                {{ $log->created_at->format('d M Y, h:i A') }}
                                                            </small>
                                                        </div>
                                                    </div>

                                                    @if($log->changed_fields && count($log->changed_fields) > 0)
                                                        <div class="changes-details">
                                                            <small class="text-muted">Changes made:</small>
                                                            <div class="mt-2">
                                                                @foreach($log->changed_fields as $field)
                                                                    @php
                                                                        $oldValue = $log->formatFieldValue($field, $log->old_values[$field] ?? null);
                                                                        $newValue = $log->formatFieldValue($field, $log->new_values[$field] ?? null);
                                                                        $fieldLabel = $log->getFieldLabel($field);
                                                                    @endphp
                                                                    <div class="row mb-2">
                                                                        <div class="col-md-3">
                                                                            <strong>{{ $fieldLabel }}:</strong>
                                                                        </div>
                                                                        <div class="col-md-9">
                                                                            <div class="change-value-row">
                                                                                <span class="badge bg-danger-subtle text-danger">{{ $oldValue }}</span>
                                                                                <iconify-icon icon="mdi:arrow-right" width="16"></iconify-icon>
                                                                                <span class="badge bg-success-subtle text-success">{{ $newValue }}</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                    @elseif(isset($log->field_name) && $log->field_name && isset($log->old_value) && isset($log->new_value))
                                                        <div class="changes-details">
                                                            <small class="text-muted">Changes made:</small>
                                                            <div class="mt-2">
                                                                <div class="row mb-2">
                                                                    <div class="col-md-3">
                                                                        <strong>{{ ucfirst(str_replace('_', ' ', $log->field_name)) }}:</strong>
                                                                    </div>
                                                                    <div class="col-md-9">
                                                                        <div class="change-value-row">
                                                                            <span class="badge bg-danger-subtle text-danger">{{ $log->old_value }}</span>
                                                                            <iconify-icon icon="mdi:arrow-right" width="16"></iconify-icon>
                                                                            <span class="badge bg-success-subtle text-success">{{ $log->new_value }}</span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endif

                                                    @if($log->ip_address || $log->user_agent)
                                                        <div class="mt-3 pt-2 border-top">
                                                            <small class="text-muted">
                                                                @if($log->ip_address)
                                                                    <div class="d-flex align-items-center mb-1 d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:ip" width="14" class="me-1"></iconify-icon>
                                                                        <span>IP: {{ $log->ip_address }}</span>
                                                                    </div>
                                                                @endif
                                                                @if($log->user_agent)
                                                                    <div class="d-flex align-items-center d-flex align-items-center">
                                                                        <iconify-icon icon="mdi:monitor" width="14" class="me-1"></iconify-icon>
                                                                        <span>{{ $log->user_agent }}</span>
                                                                    </div>
                                                                @endif
                                                            </small>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="empty-state-container">
                            <div class="empty-state-content">
                                <div class="empty-state-icon">
                                    <iconify-icon icon="solar:history-3-bold" class="text-white"></iconify-icon>
                                </div>
                                <div class="empty-state-text">
                                    <h5 class="fw-semibold text-dark mb-2">No Change History Available</h5>
                                    <p class="text-muted mb-3">This {{ strtolower($title) }} hasn't been modified since creation. Any future changes will appear here with detailed logs.</p>
                                    <div class="empty-state-features">
                                        <div class="feature-item">
                                            <iconify-icon icon="solar:shield-check-bold" class="text-white me-2"></iconify-icon>
                                            <span class="text-sm text-muted">All changes are automatically tracked</span>
                                        </div>
                                        <div class="feature-item">
                                            <iconify-icon icon="solar:clock-circle-bold" class="text-info me-2"></iconify-icon>
                                            <span class="text-sm text-muted">Real-time audit trail</span>
                                        </div>
                                        <div class="feature-item">
                                            <iconify-icon icon="solar:user-bold" class="text-warning me-2"></iconify-icon>
                                            <span class="text-sm text-muted">User activity monitoring</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

</div>

<style>
/* Enhanced Timeline Styling */
.timeline-badge {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin: 0 auto;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border: 3px solid white;
}

.timeline-item {
    position: relative;
    transition: all 0.3s ease;
}

.timeline-item:hover {
    transform: translateY(-2px);
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 52px;
    top: 55px;
    width: 3px;
    height: calc(100% - 30px);
    background: linear-gradient(to bottom, #e9ecef, #dee2e6);
    z-index: -1;
    border-radius: 2px;
}

.timeline-item .card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.timeline-item:hover .card {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #dee2e6;
}

/* Entity Detail Items */
.entity-detail-item {
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    height: 100%;
    transition: all 0.2s ease;
}

.entity-detail-item:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-1px);
}

.entity-detail-item label {
    display: block;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
    color: #64748b;
}

.entity-detail-item div {
    font-size: 0.9rem;
    line-height: 1.4;
    word-break: break-word;
}

/* Empty State Styling */
.empty-state-container {
    padding: 4rem 2rem;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px;
    border: 2px dashed #dee2e6;
    margin: 2rem auto;
    max-width: 600px;
}

.empty-state-content {
    max-width: 500px;
    margin: 0 auto;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.empty-state-icon iconify-icon {
    font-size: 2.5rem;
    color: white;
}

.empty-state-text h5 {
    font-size: 1.25rem;
    color: #2d3748;
    margin-bottom: 0.75rem;
}

.empty-state-text p {
    font-size: 1rem;
    line-height: 1.6;
    color: #718096;
    margin-bottom: 2rem;
}

.empty-state-features {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    align-items: center;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.feature-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.feature-item iconify-icon {
    font-size: 1.1rem;
}

.feature-item span {
    font-weight: 500;
}

/* Enhanced Badge Styling */
.bg-danger-subtle {
    background-color: #fee2e2 !important;
    color: #dc2626 !important;
    border: 1px solid #fecaca;
}

.bg-success-subtle {
    background-color: #dcfce7 !important;
    color: #16a34a !important;
    border: 1px solid #bbf7d0;
}

.text-xs {
    font-size: 0.75rem;
}

.change-value-row {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: nowrap;
    padding: 0.5rem;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.change-value-row .badge {
    white-space: nowrap;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
}

.change-value-row iconify-icon {
    flex-shrink: 0;
    color: #64748b;
    font-size: 1.2rem;
}

/* Card Enhancements */
.card {
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border-bottom: 1px solid #e2e8f0;
    padding: 1.25rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Enhanced Badge Styling */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

/* Timeline Badge Variants */
.timeline-badge.bg-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.timeline-badge.bg-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.timeline-badge.bg-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.timeline-badge.bg-secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .timeline-item::after {
        left: 22px;
    }

    .timeline-item .row {
        margin: 0;
    }

    .timeline-item .col-md-2 {
        padding: 0;
        margin-bottom: 1rem;
    }

    .timeline-item .col-md-10 {
        padding: 0;
    }

    .empty-state-container {
        padding: 2rem 1rem;
    }

    .empty-state-features {
        align-items: stretch;
    }

    .feature-item {
        justify-content: center;
    }

    .change-value-row {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    /* Enhanced header mobile styling */
    .card-body.py-3 .row {
        flex-direction: column;
        gap: 15px;
    }

    .card-body.py-3 .col-md-6:last-child {
        text-align: left !important;
    }

    .card-body.py-3 .d-flex.align-items-center.justify-content-md-end {
        justify-content: flex-start !important;
        flex-wrap: wrap;
        gap: 10px !important;
    }

    .btn-sm {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
    }

    /* Entity detail items mobile */
    .entity-detail-item {
        margin-bottom: 1rem;
        padding: 0.75rem;
    }

    .entity-detail-item label {
        font-size: 0.7rem;
        margin-bottom: 0.25rem;
    }

    .entity-detail-item div {
        font-size: 0.85rem;
    }
}
</style>
@endsection
