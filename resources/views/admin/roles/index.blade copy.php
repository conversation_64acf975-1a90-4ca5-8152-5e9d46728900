@extends('layouts.master')
@section('content')
    <h2>Roles Management</h2>
    <form action="{{ route('roles.create') }}" method="POST">
        @csrf
        <input type="text" name="name" placeholder="New Role" class="form-control" required>
        <button type="submit">Add Role</button>
    </form>

    <h3>Assign Permissions</h3>
    <form action="{{ route('roles.assign-permissions') }}" method="POST">
        @csrf
        <select name="role_id" class="form-control">
            @foreach($roles as $role)
                <option value="{{ $role->id }}">{{ $role->name }}</option>
            @endforeach
        </select>

        @foreach($permissions as $permission)
            <label>
                <input type="checkbox" class="form-check-input" name="permissions[]" value="{{ $permission->name }}">
                {{ $permission->name }}
            </label>
        @endforeach
        <button type="submit">Assign Permissions</button>
    </form>
@endsection
