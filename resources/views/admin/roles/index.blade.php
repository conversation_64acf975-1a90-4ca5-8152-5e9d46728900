@extends('layouts.master')
@section('content')
<div class="dashboard-main-body">
    @if(session('success'))
        <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
            <div class="d-flex align-items-center gap-2">
                <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
                <span id="snackbar-message"> {{ session('success') }}</span>

            </div>
            <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
                <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
            </button>
        </div>
    @endif
        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">Roles List</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">Roles List</li>
            </ul>
        </div>

        <div class="card">
            <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
                <div class="d-flex flex-wrap align-items-center gap-3 w-25 w-mb-100">
                    <div class="icon-field w-100">
                        {{-- <input type="text" name="searchkey" class="form-control form-control-sm w-100" placeholder="Search Client" id="searchkey">
                        <span class="icon">
                            <iconify-icon icon="ion:search-outline"></iconify-icon>
                        </span> --}}
                    </div>
                </div>
                <div class="d-flex flex-no-wrap align-items-center gap-3">
                    @can('role-permission')
                    <button type="button" class="btn btn-sm btn-outline-info d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#bulkPermissionModal">
                        <i class="ri-settings-3-line me-1"></i> Bulk Permissions
                    </button>
                    @endcan

                    @can('role-create')
                    <button type="button" class="btn btn-sm btn-outline-secondary d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#cloneRoleModal">
                        <i class="ri-file-copy-line me-1"></i> Clone Role
                    </button>

                    <button type="button" class="btn btn-sm btn-primary-600 d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#addRoleModal">
                        <i class="ri-add-line me-1"></i> Add New Role
                    </button>
                    @endcan
                </div>
            </div>
            <div class="card-body">
                <!-- Table View for Desktop -->
                <div class="d-none d-md-block">
                    <div class="mb-3 text-end">
                        
                    </div>
                    <table id="rolesTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                        <thead>
                            <tr>
                                <th>S. No</th>
                                <th>Role Name</th>
                                <th>Users Count</th>
                                <th>Permissions Count</th>
                                @can('role-permission')
                                <th>Actions</th>
                                @endcan
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($roles as $key=>$role)
                            <tr>
                                <td>{{++$key}}</td>
                                <td>
                                    <span class="fw-medium">{{$role->name}}</span>
                                    @if($role->name === 'admin')
                                        <span class="badge bg-danger-100 text-danger-600 text-xs">Super Admin</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-primary-100 text-primary-600">{{ $role->users->count() }} users</span>
                                </td>
                                <td>
                                    <span class="badge bg-info-100 text-info-600">{{ $role->permissions->count() }} permissions</span>
                                </td>
                                @can('role-permission')
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <a href="{{route('roles.permissions', $role->id) }}" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center" title="Manage Permissions">
                                            <iconify-icon icon="heroicons:shield-check"></iconify-icon>
                                        </a>

                                        <button type="button" class="w-32-px h-32-px bg-warning-focus text-warning-main rounded-circle d-inline-flex align-items-center justify-content-center border-0"
                                                onclick="applyTemplate('{{ $role->id }}', '{{ $role->name }}')" title="Apply Template">
                                            <iconify-icon icon="heroicons:document-duplicate"></iconify-icon>
                                        </button>

                                        @if($role->name !== 'admin')
                                        <button type="button" class="w-32-px h-32-px bg-info-focus text-info-main rounded-circle d-inline-flex align-items-center justify-content-center border-0"
                                                onclick="cloneRole('{{ $role->id }}', '{{ $role->name }}')" title="Clone Role">
                                            <iconify-icon icon="heroicons:document-duplicate"></iconify-icon>
                                        </button>
                                        @endif
                                    </div>
                                </td>
                                @endcan
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Add Role Modal -->
                <div class="modal fade" id="addRoleModal" tabindex="-1" aria-labelledby="addRoleModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="addRoleModalLabel">Add New Role</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <form action="{{ route('roles.create') }}" method="POST">
                                @csrf
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label for="roleName" class="form-label">Role Name</label>
                                        <input type="text" class="form-control" id="roleName" name="name" required>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="submit" class="btn btn-primary">Create Role</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Clone Role Modal -->
                <div class="modal fade" id="cloneRoleModal" tabindex="-1" aria-labelledby="cloneRoleModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="cloneRoleModalLabel">Clone Role</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <form action="{{ route('roles.clone') }}" method="POST">
                                @csrf
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label for="sourceRole" class="form-label">Source Role</label>
                                        <select class="form-control" id="sourceRole" name="source_role_id" required>
                                            <option value="">Select role to clone</option>
                                            @foreach ($roles as $role)
                                                <option value="{{ $role->id }}">{{ $role->name }} ({{ $role->permissions->count() }} permissions)</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="newRoleName" class="form-label">New Role Name</label>
                                        <input type="text" class="form-control" id="newRoleName" name="new_role_name" required>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="submit" class="btn btn-primary">Clone Role</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Permission Template Modal -->
                <div class="modal fade" id="permissionTemplateModal" tabindex="-1" aria-labelledby="permissionTemplateModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="permissionTemplateModalLabel">Apply Permission Template</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <form action="{{ route('roles.apply-template') }}" method="POST">
                                @csrf
                                <div class="modal-body">
                                    <input type="hidden" id="templateRoleId" name="role_id">
                                    <div class="mb-3">
                                        <label class="form-label">Role: <span id="templateRoleName" class="fw-bold"></span></label>
                                    </div>
                                    <div class="mb-3">
                                        <label for="template" class="form-label">Permission Template</label>
                                        <select class="form-control" id="template" name="template" required>
                                            <option value="">Select template</option>
                                            <option value="admin">Admin (All Permissions)</option>
                                            <option value="manager">Manager (Management Permissions)</option>
                                            <option value="employee">Employee (Basic Permissions)</option>
                                            <option value="viewer">Viewer (Read-only Permissions)</option>
                                        </select>
                                    </div>
                                    <div class="alert alert-warning">
                                        <small><strong>Warning:</strong> This will replace all current permissions for this role.</small>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="submit" class="btn btn-warning">Apply Template</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Bulk Permission Modal -->
                <div class="modal fade" id="bulkPermissionModal" tabindex="-1" aria-labelledby="bulkPermissionModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="bulkPermissionModalLabel">Bulk Permission Management</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <form action="{{ route('roles.bulk-update-permissions') }}" method="POST">
                                @csrf
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label class="form-label">Select Roles</label>
                                        <div class="row">
                                            @foreach ($roles as $role)
                                                <div class="col-md-6 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="role_ids[]" value="{{ $role->id }}" id="bulkRole{{ $role->id }}">
                                                        <label class="form-check-label" for="bulkRole{{ $role->id }}">
                                                            {{ $role->name }}
                                                        </label>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="bulkAction" class="form-label">Action</label>
                                        <select class="form-control" id="bulkAction" name="action" required>
                                            <option value="">Select action</option>
                                            <option value="add">Add Permissions</option>
                                            <option value="remove">Remove Permissions</option>
                                            <option value="replace">Replace All Permissions</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Select Permissions</label>
                                        <div class="row" style="max-height: 300px; overflow-y: auto;">
                                            @foreach ($permissions as $permission)
                                                <div class="col-md-6 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="permissions[]" value="{{ $permission->name }}" id="bulkPerm{{ $permission->id }}">
                                                        <label class="form-check-label" for="bulkPerm{{ $permission->id }}">
                                                            {{ ucwords(str_replace(['-', '_'], ' ', $permission->name)) }}
                                                        </label>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="submit" class="btn btn-primary">Apply Changes</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Grid View for Mobile -->
                <div id="clientsGrid" class="d-block d-md-none"></div>
            </div>
        </div>

    </div>
    @stop

@section('script')
<script>
function applyTemplate(roleId, roleName) {
    document.getElementById('templateRoleId').value = roleId;
    document.getElementById('templateRoleName').textContent = roleName;
    new bootstrap.Modal(document.getElementById('permissionTemplateModal')).show();
}

function cloneRole(roleId, roleName) {
    document.getElementById('sourceRole').value = roleId;
    document.getElementById('newRoleName').value = roleName + '_copy';
    new bootstrap.Modal(document.getElementById('cloneRoleModal')).show();
}

// Add select all functionality for bulk permissions
document.addEventListener('DOMContentLoaded', function() {
    // Add select all button for roles
    const rolesContainer = document.querySelector('#bulkPermissionModal .row');
    if (rolesContainer) {
        const selectAllRoles = document.createElement('div');
        selectAllRoles.className = 'col-12 mb-3';
        selectAllRoles.innerHTML = '<button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleAllRoles()">Select All Roles</button>';
        rolesContainer.parentNode.insertBefore(selectAllRoles, rolesContainer);
    }

    // Add select all button for permissions
    const permissionsContainer = document.querySelector('#bulkPermissionModal .row[style*="max-height"]');
    if (permissionsContainer) {
        const selectAllPerms = document.createElement('div');
        selectAllPerms.className = 'col-12 mb-3';
        selectAllPerms.innerHTML = '<button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleAllPermissions()">Select All Permissions</button>';
        permissionsContainer.parentNode.insertBefore(selectAllPerms, permissionsContainer);
    }
});

function toggleAllRoles() {
    const checkboxes = document.querySelectorAll('input[name="role_ids[]"]');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    checkboxes.forEach(cb => cb.checked = !allChecked);
}

function toggleAllPermissions() {
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    checkboxes.forEach(cb => cb.checked = !allChecked);
}
</script>
@stop
