@extends('layouts.master')
@section('content')
    <div class="dashboard-main-body">
        @if (session('success'))
            <div id="snackbar"
                class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
                <div class="d-flex align-items-center gap-2">
                    <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
                    <span id="snackbar-message"> {{ session('success') }}</span>
                </div>
                <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
                    <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
                </button>
            </div>
        @endif

        <!-- Enhanced Breadcrumb -->
        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-32">
            <div class="page-header-content">
                <div class="d-flex align-items-center gap-3 mb-2">
                    
                    <div>
                        <h4 class="fw-bold mb-0 text-dark">Role Permissions</h4>
                        <p class="text-secondary-light mb-0 text-sm">Configure access permissions for <span class="fw-semibold text-primary-600">{{ ucfirst($role->name) }}</span> role</p>
                    </div>
                </div>
            </div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-modern">
                    <li class="breadcrumb-item">
                        <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                            <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                            Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ route('roles') }}" class="hover-text-primary">Roles</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">{{ ucfirst($role->name) }} Permissions</li>
                </ol>
            </nav>
        </div>

        <!-- Enhanced Control Panel -->
        <div class="card permissions-control-panel shadow-sm border-0">
            <div class="card-header bg-gradient-primary text-white border-0">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center gap-3">
                        <div class="role-avatar">
                            <iconify-icon icon="heroicons:shield-check" class="icon text-2xl"></iconify-icon>
                        </div>
                        <div>
                            <h5 class="mb-1 fw-bold text-white">{{ ucfirst($role->name) }} Role Permissions</h5>
                            <div class="d-flex align-items-center gap-4 text-sm opacity-90">
                                <span class="d-flex align-items-center gap-1">
                                    <iconify-icon icon="heroicons:users" class="icon"></iconify-icon>
                                    {{ $role->users->count() }} {{ Str::plural('user', $role->users->count()) }}
                                </span>
                                <span class="d-flex align-items-center gap-1">
                                    <iconify-icon icon="heroicons:check-circle" class="icon"></iconify-icon>
                                    <span id="permission-count-badge">{{ count($rolePermissions) }}</span> permissions
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <button type="button" class="btn btn-outline-light btn-sm d-flex align-items-center" id="deselect-all-permissions">
                            <iconify-icon icon="heroicons:x-mark" class="icon me-1"></iconify-icon>
                            Clear All
                        </button>
                        <button type="button" class="btn btn-light btn-sm d-flex align-items-center" id="select-all-permissions">
                            <iconify-icon icon="heroicons:check" class="icon me-1"></iconify-icon>
                            Select All
                        </button>
                    </div>
                </div>
            </div>

            <!-- Enhanced Search and Filter Bar -->
            <div class="card-body bg-light border-bottom">
                <div class="row g-3 align-items-center">
                    <div class="col-md-8">
                        <div class="search-wrapper position-relative">
                            <iconify-icon icon="heroicons:magnifying-glass" class="search-icon position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></iconify-icon>
                            <input type="text" class="form-control form-control-lg ps-5 border-0 bg-white shadow-sm"
                                   placeholder="Search permissions by name or category..." id="permission-search">
                            <div class="search-clear position-absolute top-50 end-0 translate-middle-y me-3 d-none" id="search-clear">
                                <iconify-icon icon="heroicons:x-mark" class="icon text-muted cursor-pointer"></iconify-icon>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select form-select-lg border-0 bg-white shadow-sm" id="category-filter">
                            <option value="">🔍 All Categories</option>
                            <option value="dashboard">🏠 Dashboard</option>
                            <option value="client">👥 Client Management</option>
                            <option value="service">⚙️ Service Management</option>
                            <option value="invoice">📄 Invoice Management</option>
                            <option value="payment">💳 Payment Management</option>
                            <option value="employee">👤 Employee Management</option>
                            <option value="role">🛡️ Role & Permissions</option>
                            <option value="weight">⚖️ Weight Management</option>
                            <option value="inventory">📦 Inventory Management</option>
                            <option value="expense">💰 Expense Management</option>
                            <option value="report">📊 Reports & Analytics</option>
                            <option value="system">🔧 System & Settings</option>
                            <option value="audit">📋 Audit & Activity Logs</option>
                        </select>
                    </div>
                </div>

            </div>
        </div>

        <!-- Permissions Form -->
        <form action="{{ route('roles.assign-permissions') }}" method="POST" id="permissions-form">
            @csrf
            <input type="hidden" name="role_id" value="{{ $role->id }}">

            <!-- Permissions Grid Layout -->
            <div class="permissions-container">
                @php
                    $permissionCategories = [
                        'dashboard' => [
                            'title' => 'Dashboard',
                            'icon' => 'solar:home-smile-angle-outline',
                            'color' => 'primary',
                            'description' => 'Control access to dashboard and main application entry'
                        ],
                        'client' => [
                            'title' => 'Client Management',
                            'icon' => 'heroicons:users',
                            'color' => 'success',
                            'description' => 'Manage client data, creation, editing, and related features'
                        ],
                        'service' => [
                            'title' => 'Service Management',
                            'icon' => 'heroicons:cog-6-tooth',
                            'color' => 'info',
                            'description' => 'Control service creation, editing, and management'
                        ],
                        'invoice' => [
                            'title' => 'Invoice Management',
                            'icon' => 'heroicons:document-text',
                            'color' => 'warning',
                            'description' => 'Manage invoice creation, editing, and processing'
                        ],
                        'payment' => [
                            'title' => 'Payment Management',
                            'icon' => 'heroicons:credit-card',
                            'color' => 'success',
                            'description' => 'Control payment processing and tracking'
                        ],
                        'employee' => [
                            'title' => 'Employee Management',
                            'icon' => 'heroicons:user-group',
                            'color' => 'secondary',
                            'description' => 'Manage employee data and assignments'
                        ],
                        'role' => [
                            'title' => 'Role & Permissions',
                            'icon' => 'heroicons:shield-check',
                            'color' => 'danger',
                            'description' => 'Control role and permission management'
                        ],
                        'weight' => [
                            'title' => 'Weight Management',
                            'icon' => 'heroicons:scale',
                            'color' => 'info',
                            'description' => 'Manage weight entries and tracking'
                        ],
                        'inventory' => [
                            'title' => 'Inventory Management',
                            'icon' => 'heroicons:cube',
                            'color' => 'primary',
                            'description' => 'Control inventory items and stock management'
                        ],
                        'expense' => [
                            'title' => 'Expense Management',
                            'icon' => 'heroicons:banknotes',
                            'color' => 'warning',
                            'description' => 'Manage expenses and financial tracking'
                        ],
                        'report' => [
                            'title' => 'Reports & Analytics',
                            'icon' => 'heroicons:chart-bar',
                            'color' => 'success',
                            'description' => 'Access various reports and analytics'
                        ],
                        'system' => [
                            'title' => 'System & Settings',
                            'icon' => 'heroicons:cog-8-tooth',
                            'color' => 'secondary',
                            'description' => 'Control system settings and maintenance'
                        ],
                        'audit' => [
                            'title' => 'Audit & Activity Logs',
                            'icon' => 'heroicons:document-magnifying-glass',
                            'color' => 'info',
                            'description' => 'Access system audit trails and user activity logs'
                        ]
                    ];
                @endphp

                @foreach ($permissionCategories as $categoryKey => $category)
                    @php
                        $categoryPermissions = $permissions->filter(function($permission) use ($categoryKey) {
                            if ($categoryKey === 'dashboard') {
                                return $permission->name === 'dashboard';
                            }
                            if ($categoryKey === 'system') {
                                return strpos($permission->name, 'settings-') === 0 || strpos($permission->name, 'system-') === 0;
                            }
                            if ($categoryKey === 'audit') {
                                return strpos($permission->name, 'system-audit') === 0 || strpos($permission->name, 'user-activity') === 0;
                            }
                            return strpos($permission->name, $categoryKey . '-') === 0;
                        });
                    @endphp

                    @if ($categoryPermissions->count() > 0)
                        <div class="card permission-category mb-4 shadow-sm border-0" data-category="{{ $categoryKey }}">
                            <!-- Enhanced Category Header -->
                            <div class="card-header bg-white border-bottom-0 pb-0">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="category-icon-enhanced">
                                            <div class="icon-wrapper bg-{{ $category['color'] }}-100 text-{{ $category['color'] }}-600">
                                                <iconify-icon icon="{{ $category['icon'] }}" class="icon text-xl"></iconify-icon>
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="mb-1 fw-bold text-dark">{{ $category['title'] }}</h6>
                                            <div class="d-flex align-items-center gap-3 text-sm">
                                                <span class="text-muted">
                                                    {{ $categoryPermissions->count() }} {{ Str::plural('permission', $categoryPermissions->count()) }}
                                                </span>
                                                <span class="badge bg-{{ $category['color'] }}-100 text-{{ $category['color'] }}-600" id="selected-count-{{ $categoryKey }}">
                                                    0 selected
                                                </span>
                                            </div>
                                            <small class="text-muted d-block mt-1">{{ $category['description'] }}</small>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="form-switch switch-success d-flex align-items-center gap-2">
                                            <input class="form-check-input toggle-category" type="checkbox" role="switch"
                                                   id="toggle-{{ $categoryKey }}" data-category="{{ $categoryKey }}">
                                            <label class="form-check-label fw-medium text-secondary-light mb-0" for="toggle-{{ $categoryKey }}">
                                                <span class="toggle-text">Toggle All</span>
                                            </label>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-{{ $category['color'] }} category-collapse-btn"
                                                data-bs-toggle="collapse" data-bs-target="#category-{{ $categoryKey }}"
                                                aria-expanded="true" aria-controls="category-{{ $categoryKey }}">
                                            <iconify-icon icon="heroicons:chevron-up" class="icon collapse-icon"></iconify-icon>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Permission Grid -->
                            <div class="collapse show" id="category-{{ $categoryKey }}">
                                <div class="card-body pt-3">
                                    <div class="permission-grid-enhanced">
                                        @foreach ($categoryPermissions as $permission)
                                            <div class="permission-item-enhanced" data-permission-name="{{ $permission->name }}" data-category="{{ $categoryKey }}">
                                                <label class="permission-card-enhanced" for="{{ $permission->id }}">
                                                    <div class="permission-card-inner">
                                                        <div class="permission-checkbox-wrapper">
                                                            <input class="form-check-input permission-checkbox" type="checkbox" name="permissions[]"
                                                                id="{{ $permission->id }}" value="{{ $permission->name }}" data-category="{{ $categoryKey }}"
                                                                {{ in_array($permission->name, $rolePermissions) ? 'checked' : '' }}>
                                                        </div>
                                                        <div class="permission-content-enhanced">
                                                            <span class="permission-name-enhanced">
                                                                {{ ucwords(str_replace(['-', '_'], ' ', str_replace([$categoryKey . '-', 'settings-', 'system-'], '', $permission->name))) }}
                                                            </span>
                                                            <small class="permission-key-enhanced">{{ $permission->name }}</small>
                                                        </div>
                                                        <div class="permission-status">
                                                            <div class="status-indicator">
                                                                <iconify-icon icon="heroicons:check" class="icon"></iconify-icon>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                @endforeach
            </div>

            <!-- Enhanced Form Actions -->
            <div class="sticky-bottom bg-white border-top shadow-lg py-4 mt-5">
                <div class="container-fluid">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="form-summary">
                            <div class="d-flex align-items-center gap-4">
                                <div class="summary-item">
                                    <iconify-icon icon="heroicons:information-circle" class="icon text-primary me-1"></iconify-icon>
                                    <span class="text-sm text-muted">
                                        <span id="selected-permissions-summary">{{ count($rolePermissions) }} of {{ count($permissions) }}</span> permissions selected
                                    </span>
                                </div>
                                <div class="summary-item">
                                    <div class="progress-wrapper">
                                        <small class="text-muted">Completion:</small>
                                        <div class="progress ms-2" style="width: 80px; height: 4px;">
                                            <div class="progress-bar bg-success" role="progressbar"
                                                 style="width: {{ count($permissions) > 0 ? (count($rolePermissions) / count($permissions)) * 100 : 0 }}%"
                                                 id="form-progress-bar"></div>
                                        </div>
                                        <small class="text-muted ms-1" id="completion-percentage">
                                            {{ count($permissions) > 0 ? round((count($rolePermissions) / count($permissions)) * 100) : 0 }}%
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions d-flex align-items-center gap-3">
                            <a href="{{ route('roles') }}" class="btn btn-light btn-lg d-flex align-items-center gap-2">
                                <iconify-icon icon="heroicons:arrow-left" class="icon me-1"></iconify-icon>
                                Back to Roles
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg d-flex align-items-center gap-2" id="save-permissions-btn">
                                <iconify-icon icon="heroicons:check" class="icon me-1"></iconify-icon>
                                Update Permissions
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@stop

@section('script')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced permission search functionality
    const searchInput = document.getElementById('permission-search');
    const searchClear = document.getElementById('search-clear');
    const categoryFilter = document.getElementById('category-filter');
    const permissionItems = document.querySelectorAll('.permission-item-enhanced');
    const permissionCategories = document.querySelectorAll('.permission-category');

    // Search functionality with clear button
    searchInput.addEventListener('input', function() {
        const hasValue = this.value.length > 0;
        searchClear.classList.toggle('d-none', !hasValue);
        filterPermissions();
        updateVisibleCount();
    });

    // Clear search functionality
    searchClear.addEventListener('click', function() {
        searchInput.value = '';
        searchClear.classList.add('d-none');
        filterPermissions();
        updateVisibleCount();
        searchInput.focus();
    });

    // Category filter functionality
    categoryFilter.addEventListener('change', function() {
        filterPermissions();
        updateVisibleCount();
    });

    // Enhanced filter function with animations
    function filterPermissions() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedCategory = categoryFilter.value;
        let totalVisible = 0;

        permissionCategories.forEach(category => {
            const categoryKey = category.dataset.category;
            let hasVisibleItems = false;

            if (selectedCategory && selectedCategory !== categoryKey) {
                category.style.display = 'none';
                return;
            }

            const items = category.querySelectorAll('.permission-item-enhanced');
            items.forEach(item => {
                const permissionName = item.dataset.permissionName.toLowerCase();
                const label = item.querySelector('label').textContent.toLowerCase();

                if (permissionName.includes(searchTerm) || label.includes(searchTerm)) {
                    item.style.display = 'block';
                    hasVisibleItems = true;
                    totalVisible++;
                } else {
                    item.style.display = 'none';
                }
            });

            category.style.display = hasVisibleItems ? 'block' : 'none';
        });

        return totalVisible;
    }

    // Update visible permissions count
    function updateVisibleCount() {
        const visibleCount = filterPermissions();
        document.getElementById('visible-permissions-count').textContent = visibleCount;
    }

    // Select all permissions functionality
    document.getElementById('select-all-permissions').addEventListener('click', function() {
        document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
            checkbox.checked = true;
        });
        updateAllCounts();
    });

    // Deselect all permissions functionality
    document.getElementById('deselect-all-permissions').addEventListener('click', function() {
        document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        updateAllCounts();
    });

    // Toggle category functionality
    document.querySelectorAll('.toggle-category').forEach(toggle => {
        toggle.addEventListener('change', function() {
            const category = this.dataset.category;
            const isChecked = this.checked;

            document.querySelectorAll(`.permission-checkbox[data-category="${category}"]`).forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            updateCategoryCount(category);
            updateTotalCount();
        });
    });

    // Individual permission checkbox change
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const category = this.dataset.category;
            updateCategoryCount(category);
            updateCategoryToggle(category);
            updateTotalCount();
        });
    });

    // Permission card click functionality - labels handle this automatically
    // No additional click handlers needed for the new design

    // Enhanced category count update with animations
    function updateCategoryCount(category) {
        const categoryCheckboxes = document.querySelectorAll(`.permission-checkbox[data-category="${category}"]`);
        const checkedCount = Array.from(categoryCheckboxes).filter(cb => cb.checked).length;
        const totalCount = categoryCheckboxes.length;

        const countElement = document.getElementById(`selected-count-${category}`);
        if (countElement) {
            countElement.textContent = `${checkedCount} selected`;

            // Add visual feedback
            if (checkedCount === totalCount && totalCount > 0) {
                countElement.className = 'badge bg-success-100 text-success-600';
            } else if (checkedCount > 0) {
                countElement.className = 'badge bg-warning-100 text-warning-600';
            } else {
                countElement.className = 'badge bg-light text-muted';
            }
        }
    }

    // Enhanced category toggle state with visual feedback and text updates
    function updateCategoryToggle(category) {
        const categoryCheckboxes = document.querySelectorAll(`.permission-checkbox[data-category="${category}"]`);
        const toggleElement = document.getElementById(`toggle-${category}`);
        const toggleText = toggleElement?.parentElement.querySelector('.toggle-text');

        if (categoryCheckboxes.length > 0 && toggleElement) {
            const allChecked = Array.from(categoryCheckboxes).every(cb => cb.checked);
            const someChecked = Array.from(categoryCheckboxes).some(cb => cb.checked);

            toggleElement.checked = allChecked;
            toggleElement.indeterminate = someChecked && !allChecked;

            // Update toggle text similar to client view pattern
            if (toggleText) {
                if (allChecked) {
                    toggleText.textContent = 'All Selected';
                } else if (someChecked) {
                    toggleText.textContent = 'Partial Selection';
                } else {
                    toggleText.textContent = 'None Selected';
                }
            }
        }
    }

    // Enhanced total count with progress updates
    function updateTotalCount() {
        const totalCheckboxes = document.querySelectorAll('.permission-checkbox');
        const checkedCount = Array.from(totalCheckboxes).filter(cb => cb.checked).length;
        const totalCount = totalCheckboxes.length;
        const percentage = totalCount > 0 ? Math.round((checkedCount / totalCount) * 100) : 0;

        // Update all count displays
        document.getElementById('selected-permissions-count').textContent = checkedCount;
        document.getElementById('selected-permissions-summary').textContent = `${checkedCount} of ${totalCount}`;
        document.getElementById('permission-count-badge').textContent = `${checkedCount} of ${totalCount}`;
        document.getElementById('completion-percentage').textContent = `${percentage}%`;

        // Update progress bars
        const progressBars = document.querySelectorAll('#permission-progress-bar, #form-progress-bar');
        progressBars.forEach(bar => {
            bar.style.width = `${percentage}%`;

            // Update progress bar color based on completion
            bar.className = 'progress-bar';
            if (percentage === 100) {
                bar.classList.add('bg-success');
            } else if (percentage >= 50) {
                bar.classList.add('bg-primary');
            } else if (percentage > 0) {
                bar.classList.add('bg-warning');
            } else {
                bar.classList.add('bg-light');
            }
        });
    }

    // Update all counts
    function updateAllCounts() {
        const categories = ['dashboard', 'client', 'service', 'invoice', 'payment', 'employee', 'role', 'weight', 'inventory', 'expense', 'report', 'system', 'audit'];
        categories.forEach(category => {
            updateCategoryCount(category);
            updateCategoryToggle(category);
        });
        updateTotalCount();
    }

    // Initialize counts and setup
    updateAllCounts();
    updateVisibleCount();

    // Enhanced collapse functionality
    document.querySelectorAll('.category-collapse-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const icon = this.querySelector('.collapse-icon');
            const isExpanded = this.getAttribute('aria-expanded') === 'true';

            // Animate icon rotation
            setTimeout(() => {
                if (isExpanded) {
                    icon.style.transform = 'rotate(180deg)';
                } else {
                    icon.style.transform = 'rotate(0deg)';
                }
            }, 150);
        });
    });

    // Enhanced form submission with loading state and validation
    const form = document.getElementById('permissions-form');
    const submitButton = document.getElementById('save-permissions-btn');

    form.addEventListener('submit', function(e) {
        const checkedPermissions = document.querySelectorAll('.permission-checkbox:checked');

        // Add loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<div class="loading-spinner me-2"></div>Updating Permissions...';

        // Add visual feedback
        submitButton.classList.add('btn-loading');

        // Show success message after a brief delay (for better UX)
        setTimeout(() => {
            // Form will submit normally
        }, 300);
    });

    // Enhanced keyboard navigation
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + A to select all
        if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
            e.preventDefault();
            document.getElementById('select-all-permissions').click();
        }

        // Escape to clear search
        if (e.key === 'Escape' && searchInput.value) {
            searchClear.click();
        }

        // Ctrl/Cmd + F to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            searchInput.focus();
            searchInput.select();
        }
    });

    // Auto-save draft functionality (optional)
    let autoSaveTimeout;
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                // Could implement auto-save to localStorage here
                console.log('Auto-saving permissions state...');
            }, 2000);
        });
    });

    // Enhanced visual feedback for interactions
    document.querySelectorAll('.permission-card-enhanced').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        card.addEventListener('mouseleave', function() {
            if (!this.querySelector('input:checked')) {
                this.style.transform = 'translateY(0)';
            }
        });
    });

    // Smooth scroll to first error (if any)
    const firstError = document.querySelector('.is-invalid');
    if (firstError) {
        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
});
</script>

<style>
/* Enhanced Modern Design System */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Page Header */
.page-header-content {
    position: relative;
}

.role-icon-wrapper {
    width: 48px;
    height: 48px;
    background: var(--primary-gradient);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: var(--card-shadow);
}

.breadcrumb-modern {
    background: none;
    padding: 0;
    margin: 0;
}

.breadcrumb-modern .breadcrumb-item + .breadcrumb-item::before {
    content: "→";
    color: #6b7280;
    font-weight: 500;
}

/* Enhanced Control Panel */
.permissions-control-panel {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.bg-gradient-primary {
    background: var(--primary-gradient) !important;
}

.role-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

/* Enhanced Search */
.search-wrapper {
    position: relative;
}

.search-icon {
    z-index: 5;
}

.search-clear {
    z-index: 5;
    cursor: pointer;
}

.search-clear:hover .icon {
    color: #ef4444 !important;
}

/* Enhanced Permission Grid */
.permission-grid-enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.permission-item-enhanced {
    position: relative;
}

.permission-card-enhanced {
    display: block;
    background: #ffffff;
    border: 2px solid #f1f5f9;
    border-radius: var(--border-radius);
    padding: 0;
    margin: 0;
    cursor: pointer;
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.permission-card-enhanced:hover {
    border-color: #e2e8f0;
    box-shadow: var(--card-shadow);
    transform: translateY(-2px);
}

.permission-card-inner {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    position: relative;
}

.permission-checkbox-wrapper {
    position: relative;
}

.permission-checkbox-wrapper input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin: 0;
    border: 2px solid #d1d5db;
    border-radius: 6px;
    background: #ffffff;
    transition: var(--transition);
    cursor: pointer;
}

.permission-checkbox-wrapper input[type="checkbox"]:checked {
    background: var(--primary-gradient);
    border-color: transparent;
    box-shadow: 0 0 0 3px rgba(103, 126, 234, 0.1);
}

.permission-content-enhanced {
    flex: 1;
    transition: var(--transition);
}

.permission-name-enhanced {
    display: block;
    font-weight: 600;
    color: #1f2937;
    font-size: 15px;
    line-height: 1.4;
    margin-bottom: 4px;
}

.permission-key-enhanced {
    display: block;
    color: #6b7280;
    font-size: 13px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #f9fafb;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
}

.permission-status {
    position: relative;
}

.status-indicator {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #f3f4f6;
    border: 2px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    opacity: 0;
    transform: scale(0.8);
}

/* Checked state animations */
.permission-card-enhanced:has(input:checked) {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
}

.permission-card-enhanced:has(input:checked) .permission-content-enhanced {
    color: #1f2937;
}

.permission-card-enhanced:has(input:checked) .status-indicator {
    opacity: 1;
    transform: scale(1);
    background: var(--success-gradient);
    border-color: transparent;
    color: white;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

/* Enhanced Category Headers */
.category-icon-enhanced .icon-wrapper {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.category-icon-enhanced:hover .icon-wrapper {
    transform: scale(1.05);
    box-shadow: var(--card-shadow);
}

/* Enhanced Form Switch - Consistent with Client View */
.form-switch.switch-success .form-check-input {
    width: 2.5rem;
    height: 1.25rem;
    border-radius: 1rem;
    background-color: #e5e7eb;
    border: none;
    transition: var(--transition);
}

.form-switch.switch-success .form-check-input:checked {
    background-color: #10b981;
    border-color: #10b981;
}

.form-switch.switch-success .form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
}

.form-switch.switch-success .form-check-input:indeterminate {
    background-color: #f59e0b;
    border-color: #f59e0b;
}

.form-switch .form-check-label {
    cursor: pointer;
    user-select: none;
}

.toggle-text {
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
}

/* Collapse Animation */
.category-collapse-btn .collapse-icon {
    transition: var(--transition);
}

.category-collapse-btn[aria-expanded="false"] .collapse-icon {
    transform: rotate(180deg);
}

/* Progress Enhancements */
.progress {
    border-radius: 10px;
    overflow: hidden;
    background: #f3f4f6;
}

.progress-bar {
    transition: var(--transition);
    border-radius: 10px;
}

/* Enhanced Sticky Bottom */
.sticky-bottom {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95) !important;
}

.form-summary .summary-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.progress-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Enhanced Form Controls */
.form-control-lg, .form-select-lg {
    border-radius: var(--border-radius);
    padding: 16px 20px;
    font-size: 15px;
    font-weight: 500;
    border: 2px solid #f1f5f9;
    transition: var(--transition);
}

.form-control-lg:focus, .form-select-lg:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(103, 126, 234, 0.1);
}

.form-check-input:indeterminate {
    background: var(--primary-gradient);
    border-color: transparent;
}

/* Enhanced Sticky Bottom */
.sticky-bottom {
    position: sticky;
    bottom: 0;
    z-index: 100;
    margin-left: -24px;
    margin-right: -24px;
    padding-left: 24px;
    padding-right: 24px;
    border-top: 1px solid #e5e7eb;
}

/* Enhanced Animations */
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-10px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.permission-category {
    animation: fadeIn 0.3s ease-out;
}

.permission-item-enhanced {
    animation: slideIn 0.3s ease-out;
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .permission-grid-enhanced {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 12px;
    }
}

@media (max-width: 768px) {
    .permission-grid-enhanced {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .permission-card-inner {
        padding: 16px;
    }

    .sticky-bottom {
        margin-left: -16px;
        margin-right: -16px;
        padding-left: 16px;
        padding-right: 16px;
    }

    .page-header-content .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 12px;
    }

    .role-icon-wrapper {
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 576px) {
    .permissions-control-panel .card-header .d-flex {
        flex-direction: column;
        align-items: stretch !important;
        gap: 16px;
    }

    .permissions-control-panel .card-body .row {
        flex-direction: column;
    }

    .form-actions {
        flex-direction: column;
        gap: 12px !important;
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .form-summary {
        margin-bottom: 16px;
    }

    .form-summary .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 12px;
    }
}

/* Enhanced Search Input */
.search-wrapper input.form-control-lg {
    padding-left: 48px;
    padding-right: 48px;
}

/* Loading States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Enhanced Hover Effects */
.btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--card-shadow);
}

.permission-card-enhanced:hover:not(:has(input:checked)) {
    border-color: #d1d5db;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-gradient: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
        --success-gradient: linear-gradient(135deg, #059669 0%, #10b981 100%);
    }
}

/* Print Styles */
@media print {
    .sticky-bottom,
    .permissions-control-panel .card-header,
    .category-collapse-btn {
        display: none !important;
    }

    .permission-card-enhanced {
        break-inside: avoid;
        border: 1px solid #000 !important;
        background: white !important;
    }
}
</style>
@stop
