<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\InventoryCategory;
use App\Models\InventoryItem;
use App\Models\InventoryTransaction;

class InventorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create categories
        $categories = [
            ['name' => 'Bags', 'description' => 'Waste collection bags and containers'],
            ['name' => 'Equipment', 'description' => 'Medical and waste management equipment'],
            ['name' => 'Safety Equipment', 'description' => 'Personal protective equipment'],
            ['name' => 'Containers', 'description' => 'Storage and disposal containers'],
            ['name' => 'Posters', 'description' => 'Educational and safety posters']
        ];

        foreach ($categories as $categoryData) {
            InventoryCategory::create($categoryData);
        }

        // Create sample inventory items
        $items = [
            [
                'category_id' => 1, // Bags
                'name' => 'Waste Collection Bag (Red)',
                'sku_code' => 'WB-RED-001',
                'description' => 'Red biohazard waste collection bags',
                'quantity_available' => 150,
                'minimum_required' => 50,
                'unit_of_measure' => 'pieces',
                'unit_price' => 120.00,
                'cgst_rate' => 9.00,
                'sgst_rate' => 9.00,
                'igst_rate' => 18.00,
                'is_gst_applicable' => true,
                'status' => 'in_stock'
            ],
            [
                'category_id' => 2, // Equipment
                'name' => 'Needle Cutter',
                'sku_code' => 'NC-001',
                'description' => 'Automatic needle cutting device',
                'quantity_available' => 5,
                'minimum_required' => 10,
                'unit_of_measure' => 'pieces',
                'unit_price' => 450.00,
                'cgst_rate' => 9.00,
                'sgst_rate' => 9.00,
                'igst_rate' => 18.00,
                'is_gst_applicable' => true,
                'status' => 'low_stock'
            ],
            [
                'category_id' => 3, // Safety Equipment
                'name' => 'Safety Gloves (Large)',
                'sku_code' => 'SG-L-001',
                'description' => 'Disposable safety gloves - Large size',
                'quantity_available' => 0,
                'minimum_required' => 20,
                'unit_of_measure' => 'pieces',
                'unit_price' => 25.00,
                'status' => 'out_of_stock'
            ],
            [
                'category_id' => 4, // Containers
                'name' => 'Sharps Container (5L)',
                'sku_code' => 'SC-5L-001',
                'description' => '5 liter sharps disposal container',
                'quantity_available' => 25,
                'minimum_required' => 15,
                'unit_of_measure' => 'pieces',
                'unit_price' => 85.00,
                'status' => 'in_stock'
            ],
            [
                'category_id' => 5, // Posters
                'name' => 'Biohazard Safety Poster',
                'sku_code' => 'BSP-001',
                'description' => 'Educational poster for biohazard safety',
                'quantity_available' => 8,
                'minimum_required' => 12,
                'unit_of_measure' => 'pieces',
                'unit_price' => 15.00,
                'status' => 'low_stock'
            ]
        ];

        foreach ($items as $itemData) {
            $item = InventoryItem::create($itemData);

            // Create initial transaction for each item
            InventoryTransaction::create([
                'inventory_item_id' => $item->id,
                'transaction_type' => 'in',
                'quantity' => $item->quantity_available,
                'quantity_before' => 0,
                'quantity_after' => $item->quantity_available,
                'reason' => 'Initial stock',
                'created_by' => 1 // Assuming user ID 1 exists
            ]);
        }
    }
}
