<?php

namespace Database\Seeders;

use App\Models\ExpenseCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ExpenseCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Office Supplies',
                'description' => 'Stationery, equipment, and office materials',
                'color' => '#007bff',
                'is_active' => true,
                'created_by' => 1
            ],
            [
                'name' => 'Travel & Transportation',
                'description' => 'Business travel, fuel, and transportation costs',
                'color' => '#28a745',
                'is_active' => true,
                'created_by' => 1
            ],
            [
                'name' => 'Meals & Entertainment',
                'description' => 'Business meals and client entertainment',
                'color' => '#ffc107',
                'is_active' => true,
                'created_by' => 1
            ],
            [
                'name' => 'Utilities',
                'description' => 'Electricity, water, internet, and phone bills',
                'color' => '#17a2b8',
                'is_active' => true,
                'created_by' => 1
            ],
            [
                'name' => 'Marketing & Advertising',
                'description' => 'Promotional activities and advertising costs',
                'color' => '#e83e8c',
                'is_active' => true,
                'created_by' => 1
            ],
            [
                'name' => 'Professional Services',
                'description' => 'Legal, accounting, and consulting fees',
                'color' => '#6f42c1',
                'is_active' => true,
                'created_by' => 1
            ],
            [
                'name' => 'Equipment & Software',
                'description' => 'Hardware, software licenses, and equipment',
                'color' => '#fd7e14',
                'is_active' => true,
                'created_by' => 1
            ],
            [
                'name' => 'Maintenance & Repairs',
                'description' => 'Building and equipment maintenance',
                'color' => '#20c997',
                'is_active' => true,
                'created_by' => 1
            ]
        ];

        foreach ($categories as $category) {
            ExpenseCategory::firstOrCreate(
                ['name' => $category['name']],
                $category
            );
        }
    }
}
