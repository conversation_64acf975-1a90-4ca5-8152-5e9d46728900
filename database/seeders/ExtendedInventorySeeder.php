<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\InventoryCategory;
use App\Models\InventoryItem;
use App\Models\InventoryTransaction;
use Carbon\Carbon;

class ExtendedInventorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Additional categories
        $additionalCategories = [
            ['name' => 'Chemicals', 'description' => 'Laboratory and cleaning chemicals'],
            ['name' => 'Instruments', 'description' => 'Medical and laboratory instruments'],
            ['name' => 'Consumables', 'description' => 'Single-use medical consumables'],
            ['name' => 'Protective Gear', 'description' => 'Personal protective equipment'],
            ['name' => 'Cleaning Supplies', 'description' => 'Cleaning and disinfection supplies'],
            ['name' => 'Office Supplies', 'description' => 'Administrative and office materials'],
            ['name' => 'Maintenance', 'description' => 'Equipment maintenance and repair items'],
        ];

        foreach ($additionalCategories as $categoryData) {
            InventoryCategory::firstOrCreate(
                ['name' => $categoryData['name']],
                $categoryData
            );
        }

        // Get all categories for reference
        $categories = InventoryCategory::all()->keyBy('name');

        // Extended inventory items
        $extendedItems = [
            // Chemicals
            [
                'category_id' => $categories['Chemicals']->id,
                'name' => 'Disinfectant Solution (5L)',
                'sku_code' => 'CHEM-DIS-5L',
                'description' => 'Hospital-grade disinfectant solution',
                'quantity_available' => 25,
                'minimum_required' => 10,
                'unit_of_measure' => 'liters',
                'unit_price' => 45.00,
                'status' => 'in_stock'
            ],
            [
                'category_id' => $categories['Chemicals']->id,
                'name' => 'Alcohol Sanitizer (500ml)',
                'sku_code' => 'CHEM-ALC-500',
                'description' => '70% alcohol hand sanitizer',
                'quantity_available' => 8,
                'minimum_required' => 15,
                'unit_of_measure' => 'pieces',
                'unit_price' => 12.50,
                'status' => 'low_stock'
            ],
            [
                'category_id' => $categories['Chemicals']->id,
                'name' => 'Bleach Solution (1L)',
                'sku_code' => 'CHEM-BLE-1L',
                'description' => 'Sodium hypochlorite bleach solution',
                'quantity_available' => 0,
                'minimum_required' => 5,
                'unit_of_measure' => 'liters',
                'unit_price' => 8.75,
                'status' => 'out_of_stock'
            ],

            // Instruments
            [
                'category_id' => $categories['Instruments']->id,
                'name' => 'Digital Thermometer',
                'sku_code' => 'INST-THERM-DIG',
                'description' => 'Non-contact infrared thermometer',
                'quantity_available' => 12,
                'minimum_required' => 5,
                'unit_of_measure' => 'pieces',
                'unit_price' => 89.99,
                'status' => 'in_stock'
            ],
            [
                'category_id' => $categories['Instruments']->id,
                'name' => 'Blood Pressure Monitor',
                'sku_code' => 'INST-BP-MON',
                'description' => 'Automatic digital blood pressure monitor',
                'quantity_available' => 3,
                'minimum_required' => 8,
                'unit_of_measure' => 'pieces',
                'unit_price' => 156.00,
                'status' => 'low_stock'
            ],
            [
                'category_id' => $categories['Instruments']->id,
                'name' => 'Stethoscope',
                'sku_code' => 'INST-STETH-001',
                'description' => 'Professional cardiology stethoscope',
                'quantity_available' => 15,
                'minimum_required' => 10,
                'unit_of_measure' => 'pieces',
                'unit_price' => 245.00,
                'status' => 'in_stock'
            ],

            // Consumables
            [
                'category_id' => $categories['Consumables']->id,
                'name' => 'Disposable Syringes (10ml)',
                'sku_code' => 'CONS-SYR-10ML',
                'description' => 'Sterile disposable syringes 10ml',
                'quantity_available' => 500,
                'minimum_required' => 200,
                'unit_of_measure' => 'pieces',
                'unit_price' => 0.85,
                'status' => 'in_stock'
            ],
            [
                'category_id' => $categories['Consumables']->id,
                'name' => 'Medical Gauze Pads',
                'sku_code' => 'CONS-GAUZE-PAD',
                'description' => 'Sterile gauze pads 4x4 inches',
                'quantity_available' => 45,
                'minimum_required' => 100,
                'unit_of_measure' => 'boxes',
                'unit_price' => 15.25,
                'status' => 'low_stock'
            ],
            [
                'category_id' => $categories['Consumables']->id,
                'name' => 'Surgical Masks (Box of 50)',
                'sku_code' => 'CONS-MASK-50',
                'description' => 'Disposable surgical face masks',
                'quantity_available' => 0,
                'minimum_required' => 25,
                'unit_of_measure' => 'boxes',
                'unit_price' => 22.00,
                'status' => 'out_of_stock'
            ],

            // Protective Gear
            [
                'category_id' => $categories['Protective Gear']->id,
                'name' => 'Nitrile Gloves (Medium)',
                'sku_code' => 'PPE-GLOVE-M',
                'description' => 'Powder-free nitrile examination gloves',
                'quantity_available' => 85,
                'minimum_required' => 50,
                'unit_of_measure' => 'boxes',
                'unit_price' => 18.50,
                'status' => 'in_stock'
            ],
            [
                'category_id' => $categories['Protective Gear']->id,
                'name' => 'Safety Goggles',
                'sku_code' => 'PPE-GOGGLE-001',
                'description' => 'Anti-fog safety goggles',
                'quantity_available' => 22,
                'minimum_required' => 15,
                'unit_of_measure' => 'pieces',
                'unit_price' => 12.75,
                'status' => 'in_stock'
            ],
            [
                'category_id' => $categories['Protective Gear']->id,
                'name' => 'Disposable Gowns',
                'sku_code' => 'PPE-GOWN-DISP',
                'description' => 'Disposable isolation gowns',
                'quantity_available' => 5,
                'minimum_required' => 30,
                'unit_of_measure' => 'pieces',
                'unit_price' => 8.25,
                'status' => 'low_stock'
            ],

            // Cleaning Supplies
            [
                'category_id' => $categories['Cleaning Supplies']->id,
                'name' => 'Floor Cleaner (2L)',
                'sku_code' => 'CLEAN-FLOOR-2L',
                'description' => 'Hospital-grade floor cleaning solution',
                'quantity_available' => 18,
                'minimum_required' => 12,
                'unit_of_measure' => 'liters',
                'unit_price' => 16.50,
                'status' => 'in_stock'
            ],
            [
                'category_id' => $categories['Cleaning Supplies']->id,
                'name' => 'Microfiber Cloths',
                'sku_code' => 'CLEAN-CLOTH-MF',
                'description' => 'Reusable microfiber cleaning cloths',
                'quantity_available' => 35,
                'minimum_required' => 20,
                'unit_of_measure' => 'pieces',
                'unit_price' => 3.25,
                'status' => 'in_stock'
            ],
            [
                'category_id' => $categories['Cleaning Supplies']->id,
                'name' => 'Toilet Paper (12-pack)',
                'sku_code' => 'CLEAN-TP-12',
                'description' => 'Commercial toilet paper 12-roll pack',
                'quantity_available' => 8,
                'minimum_required' => 15,
                'unit_of_measure' => 'packs',
                'unit_price' => 24.99,
                'status' => 'low_stock'
            ],

            // Office Supplies
            [
                'category_id' => $categories['Office Supplies']->id,
                'name' => 'A4 Paper (500 sheets)',
                'sku_code' => 'OFF-PAPER-A4',
                'description' => 'White A4 copy paper ream',
                'quantity_available' => 45,
                'minimum_required' => 20,
                'unit_of_measure' => 'reams',
                'unit_price' => 8.75,
                'status' => 'in_stock'
            ],
            [
                'category_id' => $categories['Office Supplies']->id,
                'name' => 'Blue Ink Pens',
                'sku_code' => 'OFF-PEN-BLUE',
                'description' => 'Ballpoint pens with blue ink',
                'quantity_available' => 125,
                'minimum_required' => 50,
                'unit_of_measure' => 'pieces',
                'unit_price' => 1.25,
                'status' => 'in_stock'
            ],
            [
                'category_id' => $categories['Office Supplies']->id,
                'name' => 'Printer Cartridge (Black)',
                'sku_code' => 'OFF-CART-BLK',
                'description' => 'Black ink cartridge for office printer',
                'quantity_available' => 2,
                'minimum_required' => 8,
                'unit_of_measure' => 'pieces',
                'unit_price' => 65.00,
                'status' => 'low_stock'
            ],

            // Maintenance
            [
                'category_id' => $categories['Maintenance']->id,
                'name' => 'Equipment Lubricant',
                'sku_code' => 'MAINT-LUB-001',
                'description' => 'Multi-purpose equipment lubricant',
                'quantity_available' => 12,
                'minimum_required' => 8,
                'unit_of_measure' => 'bottles',
                'unit_price' => 18.50,
                'status' => 'in_stock'
            ],
            [
                'category_id' => $categories['Maintenance']->id,
                'name' => 'Replacement Filters',
                'sku_code' => 'MAINT-FILT-001',
                'description' => 'HEPA filters for air purification system',
                'quantity_available' => 0,
                'minimum_required' => 6,
                'unit_of_measure' => 'pieces',
                'unit_price' => 125.00,
                'status' => 'out_of_stock'
            ],
            [
                'category_id' => $categories['Maintenance']->id,
                'name' => 'Screwdriver Set',
                'sku_code' => 'MAINT-SCREW-SET',
                'description' => 'Professional screwdriver set with case',
                'quantity_available' => 4,
                'minimum_required' => 3,
                'unit_of_measure' => 'sets',
                'unit_price' => 45.75,
                'status' => 'in_stock'
            ],

            // Additional Bags and Containers
            [
                'category_id' => $categories['Bags']->id,
                'name' => 'Waste Collection Bag (Yellow)',
                'sku_code' => 'WB-YEL-001',
                'description' => 'Yellow clinical waste bags',
                'quantity_available' => 75,
                'minimum_required' => 40,
                'unit_of_measure' => 'pieces',
                'unit_price' => 135.00,
                'status' => 'in_stock'
            ],
            [
                'category_id' => $categories['Bags']->id,
                'name' => 'Waste Collection Bag (Blue)',
                'sku_code' => 'WB-BLU-001',
                'description' => 'Blue pharmaceutical waste bags',
                'quantity_available' => 25,
                'minimum_required' => 30,
                'unit_of_measure' => 'pieces',
                'unit_price' => 145.00,
                'status' => 'low_stock'
            ],

            // Additional Containers
            [
                'category_id' => $categories['Containers']->id,
                'name' => 'Sharps Container (2L)',
                'sku_code' => 'SC-2L-001',
                'description' => '2 liter sharps disposal container',
                'quantity_available' => 40,
                'minimum_required' => 25,
                'unit_of_measure' => 'pieces',
                'unit_price' => 65.00,
                'status' => 'in_stock'
            ],
            [
                'category_id' => $categories['Containers']->id,
                'name' => 'Pharmaceutical Waste Container (10L)',
                'sku_code' => 'PWC-10L-001',
                'description' => '10 liter pharmaceutical waste container',
                'quantity_available' => 8,
                'minimum_required' => 12,
                'unit_of_measure' => 'pieces',
                'unit_price' => 95.00,
                'status' => 'low_stock'
            ]
        ];

        // Create items and initial transactions
        foreach ($extendedItems as $itemData) {
            $item = InventoryItem::firstOrCreate(
                ['sku_code' => $itemData['sku_code']],
                $itemData
            );

            // Create initial transaction if item was just created
            if ($item->wasRecentlyCreated && $item->quantity_available > 0) {
                InventoryTransaction::create([
                    'inventory_item_id' => $item->id,
                    'transaction_type' => 'in',
                    'quantity' => $item->quantity_available,
                    'quantity_before' => 0,
                    'quantity_after' => $item->quantity_available,
                    'reason' => 'Initial stock',
                    'created_by' => 1,
                    'created_at' => Carbon::now()->subDays(rand(1, 30))
                ]);
            }
        }
    }
}
