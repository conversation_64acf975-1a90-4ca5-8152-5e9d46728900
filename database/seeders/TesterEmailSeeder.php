<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TesterEmailSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if tester_mailid setting already exists
        $existingSetting = DB::table('company_settings')
                            ->where('setting_key', 'tester_mailid')
                            ->first();

        if (!$existingSetting) {
            // Insert the tester email setting
            DB::table('company_settings')->insert([
                'setting_key' => 'tester_mailid',
                'setting_value' => '<EMAIL>', // Default test email
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $this->command->info('Tester email setting added to company_settings table.');
        } else {
            $this->command->info('Tester email setting already exists in company_settings table.');
        }
    }
}
