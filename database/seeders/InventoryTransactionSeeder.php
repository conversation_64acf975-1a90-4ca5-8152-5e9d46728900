<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\InventoryItem;
use App\Models\InventoryTransaction;
use Carbon\Carbon;

class InventoryTransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $items = InventoryItem::all();
        $transactionTypes = ['in', 'out', 'adjustment'];
        $reasons = [
            'in' => ['Purchase', 'Return', 'Transfer In', 'Donation', 'Initial Stock'],
            'out' => ['Sale', 'Usage', 'Transfer Out', 'Damage', 'Expired'],
            'adjustment' => ['Inventory Count', 'Correction', 'Loss', 'Found', 'System Adjustment']
        ];

        foreach ($items as $item) {
            // Create 3-8 random transactions for each item over the past 60 days
            $transactionCount = rand(3, 8);
            $currentQuantity = 0;

            for ($i = 0; $i < $transactionCount; $i++) {
                $transactionType = $transactionTypes[array_rand($transactionTypes)];
                $quantityBefore = $currentQuantity;

                // Generate realistic quantity changes
                switch ($transactionType) {
                    case 'in':
                        $quantity = rand(10, 100);
                        $quantityAfter = $quantityBefore + $quantity;
                        break;
                    case 'out':
                        $maxOut = min($quantityBefore, rand(5, 50));
                        $quantity = $maxOut > 0 ? rand(1, $maxOut) : 0;
                        $quantityAfter = $quantityBefore - $quantity;
                        break;
                    case 'adjustment':
                        $quantity = rand(1, 20);
                        $quantityAfter = rand(0, $quantityBefore + 20);
                        break;
                }

                $currentQuantity = $quantityAfter;

                // Select random reason for this transaction type
                $reason = $reasons[$transactionType][array_rand($reasons[$transactionType])];

                // Create transaction with random date in the past 60 days
                InventoryTransaction::create([
                    'inventory_item_id' => $item->id,
                    'transaction_type' => $transactionType,
                    'quantity' => $quantity,
                    'quantity_before' => $quantityBefore,
                    'quantity_after' => $quantityAfter,
                    'reason' => $reason,
                    'notes' => $this->generateRandomNotes($transactionType, $reason),
                    'reference_number' => $this->generateReferenceNumber($transactionType),
                    'created_by' => rand(1, 3), // Random user ID
                    'created_at' => Carbon::now()->subDays(rand(1, 60))->subHours(rand(0, 23))->subMinutes(rand(0, 59))
                ]);
            }

            // Update item's final quantity to match the last transaction
            $item->quantity_available = $currentQuantity;
            $item->updateStatus();
        }
    }

    /**
     * Generate random notes for transactions
     */
    private function generateRandomNotes($type, $reason)
    {
        $notes = [
            'Purchase' => ['Ordered from supplier ABC', 'Bulk purchase discount applied', 'Emergency restock', 'Regular monthly order'],
            'Sale' => ['Sold to department X', 'Patient care usage', 'Routine consumption', 'Emergency usage'],
            'Usage' => ['Used in procedure room 3', 'Consumed during patient care', 'Training session usage', 'Quality control testing'],
            'Damage' => ['Damaged during transport', 'Equipment malfunction', 'Expired items disposed', 'Quality issue reported'],
            'Transfer In' => ['Received from warehouse', 'Inter-department transfer', 'Relocated from storage', 'Consolidated inventory'],
            'Transfer Out' => ['Sent to department Y', 'Moved to main storage', 'Distributed to units', 'Emergency allocation'],
            'Inventory Count' => ['Annual inventory audit', 'Monthly stock check', 'Cycle count adjustment', 'Physical count variance'],
            'Correction' => ['Data entry error fixed', 'System sync correction', 'Manual adjustment needed', 'Reconciliation update']
        ];

        $reasonNotes = $notes[$reason] ?? ['Standard transaction', 'Routine operation', 'Regular activity'];
        return $reasonNotes[array_rand($reasonNotes)];
    }

    /**
     * Generate reference numbers for transactions
     */
    private function generateReferenceNumber($type)
    {
        $prefix = [
            'in' => 'PO',
            'out' => 'SO',
            'adjustment' => 'ADJ'
        ];

        return $prefix[$type] . '-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
}
