<?php

use Illuminate\Database\Migrations\Migration;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create service-edit permission
        Permission::firstOrCreate(['name' => 'service-edit']);

        // Assign to admin role
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminRole->givePermissionTo('service-edit');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove service-edit permission
        $permission = Permission::where('name', 'service-edit')->first();
        if ($permission) {
            $permission->delete();
        }
    }
};
