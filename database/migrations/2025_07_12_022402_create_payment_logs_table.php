<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('payment_id');
            $table->string('action'); // 'created', 'updated', 'deleted'
            $table->string('field_name')->nullable(); // Field that was changed
            $table->text('old_value')->nullable(); // Previous value
            $table->text('new_value')->nullable(); // New value
            $table->string('changed_by'); // User who made the change
            $table->string('ip_address')->nullable(); // IP address of user
            $table->text('user_agent')->nullable(); // Browser/device info
            $table->text('reason')->nullable(); // Reason for change
            $table->json('full_data')->nullable(); // Complete payment data snapshot
            $table->timestamps();

            // Indexes for better performance
            $table->index('payment_id');
            $table->index('action');
            $table->index('changed_by');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_logs');
    }
};
