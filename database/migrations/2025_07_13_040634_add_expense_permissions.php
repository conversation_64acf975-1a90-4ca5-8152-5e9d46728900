<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create expense permissions
        $permissions = [
            'expense-list',
            'expense-create',
            'expense-view',
            'expense-edit',
            'expense-delete',
            'expense-approve',
            'expense-category-list',
            'expense-category-create',
            'expense-category-edit',
            'expense-category-delete',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign to admin role
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminRole->givePermissionTo($permissions);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove expense permissions
        $permissions = [
            'expense-list',
            'expense-create',
            'expense-view',
            'expense-edit',
            'expense-delete',
            'expense-approve',
            'expense-category-list',
            'expense-category-create',
            'expense-category-edit',
            'expense-category-delete',
        ];

        foreach ($permissions as $permission) {
            Permission::where('name', $permission)->delete();
        }
    }
};
