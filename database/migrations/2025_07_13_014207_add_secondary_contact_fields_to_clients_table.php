<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Temporarily disable strict mode
        DB::statement("SET sql_mode = ''");

        Schema::table('clients', function (Blueprint $table) {
            $table->string('secondary_phone', 20)->nullable()->after('phone');
            $table->string('secondary_email', 255)->nullable()->after('email');
        });

        // Re-enable strict mode
        DB::statement("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            $table->dropColumn(['secondary_phone', 'secondary_email']);
        });
    }
};
