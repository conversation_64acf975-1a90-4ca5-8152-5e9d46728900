<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inventory_items', function (Blueprint $table) {
            $table->decimal('cgst_rate', 5, 2)->default(9.00)->after('unit_price')->comment('CGST rate percentage');
            $table->decimal('sgst_rate', 5, 2)->default(9.00)->after('cgst_rate')->comment('SGST rate percentage');
            $table->decimal('igst_rate', 5, 2)->default(18.00)->after('sgst_rate')->comment('IGST rate percentage');
            $table->boolean('is_gst_applicable')->default(true)->after('igst_rate')->comment('Whether GST is applicable for this item');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inventory_items', function (Blueprint $table) {
            $table->dropColumn(['cgst_rate', 'sgst_rate', 'igst_rate', 'is_gst_applicable']);
        });
    }
};
