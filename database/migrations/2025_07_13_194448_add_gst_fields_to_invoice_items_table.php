<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_items', function (Blueprint $table) {
            $table->decimal('cgst_rate', 5, 2)->default(0.00)->after('total_price')->comment('CGST rate percentage at time of invoice');
            $table->decimal('sgst_rate', 5, 2)->default(0.00)->after('cgst_rate')->comment('SGST rate percentage at time of invoice');
            $table->decimal('igst_rate', 5, 2)->default(0.00)->after('sgst_rate')->comment('IGST rate percentage at time of invoice');
            $table->decimal('cgst_amount', 10, 2)->default(0.00)->after('igst_rate')->comment('CGST amount for this line item');
            $table->decimal('sgst_amount', 10, 2)->default(0.00)->after('cgst_amount')->comment('SGST amount for this line item');
            $table->decimal('igst_amount', 10, 2)->default(0.00)->after('sgst_amount')->comment('IGST amount for this line item');
            $table->decimal('total_with_gst', 10, 2)->default(0.00)->after('igst_amount')->comment('Total amount including GST');
            $table->boolean('is_gst_applicable')->default(true)->after('total_with_gst')->comment('Whether GST was applicable for this item');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_items', function (Blueprint $table) {
            $table->dropColumn(['cgst_rate', 'sgst_rate', 'igst_rate', 'cgst_amount', 'sgst_amount', 'igst_amount', 'total_with_gst', 'is_gst_applicable']);
        });
    }
};
