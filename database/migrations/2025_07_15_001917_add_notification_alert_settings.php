<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add notification alert settings to company_settings table
        $notificationSettings = [
            // Invoice Creation Alerts
            ['setting_key' => 'invoice_alert_enabled', 'setting_value' => '0'],
            ['setting_key' => 'invoice_alert_emails', 'setting_value' => ''],
            ['setting_key' => 'invoice_alert_manual_creation', 'setting_value' => '0'],
            ['setting_key' => 'invoice_alert_auto_creation', 'setting_value' => '0'],

            // Payment Creation Alerts
            ['setting_key' => 'payment_alert_enabled', 'setting_value' => '0'],
            ['setting_key' => 'payment_alert_emails', 'setting_value' => ''],

            // Client Creation Alerts
            ['setting_key' => 'client_alert_enabled', 'setting_value' => '0'],
            ['setting_key' => 'client_alert_emails', 'setting_value' => ''],

            // Employee Creation Alerts
            ['setting_key' => 'employee_alert_enabled', 'setting_value' => '0'],
            ['setting_key' => 'employee_alert_emails', 'setting_value' => ''],

            // Service Creation Alerts
            ['setting_key' => 'service_alert_enabled', 'setting_value' => '0'],
            ['setting_key' => 'service_alert_emails', 'setting_value' => ''],

            // Daily Report Alerts
            ['setting_key' => 'daily_report_alert_enabled', 'setting_value' => '0'],
            ['setting_key' => 'daily_report_alert_emails', 'setting_value' => ''],
            ['setting_key' => 'daily_report_time', 'setting_value' => '08:00'],
        ];

        foreach ($notificationSettings as $setting) {
            DB::table('company_settings')->updateOrInsert(
                ['setting_key' => $setting['setting_key']],
                [
                    'setting_value' => $setting['setting_value'],
                    'remarks' => 'Notification alert setting',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove notification alert settings
        $settingKeys = [
            'invoice_alert_enabled', 'invoice_alert_emails', 'invoice_alert_manual_creation', 'invoice_alert_auto_creation',
            'payment_alert_enabled', 'payment_alert_emails',
            'client_alert_enabled', 'client_alert_emails',
            'employee_alert_enabled', 'employee_alert_emails',
            'service_alert_enabled', 'service_alert_emails',
            'daily_report_alert_enabled', 'daily_report_alert_emails',
        ];

        DB::table('company_settings')->whereIn('setting_key', $settingKeys)->delete();
    }
};
