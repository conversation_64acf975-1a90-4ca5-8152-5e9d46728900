<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if foreign key constraint already exists
        $constraintExists = \DB::select("
            SELECT COUNT(*) as count
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'expenses'
            AND COLUMN_NAME = 'category_id'
            AND REFERENCED_TABLE_NAME = 'expense_categories'
        ");

        if ($constraintExists[0]->count == 0) {
            Schema::table('expenses', function (Blueprint $table) {
                // Add foreign key constraint for category_id
                $table->foreign('category_id')->references('id')->on('expense_categories')->onDelete('cascade');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('expenses', function (Blueprint $table) {
            // Drop foreign key constraint
            $table->dropForeign(['category_id']);
        });
    }
};
