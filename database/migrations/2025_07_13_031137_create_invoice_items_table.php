<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('invoice_id');
            $table->unsignedBigInteger('inventory_item_id');
            $table->string('item_name'); // Store item name at time of invoice creation
            $table->string('item_sku'); // Store SKU at time of invoice creation
            $table->text('item_description')->nullable(); // Store description at time of invoice creation
            $table->integer('quantity'); // Quantity of this item in the invoice
            $table->decimal('unit_price', 10, 2); // Unit price at time of invoice creation
            $table->decimal('total_price', 10, 2); // Total price for this line item (quantity * unit_price)
            $table->string('unit_of_measure')->default('pieces'); // Unit of measure at time of invoice creation
            $table->timestamps();

            // Add indexes for better performance
            $table->index(['invoice_id', 'inventory_item_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_items');
    }
};
