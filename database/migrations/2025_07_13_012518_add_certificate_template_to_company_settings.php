<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Insert certificate template setting into company_settings table
        $certificateTemplateSetting = [
            'setting_key' => 'certificate_template',
            'setting_value' => 'clients.certificate_v2', // Default to v2 template
            'remarks' => 'Certificate template to use for PDF generation (clients.certificate or clients.certificate_v2)',
            'created_at' => now(),
            'updated_at' => now(),
        ];

        // Only insert if the setting doesn't already exist
        $exists = DB::table('company_settings')
            ->where('setting_key', 'certificate_template')
            ->exists();

        if (!$exists) {
            DB::table('company_settings')->insert($certificateTemplateSetting);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove certificate template setting
        DB::table('company_settings')
            ->where('setting_key', 'certificate_template')
            ->delete();
    }
};
