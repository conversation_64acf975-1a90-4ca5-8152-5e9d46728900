<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Temporarily disable strict mode
        DB::statement("SET sql_mode = ''");

        Schema::table('client_services', function (Blueprint $table) {
            // Add invoice generation type field
            $table->tinyInteger('invoice_generation_type')->default(1)->after('payment_cycle')
                ->comment('1=End of month, 2=Based on start date, 3=Custom day');

            // Add custom day field for when invoice_generation_type = 3
            $table->tinyInteger('custom_invoice_day')->nullable()->after('invoice_generation_type')
                ->comment('Day of month for custom invoice generation (1-31)');
        });

        // Re-enable strict mode
        DB::statement("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('client_services', function (Blueprint $table) {
            $table->dropColumn(['invoice_generation_type', 'custom_invoice_day']);
        });
    }
};
