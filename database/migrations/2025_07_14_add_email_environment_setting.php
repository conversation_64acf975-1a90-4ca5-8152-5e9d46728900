<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Insert email environment setting into company_settings table
        $emailEnvironmentSetting = [
            'setting_key' => 'email_environment',
            'setting_value' => 'live', // Default to live
            'remarks' => 'Email environment setting: live (use client emails) or test (use tester email)',
            'created_at' => now(),
            'updated_at' => now(),
        ];

        // Only insert if the setting doesn't already exist
        $exists = DB::table('company_settings')
            ->where('setting_key', 'email_environment')
            ->exists();

        if (!$exists) {
            DB::table('company_settings')->insert($emailEnvironmentSetting);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove email environment setting
        DB::table('company_settings')
            ->where('setting_key', 'email_environment')
            ->delete();
    }
};
