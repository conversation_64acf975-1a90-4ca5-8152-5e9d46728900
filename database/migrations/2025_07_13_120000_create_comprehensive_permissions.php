<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Define comprehensive permissions for all application modules
        $permissions = [
            // Dashboard permissions
            'dashboard',
            
            // Client permissions
            'client-list',
            'client-view',
            'client-create',
            'client-edit',
            'client-delete',
            'client-activate-deactivate',
            'client-discount',
            'client-export',
            'client-services-report',
            
            // Service permissions
            'service-list',
            'service-view',
            'service-create',
            'service-edit',
            'service-delete',
            'service-activate-deactivate',
            'service-export',
            
            // Invoice permissions
            'invoice-list',
            'invoice-view',
            'invoice-create',
            'invoice-edit',
            'invoice-delete',
            'invoice-send',
            'invoice-download',
            'invoice-export',
            'invoice-inventory-create',
            
            // Payment permissions
            'payment-list',
            'payment-view',
            'payment-create',
            'payment-edit',
            'payment-delete',
            'payment-export',
            'payment-accounts',
            
            // Employee permissions
            'employee-list',
            'employee-view',
            'employee-create',
            'employee-edit',
            'employee-delete',
            'employee-activate-deactivate',
            'employee-assign-clients',
            'employee-export',
            
            // Role & Permission management
            'role-list',
            'role-create',
            'role-edit',
            'role-delete',
            'role-permission',
            
            // Reports permissions
            'report-client-services',
            'report-payments',
            'report-invoices',
            'report-dashboard',
            'report-export',
            
            // Weight Entry permissions
            'weight-entry-list',
            'weight-entry-create',
            'weight-entry-edit',
            'weight-entry-delete',
            'weight-entry-bulk-upload',
            'weight-entry-export',
            
            // Inventory permissions
            'inventory-list',
            'inventory-view',
            'inventory-create',
            'inventory-edit',
            'inventory-delete',
            'inventory-export',
            'inventory-category-list',
            'inventory-category-create',
            'inventory-category-edit',
            'inventory-category-delete',
            
            // Expense permissions
            'expense-list',
            'expense-view',
            'expense-create',
            'expense-edit',
            'expense-delete',
            'expense-approve',
            'expense-export',
            'expense-category-list',
            'expense-category-create',
            'expense-category-edit',
            'expense-category-delete',
            
            // Settings permissions
            'settings-view',
            'settings-edit',
            'settings-company',
            'settings-system',
            
            // System permissions
            'system-backup',
            'system-logs',
            'system-maintenance',
            'system-queue-monitor',
        ];

        // Create all permissions
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'web']);
        }

        // Update admin role with all permissions
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminRole->syncPermissions($permissions);
        }

        // Update manager role with limited permissions
        $managerRole = Role::where('name', 'manager')->first();
        if ($managerRole) {
            $managerPermissions = [
                'dashboard',
                'client-list', 'client-view', 'client-create', 'client-edit',
                'service-list', 'service-view', 'service-create', 'service-edit',
                'invoice-list', 'invoice-view', 'invoice-create', 'invoice-edit',
                'payment-list', 'payment-view', 'payment-create', 'payment-edit',
                'employee-list', 'employee-view',
                'report-client-services', 'report-payments', 'report-invoices',
                'weight-entry-list', 'weight-entry-create', 'weight-entry-edit',
                'inventory-list', 'inventory-view',
                'expense-list', 'expense-view', 'expense-create', 'expense-edit',
            ];
            $managerRole->syncPermissions($managerPermissions);
        }

        // Update employee role with basic permissions
        $employeeRole = Role::where('name', 'employee')->first();
        if ($employeeRole) {
            $employeePermissions = [
                'dashboard',
                'client-list', 'client-view',
                'service-list', 'service-view',
                'invoice-list', 'invoice-view',
                'payment-list', 'payment-view',
                'weight-entry-list', 'weight-entry-create',
            ];
            $employeeRole->syncPermissions($employeePermissions);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration adds permissions, so we don't remove them in down()
        // as they might be used by other parts of the system
    }
};
