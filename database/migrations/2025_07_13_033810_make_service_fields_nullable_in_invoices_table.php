<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Temporarily disable strict mode to avoid timestamp issues
        DB::statement('SET sql_mode = ""');

        // Check if foreign keys exist and drop them
        $serviceIdForeignKeys = DB::select("
            SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'invoices'
            AND COLUMN_NAME = 'service_id'
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");

        if (!empty($serviceIdForeignKeys)) {
            $constraintName = $serviceIdForeignKeys[0]->CONSTRAINT_NAME;
            DB::statement("ALTER TABLE invoices DROP FOREIGN KEY `{$constraintName}`");
        }

        $serviceTypeForeignKeys = DB::select("
            SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'invoices'
            AND COLUMN_NAME = 'service_type'
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");

        if (!empty($serviceTypeForeignKeys)) {
            $constraintName = $serviceTypeForeignKeys[0]->CONSTRAINT_NAME;
            DB::statement("ALTER TABLE invoices DROP FOREIGN KEY `{$constraintName}`");
        }

        Schema::table('invoices', function (Blueprint $table) {
            // Make service_id and service_type nullable to support inventory invoices
            $table->unsignedBigInteger('service_id')->nullable()->change();
            $table->unsignedBigInteger('service_type')->nullable()->change();
        });

        // Note: Foreign key constraints are intentionally not recreated here
        // They can be added later if needed, but for now we just need nullable columns

        // Re-enable strict mode
        DB::statement('SET sql_mode = "STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO"');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Temporarily disable strict mode to avoid timestamp issues
        DB::statement('SET sql_mode = ""');

        // Check if foreign keys exist and drop them
        $serviceIdForeignKeys = DB::select("
            SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'invoices'
            AND COLUMN_NAME = 'service_id'
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");

        if (!empty($serviceIdForeignKeys)) {
            $constraintName = $serviceIdForeignKeys[0]->CONSTRAINT_NAME;
            DB::statement("ALTER TABLE invoices DROP FOREIGN KEY `{$constraintName}`");
        }

        $serviceTypeForeignKeys = DB::select("
            SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'invoices'
            AND COLUMN_NAME = 'service_type'
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");

        if (!empty($serviceTypeForeignKeys)) {
            $constraintName = $serviceTypeForeignKeys[0]->CONSTRAINT_NAME;
            DB::statement("ALTER TABLE invoices DROP FOREIGN KEY `{$constraintName}`");
        }

        Schema::table('invoices', function (Blueprint $table) {
            // Drop foreign key constraints first
            DB::statement('ALTER TABLE invoices DROP FOREIGN KEY fk_invoices_service_id');
            DB::statement('ALTER TABLE invoices DROP FOREIGN KEY fk_invoices_service_type');
        });

        Schema::table('invoices', function (Blueprint $table) {
            // Revert back to not nullable (be careful with existing data)
            $table->unsignedBigInteger('service_id')->nullable(false)->change();
            $table->unsignedBigInteger('service_type')->nullable(false)->change();
        });

        // Note: Foreign key constraints are intentionally not recreated here
        // They would need to be added manually if rolling back this migration

        // Re-enable strict mode
        DB::statement('SET sql_mode = "STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO"');
    }
};
