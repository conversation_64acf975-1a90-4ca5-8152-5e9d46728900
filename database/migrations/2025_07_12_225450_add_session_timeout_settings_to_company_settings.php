<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Insert session timeout settings into company_settings table
        $sessionSettings = [
            [
                'setting_key' => 'session_timeout_enabled',
                'setting_value' => '1',
                'remarks' => 'Enable/disable session timeout functionality',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => 'session_lifetime_minutes',
                'setting_value' => '120',
                'remarks' => 'Session lifetime in minutes (default: 120 minutes)',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => 'session_warning_minutes',
                'setting_value' => '5',
                'remarks' => 'Minutes before expiry to show warning (default: 5 minutes)',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => 'session_check_interval_seconds',
                'setting_value' => '60',
                'remarks' => 'How often to check session status in seconds (default: 60 seconds)',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => 'session_heartbeat_interval_minutes',
                'setting_value' => '5',
                'remarks' => 'Heartbeat interval in minutes to keep session alive (default: 5 minutes)',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'setting_key' => 'session_expire_on_close',
                'setting_value' => '0',
                'remarks' => 'Whether session expires when browser closes (0=No, 1=Yes)',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($sessionSettings as $setting) {
            // Only insert if the setting doesn't already exist
            $exists = DB::table('company_settings')
                ->where('setting_key', $setting['setting_key'])
                ->exists();

            if (!$exists) {
                DB::table('company_settings')->insert($setting);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove session timeout settings
        $settingKeys = [
            'session_timeout_enabled',
            'session_lifetime_minutes',
            'session_warning_minutes',
            'session_check_interval_seconds',
            'session_heartbeat_interval_minutes',
            'session_expire_on_close',
        ];

        DB::table('company_settings')
            ->whereIn('setting_key', $settingKeys)
            ->delete();
    }
};
