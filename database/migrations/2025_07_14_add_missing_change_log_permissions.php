<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create missing change log and audit trail permissions
        $permissions = [
            // Client change logs
            'client-change-logs',
            
            // Service change logs
            'service-change-logs',
            
            // Invoice change logs
            'invoice-change-logs',
            
            // Payment change logs and history
            'payment-change-logs',
            'payment-history',
            
            // Employee change logs
            'employee-change-logs',
            
            // Expense change logs
            'expense-change-logs',
            
            // Inventory history/logs
            'inventory-history',
            
            // Cancelled/Deleted records access
            'invoice-cancelled-list',
            'payment-deleted-list',
            
            // Additional audit permissions
            'system-audit-logs',
            'user-activity-logs',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign all new permissions to admin role
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminRole->givePermissionTo($permissions);
        }

        // Assign relevant permissions to manager role
        $managerRole = Role::where('name', 'manager')->first();
        if ($managerRole) {
            $managerPermissions = [
                'client-change-logs',
                'service-change-logs',
                'invoice-change-logs',
                'payment-change-logs',
                'payment-history',
                'employee-change-logs',
                'expense-change-logs',
                'inventory-history',
                'invoice-cancelled-list',
                'payment-deleted-list',
            ];
            $managerRole->givePermissionTo($managerPermissions);
        }

        // Assign limited permissions to employee role
        $employeeRole = Role::where('name', 'employee')->first();
        if ($employeeRole) {
            $employeePermissions = [
                'client-change-logs',
                'service-change-logs',
                'invoice-change-logs',
                'payment-history',
            ];
            $employeeRole->givePermissionTo($employeePermissions);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the permissions
        $permissions = [
            'client-change-logs',
            'service-change-logs',
            'invoice-change-logs',
            'payment-change-logs',
            'payment-history',
            'employee-change-logs',
            'expense-change-logs',
            'inventory-history',
            'invoice-cancelled-list',
            'payment-deleted-list',
            'system-audit-logs',
            'user-activity-logs',
        ];

        foreach ($permissions as $permissionName) {
            $permission = Permission::where('name', $permissionName)->first();
            if ($permission) {
                $permission->delete();
            }
        }
    }
};
