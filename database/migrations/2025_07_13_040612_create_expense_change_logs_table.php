<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expense_change_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expense_id');
            $table->string('action'); // 'created', 'updated', 'deleted', 'status_changed'
            $table->string('changed_by'); // User who made the change
            $table->json('old_values')->nullable(); // Previous values
            $table->json('new_values')->nullable(); // New values
            $table->json('changed_fields')->nullable(); // List of fields that changed
            $table->text('description')->nullable(); // Human readable description
            $table->string('ip_address')->nullable(); // IP address of user
            $table->string('user_agent')->nullable(); // Browser/device info
            $table->timestamps();

            // Indexes for better performance
            $table->index('expense_id');
            $table->index('action');
            $table->index('changed_by');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expense_change_logs');
    }
};
