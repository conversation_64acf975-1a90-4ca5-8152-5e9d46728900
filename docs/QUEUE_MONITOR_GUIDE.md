# Queue Monitor Dashboard - Complete Guide

## 📊 **Dashboard Overview**

The Queue Monitor Dashboard provides real-time monitoring and management of the email queue system. It's located at **Reports → Queue Monitor** in the application menu.

## 🎯 **Dashboard Components Explained**

### **1. Statistics Cards (Top Row)**

#### **📋 Pending Jobs**
- **What it shows**: Number of email jobs currently waiting in the queue
- **Icon**: Clock icon
- **Color**: Blue
- **Normal behavior**: 
  - Should be 0 when no emails are queued
  - Increases when invoices are created with email option
  - Decreases as queue worker processes emails

#### **❌ Failed Jobs**
- **What it shows**: Total number of jobs that failed to process
- **Icon**: Warning triangle
- **Color**: Red
- **Normal behavior**:
  - Should be 0 in ideal conditions
  - Increases when emails fail (invalid addresses, server issues)
  - Can be retried or deleted manually

#### **✅ Success Jobs**
- **What it shows**: Number of successfully sent emails in last 24 hours
- **Icon**: Check circle
- **Color**: Green
- **Normal behavior**:
  - Increases as emails are successfully sent
  - Resets daily (shows last 24 hours only)

#### **💓 Queue Status**
- **What it shows**: Overall system status and last update time
- **Icon**: Heartbeat
- **Color**: Blue
- **Shows**: "Active" status with timestamp

### **2. Queue Control Panel**

#### **🔄 Refresh Button**
- **Purpose**: Manually refresh all statistics
- **Icon**: Spinning arrows when active
- **Use**: Click to get latest queue status immediately

#### **🔄 Auto Refresh Toggle**
- **Purpose**: Enable/disable automatic refresh every 30 seconds
- **Default**: Enabled (checked)
- **Use**: Toggle off to stop automatic updates

#### **▶️ Process Queue Now**
- **Purpose**: Manually trigger queue processing
- **Use**: Click when you want to process pending emails immediately
- **Note**: Equivalent to running `php artisan email:process-queue`

#### **🗑️ Clear Failed Jobs**
- **Purpose**: Remove all failed jobs from the queue
- **Use**: Click to clean up failed jobs after fixing issues
- **Warning**: This permanently deletes failed job records

### **3. Queue Configuration Panel**

Shows current system settings:
- **Connection**: Database connection type (usually 'database')
- **Email Queue**: Queue name for emails (usually 'emails')
- **Retry Attempts**: How many times failed jobs will be retried (usually 3)
- **Delay Range**: Time delay between bulk emails (usually 5-30s)

### **4. Activity Monitoring (Tabbed Interface)**

#### **📅 Recent Activity Tab**
- **Shows**: Last 10 email queue activities
- **Information**: Timestamp and activity description
- **Updates**: Automatically refreshes with new data
- **Empty state**: Shows "No recent activity" when no recent actions

#### **✅ Success Jobs Tab**
- **Shows**: List of successfully sent emails in last 24 hours
- **Information**: 
  - Invoice code
  - Client name
  - Email address where sent
  - Time when completed
- **Updates**: Loads when tab is clicked
- **Empty state**: Shows "No successful jobs" when none found

### **5. Failed Jobs Table**

#### **Columns**:
- **Job**: Type of job (usually email-related)
- **Queue**: Which queue it was in
- **Failed**: How long ago it failed
- **Action**: Retry or Delete buttons

#### **Actions**:
- **🔄 Retry**: Attempt to process the failed job again
- **🗑️ Delete**: Permanently remove the failed job

## 🔍 **Understanding Queue Behavior**

### **Normal Flow**:
1. **Invoice Created** → Email job added to queue (Pending Jobs +1)
2. **Queue Worker Runs** → Job processed (Pending Jobs -1)
3. **Email Sent Successfully** → Job deleted from queue (Success Jobs +1)

### **When Jobs Disappear from Queue**:
✅ **This is NORMAL!** Successfully processed jobs are automatically deleted from the `jobs` table to keep the database clean. This is why you don't see completed jobs in the pending list.

### **Failure Flow**:
1. **Job Fails** → Moved to failed_jobs table (Failed Jobs +1)
2. **Auto Retry** → System retries up to 3 times
3. **Final Failure** → Job stays in failed_jobs table until manually handled

## 🚨 **Troubleshooting Guide**

### **Problem: Pending Jobs Stuck at High Number**
**Cause**: Queue worker not running
**Solution**: 
- Check if cron job is set up correctly
- Manually run: `php artisan queue:work --queue=emails`
- Click "Process Queue Now" button

### **Problem: High Failed Jobs Count**
**Cause**: Email server issues, invalid email addresses
**Solution**:
- Check failed jobs table for error details
- Fix underlying issues (email settings, client email addresses)
- Retry failed jobs or clear them

### **Problem: No Success Jobs Showing**
**Cause**: No emails sent recently or tracking not working
**Solution**:
- Create a test invoice with email option
- Check if emails are actually being sent
- Verify email configuration

### **Problem: Auto Refresh Not Working**
**Cause**: JavaScript errors or network issues
**Solution**:
- Check browser console for errors
- Manually refresh the page
- Toggle auto refresh off and on

## 📈 **Monitoring Best Practices**

### **Daily Monitoring**:
- Check pending jobs count (should be low)
- Monitor failed jobs (investigate any failures)
- Review success jobs to ensure emails are sending

### **Weekly Review**:
- Clear old failed jobs after investigation
- Review queue configuration settings
- Check system performance

### **When to Take Action**:
- **Pending jobs > 50**: Check queue worker
- **Failed jobs > 10**: Investigate email issues
- **No success jobs for hours**: Check email system
- **Auto refresh stopped**: Refresh page manually

## 🔧 **Advanced Features**

### **Real-time Updates**:
- Dashboard updates every 30 seconds automatically
- Statistics refresh without page reload
- Failed jobs table updates dynamically

### **Responsive Design**:
- Works on desktop, tablet, and mobile
- Icons and text adapt to screen size
- Tables scroll horizontally on small screens

### **Error Handling**:
- Graceful handling of network errors
- Fallback displays when data unavailable
- User-friendly error messages

## 📝 **Queue Monitor vs Database Tables**

### **What Dashboard Shows**:
- **Pending Jobs**: From `jobs` table
- **Failed Jobs**: From `failed_jobs` table  
- **Success Jobs**: Estimated from recent invoices and logs
- **Recent Activity**: From application logs

### **Why Successful Jobs Don't Stay in Database**:
- Laravel automatically deletes completed jobs
- Prevents database bloat
- Only failed jobs are retained for debugging
- Success tracking is done through logs and estimates

This behavior is by design and indicates a healthy, functioning queue system!
