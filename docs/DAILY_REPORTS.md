# Daily Report System Documentation

## Overview

The Daily Report System provides comprehensive automated business reporting functionality that generates and sends detailed daily summaries via email. This system tracks key business metrics including revenue, collections, outstanding amounts, and growth indicators.

## Features

### 📊 **Comprehensive Reporting**
- **Invoice Statistics**: Total invoices, amounts, payment status
- **Payment Analytics**: Collections by payment method, efficiency metrics
- **Client Growth**: New client acquisitions, active client counts
- **Employee Management**: New employee additions, team growth
- **Service Expansion**: New service activations, service portfolio growth
- **Outstanding Tracking**: Pending and overdue amounts monitoring
- **Top Performers**: Revenue-based client rankings
- **Recent Activities**: Timeline of business activities

### 🔧 **Configuration Options**
- **Enable/Disable**: Toggle daily report generation
- **Email Recipients**: Multiple email addresses (comma-separated)
- **Manual Generation**: On-demand report creation
- **Preview Functionality**: Review reports before sending
- **Scheduled Automation**: Daily automated delivery at 8:00 AM

### 📧 **Professional Email Templates**
- **Responsive Design**: Mobile-friendly email layout
- **Visual Metrics**: Color-coded summary cards
- **Detailed Breakdowns**: Comprehensive statistics tables
- **Company Branding**: Customized with company information
- **Interactive Elements**: Hover effects and professional styling

## Setup Instructions

### 1. **Database Configuration**
The system automatically creates the following settings in `company_settings` table:
- `daily_report_alert_enabled`: Enable/disable daily reports (0/1)
- `daily_report_alert_emails`: Comma-separated email addresses

### 2. **Email Configuration**
Ensure your email settings are properly configured in the Settings page:
- SMTP settings for email delivery
- Company email address for sender information
- Test email functionality before enabling reports

### 3. **Cron Job Setup**
Add the following to your server's crontab for automated scheduling:
```bash
* * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1
```

### 4. **Enable Daily Reports**
1. Go to **Settings → Notification Alerts**
2. Scroll to **Daily Report Alerts** section
3. Toggle **Enable Daily Report Alerts**
4. Add email addresses in **Email Recipients** field
5. Save settings

## Usage Guide

### **Automated Daily Reports**
- Reports are automatically generated daily at 8:00 AM
- Sent to all configured email addresses
- Covers data from the previous day
- No manual intervention required

### **Manual Report Generation**
1. Go to **Settings → Notification Alerts → Daily Report Alerts**
2. Select desired **Report Date**
3. Click **Preview** to review report data
4. Click **Generate & Send** to send the report

### **Command Line Usage**
```bash
# Generate report for yesterday (default)
php artisan report:daily

# Generate report for specific date
php artisan report:daily --date=2025-01-15

# View command help
php artisan report:daily --help
```

## Report Content

### **Summary Metrics**
- **Total Revenue**: Sum of all invoices generated
- **Collections**: Total payments received
- **Outstanding**: Pending and overdue amounts
- **Collection Efficiency**: Payment collection percentage

### **Detailed Statistics**
- **Invoice Breakdown**: Count and amounts by status
- **Payment Analysis**: Methods and amounts
- **Growth Metrics**: New clients, services, employees
- **Top Clients**: Revenue-based rankings
- **Recent Activities**: Latest business transactions

### **Visual Elements**
- Color-coded summary cards
- Professional charts and graphs
- Responsive table layouts
- Mobile-friendly design

## Technical Implementation

### **Core Components**
- **DailyReportService**: Main service class for report generation
- **SendDailyReport**: Artisan command for CLI execution
- **Email Template**: Professional HTML email layout
- **Settings Integration**: Configuration management
- **Scheduled Tasks**: Automated execution

### **Database Queries**
- Optimized queries for large datasets
- Date-range filtering for accurate reporting
- Relationship loading for complete data
- Performance-optimized aggregations

### **Error Handling**
- Comprehensive exception handling
- Detailed logging for troubleshooting
- Graceful failure recovery
- User-friendly error messages

## Customization Options

### **Report Content**
Modify `app/Services/DailyReportService.php` to:
- Add new metrics
- Change calculation methods
- Include additional data sources
- Customize report structure

### **Email Template**
Edit `resources/views/email/reports/daily-report.blade.php` to:
- Change visual design
- Add company branding
- Modify layout structure
- Include additional sections

### **Scheduling**
Update `app/Console/Kernel.php` to:
- Change report timing
- Add multiple schedules
- Modify execution parameters
- Add conditional scheduling

## Troubleshooting

### **Common Issues**

1. **Reports Not Sending**
   - Check email configuration in Settings
   - Verify cron job is running
   - Check Laravel logs for errors
   - Ensure daily_report_alert_enabled = 1

2. **Empty Report Data**
   - Verify database connections
   - Check date range parameters
   - Ensure data exists for selected date
   - Review query filters

3. **Email Delivery Issues**
   - Test SMTP settings
   - Check spam folders
   - Verify recipient email addresses
   - Review email logs

4. **Performance Issues**
   - Optimize database queries
   - Add database indexes
   - Implement query caching
   - Monitor server resources

### **Debugging Commands**
```bash
# Test report generation
php artisan report:daily --date=2025-01-15

# Check scheduled tasks
php artisan schedule:list

# View logs
tail -f storage/logs/laravel.log

# Test email configuration
php artisan tinker
```

## Security Considerations

- **Email Validation**: All email addresses are validated
- **Access Control**: Settings require admin permissions
- **Data Privacy**: Reports contain sensitive business data
- **Secure Transmission**: Use HTTPS for email delivery

## Performance Optimization

- **Database Indexing**: Ensure proper indexes on date columns
- **Query Optimization**: Use efficient database queries
- **Caching**: Implement report data caching if needed
- **Background Processing**: Reports run in background

## Future Enhancements

- **Custom Report Templates**: Multiple template options
- **Advanced Filtering**: Date ranges, client segments
- **Export Options**: PDF, Excel export formats
- **Dashboard Integration**: Real-time report viewing
- **Webhook Support**: Third-party integrations
- **Multi-language Support**: Localized reports

## Support

For technical support or feature requests:
1. Check the troubleshooting section
2. Review Laravel logs for errors
3. Test individual components
4. Contact system administrator

---

**Last Updated**: January 2025  
**Version**: 1.0  
**Compatibility**: Laravel 10+, PHP 8.1+
