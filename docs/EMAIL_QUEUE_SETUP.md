# Email Queue System for Invoice Generation

This document explains how to set up and use the email queue system for sending invoice emails during bulk generation.

## Features

- **Environment-based Email Routing**: Automatically routes emails to test addresses in non-live environments
- **Queue-based Processing**: Prevents overwhelming email servers during bulk operations
- **Configurable Delays**: Adds random delays between emails to comply with rate limits
- **Retry Logic**: Automatically retries failed email sends
- **Comprehensive Logging**: Tracks all email sending attempts

## Setup Instructions

### 1. Environment Configuration

Add the following variables to your `.env` file:

```env
# Email Queue Configuration
EMAIL_QUEUE_CONNECTION=database
EMAIL_QUEUE_NAME=emails
EMAIL_DELAY_MIN=5
EMAIL_DELAY_MAX=30
EMAIL_RETRY_ATTEMPTS=3
EMAIL_RETRY_DELAY=60
EMAIL_LIVE_ENV=live
EMAIL_TEST_FALLBACK=<EMAIL>
EMAIL_TESTER_SETTING_KEY=tester_mailid
EMAIL_QUEUE_LOGGING=true

# Queue Configuration (if using database queue)
QUEUE_CONNECTION=database

# UPI Configuration (for QR codes)
UPI_ID=your-upi-id@bank
UPI_NAME=Your Company Name
```

### 2. Database Setup

#### Create Queue Tables
```bash
php artisan queue:table
php artisan migrate
```

#### Add Tester Email Setting
```bash
php artisan db:seed --class=TesterEmailSeeder
```

Or manually add to `company_settings` table:
```sql
INSERT INTO company_settings (setting_key, setting_value, created_at, updated_at) 
VALUES ('tester_mailid', '<EMAIL>', NOW(), NOW());
```

### 3. Queue Worker Setup

#### Option A: Manual Processing
```bash
# Process emails immediately
php artisan queue:work --queue=emails

# Process with timeout
php artisan email:process-queue --timeout=120
```

#### Option B: Cron Job Setup
Add to your crontab:
```bash
# Process email queue every minute
* * * * * cd /path/to/your/project && php artisan email:process-queue --timeout=60 >> /dev/null 2>&1

# Alternative: Use Laravel's scheduler
* * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1
```

Then add to `app/Console/Kernel.php`:
```php
protected function schedule(Schedule $schedule)
{
    $schedule->command('email:process-queue --timeout=60')
             ->everyMinute()
             ->withoutOverlapping();
}
```

## How It Works

### Environment-based Email Routing

#### Live Environment (`APP_ENV=live`)
- Emails are sent to actual client email addresses
- Uses `clients.email` field

#### Non-Live Environments (development, staging, etc.)
- Emails are redirected to test email address
- Uses `tester_mailid` from `company_settings` table
- Fallback to `EMAIL_TEST_FALLBACK` if setting not found

### Email Queue Process

1. **Bulk Invoice Generation**:
   - Invoices are created in database
   - Email jobs are queued for each invoice
   - Random delays (5-30 seconds) are added to prevent server overload

2. **Manual Invoice Creation**:
   - User creates invoice via web form
   - Optional checkbox to send email automatically
   - If enabled, email is queued for immediate processing (no delay)

3. **Individual Invoice Email** (from invoice list):
   - Uses same queue system
   - Immediate processing (no delay)

4. **Queue Processing**:
   - Jobs are processed by queue worker
   - Failed jobs are retried up to 3 times
   - All attempts are logged

## Usage Examples

### Bulk Invoice Generation
```php
// In InvoiceController::generateInvoices()
// Emails are automatically queued for each generated invoice with delays
$this->queueInvoiceEmail($invoiceId, $clientId, false); // false = bulk generation
```

### Manual Invoice Creation
```php
// In InvoiceController::manul_invoice_store()
// Email is queued immediately if user checks the email option
if ($request->has('send_email') && $request->send_email) {
    $this->queueInvoiceEmail($invoice->id, $request->client, true); // true = manual
}
```

### Individual Invoice Email
```php
// In InvoiceController::mail_sent()
// Email is queued for immediate processing
SendInvoiceEmailJob::dispatch($invoiceId, $recipientEmail);
```

### Manual Queue Processing
```bash
# Process all pending email jobs
php artisan queue:work --queue=emails --stop-when-empty

# Process with custom timeout
php artisan email:process-queue --timeout=300
```

## Monitoring and Troubleshooting

### Check Queue Status
```bash
# View failed jobs
php artisan queue:failed

# Retry failed jobs
php artisan queue:retry all

# Clear failed jobs
php artisan queue:flush
```

### Log Files
- Email sending attempts: `storage/logs/laravel.log`
- Queue processing: Check your configured log channel

### Common Issues

1. **Emails not sending**: Check queue worker is running
2. **Wrong email addresses**: Verify `tester_mailid` setting in database
3. **Rate limiting**: Increase delay settings in config
4. **Failed jobs**: Check logs and retry failed jobs

## Configuration Reference

All configuration options are in `config/email-queue.php`:

- `connection`: Queue connection to use
- `queue`: Queue name for email jobs
- `delay`: Min/max delay between emails
- `retry`: Retry attempts and delay
- `environment_routing`: Email routing settings
- `logging`: Logging configuration

## Queue Monitor Dashboard

### Accessing the Dashboard
Navigate to: **Reports → Queue Monitor** in the application menu

### Dashboard Features
- **Real-time Statistics**: Pending jobs, failed jobs, recent failures
- **Auto-refresh**: Updates every 30 seconds (can be toggled)
- **Queue Control**: Process queue manually, clear failed jobs
- **Failed Jobs Management**: Retry or delete individual failed jobs
- **Configuration Display**: Shows current queue settings
- **Recent Activity**: Shows recent email queue activity

### Dashboard Actions
1. **Process Queue Now**: Manually trigger queue processing
2. **Clear Failed Jobs**: Remove all failed jobs from the queue
3. **Retry Job**: Retry a specific failed job
4. **Delete Job**: Remove a specific failed job
5. **Auto Refresh**: Toggle automatic dashboard updates

## Testing the System

### Test Email Queue
```bash
# Test with latest invoice
php artisan email:test-queue

# Test with specific invoice ID
php artisan email:test-queue 123

# Test manual invoice creation process
php artisan email:test-manual-invoice

# Test with specific client
php artisan email:test-manual-invoice 5
```

### Test Manual Invoice Creation (Web Interface)
1. Navigate to: **Invoices → Add New Invoice**
2. Fill in the invoice details
3. Check **"Send invoice email automatically"** checkbox
4. Click **"Create Invoice"**
5. Check Queue Monitor dashboard for email status
6. Process queue: `php artisan queue:work --queue=emails`

### Monitor Queue Status
```bash
# Check pending jobs
php artisan queue:monitor

# View queue dashboard
# Navigate to Reports → Queue Monitor in web interface
```

## Security Considerations

- Test email addresses are only used in non-live environments
- Actual client emails are protected in development
- Queue jobs contain minimal sensitive data
- Failed jobs are logged but not exposed publicly
- Queue monitor dashboard requires authentication
