<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Email Queue Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the email queue system
    | used for sending invoice emails in bulk operations.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Queue Connection
    |--------------------------------------------------------------------------
    |
    | The queue connection to use for email jobs. This should match one of
    | the connections defined in your queue configuration file.
    |
    */
    'connection' => env('EMAIL_QUEUE_CONNECTION', 'database'),

    /*
    |--------------------------------------------------------------------------
    | Queue Name
    |--------------------------------------------------------------------------
    |
    | The name of the queue to use for email jobs. This allows you to
    | separate email jobs from other queued jobs.
    |
    */
    'queue' => env('EMAIL_QUEUE_NAME', 'emails'),

    /*
    |--------------------------------------------------------------------------
    | Email Delay Settings
    |--------------------------------------------------------------------------
    |
    | Configure delays for email sending to avoid overwhelming the email
    | server and to comply with rate limits.
    |
    */
    'delay' => [
        'min_seconds' => env('EMAIL_DELAY_MIN', 5),
        'max_seconds' => env('EMAIL_DELAY_MAX', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Retry Settings
    |--------------------------------------------------------------------------
    |
    | Configure how many times failed email jobs should be retried and
    | the delay between retries.
    |
    */
    'retry' => [
        'attempts' => env('EMAIL_RETRY_ATTEMPTS', 3),
        'delay_seconds' => env('EMAIL_RETRY_DELAY', 60),
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment-based Email Routing
    |--------------------------------------------------------------------------
    |
    | Configure how emails are routed based on the application environment.
    | In non-live environments, emails can be redirected to test addresses.
    |
    */
    'environment_routing' => [
        'live_env' => env('EMAIL_LIVE_ENV', 'live'),
        'test_email_fallback' => env('EMAIL_TEST_FALLBACK', '<EMAIL>'),
        'tester_setting_key' => env('EMAIL_TESTER_SETTING_KEY', 'tester_mailid'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging
    |--------------------------------------------------------------------------
    |
    | Configure logging for email queue operations.
    |
    */
    'logging' => [
        'enabled' => env('EMAIL_QUEUE_LOGGING', true),
        'channel' => env('EMAIL_QUEUE_LOG_CHANNEL', 'single'),
    ],

];
