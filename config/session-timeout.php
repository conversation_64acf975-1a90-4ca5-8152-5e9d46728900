<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Session Timeout Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the session timeout
    | functionality. You can customize the behavior of automatic logout
    | and user experience settings here.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Enable Session Timeout
    |--------------------------------------------------------------------------
    |
    | This option controls whether the session timeout functionality is
    | enabled. Set to false to disable automatic session timeout.
    |
    */

    'enabled' => env('SESSION_TIMEOUT_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Warning Time
    |--------------------------------------------------------------------------
    |
    | The number of minutes before session expiry to show the warning dialog.
    | Default is 5 minutes before expiry.
    |
    */

    'warning_time' => env('SESSION_TIMEOUT_WARNING', 5),

    /*
    |--------------------------------------------------------------------------
    | Check Interval
    |--------------------------------------------------------------------------
    |
    | How often (in milliseconds) to check for session timeout.
    | Default is every 60 seconds (60000ms).
    |
    */

    'check_interval' => env('SESSION_TIMEOUT_CHECK_INTERVAL', 60000),

    /*
    |--------------------------------------------------------------------------
    | Heartbeat Interval
    |--------------------------------------------------------------------------
    |
    | How often (in milliseconds) to send heartbeat requests to keep
    | the session alive when user is active. Default is every 5 minutes.
    |
    */

    'heartbeat_interval' => env('SESSION_TIMEOUT_HEARTBEAT_INTERVAL', 300000),

    /*
    |--------------------------------------------------------------------------
    | Activity Events
    |--------------------------------------------------------------------------
    |
    | List of DOM events that should be considered as user activity.
    | These events will reset the inactivity timer.
    |
    */

    'activity_events' => [
        'mousedown',
        'mousemove', 
        'keypress',
        'scroll',
        'touchstart',
        'click'
    ],

    /*
    |--------------------------------------------------------------------------
    | Redirect URLs
    |--------------------------------------------------------------------------
    |
    | URLs for logout and login redirects.
    |
    */

    'logout_url' => '/logout',
    'login_url' => '/login',

    /*
    |--------------------------------------------------------------------------
    | Modal Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the session timeout warning modal.
    |
    */

    'modal' => [
        'title' => 'Session Timeout Warning',
        'message' => 'Your session will expire soon due to inactivity.',
        'stay_button_text' => 'Stay Logged In',
        'logout_button_text' => 'Logout Now',
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Messages
    |--------------------------------------------------------------------------
    |
    | Messages shown to users during session timeout events.
    |
    */

    'messages' => [
        'session_extended' => 'Session extended successfully!',
        'session_expired' => 'Session expired. Redirecting to login...',
        'heartbeat_failed' => 'Connection lost. Please check your internet connection.',
    ],

];
