<?php
/**
 * Queue Worker Script for cPanel Hosting
 * This script processes the email queue when cron jobs have limitations
 */

// Set the working directory to your Laravel project
$projectPath = __DIR__;
chdir($projectPath);

// Include <PERSON>vel's autoloader
require_once $projectPath . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once $projectPath . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);

try {
    // Log the start of queue processing
    error_log('[' . date('Y-m-d H:i:s') . '] Queue worker started');
    
    // Process the queue with a timeout
    $exitCode = $kernel->call('email:process-queue', [
        '--timeout' => 60
    ]);
    
    if ($exitCode === 0) {
        error_log('[' . date('Y-m-d H:i:s') . '] Queue processed successfully');
    } else {
        error_log('[' . date('Y-m-d H:i:s') . '] Queue processing failed with exit code: ' . $exitCode);
    }
    
} catch (Exception $e) {
    error_log('[' . date('Y-m-d H:i:s') . '] Queue worker error: ' . $e->getMessage());
}

echo "Queue processing completed at " . date('Y-m-d H:i:s') . "\n";
?>
