<?php
/**
 * Test Queue Setup Script
 * Run this via browser or command line to test queue functionality
 */

// Include Laravel
require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);

echo "<h2>Queue System Test</h2>";

try {
    // Test database connection
    $pdo = new PDO(
        'mysql:host=' . env('DB_HOST') . ';dbname=' . env('DB_DATABASE'),
        env('DB_USERNAME'),
        env('DB_PASSWORD')
    );
    echo "✅ Database connection: OK<br>";
    
    // Check if queue tables exist
    $tables = $pdo->query("SHOW TABLES LIKE 'jobs'")->fetchAll();
    if (count($tables) > 0) {
        echo "✅ Jobs table: EXISTS<br>";
    } else {
        echo "❌ Jobs table: MISSING<br>";
    }
    
    $tables = $pdo->query("SHOW TABLES LIKE 'failed_jobs'")->fetchAll();
    if (count($tables) > 0) {
        echo "✅ Failed jobs table: EXISTS<br>";
    } else {
        echo "❌ Failed jobs table: MISSING<br>";
    }
    
    // Check pending jobs
    $pendingJobs = $pdo->query("SELECT COUNT(*) FROM jobs")->fetchColumn();
    echo "📊 Pending jobs: {$pendingJobs}<br>";
    
    // Check failed jobs
    $failedJobs = $pdo->query("SELECT COUNT(*) FROM failed_jobs")->fetchColumn();
    echo "📊 Failed jobs: {$failedJobs}<br>";
    
    // Check tester email setting
    $testerEmail = $pdo->query("SELECT setting_value FROM company_settings WHERE setting_key = 'tester_mailid'")->fetchColumn();
    if ($testerEmail) {
        echo "✅ Tester email: {$testerEmail}<br>";
    } else {
        echo "❌ Tester email: NOT CONFIGURED<br>";
    }
    
    // Test environment
    echo "🌍 Environment: " . env('APP_ENV') . "<br>";
    echo "📧 Queue connection: " . env('QUEUE_CONNECTION') . "<br>";
    
    echo "<br><h3>Test Results:</h3>";
    if ($pendingJobs > 0) {
        echo "⚠️ You have {$pendingJobs} pending jobs. Run queue worker to process them.<br>";
    } else {
        echo "✅ No pending jobs in queue.<br>";
    }
    
    if ($failedJobs > 0) {
        echo "⚠️ You have {$failedJobs} failed jobs. Check the Queue Monitor dashboard.<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<br><h3>Next Steps:</h3>";
echo "1. Create a manual invoice with email option checked<br>";
echo "2. Check if job appears in queue<br>";
echo "3. Verify cron job is processing the queue<br>";
echo "4. Monitor via Queue Monitor dashboard<br>";
?>
