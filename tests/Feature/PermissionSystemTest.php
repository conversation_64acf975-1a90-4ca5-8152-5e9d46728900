<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PermissionSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create permissions
        $permissions = [
            'dashboard',
            'client-list', 'client-view', 'client-create', 'client-edit', 'client-delete',
            'service-list', 'service-view', 'service-create', 'service-edit',
            'invoice-list', 'invoice-view', 'invoice-create', 'invoice-edit',
            'payment-list', 'payment-view', 'payment-create', 'payment-edit',
            'employee-list', 'employee-view', 'employee-create', 'employee-edit',
            'role-list', 'role-create', 'role-edit', 'role-permission',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles
        $adminRole = Role::create(['name' => 'admin']);
        $managerRole = Role::create(['name' => 'manager']);
        $employeeRole = Role::create(['name' => 'employee']);

        // Assign permissions
        $adminRole->givePermissionTo($permissions);
        $managerRole->givePermissionTo([
            'dashboard', 'client-list', 'client-view', 'client-create', 'client-edit',
            'service-list', 'service-view', 'invoice-list', 'invoice-view'
        ]);
        $employeeRole->givePermissionTo(['dashboard', 'client-list', 'client-view']);
    }

    public function test_admin_can_access_all_permissions()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $this->assertTrue($admin->can('dashboard'));
        $this->assertTrue($admin->can('client-create'));
        $this->assertTrue($admin->can('role-permission'));
    }

    public function test_manager_has_limited_permissions()
    {
        $manager = User::factory()->create();
        $manager->assignRole('manager');

        $this->assertTrue($manager->can('dashboard'));
        $this->assertTrue($manager->can('client-create'));
        $this->assertFalse($manager->can('role-permission'));
        $this->assertFalse($manager->can('client-delete'));
    }

    public function test_employee_has_basic_permissions()
    {
        $employee = User::factory()->create();
        $employee->assignRole('employee');

        $this->assertTrue($employee->can('dashboard'));
        $this->assertTrue($employee->can('client-list'));
        $this->assertFalse($employee->can('client-create'));
        $this->assertFalse($employee->can('role-permission'));
    }

    public function test_permission_middleware_blocks_unauthorized_access()
    {
        $employee = User::factory()->create();
        $employee->assignRole('employee');

        $response = $this->actingAs($employee)->get('/roles');
        $response->assertStatus(403);
    }

    public function test_permission_middleware_allows_authorized_access()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)->get('/roles');
        $response->assertStatus(200);
    }

    public function test_role_cloning_works()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)->post('/roles/clone', [
            'source_role_id' => Role::where('name', 'manager')->first()->id,
            'new_role_name' => 'manager_copy'
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('roles', ['name' => 'manager_copy']);
        
        $originalRole = Role::where('name', 'manager')->first();
        $clonedRole = Role::where('name', 'manager_copy')->first();
        
        $this->assertEquals(
            $originalRole->permissions->count(),
            $clonedRole->permissions->count()
        );
    }

    public function test_permission_template_application()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $testRole = Role::create(['name' => 'test_role']);

        $response = $this->actingAs($admin)->post('/roles/apply-template', [
            'role_id' => $testRole->id,
            'template' => 'employee'
        ]);

        $response->assertRedirect();
        
        $testRole->refresh();
        $this->assertTrue($testRole->hasPermissionTo('dashboard'));
        $this->assertTrue($testRole->hasPermissionTo('client-list'));
        $this->assertFalse($testRole->hasPermissionTo('client-create'));
    }

    public function test_bulk_permission_update()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $role1 = Role::create(['name' => 'test_role_1']);
        $role2 = Role::create(['name' => 'test_role_2']);

        $response = $this->actingAs($admin)->post('/roles/bulk-update-permissions', [
            'role_ids' => [$role1->id, $role2->id],
            'permissions' => ['dashboard', 'client-list'],
            'action' => 'add'
        ]);

        $response->assertRedirect();
        
        $role1->refresh();
        $role2->refresh();
        
        $this->assertTrue($role1->hasPermissionTo('dashboard'));
        $this->assertTrue($role2->hasPermissionTo('client-list'));
    }

    public function test_permission_helper_trait_methods()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $this->actingAs($admin);

        // Test the trait methods through a controller that uses them
        $controller = new \App\Http\Controllers\AdminController();
        
        // Use reflection to test protected methods
        $reflection = new \ReflectionClass($controller);
        $hasPermissionMethod = $reflection->getMethod('hasPermission');
        $hasPermissionMethod->setAccessible(true);

        $this->assertTrue($hasPermissionMethod->invoke($controller, 'dashboard'));
        $this->assertTrue($hasPermissionMethod->invoke($controller, 'role-permission'));
    }
}
